<template>
  <div class="vue-data-table" :class="[`theme-${theme}`, { 'with-sticky-first-column': stickyFirstColumn }]">
    <div class="vue-data-table-header">
      <slot name="header">
        <table-header :title="title" :show-search="showSearch" :show-export="showExport" :show-print="showPrint"
          :show-column-selector="showColumnSelector" :columns="columns" :visible-columns="visibleColumns"
          :show-selection-toggle="selectable" :selection-count="selectedItems.length" @search="handleSearch"
          @column-visibility-change="updateVisibleColumns" @toggle-selection="toggleSelectAll">
          <template #actions>
            <slot name="header-actions"></slot>
          </template>
        </table-header>
      </slot>
    </div>

    <div v-if="showAdditionalFilters && additionalFilters.length > 0" class="vue-data-table-additional-filters">
      <additional-filters :filters="additionalFilters" @filter-change="handleAdditionalFilter"
        @filter-remove="removeAdditionalFilter" @filters-reset="resetAdditionalFilters" />
    </div>

    <div class="vue-data-table-body" :class="{ 'loading': loading }">
      <div v-if="loading" class="vue-data-table-loading">
        <div class="spinner"></div>
        <span>Loading...</span>
      </div>

      <div class="table-responsive"
        :style="{ maxHeight: maxHeight ? `${maxHeight}px` : 'none', overflowY: maxHeight || stickyHeader ? 'auto' : 'visible' }"
        :class="{ 'has-sticky-header': stickyHeader }">
        <table class="table" :class="tableClass">
          <thead>
            <!-- Column groups header row (if using column groups) -->
            <template v-if="hasColumnGroups">
              <!-- First row with column groups -->
              <tr class="column-groups-row">
                <th v-if="selectable" class="select-column" :class="{ 'sticky-column': stickyFirstColumn }" rowspan="2">
                  <div class="th-content">
                    <input type="checkbox" :checked="allSelected" @change="toggleSelectAll"
                      class="select-all-checkbox" />
                  </div>
                </th>

                <template v-for="(column, index) in columns" :key="`group-${index}`">
                  <!-- Column with children (group) -->
                  <template v-if="column.children">
                    <column-group :label="column.label" :colspan="column.children.length"
                      :rowspan="column.rowspan || 1" />
                  </template>

                  <!-- Regular column with rowspan -->
                  <template v-else-if="column.key">
                    <th :rowspan="2" :class="[
                      column.sortable ? 'sortable' : '',
                      sortKey === column.key ? `sorted-${sortOrder}` : '',
                      { 'sticky-column': stickyFirstColumn && index === 0 }
                    ]" :style="getColumnStyle(column)" @click="column.sortable ? sort(column.key) : null">
                      <div class="th-content">
                        {{ column.label }}
                        <span v-if="column.sortable" class="sort-icon">
                          <span class="sort-arrow sort-arrow-up"></span>
                          <span class="sort-arrow sort-arrow-down"></span>
                        </span>
                      </div>
                    </th>
                  </template>
                </template>

                <th v-if="hasActions" class="actions-column" rowspan="2">
                  <div class="th-content">Actions</div>
                </th>
              </tr>

              <!-- Second row with child columns -->
              <tr class="column-child-row">
                <template v-for="column in columns" :key="`child-group-${column.label}`">
                  <template v-if="column.children">
                    <th v-for="childCol in column.children" :key="`child-${childCol.key}`" :class="[
                      childCol.sortable ? 'sortable' : '',
                      sortKey === childCol.key ? `sorted-${sortOrder}` : ''
                    ]" :style="getColumnStyle(childCol)" @click="childCol.sortable ? sort(childCol.key) : null">
                      <div class="th-content">
                        {{ childCol.label }}
                        <span v-if="childCol.sortable" class="sort-icon">
                          <span class="sort-arrow sort-arrow-up"></span>
                          <span class="sort-arrow sort-arrow-down"></span>
                        </span>
                      </div>
                    </th>
                  </template>
                </template>
              </tr>
            </template>

            <!-- Standard header row (if not using column groups) -->
            <tr v-else>
              <th v-if="selectable" class="select-column" :class="{ 'sticky-column': stickyFirstColumn }">
                <div class="th-content">
                  <input type="checkbox" :checked="allSelected" @change="toggleSelectAll" class="select-all-checkbox" />
                </div>
              </th>
              <th v-for="column in visibleColumnsData" :key="column.key" :class="[
                column.sortable ? 'sortable' : '',
                sortKey === column.key ? `sorted-${sortOrder}` : '',
                { 'sticky-column': stickyFirstColumn && column.isFirstDataColumn }
              ]" :style="getColumnStyle(column)" @click="column.sortable ? sort(column.key) : null">
                <div class="th-content">
                  {{ column.label }}
                  <span v-if="column.sortable" class="sort-icon">
                    <span class="sort-arrow sort-arrow-up"></span>
                    <span class="sort-arrow sort-arrow-down"></span>
                  </span>
                </div>
              </th>
              <th v-if="hasActions" class="actions-column">
                <div class="th-content">Actions</div>
              </th>
            </tr>

            <!-- Column filters row -->
            <tr v-if="showColumnFilters" class="column-filters-row" :class="{ 'sticky-filters': stickyFilters }">
              <th v-if="selectable" class="select-column" :class="{ 'sticky-column': stickyFirstColumn }"></th>
              <th v-for="column in visibleColumnsData" :key="`filter-${column.key}`" :class="{
                'sticky-column': stickyFirstColumn && column.isFirstDataColumn
              }" :style="getColumnStyle(column)">
                <column-filter v-if="isFilterableColumn(column.key)" :column="column" :type="getFilterType(column.key)"
                  :options="getFilterOptions(column.key)" :debounce="300" @filter="handleColumnFilter" />
              </th>
              <th v-if="hasActions" class="actions-column"></th>
            </tr>
          </thead>
          <tbody>
            <tr v-if="!loading && (!items || items.length === 0)">
              <td :colspan="totalVisibleColumns" class="no-data">
                <slot name="no-data">
                  <div class="no-data-content">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                      <path fill="none" d="M0 0h24v24H0z" />
                      <path
                        d="M20 2c.552 0 1 .448 1 1v18c0 .552-.448 1-1 1H4c-.552 0-1-.448-1-1V3c0-.552.448-1 1-1h16zm-1 2H5v16h14V4zM8 7h8v2H8V7zm0 4h8v2H8v-2zm0 4h5v2H8v-2z"
                        fill="currentColor" />
                    </svg>
                    <p>No data available</p>
                  </div>
                </slot>
              </td>
            </tr>
            <tr v-for="(item, index) in paginatedItems" :key="getItemKey(item, index)"
              :class="{ 'selected': isSelected(item) }" @click="handleRowClick(item, index)">
              <td v-if="selectable" class="select-column" :class="{ 'sticky-column': stickyFirstColumn }">
                <div class="td-content">
                  <input type="checkbox" :checked="isSelected(item)" @change="toggleSelect(item)" @click.stop
                    class="select-row-checkbox" />
                </div>
              </td>
              <td v-for="column in visibleColumnsData" :key="column.key"
                :class="{ 'sticky-column': stickyFirstColumn && column.isFirstDataColumn }"
                :style="getColumnStyle(column)">
                <div class="td-content">
                  <slot :name="`cell-${column.key}`" :item="item" :value="getItemValue(item, column.key)">
                    {{ getItemValue(item, column.key) }}
                  </slot>
                </div>
              </td>
              <td v-if="hasActions" class="actions-column">
                <div class="td-content">
                  <slot name="actions" :item="item" :index="index"></slot>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div class="vue-data-table-footer">
      <slot name="footer">
        <table-pagination v-if="showPagination" :total="serverSide ? totalItems : filteredItems.length"
          :page="currentPage" :page-size="pageSize" :page-sizes="pageSizes" :server-side="serverSide"
          @page-change="handlePageChange" @page-size-change="handlePageSizeChange" />
      </slot>
    </div>

    <table-export v-if="showExport" :columns="columns" :data="exportData" :file-name="exportFileName"
      :visible="showExportModal" :selected-items="selectedItems" :is-server-side="serverSide"
      :is-loading-all-data="isLoadingAllData" :fetch-all-data="fetchAllServerData" @close="showExportModal = false" />

    <print-table v-if="showPrint" :columns="columns" :data="exportData" :title="title" :visible="showPrintModal"
      :selected-items="selectedItems" :filters="columnFilters" :is-server-side="serverSide"
      :is-loading-all-data="isLoadingAllData" :fetch-all-data="fetchAllServerData" @close="showPrintModal = false" />
  </div>
</template>

<script>
import {
  computed,
  nextTick,
  onMounted,
  onUnmounted,
  provide,
  ref,
  watch,
} from 'vue'

import AdditionalFilters from './AdditionalFilters.vue'
import ColumnFilter from './ColumnFilter.vue'
import ColumnGroup from './ColumnGroup.vue'
import PrintTable from './PrintTable.vue'
import TableExport from './TableExport.vue'
import TableHeader from './TableHeader.vue'
import TablePagination from './TablePagination.vue'

export default {
  name: 'DataTable',
  components: {
    AdditionalFilters,
    TableHeader,
    TablePagination,
    TableExport,
    PrintTable,
    ColumnFilter,
    ColumnGroup
  },
  props: {
    // Data props
    items: {
      type: Array,
      default: () => []
    },
    columns: {
      type: Array,
      required: true,
      validator: (columns) => columns.every(col => {
        // Allow columns with children (column groups)
        if (col.children) {
          return col.label && Array.isArray(col.children) &&
            col.children.every(child => child.key && child.label);
        }
        // Regular columns must have key and label
        return col.key && col.label;
      })
    },
    itemKey: {
      type: String,
      default: 'id'
    },

    // Appearance props
    title: {
      type: String,
      default: ''
    },
    tableClass: {
      type: String,
      default: ''
    },
    theme: {
      type: String,
      default: 'default',
      validator: (value) => ['default', 'primary', 'dark', 'light'].includes(value)
    },

    // Feature flags
    selectable: {
      type: Boolean,
      default: false
    },
    showSearch: {
      type: Boolean,
      default: true
    },
    showPagination: {
      type: Boolean,
      default: true
    },
    showExport: {
      type: Boolean,
      default: true
    },
    showPrint: {
      type: Boolean,
      default: true
    },
    showColumnSelector: {
      type: Boolean,
      default: false // Disabled by default since it's not working correctly
    },
    stickyFirstColumn: {
      type: Boolean,
      default: false
    },

    // Column filters
    showColumnFilters: {
      type: Boolean,
      default: false
    },
    filterableColumns: {
      type: Array,
      default: () => []
    },
    filterConfig: {
      type: Object,
      default: () => ({})
    },

    // Additional filters (not tied to visible columns)
    additionalFilters: {
      type: Array,
      default: () => []
      // Each filter should have: key, label, type, and options (for select type)
    },
    showAdditionalFilters: {
      type: Boolean,
      default: false
    },

    // Pagination props
    pageSize: {
      type: Number,
      default: 10
    },
    pageSizes: {
      type: Array,
      default: () => [10, 20, 50, 100]
    },
    serverSide: {
      type: Boolean,
      default: false
    },
    totalItems: {
      type: Number,
      default: 0
    },
    currentPageServer: {
      type: Number,
      default: 1
    },

    // Loading state
    loading: {
      type: Boolean,
      default: false
    },

    // Export options
    exportFileName: {
      type: String,
      default: 'table-export'
    },

    // Table height and scrolling
    maxHeight: {
      type: Number,
      default: null
    },

    // Sticky header
    stickyHeader: {
      type: Boolean,
      default: true
    },

    // Sticky filters
    stickyFilters: {
      type: Boolean,
      default: false
    }
  },
  emits: [
    'update:selected',
    'row-click',
    'sort',
    'search',
    'page-change',
    'page-size-change',
    'export',
    'column-visibility-change',
    'column-filter'
  ],
  setup(props, { emit, slots }) {
    // Sorting state
    const sortKey = ref('');
    const sortOrder = ref('asc');

    // Pagination state (client-side)
    const currentPage = ref(props.serverSide ? props.currentPageServer : 1);

    // Selection state
    const selectedItems = ref([]);

    // Search state
    const searchQuery = ref('');

    // Export and print state
    const showExportModal = ref(false);
    const showPrintModal = ref(false);

    // Extract all leaf column keys (including those in column groups)
    const extractColumnKeys = (columns) => {
      const allKeys = [];

      columns.forEach(col => {
        if (col.children) {
          // Add all child column keys
          col.children.forEach(child => {
            if (child.key) allKeys.push(child.key);
          });
        } else if (col.key) {
          // Add regular column key
          allKeys.push(col.key);
        }
      });

      return allKeys;
    };

    // Column visibility state
    const visibleColumns = ref(extractColumnKeys(props.columns));

    // Column filters state
    const columnFilters = ref({});

    // Computed properties
    const hasActions = computed(() => !!slots.actions);

    // Computed property to get all leaf columns (for data display)
    const leafColumns = computed(() => {
      const result = [];

      for (const col of props.columns) {
        if (col.children) {
          result.push(...col.children);
        } else if (col.key) { // Only include columns with keys (not just group headers)
          result.push(col);
        }
      }

      return result;
    });

    // Computed property to check if we have column groups
    const hasColumnGroups = computed(() => {
      return props.columns.some(col => col.children || col.colspan > 1 || col.rowspan > 1);
    });

    const visibleColumnsData = computed(() => {
      // Ensure visibleColumns.value is an array
      const visibleKeys = Array.isArray(visibleColumns.value) ? visibleColumns.value : [];

      // Filter leaf columns based on visibility
      const filtered = leafColumns.value.filter(col => visibleKeys.includes(col.key));

      // Mark the first data column for sticky positioning if needed
      if (filtered.length > 0) {
        return filtered.map((col, index) => ({
          ...col,
          isFirstDataColumn: index === 0
        }));
      }

      return [];
    });

    const totalVisibleColumns = computed(() => {
      let count = visibleColumnsData.value.length;
      if (props.selectable) count++;
      if (hasActions.value) count++;
      return count;
    });

    const filteredItems = computed(() => {
      if (props.serverSide) return props.items;

      let result = [...props.items];

      // Apply global search filter
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase();
        // Ensure visibleColumns.value is an array
        const visibleKeys = Array.isArray(visibleColumns.value) ? visibleColumns.value : [];

        result = result.filter(item => {
          return props.columns.some(column => {
            // Only search in visible columns
            if (!visibleKeys.includes(column.key)) return false;

            const value = getItemValue(item, column.key);
            return value && String(value).toLowerCase().includes(query);
          });
        });
      }

      // Apply column filters
      if (Object.keys(columnFilters.value).length > 0) {
        result = result.filter(item => {
          return Object.entries(columnFilters.value).every(([columnKey, filter]) => {
            const value = getItemValue(item, columnKey);

            // Handle different filter types
            if (typeof filter === 'object' && (filter.start || filter.end)) {
              // Date range filter
              const itemDate = new Date(value);
              const startDate = filter.start ? new Date(filter.start) : null;
              const endDate = filter.end ? new Date(filter.end) : null;

              if (startDate && endDate) {
                return itemDate >= startDate && itemDate <= endDate;
              } else if (startDate) {
                return itemDate >= startDate;
              } else if (endDate) {
                return itemDate <= endDate;
              }
              return true;
            } else {
              // Text or select filter
              return String(value).toLowerCase().includes(String(filter).toLowerCase());
            }
          });
        });
      }

      return result;
    });

    const sortedItems = computed(() => {
      if (!sortKey.value || props.serverSide) return filteredItems.value;

      return [...filteredItems.value].sort((a, b) => {
        const aValue = getItemValue(a, sortKey.value);
        const bValue = getItemValue(b, sortKey.value);

        if (aValue === bValue) return 0;

        const result = aValue > bValue ? 1 : -1;
        return sortOrder.value === 'asc' ? result : -result;
      });
    });

    const paginatedItems = computed(() => {
      if (props.serverSide) return sortedItems.value;

      const start = (currentPage.value - 1) * props.pageSize;
      const end = start + props.pageSize;
      return sortedItems.value.slice(start, end);
    });

    const allSelected = computed(() => {
      return paginatedItems.value.length > 0 &&
        paginatedItems.value.every(item => isSelected(item));
    });

    // Computed property for export data
    const exportData = computed(() => {
      // For server-side tables, we'll use the current items
      // For client-side tables, we'll use all filtered items
      return props.serverSide ? props.items : filteredItems.value;
    });

    // State for server-side export/print
    const isLoadingAllData = ref(false);
    const allServerData = ref([]);

    // Method to fetch all data from server for export/print
    const fetchAllServerData = async () => {
      if (!props.serverSide) {
        return filteredItems.value; // For client-side, just use filtered items
      }

      isLoadingAllData.value = true;
      allServerData.value = [];

      try {
        // Emit an event to request all data with current filters
        const result = await new Promise((resolve) => {
          emit('fetch-all-data', {
            search: searchQuery.value,
            filters: columnFilters.value,
            sort: sortKey.value ? { key: sortKey.value, order: sortOrder.value } : null,
            callback: (data) => resolve(data)
          });
        });

        // If result is an array, use it directly
        if (Array.isArray(result)) {
          allServerData.value = result;
        }
        // If result is an object with data property (common API response format)
        else if (result && Array.isArray(result.data)) {
          allServerData.value = result.data;
        }

        return allServerData.value;
      } catch (error) {
        console.error('Error fetching all data for export/print:', error);
        return [];
      } finally {
        isLoadingAllData.value = false;
      }
    };

    // Methods
    const getItemKey = (item, index) => {
      return item[props.itemKey] || index;
    };

    const getItemValue = (item, key) => {
      if (!item) return '';

      // Handle nested properties with dot notation (e.g., 'user.name')
      if (key.includes('.')) {
        return key.split('.').reduce((obj, path) => {
          return obj && obj[path] !== undefined ? obj[path] : '';
        }, item);
      }

      return item[key] !== undefined ? item[key] : '';
    };

    // Method to get column style based on width property
    const getColumnStyle = (column) => {
      if (!column) return {};

      const style = {};

      // Apply width if specified
      if (column.width) {
        style.width = typeof column.width === 'number' ? `${column.width}px` : column.width;
        style.minWidth = style.width;
      }

      // Apply maxWidth if specified
      if (column.maxWidth) {
        style.maxWidth = typeof column.maxWidth === 'number' ? `${column.maxWidth}px` : column.maxWidth;
      }

      return style;
    };

    const sort = (key) => {
      if (sortKey.value === key) {
        sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
      } else {
        sortKey.value = key;
        sortOrder.value = 'asc';
      }

      if (props.serverSide) {
        emit('sort', { key, order: sortOrder.value });
      }
    };

    const handleSearch = (query) => {
      searchQuery.value = query;

      if (props.serverSide) {
        emit('search', query);
      } else {
        // Reset to first page when searching
        currentPage.value = 1;
      }
    };

    // Handle column filter
    const handleColumnFilter = ({ column, value }) => {
      if (!value || (typeof value === 'object' && !value.start && !value.end)) {
        // Remove filter if empty
        delete columnFilters.value[column];
      } else {
        // Add/update filter
        columnFilters.value[column] = value;
      }

      // Create a new object to trigger reactivity
      columnFilters.value = { ...columnFilters.value };

      if (props.serverSide) {
        emit('column-filter', columnFilters.value);
      }

      // Reset to first page when filtering
      currentPage.value = 1;
    };

    // Handle additional filter
    const handleAdditionalFilter = ({ key, value }) => {
      // Add/update filter
      columnFilters.value[key] = value;

      // Create a new object to trigger reactivity
      columnFilters.value = { ...columnFilters.value };

      if (props.serverSide) {
        emit('column-filter', columnFilters.value);
      }

      // Reset to first page when filtering
      currentPage.value = 1;
    };

    // Remove additional filter
    const removeAdditionalFilter = (key) => {
      // Remove filter
      delete columnFilters.value[key];

      // Create a new object to trigger reactivity
      columnFilters.value = { ...columnFilters.value };

      if (props.serverSide) {
        emit('column-filter', columnFilters.value);
      }

      // Reset to first page when filtering
      currentPage.value = 1;
    };

    // Reset all additional filters
    const resetAdditionalFilters = () => {
      // Remove all additional filters
      props.additionalFilters.forEach(filter => {
        delete columnFilters.value[filter.key];
      });

      // Create a new object to trigger reactivity
      columnFilters.value = { ...columnFilters.value };

      if (props.serverSide) {
        emit('column-filter', columnFilters.value);
      }

      // Reset to first page when filtering
      currentPage.value = 1;
    };

    // Check if a column is filterable
    const isFilterableColumn = (columnKey) => {
      return props.filterableColumns.includes(columnKey);
    };

    // Get filter type for a column
    const getFilterType = (columnKey) => {
      return props.filterConfig[columnKey]?.type || 'text';
    };

    // Get filter options for a column
    const getFilterOptions = (columnKey) => {
      return props.filterConfig[columnKey]?.options || [];
    };

    const handlePageChange = (page) => {
      currentPage.value = page;

      if (props.serverSide) {
        emit('page-change', page);
      }
    };

    const handlePageSizeChange = (size) => {
      if (props.serverSide) {
        emit('page-size-change', size);
      } else {
        // Reset to first page when changing page size
        currentPage.value = 1;
      }
    };

    const isSelected = (item) => {
      return selectedItems.value.some(
        selected => getItemKey(selected) === getItemKey(item)
      );
    };

    const toggleSelect = (item) => {
      const itemKey = getItemKey(item);

      if (isSelected(item)) {
        selectedItems.value = selectedItems.value.filter(
          selected => getItemKey(selected) !== itemKey
        );
      } else {
        selectedItems.value.push(item);
      }

      emit('update:selected', selectedItems.value);
    };

    const toggleSelectAll = () => {
      if (allSelected.value) {
        selectedItems.value = [];
      } else {
        selectedItems.value = [...paginatedItems.value];
      }

      emit('update:selected', selectedItems.value);
    };

    const openExportModal = () => {
      showExportModal.value = true;
    };

    const openPrintModal = () => {
      showPrintModal.value = true;
    };

    const updateVisibleColumns = (columns) => {
      // Ensure columns is an array
      visibleColumns.value = Array.isArray(columns) ? columns : [];
      emit('column-visibility-change', visibleColumns.value);
    };

    const handleRowClick = (item, index) => {
      emit('row-click', { item, index });
    };

    // Watch for changes in server-side pagination
    watch(() => props.currentPageServer, (newPage) => {
      if (props.serverSide) {
        currentPage.value = newPage;
      }
    });

    // Watch for changes in columns prop
    watch(() => props.columns, (newColumns) => {
      // Extract all leaf column keys from the new columns
      const newColumnKeys = extractColumnKeys(newColumns);

      // Ensure visibleColumns.value is an array
      if (!Array.isArray(visibleColumns.value)) {
        visibleColumns.value = [];
      }

      const currentVisibleKeys = visibleColumns.value;

      // Find new columns that aren't in the current visible columns
      const newKeys = newColumnKeys.filter(key => !currentVisibleKeys.includes(key));

      if (newKeys.length > 0) {
        visibleColumns.value = [...currentVisibleKeys, ...newKeys];
      }

      // Remove any visible columns that no longer exist in the columns prop
      visibleColumns.value = visibleColumns.value.filter(key => newColumnKeys.includes(key));
    });

    // Method to calculate and set header row height
    const calculateHeaderRowHeight = () => {
      if (!props.stickyHeader) return;

      nextTick(() => {
        const tableResponsive = document.querySelector('.table-responsive.has-sticky-header');
        if (!tableResponsive) return;

        const firstHeaderRow = tableResponsive.querySelector('thead tr:first-child');
        if (firstHeaderRow) {
          const rowHeight = firstHeaderRow.offsetHeight;
          tableResponsive.style.setProperty('--header-row-height', `${rowHeight}px`);

          // If we have column groups, adjust the position of the filter row
          if (hasColumnGroups.value && props.showColumnFilters) {
            const secondHeaderRow = tableResponsive.querySelector('thead tr.column-child-row');
            if (secondHeaderRow) {
              const secondRowHeight = secondHeaderRow.offsetHeight;
              const combinedHeight = rowHeight + secondRowHeight;
              tableResponsive.style.setProperty('--combined-header-height', `${combinedHeight}px`);
            }
          }
        }
      });
    };

    // Calculate header row height when component is mounted
    onMounted(() => {
      calculateHeaderRowHeight();

      // Recalculate on window resize
      window.addEventListener('resize', calculateHeaderRowHeight);
    });

    // Clean up event listener when component is unmounted
    onUnmounted(() => {
      window.removeEventListener('resize', calculateHeaderRowHeight);
    });

    // Recalculate header row height when filters or column groups change
    watch([() => props.showColumnFilters, hasColumnGroups], () => {
      nextTick(() => {
        calculateHeaderRowHeight();
      });
    });

    // Provide context to child components
    provide('dataTable', {
      openExportModal,
      openPrintModal,
      updateVisibleColumns
    });

    return {
      // State
      sortKey,
      sortOrder,
      currentPage,
      selectedItems,
      searchQuery,
      showExportModal,
      showPrintModal,
      visibleColumns,

      // Computed
      hasActions,
      hasColumnGroups,
      leafColumns,
      totalVisibleColumns,
      filteredItems,
      sortedItems,
      paginatedItems,
      allSelected,
      visibleColumnsData,
      exportData,
      isLoadingAllData,
      allServerData,

      // Methods
      getItemKey,
      getItemValue,
      getColumnStyle,
      calculateHeaderRowHeight,
      sort,
      handleSearch,
      handlePageChange,
      handlePageSizeChange,
      isSelected,
      toggleSelect,
      toggleSelectAll,
      openExportModal,
      openPrintModal,
      updateVisibleColumns,
      handleRowClick,
      handleColumnFilter,
      handleAdditionalFilter,
      removeAdditionalFilter,
      resetAdditionalFilters,
      isFilterableColumn,
      getFilterType,
      getFilterOptions,
      columnFilters,
      fetchAllServerData
    };
  }
};
</script>

<style>
/* CSS Variables for theming */
:root {
  --vdt-primary-color: #3b82f6;
  --vdt-primary-hover: #2563eb;
  --vdt-text-color: #1e293b;
  --vdt-text-light: #64748b;
  --vdt-border-color: #e2e8f0;
  --vdt-bg-white: #ffffff;
  --vdt-bg-light: #f8fafc;
  --vdt-bg-hover: #f1f5f9;
  --vdt-bg-selected: rgba(59, 130, 246, 0.1);
  --vdt-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  --vdt-radius: 8px;
  --vdt-transition: all 0.2s ease;
  --vdt-z-index-modal: 10000;
}

/* Main container */
.vue-data-table {
  width: 100%;
  border: 1px solid var(--vdt-border-color);
  border-radius: var(--vdt-radius);
  overflow: hidden;
  background-color: var(--vdt-bg-white);
  box-shadow: var(--vdt-shadow);
  transition: var(--vdt-transition);
  position: relative;
}

/* Header section */
.vue-data-table-header {
  padding: 16px;
  border-bottom: 1px solid var(--vdt-border-color);
}

/* Additional filters section */
.vue-data-table-additional-filters {
  padding: 0 16px;
  margin-bottom: 16px;
}

/* Body section */
.vue-data-table-body {
  position: relative;
}

/* Table responsive wrapper */
.table-responsive {
  overflow-x: auto;
  width: 100%;
  -webkit-overflow-scrolling: touch;
  /* Improve horizontal scrolling */
  scrollbar-width: thin;
  scrollbar-color: var(--vdt-border-color, #e2e8f0) transparent;
  position: relative;
}

/* Custom scrollbar for WebKit browsers */
.table-responsive::-webkit-scrollbar {
  height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
  background: transparent;
}

.table-responsive::-webkit-scrollbar-thumb {
  background-color: var(--vdt-border-color, #e2e8f0);
  border-radius: 20px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
  background-color: var(--vdt-text-light, #94a3b8);
}

/* Loading overlay */
.vue-data-table-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 5;
}

.vue-data-table-loading .spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--vdt-primary-color);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 8px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Footer section */
.vue-data-table-footer {
  padding: 16px;
  border-top: 1px solid var(--vdt-border-color);
}

/* Table styles */
.vue-data-table table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.vue-data-table th,
.vue-data-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid var(--vdt-border-color);
  position: relative;
}

.vue-data-table th {
  font-weight: 600;
  background-color: var(--vdt-bg-light);
  white-space: nowrap;
  color: var(--vdt-text-color);
  position: sticky;
  top: 0;
  z-index: 2;
}

/* We're keeping the bottom border for all rows now */
/* .vue-data-table tr:last-child td {
  border-bottom: none;
} */

.vue-data-table tbody tr {
  transition: background-color 0.15s ease;
  cursor: pointer;
}

.vue-data-table tbody tr:hover {
  background-color: var(--vdt-bg-hover);
}

/* Content wrappers for th and td */
.th-content,
.td-content {
  position: relative;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Sortable columns */
.vue-data-table .sortable {
  cursor: pointer;
  position: relative;
}

.vue-data-table .sortable .th-content {
  padding-right: 24px;
}

.vue-data-table .sort-icon {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  height: 12px;
}

.vue-data-table .sort-arrow {
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  transition: border-color 0.15s ease;
}

.vue-data-table .sort-arrow-up {
  border-bottom: 4px solid var(--vdt-text-light);
  margin-bottom: 2px;
}

.vue-data-table .sort-arrow-down {
  border-top: 4px solid var(--vdt-text-light);
}

.vue-data-table .sorted-asc .sort-arrow-up {
  border-bottom-color: var(--vdt-primary-color);
}

.vue-data-table .sorted-desc .sort-arrow-down {
  border-top-color: var(--vdt-primary-color);
}

/* Special columns */
.vue-data-table .select-column {
  width: 40px;
  text-align: center;
}

.vue-data-table .actions-column {
  width: 100px;
  text-align: center;
}

/* Sticky first column */
.vue-data-table.with-sticky-first-column .sticky-column {
  position: sticky;
  left: 0;
  z-index: 1;
  background-color: inherit;
}

.vue-data-table.with-sticky-first-column th.sticky-column {
  z-index: 3;
  background-color: var(--vdt-bg-light);
}

.vue-data-table.with-sticky-first-column td.sticky-column {
  background-color: var(--vdt-bg-white);
}

.vue-data-table.with-sticky-first-column tr:hover td.sticky-column {
  background-color: var(--vdt-bg-hover);
}

.vue-data-table.with-sticky-first-column tr.selected td.sticky-column {
  background-color: var(--vdt-bg-selected);
}

/* Selected row */
.vue-data-table tr.selected {
  background-color: var(--vdt-bg-selected);
}

/* No data message */
.vue-data-table .no-data {
  text-align: center;
  padding: 32px;
  color: var(--vdt-text-light);
}

.vue-data-table .no-data-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.vue-data-table .no-data-content svg {
  width: 48px;
  height: 48px;
  color: var(--vdt-text-light);
  opacity: 0.5;
}

.vue-data-table .no-data-content p {
  margin: 0;
  font-size: 16px;
}

/* Themes */
.vue-data-table.theme-primary {
  --vdt-primary-color: #6366f1;
  --vdt-primary-hover: #4f46e5;
  --vdt-bg-selected: rgba(99, 102, 241, 0.1);
}

.vue-data-table.theme-dark {
  --vdt-text-color: #f8fafc;
  --vdt-text-light: #cbd5e1;
  --vdt-border-color: #334155;
  --vdt-bg-white: #1e293b;
  --vdt-bg-light: #0f172a;
  --vdt-bg-hover: #334155;
  --vdt-bg-selected: rgba(99, 102, 241, 0.2);
}

.vue-data-table.theme-light {
  --vdt-border-color: #f1f5f9;
  --vdt-bg-light: #f8fafc;
  --vdt-bg-hover: #f1f5f9;
  --vdt-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Column groups */
.column-groups-row th,
.column-child-row th {
  text-align: center;
  position: relative;
}

.column-groups-row th {
  background-color: var(--vdt-bg-light);
  border-bottom: 1px solid var(--vdt-border-color);
  border-right: 1px solid var(--vdt-border-color);
}

.column-child-row th {
  background-color: var(--vdt-bg-light);
  border-right: 1px solid var(--vdt-border-color);
}

/* Last column in a row shouldn't have right border */
.column-groups-row th:last-child,
.column-child-row th:last-child {
  border-right: none;
}

/* Create a table with column group borders */
.vue-data-table table {
  border-collapse: collapse;
  border-spacing: 0;
  border: 2px solid var(--vdt-border-color);
}

/* Add vertical borders to all cells */
.vue-data-table th,
.vue-data-table td {
  border-right: 1px solid var(--vdt-border-color);
  border-bottom: 1px solid var(--vdt-border-color);
}

/* Add thicker borders for column groups */
.vue-data-table .column-groups-row th[colspan] {
  border-right: 2px solid var(--vdt-border-color);
  border-left: 2px solid var(--vdt-border-color);
  border-top: 2px solid var(--vdt-border-color);
  border-bottom: 1px solid var(--vdt-border-color);
  background-color: #f8fafc;
}

/* Add thicker right borders for the last column in each group */
.vue-data-table .column-child-row th:nth-of-type(3),
.vue-data-table .column-child-row th:nth-of-type(6),
.vue-data-table tbody td:nth-of-type(4),
.vue-data-table tbody td:nth-of-type(7) {
  border-right: 2px solid var(--vdt-border-color);
}

/* Ensure vertical borders extend all the way down */
.vue-data-table tbody tr:last-child td {
  border-bottom: 2px solid var(--vdt-border-color);
}

/* First column in each group should have thicker left border */
.vue-data-table .column-child-row th:nth-of-type(1),
.vue-data-table .column-child-row th:nth-of-type(4),
.vue-data-table tbody td:nth-of-type(2),
.vue-data-table tbody td:nth-of-type(5) {
  border-left: 2px solid var(--vdt-border-color);
}

/* Add thicker bottom border to the header rows */
.vue-data-table thead tr:last-child th {
  border-bottom: 2px solid var(--vdt-border-color);
}

/* Column filters */
.column-filters-row th {
  padding: 4px 16px 8px;
  background-color: var(--vdt-bg-light);
  border-top: none;
}

.column-filters-row th.select-column,
.column-filters-row th.actions-column {
  padding: 0;
}

/* Ensure proper borders for spanned cells */
.vue-data-table th[colspan],
.vue-data-table th[rowspan] {
  border-right: 1px solid var(--vdt-border-color);
}

/* Fix for rowspan columns to ensure they have proper top border */
.vue-data-table th[rowspan] {
  border-top: 2px solid var(--vdt-border-color);
}

/* Table responsive container */
.table-responsive {
  overflow-x: auto;
  width: 100%;
  position: relative;
}

/* CSS variables for header row heights */
.table-responsive.has-sticky-header {
  --header-row-height: 49px;
  /* Default value, will be updated by JS */
  --combined-header-height: 98px;
  /* Default value, will be updated by JS */
}

/* Make the header sticky when scrolling vertically */
.table-responsive.has-sticky-header thead th {
  position: sticky;
  top: 0;
  z-index: 2;
  background-color: var(--vdt-bg-light);
}

/* Ensure column group headers are sticky at the top */
.table-responsive.has-sticky-header thead tr:first-child th {
  top: 0;
  z-index: 3;
}

/* Position the second row of headers (child columns) */
.table-responsive.has-sticky-header thead tr.column-child-row th {
  top: 49px;
  /* Height of the first row header */
  z-index: 2;
}

/* Position the filter row below the headers (only when sticky) */
.table-responsive.has-sticky-header thead tr.column-filters-row.sticky-filters th {
  top: 98px;
  /* Combined height of the header rows */
  z-index: 1;
  /* Lower z-index so it appears below headers when scrolling */
  position: sticky;
  background-color: var(--vdt-bg-light);
}

/* When not using column groups, position the filter row directly below the header */
.table-responsive.has-sticky-header thead tr:first-child:not(.column-groups-row)+tr.column-filters-row.sticky-filters th {
  top: 49px;
}

/* Add box-shadow to sticky headers for better visual separation */
.table-responsive.has-sticky-header thead th {
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.05);
}

/* For horizontal scrolling, ensure the table takes up enough space */
.table-responsive table {
  min-width: 100%;
  width: auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {

  .vue-data-table th,
  .vue-data-table td {
    padding: 8px 12px;
  }

  .vue-data-table-header,
  .vue-data-table-footer {
    padding: 12px;
  }

  .column-filters-row th {
    padding: 4px 12px 8px;
  }
}
</style>
