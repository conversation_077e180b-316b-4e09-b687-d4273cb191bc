<template>
  <div class="api-data-table">
    <data-table
      :items="items"
      :columns="columns"
      :title="title"
      :server-side="true"
      :total-items="totalItems"
      :current-page-server="currentPage"
      :page-size="pageSize"
      :loading="loading"
      :selectable="selectable"
      :show-search="showSearch"
      :show-export="showExport"
      :table-class="tableClass"
      :export-file-name="exportFileName"
      v-model:selected="selectedItems"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
      @sort="handleSort"
      @search="handleSearch"
      @update:selected="$emit('update:selected', $event)"
    >
      <!-- Forward all slots -->
      <template v-for="(_, name) in $slots" #[name]="slotData">
        <slot :name="name" v-bind="slotData"></slot>
      </template>
    </data-table>
    
    <!-- Error message display -->
    <div v-if="error" class="api-data-table-error">
      <div class="api-data-table-error-content">
        <div class="api-data-table-error-icon">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
            <path fill="none" d="M0 0h24v24H0z"/>
            <path d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-2a8 8 0 1 0 0-16 8 8 0 0 0 0 16zm-1-5h2v2h-2v-2zm0-8h2v6h-2V7z" fill="currentColor"/>
          </svg>
        </div>
        <div class="api-data-table-error-message">{{ error }}</div>
        <button class="api-data-table-error-retry" @click="fetchData">
          Retry
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted } from 'vue';
import DataTable from './DataTable.vue';

export default {
  name: 'ApiDataTable',
  components: {
    DataTable
  },
  props: {
    // API configuration
    apiUrl: {
      type: String,
      required: true
    },
    apiMethod: {
      type: String,
      default: 'GET'
    },
    apiHeaders: {
      type: Object,
      default: () => ({})
    },
    apiParams: {
      type: Object,
      default: () => ({})
    },
    apiBody: {
      type: Object,
      default: null
    },
    
    // Response mapping
    responseDataPath: {
      type: String,
      default: 'data'
    },
    responseTotalPath: {
      type: String,
      default: 'total'
    },
    
    // API parameter names
    pageParamName: {
      type: String,
      default: 'page'
    },
    pageSizeParamName: {
      type: String,
      default: 'limit'
    },
    sortKeyParamName: {
      type: String,
      default: 'sortBy'
    },
    sortOrderParamName: {
      type: String,
      default: 'sortOrder'
    },
    searchParamName: {
      type: String,
      default: 'search'
    },
    
    // Table configuration
    columns: {
      type: Array,
      required: true
    },
    title: {
      type: String,
      default: ''
    },
    initialPageSize: {
      type: Number,
      default: 10
    },
    selectable: {
      type: Boolean,
      default: false
    },
    showSearch: {
      type: Boolean,
      default: true
    },
    showExport: {
      type: Boolean,
      default: true
    },
    tableClass: {
      type: String,
      default: ''
    },
    exportFileName: {
      type: String,
      default: 'table-export'
    },
    
    // Auto-load data on mount
    autoLoad: {
      type: Boolean,
      default: true
    }
  },
  emits: [
    'update:selected',
    'data-loaded',
    'data-error',
    'page-change',
    'page-size-change',
    'sort-change',
    'search-change'
  ],
  setup(props, { emit }) {
    // State
    const items = ref([]);
    const totalItems = ref(0);
    const currentPage = ref(1);
    const pageSize = ref(props.initialPageSize);
    const loading = ref(false);
    const error = ref(null);
    const sortKey = ref('');
    const sortOrder = ref('asc');
    const searchQuery = ref('');
    const selectedItems = ref([]);
    
    // Computed API parameters
    const apiQueryParams = computed(() => {
      const params = { ...props.apiParams };
      
      // Add pagination parameters
      params[props.pageParamName] = currentPage.value;
      params[props.pageSizeParamName] = pageSize.value;
      
      // Add sorting parameters if available
      if (sortKey.value) {
        params[props.sortKeyParamName] = sortKey.value;
        params[props.sortOrderParamName] = sortOrder.value;
      }
      
      // Add search parameter if available
      if (searchQuery.value) {
        params[props.searchParamName] = searchQuery.value;
      }
      
      return params;
    });
    
    // Methods
    async function fetchData() {
      loading.value = true;
      error.value = null;
      
      try {
        // Prepare request options
        const options = {
          method: props.apiMethod,
          headers: {
            'Content-Type': 'application/json',
            ...props.apiHeaders
          }
        };
        
        // Add body for non-GET requests
        if (props.apiMethod !== 'GET' && props.apiBody) {
          options.body = JSON.stringify(props.apiBody);
        }
        
        // Build URL with query parameters for GET requests
        let url = props.apiUrl;
        if (props.apiMethod === 'GET') {
          const queryParams = new URLSearchParams();
          Object.entries(apiQueryParams.value).forEach(([key, value]) => {
            queryParams.append(key, value);
          });
          
          url = `${url}${url.includes('?') ? '&' : '?'}${queryParams.toString()}`;
        }
        
        // Make the request
        const response = await fetch(url, options);
        
        // Check if response is ok
        if (!response.ok) {
          throw new Error(`API request failed with status ${response.status}`);
        }
        
        // Parse response
        const data = await response.json();
        
        // Extract items and total from response using the configured paths
        const extractPath = (obj, path) => {
          return path.split('.').reduce((o, p) => (o && o[p] !== undefined ? o[p] : null), obj);
        };
        
        const extractedItems = extractPath(data, props.responseDataPath) || [];
        const extractedTotal = extractPath(data, props.responseTotalPath) || extractedItems.length;
        
        // Update state
        items.value = extractedItems;
        totalItems.value = extractedTotal;
        
        // Emit event
        emit('data-loaded', { items: extractedItems, total: extractedTotal, response: data });
      } catch (err) {
        console.error('Error fetching data:', err);
        error.value = err.message || 'Failed to load data. Please try again.';
        emit('data-error', err);
      } finally {
        loading.value = false;
      }
    }
    
    // Event handlers
    function handlePageChange(page) {
      currentPage.value = page;
      emit('page-change', page);
      fetchData();
    }
    
    function handlePageSizeChange(size) {
      pageSize.value = size;
      currentPage.value = 1; // Reset to first page
      emit('page-size-change', size);
      fetchData();
    }
    
    function handleSort({ key, order }) {
      sortKey.value = key;
      sortOrder.value = order;
      emit('sort-change', { key, order });
      fetchData();
    }
    
    function handleSearch(query) {
      searchQuery.value = query;
      currentPage.value = 1; // Reset to first page
      emit('search-change', query);
      fetchData();
    }
    
    // Reload data when API URL changes
    watch(() => props.apiUrl, () => {
      if (props.autoLoad) {
        fetchData();
      }
    });
    
    // Load data on mount
    onMounted(() => {
      if (props.autoLoad) {
        fetchData();
      }
    });
    
    // Expose methods and state
    return {
      // State
      items,
      totalItems,
      currentPage,
      pageSize,
      loading,
      error,
      selectedItems,
      
      // Methods
      fetchData,
      handlePageChange,
      handlePageSizeChange,
      handleSort,
      handleSearch
    };
  }
};
</script>

<style>
.api-data-table {
  position: relative;
}

.api-data-table-error {
  margin-top: 16px;
  padding: 16px;
  background-color: #fee2e2;
  border: 1px solid #ef4444;
  border-radius: 8px;
  color: #b91c1c;
}

.api-data-table-error-content {
  display: flex;
  align-items: center;
}

.api-data-table-error-icon {
  margin-right: 12px;
  flex-shrink: 0;
}

.api-data-table-error-message {
  flex: 1;
}

.api-data-table-error-retry {
  margin-left: 16px;
  padding: 6px 12px;
  background-color: #b91c1c;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.api-data-table-error-retry:hover {
  background-color: #991b1b;
}
</style>
