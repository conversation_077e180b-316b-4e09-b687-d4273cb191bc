export default [
    {
        path: '/admin',
        component: () => import('@/layouts/AdminLayout.vue'),
        meta: { isAdmin: true },
        children: [
            {
                path: 'dashboard',
                name: 'admin-dashboard',
                component: () => import('@/views/backend/AdminDashboard.vue'),
            },
            {
                path: 'users',
                name: 'admin-users',
                component: () => import('@/views/backend/Userlist.vue'),
            },
            {
                path: 'social-media',
                name: 'admin-social-media',
                component: () => import('@/views/backend/SocialMedia.vue'),
            },
            {
                path: 'newsletter',
                name: 'admin-newsletter',
                component: () => import('@/views/backend/NewsLetter.vue'),
            },
            {
                path: 'admissions',
                name: 'admin-admissions',
                component: () => import('@/views/backend/Admission.vue'),
            },
            {
                path: 'menu-manager',
                name: 'admin-menu-manager',
                component: () => import('@/components/admin/MenuManager.vue'),
            },
            // Content Management routes
            {
                path: 'posts',
                name: 'admin-posts',
                component: () => import('@/views/backend/Posts.vue'),
            },
            {
                path: 'categories',
                name: 'admin-categories',
                component: () => import('@/views/backend/Categories.vue'),
            },
            {
                path: 'media',
                name: 'admin-media',
                component: () => import('@/views/backend/Media.vue'),
            },
            // Settings routes
            {
                path: 'settings/general',
                name: 'admin-settings-general',
                component: () => import('@/views/backend/settings/General.vue'),
            },
            {
                path: 'settings/permissions',
                name: 'admin-settings-permissions',
                component: () => import('@/views/backend/settings/Permissions.vue'),
            },
            {
                path: 'settings/logs',
                name: 'admin-settings-logs',
                component: () => import('@/views/backend/settings/Logs.vue'),
            },
        ],
    },
];
