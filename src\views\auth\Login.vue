<script setup>
import {
  ref,
  watch,
} from 'vue'

import { useRouter } from 'vue-router'

import useAuthStore from '@/stores/auth'

const router = useRouter();
const email = ref(null);
const password = ref(null);
async function loginData() {
  const isLoggedin = await useAuthStore().login({ email: email.value, password: password.value });

  if (isLoggedin) {
    router.push('/admin/users');
  }
}
</script>
<template>
  <main class="main-content">

    <div class="admin">
      <div class="container-fluid">
        <div class="row justify-content-center">
          <div class="col-xxl-3 col-xl-4 col-md-6 col-sm-8">
            <div class="edit-profile">
              <div class="edit-profile__logos">
                <a href="index.html">
                  <img class="dark" src="/img/sabyata_logo_2.png" alt="">
                  <!-- <img class="light" src="/img/logo-white.png" alt=""> -->
                </a>
              </div>
              <div class="card border-0">
                <div class="card-header">
                  <div class="edit-profile__title">
                    <h6>Sign in Sabhyata Little Star</h6>
                  </div>
                </div>
                <div class="card-body">
                  <form @submit.prevent="loginData">
                    <div class="edit-profile__body">
                      <div class="form-group mb-25">
                        <label for="email"> Email Address</label>
                        <input v-model="email" type="text" class="form-control" id="email"
                          placeholder="<EMAIL>">
                      </div>
                      <div class="form-group mb-15">
                        <label for="password-field">password</label>
                        <div class="position-relative">
                          <input v-model="password" id="password-field" type="password" class="form-control"
                            name="password" placeholder="Password">
                          <div class="uil uil-eye-slash text-lighten fs-15 field-icon toggle-password2">
                          </div>
                        </div>
                      </div>
                      <div class="admin-condition">
                        <div class="checkbox-theme-default custom-checkbox ">
                          <input class="checkbox" type="checkbox" id="check-1">
                          <label for="check-1">
                            <span class="checkbox-text">Keep me logged in</span>
                          </label>
                        </div>
                        <a href="forget-password.html">forget password?</a>
                      </div>
                      <div
                        class="admin__button-group button-group d-flex pt-1 justify-content-md-start justify-content-center">
                        <button
                          class="btn btn-primary btn-default w-100 btn-squared text-capitalize lh-normal px-50 signIn-createBtn ">
                          sign in
                        </button>
                      </div>
                    </div>
                  </form>

                </div>
              </div><!-- End: .card -->
            </div><!-- End: .edit-profile -->
          </div><!-- End: .col-xl-5 -->
        </div>
      </div>
    </div><!-- End: .admin-element  -->

  </main>
</template>