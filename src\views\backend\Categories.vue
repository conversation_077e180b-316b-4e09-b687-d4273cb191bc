<template>
  <div class="p-6">
    <div class="mb-6">
      <h1 class="text-3xl font-bold text-gray-900">Categories</h1>
      <p class="text-gray-600 mt-2">Organize your content with categories</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Add Category Form -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">Add New Category</h2>
        <form @submit.prevent="addCategory" class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Category Name</label>
            <input 
              v-model="newCategory.name"
              type="text" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter category name"
            >
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
            <textarea 
              v-model="newCategory.description"
              rows="3" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter category description"
            ></textarea>
          </div>
          <button 
            type="submit"
            class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
          >
            Add Category
          </button>
        </form>
      </div>

      <!-- Categories List -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">Existing Categories</h2>
        <div class="space-y-3">
          <div 
            v-for="category in categories" 
            :key="category.id"
            class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
          >
            <div>
              <h3 class="font-medium text-gray-900">{{ category.name }}</h3>
              <p class="text-sm text-gray-600">{{ category.description }}</p>
              <span class="text-xs text-gray-500">{{ category.postCount }} posts</span>
            </div>
            <div class="flex space-x-2">
              <button class="text-blue-600 hover:text-blue-800 text-sm">Edit</button>
              <button class="text-red-600 hover:text-red-800 text-sm">Delete</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const newCategory = ref({
  name: '',
  description: ''
})

const categories = ref([
  {
    id: 1,
    name: 'Technology',
    description: 'Articles about latest technology trends',
    postCount: 15
  },
  {
    id: 2,
    name: 'Education',
    description: 'Educational content and tutorials',
    postCount: 8
  },
  {
    id: 3,
    name: 'News',
    description: 'Latest news and updates',
    postCount: 12
  }
])

const addCategory = () => {
  if (newCategory.value.name.trim()) {
    categories.value.push({
      id: Date.now(),
      name: newCategory.value.name,
      description: newCategory.value.description,
      postCount: 0
    })
    
    newCategory.value = {
      name: '',
      description: ''
    }
  }
}
</script>
