<template>
  <div class="p-6">
    <div class="mb-6">
      <h1 class="text-3xl font-bold text-gray-900">Media Library</h1>
      <p class="text-gray-600 mt-2">Manage your images, videos, and documents</p>
    </div>

    <!-- Upload Area -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
      <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors">
        <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
          <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <div class="mt-4">
          <p class="text-lg font-medium text-gray-900">Drop files here or click to upload</p>
          <p class="text-sm text-gray-500 mt-1">PNG, JPG, GIF up to 10MB</p>
        </div>
        <button class="mt-4 bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
          Choose Files
        </button>
      </div>
    </div>

    <!-- Media Grid -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-xl font-semibold text-gray-800">Media Files</h2>
        <div class="flex space-x-2">
          <button class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200">Grid</button>
          <button class="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200">List</button>
        </div>
      </div>

      <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
        <div 
          v-for="file in mediaFiles" 
          :key="file.id"
          class="group relative bg-gray-100 rounded-lg overflow-hidden hover:shadow-md transition-shadow cursor-pointer"
        >
          <div class="aspect-square flex items-center justify-center">
            <img 
              v-if="file.type === 'image'"
              :src="file.thumbnail" 
              :alt="file.name"
              class="w-full h-full object-cover"
            >
            <div v-else class="text-gray-400">
              <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"/>
              </svg>
            </div>
          </div>
          
          <!-- Overlay -->
          <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-200 flex items-center justify-center">
            <div class="opacity-0 group-hover:opacity-100 transition-opacity">
              <button class="text-white hover:text-blue-300 mr-2">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                  <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                </svg>
              </button>
              <button class="text-white hover:text-red-300">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" clip-rule="evenodd"/>
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 012 0v4a1 1 0 11-2 0V7zM12 7a1 1 0 012 0v4a1 1 0 11-2 0V7z" clip-rule="evenodd"/>
                </svg>
              </button>
            </div>
          </div>
          
          <!-- File Info -->
          <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-2">
            <p class="text-white text-xs truncate">{{ file.name }}</p>
            <p class="text-gray-300 text-xs">{{ file.size }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const mediaFiles = ref([
  {
    id: 1,
    name: 'hero-image.jpg',
    type: 'image',
    size: '2.4 MB',
    thumbnail: 'https://via.placeholder.com/200x200/4F46E5/FFFFFF?text=Image+1'
  },
  {
    id: 2,
    name: 'logo.png',
    type: 'image',
    size: '156 KB',
    thumbnail: 'https://via.placeholder.com/200x200/7C3AED/FFFFFF?text=Image+2'
  },
  {
    id: 3,
    name: 'document.pdf',
    type: 'document',
    size: '1.2 MB',
    thumbnail: null
  },
  {
    id: 4,
    name: 'banner.jpg',
    type: 'image',
    size: '3.1 MB',
    thumbnail: 'https://via.placeholder.com/200x200/EC4899/FFFFFF?text=Image+3'
  },
  {
    id: 5,
    name: 'profile.jpg',
    type: 'image',
    size: '890 KB',
    thumbnail: 'https://via.placeholder.com/200x200/10B981/FFFFFF?text=Image+4'
  },
  {
    id: 6,
    name: 'presentation.pptx',
    type: 'document',
    size: '5.6 MB',
    thumbnail: null
  }
])
</script>
