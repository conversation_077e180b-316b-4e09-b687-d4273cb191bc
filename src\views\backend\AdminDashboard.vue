<script setup>
import {
  onMounted,
  ref,
} from 'vue'

import useAdminDashboard from '@/stores/adminDashboard'

const totalUser = ref(null)
const totalAdmissions = ref(null)
const totalnewsLetter = ref(null)

onMounted(async () => {
    totalUser.value = await useAdminDashboard().userlist()
    console.log(totalUser.value);
    totalAdmissions.value = await useAdminDashboard().admissionList()
    totalnewsLetter.value = await useAdminDashboard().newsletterList()
})
</script>

<template>
    <div class="mb-6">
        <div class="container mx-auto px-4">
            <div class="flex flex-col gap-6">
                <div>
                    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                        <h4 class="text-2xl font-semibold capitalize">Dashboard</h4>
                        <nav aria-label="breadcrumb">
                            <ol class="flex space-x-2 text-sm text-gray-500">
                                <li>
                                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                                        <i class="uil uil-estate mr-1"></i>Dashboard
                                    </a>
                                </li>
                                <li>
                                    <span class="mx-2">/</span>
                                </li>
                                <li class="text-gray-700">Demo 1</li>
                            </ol>
                        </nav>
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-2 gap-6">
                    <!-- Total users -->
                    <div class="bg-white rounded-xl shadow p-6 flex items-center justify-between">
                        <div class="flex-1">
                            <div>
                                <h1 v-if="totalUser === null" class="text-3xl font-bold flex items-center">
                                    Loading
                                    <span class="ml-2 flex space-x-1">
                                        <span class="w-2 h-2 rounded-full bg-blue-500 animate-bounce"></span>
                                        <span class="w-2 h-2 rounded-full bg-blue-500 animate-bounce delay-75"></span>
                                        <span class="w-2 h-2 rounded-full bg-blue-500 animate-bounce delay-150"></span>
                                    </span>
                                </h1>
                                <h1 v-else class="text-3xl font-bold">{{ totalUser }}+</h1>
                                <p class="text-gray-500">Total Users</p>
                            </div>
                            <div class="mt-2 text-green-600 text-sm flex items-center">
                                <i class="las la-arrow-up mr-1"></i>
                                <strong>25.36%</strong>
                                <span class="ml-2 text-gray-400">Since last month</span>
                            </div>
                        </div>
                        <div class="ml-4 flex-shrink-0">
                            <div class="bg-blue-100 text-blue-600 rounded-full p-3">
                                <i class="uil uil-users-alt text-2xl"></i>
                            </div>
                        </div>
                    </div>
                    <!-- Total Admissions -->
                    <div class="bg-white rounded-xl shadow p-6 flex items-center justify-between">
                        <div class="flex-1">
                            <div>
                                <h1 v-if="totalAdmissions === null" class="text-3xl font-bold flex items-center">
                                    Loading
                                    <span class="ml-2 flex space-x-1">
                                        <span class="w-2 h-2 rounded-full bg-blue-500 animate-bounce"></span>
                                        <span class="w-2 h-2 rounded-full bg-blue-500 animate-bounce delay-75"></span>
                                        <span class="w-2 h-2 rounded-full bg-blue-500 animate-bounce delay-150"></span>
                                    </span>
                                </h1>
                                <h1 v-else class="text-3xl font-bold">{{ totalAdmissions }}+</h1>
                                <p class="text-gray-500">Total Admissions</p>
                            </div>
                            <div class="mt-2 text-green-600 text-sm flex items-center">
                                <i class="las la-arrow-up mr-1"></i>
                                <strong>25.36%</strong>
                                <span class="ml-2 text-gray-400">Since last month</span>
                            </div>
                        </div>
                        <div class="ml-4 flex-shrink-0">
                            <div class="bg-cyan-100 text-cyan-600 rounded-full p-3">
                                <i class="uil uil-graduation-cap text-2xl"></i>
                            </div>
                        </div>
                    </div>
                    <!-- Total NewsLetter -->
                    <div class="bg-white rounded-xl shadow p-6 flex items-center justify-between">
                        <div class="flex-1">
                            <div>
                                <h1 v-if="totalnewsLetter === null" class="text-3xl font-bold flex items-center">
                                    Loading
                                    <span class="ml-2 flex space-x-1">
                                        <span class="w-2 h-2 rounded-full bg-blue-500 animate-bounce"></span>
                                        <span class="w-2 h-2 rounded-full bg-blue-500 animate-bounce delay-75"></span>
                                        <span class="w-2 h-2 rounded-full bg-blue-500 animate-bounce delay-150"></span>
                                    </span>
                                </h1>
                                <h1 v-else class="text-3xl font-bold">{{ totalnewsLetter }}+</h1>
                                <p class="text-gray-500">Total NewsLetter</p>
                            </div>
                            <div class="mt-2 text-red-600 text-sm flex items-center">
                                <i class="las la-arrow-down mr-1"></i>
                                <strong>25.36%</strong>
                                <span class="ml-2 text-gray-400">Since last month</span>
                            </div>
                        </div>
                        <div class="ml-4 flex-shrink-0">
                            <div class="bg-gray-200 text-gray-600 rounded-full p-3">
                                <i class="uil uil-envelope text-2xl"></i>
                            </div>
                        </div>
                    </div>
                    <!-- Total Children -->
                    <div class="bg-white rounded-xl shadow p-6 flex items-center justify-between">
                        <div class="flex-1">
                            <div>
                                <h1 class="text-3xl font-bold">30,825</h1>
                                <p class="text-gray-500">New Childrens</p>
                            </div>
                            <div class="mt-2 text-green-600 text-sm flex items-center">
                                <i class="las la-arrow-up mr-1"></i>
                                <strong>25.36%</strong>
                                <span class="ml-2 text-gray-400">Since last month</span>
                            </div>
                        </div>
                        <div class="ml-4 flex-shrink-0">
                            <div class="bg-yellow-100 text-yellow-600 rounded-full p-3">
                                <i class="uil uil-users-alt text-2xl"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
