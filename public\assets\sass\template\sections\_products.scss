.product-big-img {
  background-color: #f1f1f1;
  border: 1px solid rgba(#cbcbcb, 0.3);
  margin-bottom: 20px;
  text-align: center;
  border-radius: 20px;

  img {
    width: 100%;
  }
}

.product-thumb-slide {
  margin-bottom: 22px;

  &.row {
    --bs-gutter-x: 20px;
  }

  .thumb {
    background-color: #f1f1f1;
    overflow: hidden;
    transition: all ease 0.4s;
    border: 1px solid rgba(#cbcbcb, 0.3);
    cursor: pointer;
    border-radius: 14px;

    img {
      transition: all ease 0.4s;
      width: 100%;
    }

    &:hover {
      border-color: $theme-color;
    }
  }

  .slick-current .thumb {
    border-color: $theme-color;

    img {
      transform: scale(1.15);
    }
  }
}

.product-about {
  margin-bottom: 25px;

  .product-rating {
    display: flex;
    align-items: center;
    font-weight: 500;
    margin: 0 0 17px 0;

    .star-rating {
      margin-right: 10px;
      font-size: 16px;
      width: 104px;
    }
  }

  .product-title {
    font-size: 40px;
    margin-bottom: 6px;
  }

  .quantity {
    // margin-bottom: 20px;
    width: 100%;
  }

  .product-price {
    font-size: 34px;
    font-weight: 500;
    color: $theme-color;
    line-height: 1;
    margin: 0 0 8px 0;
    font-family: $title-font;

    del {
      font-size: 0.6em;
      color: #777777;
    }
  }

  .product-text {
    margin-bottom: 24px;
  }

  .actions {
    display: flex;
    flex-wrap: wrap;
    gap: 20px 10px;
    margin-bottom: 20px;

    .vs-btn {
      flex: 1;
      padding-top: 0;
      padding-bottom: 0;
      height: 50px;
      line-height: 50px;
      background-color: $secondary-color;

      &::after,
      &::before {
        background-color: $theme-color;
      }
    }

    .icon-btn {
      --icon-size: 50px;
      background-color: $white-color;
      color: $body-color;
      border: 2px solid $border-color;
      font-size: 17px;

      &::before {
        background-color: $theme-color;
        --border-size: 0;
      }

      &:hover {
        border-color: transparent;
        color: $white-color;
      }
    }
  }

  .product-getway {
    border-bottom: 1px solid $border-color;
    padding: 0 0 30px 0;
    margin-bottom: 22px;
  }

  .getway-title {
    font-size: 14px;
    color: $body-color;
    font-weight: 700;
    margin: -0.15em 0 15px 0;
    line-height: 1;
    font-family: $body-font;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    display: block;
  }
}

.product_meta {
  font-family: $body-font;
  font-size: 16px;
  font-weight: 400;
  text-transform: uppercase;

  >span {
    display: block;
    margin-bottom: 3px;
    color: $theme-color;

    &:last-child {
      margin-bottom: 0;
    }

    a {
      color: inherit;

      &:hover {
        color: $theme-color;
      }
    }

    >a,
    >span {
      position: relative;
      margin-left: 3px;
      color: $title-color;
      text-transform: capitalize;
      font-weight: 400;

      &:first-child {
        margin-left: 5px;
      }

      &:not(:last-child)::after {
        content: ',';
        margin-right: 3px;
      }
    }
  }
}

.vs-product-wrapper {
  .vs-pagination {
    justify-content: center;
    display: flex;
    margin-top: 30px;
  }
}

.product-description {
  margin-top: 26px;
}

.description {
  margin-bottom: 40px;
}

.product-style1 {
  border: 3px solid $theme-color2;
  margin-bottom: var(--bs-gutter-x, 30px);
  border-radius: 30px;
  transition: all ease 0.4s;

  &:hover {
    border-color: $theme-color;
  }

  .product-img {
    img {
      width: 100%;
    }
  }

  .product-content {
    padding: 20px 30px 20px 30px;
  }

  .product-price {
    font-size: 22px;
    font-weight: 500;
    color: $theme-color;
    font-family: $title-font;
    margin-bottom: 12px;
    display: block;
    line-height: 1;

    del {
      font-size: 14px;
      color: #777777;
    }
  }

  .product-title {
    font-size: 22px;
    margin-bottom: 10px;
    text-transform: capitalize;
  }

  .star-rating {
    margin-bottom: 18px;
  }

  .vs-btn {
    i {
      margin-right: 10px;
    }
  }

  .actions {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 6px;
  }

  .icon-btn {
    background-color: transparent;
    color: $body-color;
    border: 2px solid #D0D0D0;
    font-size: 16px;
    width: 50px;
    height: 50px;
    line-height: 50px;

    &:before {
      background-color: $secondary-color;
      --border-size: 1px;
    }

    &:hover {
      background-color: $theme-color;
      border-color: transparent;
      color: $white-color;
    }
  }

  .vs-btn {
    background-color: $secondary-color;
    padding: 17px 26px;

    &:after,
    &:before {
      background-color: $theme-color;
    }
  }

}


@include ml {
  .product-style1 {
    .product-content {
      padding: 20px 20px 20px 20px;
    }

    .vs-btn {
      padding: 17px 19px;
    }
  }
}


@include lg {}


@include md {
  .product-thumb-slide {
    margin-bottom: 30px;
  }

  
  .product-about {
    .product-title {
      font-size: 34px;
      margin-bottom: 6px;
    }
    .product-price {
      font-size: 26px;
      margin: 0 0 11px 0;
    }
  }
}

@include sm {
  .product-about {
    .product-title {
      font-size: 30px;
      margin-bottom: 8px;
    }
  }
}