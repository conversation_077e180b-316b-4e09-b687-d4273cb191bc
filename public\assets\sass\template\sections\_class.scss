.class-style1,
.class-style2 {  
  .class-info {
    color: $secondary-color;
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 15px 0;
    line-height: 1;

    .info {
      font-weight: 400;
      color: $body-color;
    }
  }

  .class-title {
    text-transform: capitalize;
  }

  .class-price {
    font-size: 50px;
    font-weight: 600;
    font-family: $title-font;
    color: $theme-color;
    margin: 25px 0 0 0;
    line-height: 1;

    .duration {
      font-weight: 500;
      font-size: 16px;
      color: $title-color;
    }
  }
}


.class-style1 {
  display: flex;
  align-items: center;

  .class-content {
    flex: 1;
    background-color: $white-color;
    border: 2px solid #C5C5C5;
    padding: 33px 40px;
    position: relative;
    z-index: 1;
    border-radius: 35px;
    transition: all ease 0.4s;
    box-shadow: 0px 4px 50px 0px rgba(0, 0, 0, 0.10);
  }

  .class-img {
    + .class-content {
      margin-left: -85px;
    }
  }

  &:hover {
    .class-content {
      border-color: $theme-color;
      box-shadow: none;
    }
  }

  .class-title {
    font-size: 28px;
  }

  .class-img {
    overflow: hidden;
    border-radius: 35px;
    min-width: 100px;

    img {
      width: 100%;
      transform: scale(1.001);
      transition: all ease 0.4s;
    }
  }

  &:hover {
    .class-img {
      img {
        transform: scale(1.15);
      }
    }
  }

  .class-btn {
    display: inline-block;
    position: absolute;
    right: 20px;
    bottom: 20px;
    width: 50px;
    height: 50px;
    line-height: 50px;
    font-size: 20px;
    text-align: center;
    border: 2px solid $border-color;
    background-color: $white-color;
    color: $body-color;
    border-radius: 50%;
    transition: all ease 0.4s;
  }
  
  &:hover {
    .class-btn {
      background-color: $theme-color;
      color: $white-color;
      border-color: transparent;

      &:hover {
        background-color: $secondary-color;
        color: $white-color;
      }
    }
  }

}

.class-style2 {
  background: rgba(#fff, 0.50);
  border-radius: 30px;
  margin-bottom: 30px;
  padding: 40px 35px 34px 35px;
  box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.15);
  transition: all ease 0.4s;

  &:hover {
    background-color: $white-color;
  }

  .class-title {
    margin: -0.2em 0 23px 0;
    text-transform: capitalize;

    @media (min-width: $ml) {
      font-size: 24px;
    }
  }  

  .class-img {
    position: relative; 
    margin-bottom: 28px;

    .img {
      border-radius: 50%;
      overflow: hidden;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 7px;
        right: 7px;
        bottom: 7px;
        left: 7px;
        border: 3px dashed $white-color;
        border-radius: inherit;
        display: block;
        z-index: 1;
        pointer-events: none;
        animation: rotate 45s infinite linear;
        transition: all ease 0.4s;
      }

      img {
        width: 100%;
        transform: scale(1.001);
        transition: all ease 0.4s;
      }
    }
  }

  &:hover {
    .class-img {
      .img {
        &:before {
          border-color: $theme-color;
        }

        img {
          transform: scale(1.15);
        }
      }
    }
  }

  .class-number {
    display: inline-block;
    width: 50px;
    height: 50px;
    line-height: 50px;
    text-align: center;
    background-color: $secondary-color;
    color: $white-color;
    font-size: 16px;
    font-weight: 600;
    font-family: $title-font;
    border-radius: 50%;
    position: absolute;
    left: 10px;
    top: 0;
    z-index: 2;
    transition: all ease 0.4s;
  }

  &:hover {
    .class-number {
      background-color: $theme-color;
    }
  }


}

.class-bar {
  background-color: $smoke-color;
  padding: 20px;
  margin: 22px 0 30px 0;
  border-radius: 25px 76px 25px 25px;

  .info-style1 {
    margin: 0;
  }

  .info-title {
    font-family: $body-font;
    color: $secondary-color;
    font-weight: 700;
  }

  .info-text {
    font-weight: 400;
    font-family: $body-font;
    margin: 0;
  }
}

.lession-price {
  font-size: 50px;
  color: $theme-color;
  font-weight: 600;
  line-height: 1;
  margin: 0 0 11px 0;
  font-family: $title-font;

  .duration {
    font-size: 16px; 
    color: #777777;
    text-transform: capitalize;
  }
}

.lession-title {
  margin: -0.2em 0 10px 0;
}

.lession-social {
  margin-bottom: 30px;
}


@include ml {
  .class-style1 {    
    .class-content {
      padding: 27px 25px;
    }

    .class-img {
      + .class-content {
        margin-left: -120px;
      }
    }
  }

  .class-style2 {
    padding: 35px 25px 34px 25px;

    .class-title {
      font-size: 22px;
    }
  }
}

@include lg {
  .class-style1 {
    display: block;

    .class-content {
      margin: -60px 20px 0 20px;
    }

    .class-img {
      + .class-content {
        margin-left: 20px;
      }
    }
  }
}

@include md {
  .class-style1 {
    .class-content {
      margin: -30px 15px 0 15px;
    }

    .class-title {
      font-size: 24px;
    }

    .class-info {
      font-size: 14px;
    }

    .class-price {
      font-size: 38px;
      margin: 20px 0 0 0;
    }
  }
}


@include sm {
  .class-style1 {
    .class-btn {
      right: 10px;
      bottom: 10px;
      width: 40px;
      height: 40px;
      line-height: 40px;
      font-size: 16px;
      border-radius: 50%;
    }
  }

  .class-bar {
    padding: 15px;
    border-radius: 20px;
  }

  .class-style2 {
    padding: 35px 20px 29px 20px;

    .class-price {
      font-size: 34px;
      margin-top: 20px;
    }

    .class-info {
      font-size: 14px;
    }
  }

  .lession-title.h1 {
    font-size: 28px;
  }

  .lession-price {
    font-size: 40px;
  }

  .class-bar {
    text-align: center;

    .info-title {
      font-size: 14px;
    }

    .info-text {
      font-size: 16px;
    }
  }

}