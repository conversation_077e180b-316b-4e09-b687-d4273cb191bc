import axios from 'axios'

// Create axios instance with base URL
const api = axios.create({
    baseURL: import.meta.env.VITE_API_URL || 'http://localhost:8000/api',
    headers: {
        'Content-Type': 'application/json'
    },
    withCredentials: true
})

// Request interceptor - add auth token
api.interceptors.request.use(config => {
    const user = JSON.parse(localStorage.getItem('user'))
    if (user && user.token) {
        config.headers.Authorization = `Bearer ${user.token}`
    }
    // Automatically set headers for POST requests
    if (config.method === 'post') {
        if (config.data instanceof FormData) {
            config.headers['Content-Type'] = 'multipart/form-data'
        } else {
            config.headers['Content-Type'] = 'application/json'
        }
    }
    return config
})

// Response interceptor - handle common errors
api.interceptors.response.use(
    response => {
        if (response.data && response.data.message) {
        }
        return response
    },
    error => {
        // Handle 401 Unauthorized globally
        if (error.response) {
            console.error(error.response);
        }
        return Promise.reject(error)
    }

)

export default api