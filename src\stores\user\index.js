import 'vue3-toastify/dist/index.css'

import { defineStore } from 'pinia'
import { toast } from 'vue3-toastify'

import api from '@/api/api'

import useAuthStore from '../auth'

const useUserStore = defineStore("useUserStore", {
    state: () => ({
        user: [],
        totalPages: 1, // Default total pages
    }),

    actions: {
        async fetchUser(page = 1, limit = 20) {
            try {
                const response = await api.get(`/user-list?page=${page}&limit=${limit}`);
                console.log(response.data.data.data);
                if (response.data.data.data.length === 0) {
                    toast.error("No user found");
                    return;
                }
                Object.assign(this.user, response.data.data.data);
                this.totalPages = response.data.data.total || 1;
            } catch (err) {
                console.error("Error fetching users:", err);
                toast.error("Failed to fetch users");
            }
        },

        async createUser(userData) {
            await api.post("/createUser", userData).then((response) => {
                if (response.data) {
                    this.user.push(response.data.data.user);
                    toast.success(response.data.message);
                    return true;
                }
            }).catch((err) => {
                console.log(err);
                // console.log(err.response.data.message);
                toast.error(err.response.data.message);
            });
        },

        async updateUser(userData) {
            await api.post("/editUser", userData).then((response) => {
                if (response.data) {
                    toast.success(response.data.message);
                }
            }).catch((err) => {
                console.log(err);
                toast.error(err.response.data.message);
            });
        },
        async deleteUser(id) {
            this.user = this.user.filter((user) => user.id !== id);

            await api.delete(`/deleteUser/${id}`).then((response) => {
                if (response.data) {
                    // Remove user from the list
                    this.user = this.user.filter((user) => user.id !== id);
                    toast.success(response.data.message);
                    window.location.reload();
                }
            }).catch((err) => {
                console.log(err);
                toast.error(err.response.data.message);
            });
        },
        async updatePassword(data) {
            try {
                const response = await api.post('/update-password', data);
                toast.success(response.data.message);
                useAuthStore().logout();
                window.location.reload();
            } catch (err) {
                console.error("Error updating password:", err);
                toast.error(err.response?.data?.message || "Failed to update password");
            }
        },
        async uploadAvatar(formData) {
            try {
                const response = await api.post('/uploadUserAvatar', formData);
                if (response.data) {
                    toast.success(response.data.message);
                    return response.data.data.file_path;
                }
            } catch (err) {
                console.error("Error uploading avatar:", err);
                toast.error(err.response?.data?.message || "Failed to upload avatar");
            }
        }

    },
});

export default useUserStore;

