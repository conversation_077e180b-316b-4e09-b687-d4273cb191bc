import { ref } from 'vue'

const menuItems = ref([
    {
        text: 'Dashboard',
        icon: 'uil uil-create-dashboard',
        active: false,
        open: false,
        link: '/admin/dashboard', // Added link for parent
        // children: [
        //     { text: 'Demo 3', link: '/admin/home', active: false },
        // ],
    },
    {
        text: 'Users',
        icon: 'uil uil-user-circle',
        active: false,
        open: false,
        link: '/admin/users',
    },

    //Social Media Menu
    {
        text: 'Social Media & Contact',
        icon: 'uil uil-twitter',
        active: false,
        open: false,
        link: '/admin/social-media', // No link for parent
        // children: [
        //     { text: 'Facebook', link: 'https://www.facebook.com/', active: false },
        // ],
    },
    // News Letter
    {
        text: 'Newsletter',
        icon: 'uil uil-envelope',
        active: false,
        open: false,
        link: '/admin/newsletter',

    },
    // Admissions
    {
        text: 'Admissions',
        icon: 'uil uil-file-alt',
        active: false,
        open: false,
        link: '/admin/admissions',
    }

    // {
    //     text: 'Pages',
    //     icon: 'uil uil-file-alt',
    //     active: false,
    //     open: false,
    //     link: null, // No link for parent
    //     children: [
    //         { text: 'Page 1', link: '/pages/page1', active: false },
    //         { text: 'Page 2', link: '/pages/page2', active: false },
    //         { text: 'Page 3', link: '/pages/page3', active: false },
    //     ],
    // }
]);

const restoreActiveState = () => {
    const savedState = JSON.parse(localStorage.getItem('activeMenu'));
    if (savedState) {
        const { parentIndex, childIndex } = savedState;
        menuItems.value.forEach((menu, i) => {
            menu.active = i === parentIndex;
            menu.open = i === parentIndex; // Open the parent menu
            menu.children?.forEach((child, j) => {
                child.active = i === parentIndex && j === childIndex;
            });
        });
    }
};

export { menuItems, restoreActiveState }
