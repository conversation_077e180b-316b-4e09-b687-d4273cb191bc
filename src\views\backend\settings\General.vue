<template>
  <div class="p-6">
    <div class="mb-6">
      <h1 class="text-3xl font-bold text-gray-900">General Settings</h1>
      <p class="text-gray-600 mt-2">Configure your application's general settings</p>
    </div>

    <div class="max-w-4xl">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <form @submit.prevent="saveSettings" class="space-y-6">
          <!-- Site Information -->
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">Site Information</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Site Name</label>
                <input 
                  v-model="settings.siteName"
                  type="text" 
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Site URL</label>
                <input 
                  v-model="settings.siteUrl"
                  type="url" 
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
              </div>
            </div>
            <div class="mt-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">Site Description</label>
              <textarea 
                v-model="settings.siteDescription"
                rows="3" 
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              ></textarea>
            </div>
          </div>

          <!-- Contact Information -->
          <div class="border-t pt-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Admin Email</label>
                <input 
                  v-model="settings.adminEmail"
                  type="email" 
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Support Email</label>
                <input 
                  v-model="settings.supportEmail"
                  type="email" 
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                <input 
                  v-model="settings.phoneNumber"
                  type="tel" 
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                <input 
                  v-model="settings.address"
                  type="text" 
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
              </div>
            </div>
          </div>

          <!-- Preferences -->
          <div class="border-t pt-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Preferences</h3>
            <div class="space-y-4">
              <div class="flex items-center">
                <input 
                  v-model="settings.maintenanceMode"
                  type="checkbox" 
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                >
                <label class="ml-2 block text-sm text-gray-900">
                  Maintenance Mode
                </label>
              </div>
              <div class="flex items-center">
                <input 
                  v-model="settings.allowRegistration"
                  type="checkbox" 
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                >
                <label class="ml-2 block text-sm text-gray-900">
                  Allow User Registration
                </label>
              </div>
              <div class="flex items-center">
                <input 
                  v-model="settings.emailNotifications"
                  type="checkbox" 
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                >
                <label class="ml-2 block text-sm text-gray-900">
                  Email Notifications
                </label>
              </div>
            </div>
          </div>

          <!-- Save Button -->
          <div class="border-t pt-6">
            <button 
              type="submit"
              class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Save Settings
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const settings = ref({
  siteName: 'My Admin Panel',
  siteUrl: 'https://example.com',
  siteDescription: 'A modern admin panel built with Vue 3 and Tailwind CSS',
  adminEmail: '<EMAIL>',
  supportEmail: '<EMAIL>',
  phoneNumber: '+****************',
  address: '123 Main St, City, State 12345',
  maintenanceMode: false,
  allowRegistration: true,
  emailNotifications: true
})

const saveSettings = () => {
  // Here you would typically send the settings to your API
  console.log('Saving settings:', settings.value)
  alert('Settings saved successfully!')
}
</script>
