<template>
  <div class="print-modal" v-if="visible">
    <div class="print-modal-content">
      <div class="print-modal-header">
        <h3>Print Table</h3>
        <button class="close-button" @click="close">×</button>
      </div>
      <div class="print-modal-body">
        <div class="print-options">
          <div class="option-group">
            <label>
              <input type="checkbox" v-model="printOptions.includeTitle" />
              Include Title
            </label>
          </div>
          <div class="option-group">
            <label>
              <input type="checkbox" v-model="printOptions.includeFilters" />
              Include Active Filters
            </label>
          </div>
          <div class="option-group">
            <label>
              <input type="checkbox" v-model="printOptions.selectedOnly" :disabled="!hasSelectedItems" />
              Print Selected Items Only
            </label>
            <span v-if="hasSelectedItems" class="selected-count">({{ selectedItems.length }} items selected)</span>
          </div>
          <div class="option-group">
            <label>
              <input type="checkbox" v-model="printOptions.landscape" />
              Landscape Orientation
            </label>
          </div>
        </div>
        <div class="print-preview">
          <h4>Preview</h4>
          <div class="preview-container">
            <div class="preview-table">
              <table>
                <thead>
                  <tr>
                    <th v-for="column in visibleColumns" :key="column.key">{{ column.label }}</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(item, index) in previewData" :key="index">
                    <td v-for="column in visibleColumns" :key="`${index}-${column.key}`">
                      {{ getItemValue(item, column.key) }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      <div class="print-modal-footer">
        <button class="cancel-button" @click="close">Cancel</button>
        <button class="print-button" @click="printTable">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16">
            <path fill="none" d="M0 0h24v24H0z" />
            <path
              d="M7 17h10v5H7v-5zm12 3v-5H5v5H3a1 1 0 0 1-1-1V9a1 1 0 0 1 1-1h18a1 1 0 0 1 1 1v10a1 1 0 0 1-1 1h-2zM5 10v2h3v-2H5zm2-8h10a1 1 0 0 1 1 1v3H6V3a1 1 0 0 1 1-1z"
              fill="currentColor" />
          </svg>
          Print
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PrintTable',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    columns: {
      type: Array,
      required: true
    },
    data: {
      type: Array,
      required: true
    },
    selectedItems: {
      type: Array,
      default: () => []
    },
    title: {
      type: String,
      default: ''
    },
    filters: {
      type: Object,
      default: () => ({})
    },
    isServerSide: {
      type: Boolean,
      default: false
    },
    isLoadingAllData: {
      type: Boolean,
      default: false
    },
    fetchAllData: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      printOptions: {
        includeTitle: true,
        includeFilters: true,
        selectedOnly: false,
        landscape: false
      }
    };
  },
  computed: {
    hasSelectedItems() {
      return this.selectedItems && this.selectedItems.length > 0;
    },
    visibleColumns() {
      // Get all leaf columns (for data display)
      const result = [];

      for (const col of this.columns) {
        if (col.children) {
          result.push(...col.children);
        } else if (col.key) { // Only include columns with keys (not just group headers)
          result.push(col);
        }
      }

      return result;
    },
    previewData() {
      // Return a small subset of data for preview
      const data = this.printOptions.selectedOnly && this.hasSelectedItems
        ? this.selectedItems
        : this.data;

      return data.slice(0, 3); // Show only first 3 rows in preview
    }
  },
  methods: {
    close() {
      this.$emit('close');
    },
    getItemValue(item, key) {
      if (!item) return '';

      // Handle nested properties with dot notation (e.g., 'user.name')
      if (key.includes('.')) {
        return key.split('.').reduce((obj, path) => {
          return obj && obj[path] !== undefined ? obj[path] : '';
        }, item);
      }

      return item[key] !== undefined ? item[key] : '';
    },
    async printTable() {
      // Create a new window for printing
      const printWindow = window.open('', '_blank');

      // Get the data to print
      let dataToPrint;

      // If selected items only is chosen and we have selected items
      if (this.printOptions.selectedOnly && this.hasSelectedItems) {
        dataToPrint = this.selectedItems;
      }
      // For server-side tables, fetch all data if not printing selected items only
      else if (this.isServerSide && this.fetchAllData && !this.printOptions.selectedOnly) {
        try {
          dataToPrint = await this.fetchAllData();
        } catch (error) {
          console.error('Error fetching all data for printing:', error);
          // Fall back to current data if fetch fails
          dataToPrint = this.data;
        }
      }
      // Otherwise use the current data
      else {
        dataToPrint = this.data;
      }

      // Generate the HTML content
      let html = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>${this.title || 'Table Print'}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              margin: 20px;
              ${this.printOptions.landscape ? 'width: 1200px;' : ''}
            }
            h1 {
              font-size: 18px;
              margin-bottom: 10px;
            }
            .filters {
              margin-bottom: 15px;
              font-size: 12px;
            }
            .filter-item {
              margin-right: 15px;
              display: inline-block;
            }
            table {
              width: 100%;
              border-collapse: collapse;
              margin-bottom: 20px;
            }
            th, td {
              border: 1px solid #ddd;
              padding: 8px;
              text-align: left;
            }
            th {
              background-color: #f2f2f2;
              font-weight: bold;
            }
            tr:nth-child(even) {
              background-color: #f9f9f9;
            }
            .print-footer {
              font-size: 10px;
              color: #666;
              margin-top: 20px;
              text-align: center;
            }
            @media print {
              body {
                margin: 0;
                padding: 10px;
                ${this.printOptions.landscape ? 'width: 100%;' : ''}
              }
              @page {
                size: ${this.printOptions.landscape ? 'landscape' : 'portrait'};
              }
            }
          </style>
        </head>
        <body>
      `;

      // Add title if selected
      if (this.printOptions.includeTitle && this.title) {
        html += `<h1>${this.title}</h1>`;
      }

      // Add filters if selected
      if (this.printOptions.includeFilters && Object.keys(this.filters).length > 0) {
        html += `<div class="filters"><strong>Filters:</strong> `;
        for (const [key, value] of Object.entries(this.filters)) {
          const column = this.visibleColumns.find(col => col.key === key);
          if (column) {
            html += `<span class="filter-item">${column.label}: ${value}</span>`;
          }
        }
        html += `</div>`;
      }

      // Add table
      html += `<table>
        <thead>
          <tr>
            ${this.visibleColumns.map(col => `<th>${col.label}</th>`).join('')}
          </tr>
        </thead>
        <tbody>
      `;

      // Add data rows
      dataToPrint.forEach(item => {
        html += `<tr>`;
        this.visibleColumns.forEach(column => {
          html += `<td>${this.getItemValue(item, column.key)}</td>`;
        });
        html += `</tr>`;
      });

      // Close table and add footer
      html += `
        </tbody>
      </table>
      <div class="print-footer">
        Printed on ${new Date().toLocaleString()}
      </div>
      </body>
      </html>
      `;

      // Write to the new window and trigger print
      printWindow.document.write(html);
      printWindow.document.close();

      // Wait for the content to load and then print
      printWindow.onload = function () {
        printWindow.focus(); // Focus on the new window
        setTimeout(function () {
          printWindow.print(); // Trigger print dialog
          // Close the window after printing (or after dialog is closed)
          setTimeout(function () {
            printWindow.close();
          }, 500);
        }, 250); // Small delay to ensure content is fully loaded
      };
    }
  }
};
</script>

<style>
.print-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  /* Match the export modal z-index */
}

.print-modal-content {
  background-color: white;
  border-radius: 8px;
  width: 800px;
  max-width: 90%;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 10000;
  /* Ensure content is above other elements */
}

.print-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e2e8f0;
  background-color: white;
  position: sticky;
  top: 0;
  z-index: 10001;
  /* Higher than the modal content */
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.print-modal-header h3 {
  margin: 0;
  font-size: 18px;
  color: #1e293b;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #64748b;
}

.print-modal-body {
  padding: 20px;
  overflow-y: auto;
  display: flex;
  gap: 20px;
}

.print-options {
  flex: 1;
  min-width: 200px;
}

.option-group {
  margin-bottom: 16px;
}

.option-group label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.option-group input[type="checkbox"] {
  margin-right: 8px;
}

.selected-count {
  margin-left: 8px;
  font-size: 12px;
  color: #64748b;
}

.print-preview {
  flex: 2;
}

.print-preview h4 {
  margin-top: 0;
  margin-bottom: 12px;
  color: #1e293b;
}

.preview-container {
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  padding: 16px;
  background-color: #f8fafc;
  max-height: 300px;
  overflow: auto;
}

.preview-table table {
  width: 100%;
  border-collapse: collapse;
}

.preview-table th,
.preview-table td {
  border: 1px solid #e2e8f0;
  padding: 8px;
  text-align: left;
  font-size: 12px;
}

.preview-table th {
  background-color: #f1f5f9;
  font-weight: 600;
}

.print-modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 16px 20px;
  border-top: 1px solid #e2e8f0;
  gap: 12px;
  background-color: white;
  position: sticky;
  bottom: 0;
  z-index: 10001;
  /* Same as header */
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.cancel-button {
  padding: 8px 16px;
  background-color: #f1f5f9;
  border: 1px solid #cbd5e1;
  border-radius: 4px;
  color: #334155;
  cursor: pointer;
}

.print-button {
  padding: 8px 16px;
  background-color: #3b82f6;
  border: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
}

.print-button:hover {
  background-color: #2563eb;
}
</style>
