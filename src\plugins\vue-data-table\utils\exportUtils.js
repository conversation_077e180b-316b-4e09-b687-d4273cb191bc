/**
 * Utility functions for exporting table data in various formats
 */

/**
 * Export data to CSV format
 * @param {Array} data - The data to export
 * @param {Object} columnLabels - Object mapping column keys to display labels
 * @param {String} filename - The filename without extension
 */
export function exportToCsv(data, columnLabels, filename) {
  if (!data || !data.length) return;
  
  const columnKeys = Object.keys(columnLabels);
  const headerRow = columnKeys.map(key => columnLabels[key]);
  
  // Create CSV content
  const csvContent = [
    // Header row
    headerRow.join(','),
    // Data rows
    ...data.map(item => {
      return columnKeys.map(key => {
        const value = item[key];
        // Handle values that need to be quoted (contain commas, quotes, or newlines)
        if (value === null || value === undefined) {
          return '';
        }
        const stringValue = String(value);
        if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
          return `"${stringValue.replace(/"/g, '""')}"`;
        }
        return stringValue;
      }).join(',');
    })
  ].join('\n');
  
  // Create and download the file
  downloadFile(csvContent, `${filename}.csv`, 'text/csv');
}

/**
 * Export data to Excel format
 * Note: This is a simple implementation that creates an HTML table and opens it in Excel
 * For a more robust solution, consider using a library like xlsx or exceljs
 * 
 * @param {Array} data - The data to export
 * @param {Object} columnLabels - Object mapping column keys to display labels
 * @param {String} filename - The filename without extension
 */
export function exportToExcel(data, columnLabels, filename) {
  if (!data || !data.length) return;
  
  const columnKeys = Object.keys(columnLabels);
  
  // Create HTML table
  let html = '<table>';
  
  // Add header row
  html += '<thead><tr>';
  for (const key of columnKeys) {
    html += `<th>${columnLabels[key]}</th>`;
  }
  html += '</tr></thead>';
  
  // Add data rows
  html += '<tbody>';
  for (const item of data) {
    html += '<tr>';
    for (const key of columnKeys) {
      const value = item[key] !== undefined && item[key] !== null ? item[key] : '';
      html += `<td>${value}</td>`;
    }
    html += '</tr>';
  }
  html += '</tbody></table>';
  
  // Create a blob with the HTML content
  const blob = new Blob([html], { type: 'application/vnd.ms-excel' });
  
  // Create a download link and trigger the download
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = `${filename}.xls`;
  link.click();
  
  // Clean up
  URL.revokeObjectURL(link.href);
}

/**
 * Export data to JSON format
 * @param {Array} data - The data to export
 * @param {String} filename - The filename without extension
 */
export function exportToJson(data, filename) {
  if (!data) return;
  
  const jsonContent = JSON.stringify(data, null, 2);
  downloadFile(jsonContent, `${filename}.json`, 'application/json');
}

/**
 * Helper function to download a file
 * @param {String} content - The file content
 * @param {String} filename - The filename with extension
 * @param {String} contentType - The content type
 */
function downloadFile(content, filename, contentType) {
  const blob = new Blob([content], { type: contentType });
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = filename;
  link.click();
  
  // Clean up
  URL.revokeObjectURL(link.href);
}
