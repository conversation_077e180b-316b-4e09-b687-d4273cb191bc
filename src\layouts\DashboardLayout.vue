<script setup lang="ts">
import {
  onMounted,
  ref,
} from 'vue'

import Footer from '../components/backend/Footer.vue'
import Header from '../components/backend/Header.vue'
import Sidebar from '../components/backend/Sidebar.vue'

const isSidebarOpen = ref(false);

const toggleSidebar = () => {
    isSidebarOpen.value = !isSidebarOpen.value;
};

</script>

<template>
    <div class="mobile-search">
        <form action="/" class="search-form">
            <img src="/img/svg/search.svg" alt="search" class="svg">
            <input class="form-control me-sm-2 box-shadow-none" type="search" placeholder="Search..."
                aria-label="Search">
        </form>
    </div>
    <div class="mobile-author-actions"></div>
    <Header :toggleSidebar="toggleSidebar" />



    <main class="main-content">
        <Sidebar :class="{ 'sidebar-collapse': !isSidebarOpen }" />



        <div class="contents">
            <router-view> </router-view>
        </div>
        <Footer />
    </main>
    <!-- <div id="overlayer">
        <div class="loader-overlay">
            <div class="dm-spin-dots spin-lg">
                <span class="spin-dot badge-dot dot-primary"></span>
                <span class="spin-dot badge-dot dot-primary"></span>
                <span class="spin-dot badge-dot dot-primary"></span>
                <span class="spin-dot badge-dot dot-primary"></span>
            </div>
        </div>
    </div>
    <div class="overlay-dark-sidebar"></div>
    <div class="customizer-overlay"></div> -->
</template>
