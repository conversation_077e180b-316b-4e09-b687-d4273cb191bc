<script setup>
import { onMounted } from 'vue'

import router from '@/router'

onMounted(() => {
  const isModalClosed = sessionStorage.getItem('isModalClosed');
  if (isModalClosed == 'true') {
    const modal = document.getElementById('admissionModal');
    if (modal) {
      modal.classList.remove('show');
      modal.style.display = 'none';
      modal.setAttribute('aria-hidden', 'false');
    }
  }
});

function closeModal() {
  const modal = document.getElementById('admissionModal');
  if (modal) {
    modal.classList.remove('show');
    modal.style.display = 'none';
    modal.setAttribute('aria-hidden', 'true');
  }
  sessionStorage.setItem('isModalClosed', 'true');

}


function enrollNow() {
  const modal = document.getElementById('admissionModal');
  if (modal) {
    modal.classList.remove('show');
    modal.style.display = 'none';
    modal.setAttribute('aria-hidden', 'true');
  }
  sessionStorage.setItem('isModalClosed', 'true');
  router.push('/admission')
}
</script>
<template>

  <!-- Modal -->
  <div class="modal fade show" id="admissionModal" tabindex="-1" aria-labelledby="admissionModalLabel"
    aria-hidden="false">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header border-0 p-0">
          <button type="button" class="btn-close position-absolute top-0 end-0 m-3 bg-white rounded-circle"
            data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body p-0">
          <div class="row g-0">
            <div class="col-md-12">
              <div class="admission-bg p-4">
                <!-- Logo -->
                <div class="logo-container">
                  <div class="logo-icon">
                    <i class="fas fa-graduation-cap"></i>
                  </div>
                  <div class="logo-text">LOGO</div>
                </div>

                <!-- Decorative elements -->
                <div class="decoration-atom">
                  <i class="fas fa-atom"></i>
                </div>
                <div class="decoration-rocket">
                  <i class="fas fa-rocket"></i>
                </div>
                <div class="decoration-flask">
                  <i class="fas fa-flask"></i>
                </div>

                <!-- Main content -->
                <div class="row">
                  <div class="col-md-7">
                    <h1 class="header-banner">ADMISSIONS OPEN</h1>

                    <!-- Discount badge -->
                    <div class="discount-badge">
                      <div class="discount-text">75%</div>
                      <div class="discount-label">OFF!</div>
                    </div>

                    <button class="btn registration-btn">
                      Now Enrolling for New Sessions!
                    </button>

                    <p class="text-white mb-4">
                      Give your little one the best start in life! At Sabhyata Little Stars, we nurture curiosity,
                      creativity, and confidence in every child through playful learning and loving care.
                    </p>

                    <div class="benefits-section">
                      <button @click="admissionPage" class="btn benefits-btn ">
                        Why Choose Us?
                      </button>

                      <ul class="benefits-list">
                        <li><i class="fas fa-check-circle"></i> Warm, nurturing environment</li>
                        <li><i class="fas fa-check-circle"></i> Fun-filled learning activities</li>
                        <li><i class="fas fa-check-circle"></i> Experienced and caring teachers</li>
                        <li><i class="fas fa-check-circle"></i> Focus on early development</li>
                      </ul>
                    </div>
                  </div>

                  <div class="col-md-5 d-flex align-items-center justify-content-center">
                    <div class="student-image">
                      <img
                        src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/image-lrZ9Bfvs8ZU57MjrWK1BHx6v8G6j63.png"
                        alt="Happy Child at School" class="img-fluid"
                        style="max-height: 350px; object-fit: contain; object-position: right;">
                    </div>
                  </div>
                </div>


                <button @click="closeModal"
                  class="btn close-btn position-absolute top-0 start-0 m-3 bg-white rounded-circle" aria-label="Close">
                  <i class="fas fa-times"></i>
                </button>

                <button @click="enrollNow" class="btn btn-primary mt-3 position-absolute bottom-0 end-0 m-3">
                  Enroll Now
                </button>

              

              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

</template>
<style scoped>
.modal-content {
  border: none;
  border-radius: 10px;
  overflow: hidden;
}

.admission-bg {
  background-color: #0d6e58;
  position: relative;
  overflow: hidden;
}

.admission-bg::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 2 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 2 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E");
  z-index: 0;
}

.logo-container {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.logo-icon {
  background-color: #0d6e58;
  color: white;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-right: 10px;
}

.logo-text {
  color: #0d6e58;
  font-weight: bold;
  font-size: 24px;
}

.header-banner {
  background-color: #ff9800;
  color: white;
  font-weight: bold;
  padding: 8px 20px;
  border-radius: 5px;
  display: inline-block;
  font-size: 28px;
  margin-bottom: 15px;
  transform: rotate(-2deg);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.discount-badge {
  background-color: #ffc107;
  color: white;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  position: absolute;
  top: 30%;
  right: 10%;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 2;
}

.discount-text {
  font-size: 32px;
  line-height: 1;
}

.discount-label {
  font-size: 18px;
}

.registration-btn {
  background-color: #0d6e58;
  color: white;
  border: 2px solid white;
  border-radius: 30px;
  padding: 8px 20px;
  font-weight: bold;
  margin-bottom: 15px;
}

.benefits-btn {
  background-color: transparent;
  color: white;
  border: 2px solid #ffc107;
  border-radius: 30px;
  padding: 8px 20px;
  font-weight: bold;
  margin-top: 10px;
}

.benefits-list {
  list-style: none;
  padding-left: 0;
  margin-top: 15px;
}

.benefits-list li {
  margin-bottom: 8px;
  color: white;
  display: flex;
  align-items: center;
}

.benefits-list li i {
  color: #ffc107;
  margin-right: 10px;
}

.student-image {
  position: relative;
  z-index: 1;
}

.decoration-atom {
  position: absolute;
  top: 10px;
  right: 10px;
  color: #4fc3f7;
  font-size: 40px;
  opacity: 0.8;
  transform: rotate(15deg);
}

.decoration-rocket {
  position: absolute;
  top: 20%;
  left: 10px;
  color: #f44336;
  font-size: 30px;
  transform: rotate(-30deg);
}

.decoration-flask {
  position: absolute;
  bottom: 20%;
  right: 20px;
  color: #ffc107;
  font-size: 30px;
}

/* Modal trigger button styling */
.modal-trigger {
  background-color: #0d6e58;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  margin: 20px;
  font-weight: bold;
}

.modal.fade.show {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1050;
  /* Ensure it appears above other elements */
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-color: rgba(0, 0, 0, 0.5);
}

.close-btn {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  color: #0d6e58;
  font-size: 18px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 1051;
}
</style>