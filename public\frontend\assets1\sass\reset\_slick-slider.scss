.row>.slick-list {
  padding-left: 0;
  padding-right: 0;
}

.slick-track>[class*=col] {
  flex-shrink: 0;
  width: 100%;
  max-width: 100%;
  padding-right: calc(var(--bs-gutter-x)/ 2);
  padding-left: calc(var(--bs-gutter-x)/ 2);
  margin-top: var(--bs-gutter-y);
}

.slick-track {
  min-width: 100%;
}

.slick-slide img {
  display: inline-block;
}


.slick-dots {
  list-style-type: none;
  padding: 0;
  margin: 10px 0 30px 0;
  text-align: center;
  height: max-content;
  line-height: 0;

  li {
    display: inline-block;
    margin-right: 7px;

    &:last-child {
      margin-right: 0;
    }
  }

  button {
    position: relative;
    font-size: 0;
    padding: 0;
    width: 14px;
    height: 14px;
    line-height: 0;
    border: none;
    background-color: #B5B5B5;
    border-radius: 50%;
    transition: all ease 0.4s;
  }

  button:hover,
  .slick-active button {
    background-color: $theme-color;
    border-color: $theme-color;
  }
}


.slick-arrow {
  display: inline-block;
  border: none;
  padding: 0;
  position: absolute;
  top: 50%;
  z-index: 2;
  left: var(--pos-x, -150px);
  width: var(--icon-size, 60px);
  height: var(--icon-size, 60px);
  font-size: var(--icon-font-size, 24px);
  margin-top: calc(var(--icon-size, 60px) / -2);
  background-color: $theme-color;
  color: $white-color;
  border-radius: 50%;
  transition: all ease 0.6s;

  &:before {
    content: '';
    position: absolute;
    left: 3px;
    top: 3px;
    bottom: 3px;
    right: 3px;
    background-color: $theme-color2;
    border-radius: inherit;
    z-index: -1;
    transform: scale(0.5);
    opacity: 0;
    visibility: hidden;
    transition: all ease 0.4s;
  }

  &.default {
    position: relative;
    --pos-x: 0;
    margin-top: 0;
  }

  &.slick-next {
    right: var(--pos-x, -150px);
    left: auto;
  }

  &:hover {
    background-color: $secondary-color;
    color: $title-color;

    &::before {
      opacity: 1;
      visibility: visible;
      transform: scale(1);
    }
  }
}

.arrow-margin {
  .slick-arrow {
    top: calc(50% - 30px);
  }
}

.arrow-wrap {
  .slick-arrow {
    opacity: 0;
    visibility: hidden;
  }

  &:hover {
    .slick-arrow {
      opacity: 1;
      visibility: visible;
    }
  }
}



@include xl {
  .slick-arrow {
    --arrow-horizontal: -20px;
  }
}

@include ml {
  .slick-arrow {
    --arrow-horizontal: 40px;
  }
}

@include md {
  .slick-arrow {
    --icon-size: 40px;
    margin-right: 40px;

    &.slick-next {
      margin-right: 0;
      margin-left: 40px;
    }
  }

  .slick-dots {
    margin: 8px 0 38px 0;
  }
}