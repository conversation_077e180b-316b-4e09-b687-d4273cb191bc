.widget-area {
  padding-top: 80px;
  padding-bottom: 40px;
}

.copyright-wrap {
  text-align: center;
  padding: 29px 0;
  background-color: $theme-color2;
}

.copyright-text {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: $black-color;
  font-family: $title-font;

  a {
    color: inherit;
    text-decoration: underline;

    &:hover {
      color: $theme-color;
    }
  }
}

.copyright-menu {
  ul {
    margin: 0;
  }

  li {
    margin-right: 20px;
    display: inline-block;
  }

  a {
    display: block;
    text-decoration: underline;
    text-transform: uppercase;
    color: $body-color;
    font-size: 16px;
    font-weight: 500;

    &:hover {
      color: $theme-color;
    }
  }
}


.footer-layout3,
.footer-layout1 {
  background-color: $secondary-color;
  --title-color: #FFFFFF;
  --body-color: #FFFFFF;
  
  .countdown-style1,
  .vs-social,
  .vs-btn {
    --title-color: #000;
  }
}

.footer-layout1 {
  .footer-top {
    padding: 60px 0 0 0;
  }
}

.footer-call {
  font-size: 18px;
  color: $white-color;
  display: flex;

  i {
    color: $theme-color2;
    margin-right: 10px;
    font-size: 30px;
  }

  a {
    text-decoration: underline;
    text-decoration-thickness: 2px;
    text-decoration-color: $theme-color2;
  }
}

.footer-layout3 {
  .copyright-wrap {
    background-color: transparent;
    border-top: 1px solid rgba(#FFFFFF, 0.30);
  }

  .widget-area {
    @media (min-width: $lg) {
      padding-top: 90px;
      padding-bottom: 50px;
    }
  }

  .footer-top {
    background-color: $white-color;
    padding: 30px 60px;
    border-radius: 30px;
  }
}

@include ml {
  .footer-layout3 {
    .footer-top {
      padding: 25px 30px;
    }
  }
}


@include sm {
  .copyright-text {
    font-size: 16px;
  }

  .footer-layout3 {
    .footer-top {
      padding: 25px 15px;
    }
  }
}