.singletab .tabcontents {
	position: relative;
}
.singletab .tabcontents .tabitem {
	transform: translateY(100px);
	position: absolute;
	z-index: -1;
	top: 0;
	width: 100%;
	opacity: 0;
	transition: 0.8s all;
}
.singletab .tabcontents .tabitem.active {
	position: initial;
	z-index: 1;
	opacity: 1;
	transform: translateY(0);
}
//Global tabs


//Global Social
.social-wrapper {
	a {
		width: 35px;
		height: 35px;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 5px;
	}
}
//Global Social

// Inspair Section
.inspair-section {
	background: url(../../assets/img/ainspair/inspair-bg.png) no-repeat center
		center;
	background-size: cover;
	.inspainr-thumb-box {
		position: relative;
	}
	.inspair-thumb {
		animation: lf 2s linear infinite;
		width: 100%;
		img {
			width: 100%;
		}
	}
	.inspair-content {
		padding: 190px 0;
		form {
			display: flex;
			align-items: center;
			border-radius: 10px;
			padding: 10px;
			border: 1px solid $black;
			background: $white;
			input {
				padding: 16px 2px 16px 22px;
				width: 100%;
				border: unset;
				color: $black;
				background: transparent;
				font-weight: 500;
				font-size: 16px;
			}
			::placeholder {
				color: $pra;
			}
			button {
				border-radius: 10px;
				height: 55px;
				min-width: 130px;
				background: $p2-clr;
				display: flex;
				align-items: center;
				justify-content: center;
				border: unset;
				i {
					color: $white;
					font-size: 16px;
				}
			}
		}
	}
	@include breakpoint(max-xxl) {
		.inspair-content {
			padding: 110px 0;
		}
	}
	@include breakpoint(max-lg) {
		.inspair-content {
			form {
				padding: 2px 4px;
				input {
					padding: 10px 2px 10px 16px;
				}
				button {
					height: 40px;
					min-width: 90px;
					font-size: 14px;
					i {
						color: $white;
						font-size: 12px;
					}
				}
			}
		}
	}
	@include breakpoint(max-sm) {
		.inspair-content {
			padding: 0px 0;
		}
	}
}
// Inspair Section

// Blog Section
.blog-sectionv1 {
	background: linear-gradient(180deg, #fff0e5 0%, rgba(255, 240, 229, 0) 100%);
	.news-small-items {
		padding: 10px;
		box-shadow: 0px 4.4px 20px -1px rgba(19, 16, 34, 0.05);
		background: $white;
		border-radius: 10px;
		@include flex;
		gap: 20px;
		.news-thumb {
			img {
				border-radius: 10px;
			}
		}
		.news-content {
			ul {
				@include flex;
				gap: 20px;
				margin-bottom: 14px;
				li {
					font-size: 14px;
					font-family: $body-font;
					color: $pra !important;
					i {
						color: $p5-clr;
						margin-right: 5px;
					}
				}
			}

			h4 {
				margin-bottom: 20px;
				a {
					&:hover {
						color: $p5-clr;
					}
				}
			}
			.readmore {
				&:hover {
					color: $p5-clr;
					.arrows {
						color: $p5-clr;
					}
				}
			}
		}
		@include breakpoint(max-xl) {
			gap: 14px;
			.news-thumb {
				width: 160px;
				img {
					width: 100%;
				}
			}
			.news-content {
				ul {
					@include flex;
					gap: 14px;
					margin-bottom: 7px;
				}

				h4 {
					margin-bottom: 7px;
					font-weight: 500;
				}
				.readmore {
					font-size: 14px;
					i {
						font-size: 13px;
					}
				}
			}
		}
		@include breakpoint(max-sm) {
			gap: 14px;
			display: grid;
			justify-content: center;
			text-align: center;
			.news-thumb {
				width: 100%;
				margin: 0 auto;
				img {
					width: 100%;
				}
			}
			.news-content {
				ul {
					@include flex;
					justify-content: center;
				}
				.readmore {
					justify-content: center;
				}
			}
		}
	}
	//blog single items
	.news-single-items {
		.news-thumbig {
			width: 100%;
			img {
				width: 100%;
			}
		}
		.news-content {
			padding: 36px 40px;
		}
		@include breakpoint(max-xxl) {
			.news-content {
				padding: 20px 24px;
			}
		}
	}
}
// Blog Section

// Talk Counter Section
.talk-content {
	.section-title {
		h3 {
			margin-bottom: 20px;
		}
		p {
			margin-bottom: 40px;
			color: $pra;
		}
	}
	@include breakpoint(max-lg) {
		.section-title {
			h3 {
				margin-bottom: 16px;
			}
			p {
				margin-bottom: 24px;
				color: $pra;
			}
		}
	}
}
.counter-inner {
	.counter-talk-items {
		padding: 30px 18px;
		display: flex;
		gap: 20px;
		align-items: center;
		transition: all 0.4s;
		.icon {
			min-width: 80px;
			min-height: 80px;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			transition: all 0.4s;
		}
		.content {
			h3 {
				color: $black;
				span {
					font-size: 32px;
					color: $black;
				}
			}
			p {
				font-size: 16px;
				color: $pra;
			}
		}
		&:hover {
			.icon{
				transform: scale(1.04);
			}
			box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
		}
	}
	@include breakpoint(max-xxl) {
		.counter-talk-items {
			padding: 20px 16px;
			gap: 14px;
			.icon {
				min-width: 65px;
				min-height: 65px;
				img {
					width: 30px;
					object-fit: contain;
				}
			}
			.content {
				h3 {
					color: $black;
					span {
						font-size: 32px;
						color: $black;
					}
				}
				p {
					font-size: 16px;
					color: $pra;
				}
			}
		}
	}
	@include breakpoint(max-xxl) {
		.counter-talk-items {
			padding: 18px 14px;
			gap: 14px;
			.icon {
				min-width: 50px;
				min-height: 50px;
				img {
					width: 24px;
					object-fit: contain;
				}
			}
			.content {
				h3 {
					color: $black;
					span {
						font-size: 32px;
						color: $black;
					}
				}
				p {
					font-size: 16px;
					color: $pra;
				}
			}
		}
	}
}
// Talk Counter Section
