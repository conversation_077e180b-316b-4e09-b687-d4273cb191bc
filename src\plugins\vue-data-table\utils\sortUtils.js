/**
 * Utility functions for sorting table data
 */

/**
 * Sort an array of objects by a specific key
 * 
 * @param {Array} items - The array of items to sort
 * @param {String} key - The key to sort by
 * @param {String} order - The sort order ('asc' or 'desc')
 * @returns {Array} The sorted array
 */
export function sortItems(items, key, order = 'asc') {
  if (!items || !items.length || !key) return items;
  
  const sortOrder = order === 'desc' ? -1 : 1;
  
  return [...items].sort((a, b) => {
    const aValue = getNestedValue(a, key);
    const bValue = getNestedValue(b, key);
    
    return compareValues(aValue, bValue) * sortOrder;
  });
}

/**
 * Get a nested value from an object using dot notation
 * 
 * @param {Object} obj - The object to get the value from
 * @param {String} key - The key path (e.g., 'user.name')
 * @returns {*} The value at the specified path
 */
export function getNestedValue(obj, key) {
  if (!obj || !key) return null;
  
  // Handle nested properties with dot notation
  if (key.includes('.')) {
    return key.split('.').reduce((o, k) => (o && o[k] !== undefined ? o[k] : null), obj);
  }
  
  return obj[key] !== undefined ? obj[key] : null;
}

/**
 * Compare two values for sorting
 * 
 * @param {*} a - First value
 * @param {*} b - Second value
 * @returns {Number} -1, 0, or 1 for sorting
 */
export function compareValues(a, b) {
  // Handle null/undefined values
  if (a === null || a === undefined) return b === null || b === undefined ? 0 : -1;
  if (b === null || b === undefined) return 1;
  
  // Handle different types
  const typeA = typeof a;
  const typeB = typeof b;
  
  // If types are different, sort by type name
  if (typeA !== typeB) {
    return typeA.localeCompare(typeB);
  }
  
  // Sort based on type
  switch (typeA) {
    case 'number':
      return a - b;
    case 'string':
      return a.localeCompare(b);
    case 'boolean':
      return a === b ? 0 : (a ? 1 : -1);
    case 'object':
      // Handle dates
      if (a instanceof Date && b instanceof Date) {
        return a.getTime() - b.getTime();
      }
      // For other objects, convert to string
      return String(a).localeCompare(String(b));
    default:
      return String(a).localeCompare(String(b));
  }
}
