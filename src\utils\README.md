# Dynamic Menu System Documentation

## Overview

The dynamic menu system provides a flexible and powerful way to manage navigation menus in your admin panel. It supports up to 3 levels of nesting, dynamic badges, and real-time menu manipulation.

## Features

- ✅ **Dynamic Menu Management**: Add, remove, and update menu items programmatically
- ✅ **Multi-level Support**: Support for parent and child menu items (up to 3 levels)
- ✅ **Badge System**: Add badges to menu items for notifications or status indicators
- ✅ **Active State Management**: Automatic active state detection based on current route
- ✅ **Submenu Toggle**: Collapsible submenus with smooth animations
- ✅ **Responsive Design**: Mobile-friendly with touch support
- ✅ **Tailwind CSS Integration**: Uses Tailwind CSS for styling (CDN imported)

## Usage

### Basic Setup

```javascript
import { useMenuStore } from '@/utils/menuStore.js'

const menuStore = useMenuStore()
```

### Adding Menu Items

```javascript
// Add a main menu item
menuStore.addMenuItem({
  text: 'Reports',
  icon: 'uil uil-chart',
  link: '/admin/reports',
  badge: null,
  children: null
})

// Add a child menu item
menuStore.addMenuItem({
  text: 'Sales Report',
  link: '/admin/reports/sales',
  icon: 'uil uil-money-bill'
}, 'Reports') // Parent menu text
```

### Managing Badges

```javascript
// Set a badge on a menu item
menuStore.setBadge('Reports', 'New')

// Set a badge on a child menu item
menuStore.setBadge('Sales Report', '5', 'Reports')

// Clear a specific badge
menuStore.setBadge('Reports', null)

// Clear all badges
menuStore.clearAllBadges()
```

### Menu Navigation

```javascript
// Set active menu
menuStore.setActiveMenu('Dashboard')

// Toggle submenu
menuStore.toggleSubmenu('Reports')

// Open specific submenu
menuStore.openSubmenu('Reports')

// Close specific submenu
menuStore.closeSubmenu('Reports')

// Close all submenus
menuStore.closeAllSubmenus()
```

### Checking Menu States

```javascript
// Check if menu is active
const isActive = menuStore.isMenuActive(menuItem)

// Check if submenu is open
const isOpen = menuStore.isSubmenuOpen('Reports')

// Get current active menu
const activeMenu = menuStore.activeMenu
```

### Removing Menu Items

```javascript
// Remove a main menu item
menuStore.removeMenuItem('Reports')

// Remove a child menu item
menuStore.removeMenuItem('Sales Report', 'Reports')
```

### Updating Menu Items

```javascript
// Update a menu item
menuStore.updateMenuItem('Reports', {
  text: 'Analytics',
  icon: 'uil uil-analytics'
})

// Update a child menu item
menuStore.updateMenuItem('Sales Report', {
  text: 'Sales Analytics',
  badge: 'Updated'
}, 'Reports')
```

## Menu Item Structure

```javascript
{
  text: 'Menu Name',           // Required: Display text
  icon: 'uil uil-icon-name',   // Required: Icon class
  link: '/admin/path',         // Optional: Route path
  badge: 'New',               // Optional: Badge text
  children: [                 // Optional: Child menu items
    {
      text: 'Child Menu',
      link: '/admin/child-path',
      icon: 'uil uil-child-icon'
    }
  ]
}
```

## Helper Functions

### Creating Menu Items

```javascript
import { menuUtils } from '@/utils/menuStore.js'

// Create a main menu item
const menuItem = menuUtils.createMenuItem(
  'Dashboard',           // text
  'uil uil-dashboard',   // icon
  '/admin/dashboard',    // link
  null,                  // children
  'New'                  // badge
)

// Create a child menu item
const childItem = menuUtils.createChildMenuItem(
  'Sub Menu',            // text
  '/admin/sub-menu',     // link
  'uil uil-circle'       // icon (optional)
)
```

### Validation

```javascript
// Validate menu item structure
const isValid = menuUtils.validateMenuItem(menuItem)
```

### Breadcrumb Generation

```javascript
// Generate breadcrumb from route
const breadcrumb = menuUtils.generateBreadcrumb('/admin/reports/sales', menuStore.menuItems)
// Returns: ['Reports', 'Sales Report']
```

## Color Scheme

The admin layout uses a unique gradient color scheme:

- **Primary Gradient**: Indigo → Purple → Pink
- **Active States**: Pink/Violet gradients
- **Hover Effects**: White overlay with opacity
- **Badges**: Pink and Violet variants

## Responsive Behavior

- **Desktop**: Full sidebar with text and icons
- **Tablet**: Collapsible sidebar
- **Mobile**: Overlay sidebar with backdrop

## Animation Features

- **Smooth Transitions**: All state changes are animated
- **Hover Effects**: Scale and color transitions
- **Submenu Animations**: Slide and fade effects
- **Badge Animations**: Pulse effect for new badges

## Integration with Vue Router

The menu system automatically:
- Detects current route and sets active menu
- Opens parent submenus for nested routes
- Updates breadcrumb based on current location

## Example Implementation

See `src/components/admin/MenuManager.vue` for a complete example of how to use the dynamic menu system with a management interface.

## Customization

### Adding Custom Animations

```css
/* Add to your component styles */
.custom-menu-animation {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.custom-menu-animation:hover {
  transform: translateX(4px);
}
```

### Custom Badge Styles

```javascript
// Add custom badge classes in your menu items
{
  text: 'Special Menu',
  icon: 'uil uil-star',
  link: '/admin/special',
  badge: 'VIP',
  badgeClass: 'bg-gold-500 text-white' // Custom badge styling
}
```

## Best Practices

1. **Icon Consistency**: Use consistent icon library (Unicons recommended)
2. **Badge Management**: Keep badges relevant and up-to-date
3. **Menu Depth**: Limit to 3 levels for better UX
4. **Route Naming**: Use consistent route naming conventions
5. **Performance**: Use lazy loading for menu components

## Troubleshooting

### Common Issues

1. **Menu not updating**: Ensure you're using the store correctly
2. **Icons not showing**: Check icon class names and CDN links
3. **Active state not working**: Verify route paths match exactly
4. **Animations not smooth**: Check CSS transitions and Tailwind classes

### Debug Mode

```javascript
// Enable debug logging
console.log('Current menu state:', {
  activeMenu: menuStore.activeMenu,
  openSubmenus: Array.from(menuStore.openSubmenus),
  menuItems: menuStore.menuItems
})
```
