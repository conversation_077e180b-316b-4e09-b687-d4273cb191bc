import { ref, computed } from 'vue'
import { defineStore } from 'pinia'

export const useMenuStore = defineStore('menuStore', () => {
  // State
  const activeMenu = ref('')
  const openSubmenus = ref(new Set())
  const menuItems = ref([
    {
      text: 'Dashboard',
      icon: 'uil uil-create-dashboard',
      link: '/admin/dashboard',
      badge: null,
      children: null
    },
    {
      text: 'Users',
      icon: 'uil uil-user-circle',
      link: '/admin/users',
      badge: null,
      children: null
    },
    {
      text: 'Content Management',
      icon: 'uil uil-file-alt',
      link: null,
      badge: 'New',
      children: [
        { 
          text: 'Posts', 
          link: '/admin/posts',
          icon: 'uil uil-document-layout-left'
        },
        { 
          text: 'Categories', 
          link: '/admin/categories',
          icon: 'uil uil-folder'
        },
        { 
          text: 'Media Library', 
          link: '/admin/media',
          icon: 'uil uil-image'
        }
      ]
    },
    {
      text: 'Social Media & Contact',
      icon: 'uil uil-twitter',
      link: '/admin/social-media',
      badge: null,
      children: null
    },
    {
      text: 'Newsletter',
      icon: 'uil uil-envelope',
      link: '/admin/newsletter',
      badge: null,
      children: null
    },
    {
      text: 'Admissions',
      icon: 'uil uil-graduation-cap',
      link: '/admin/admissions',
      badge: null,
      children: null
    },
    {
      text: 'Settings',
      icon: 'uil uil-setting',
      link: null,
      badge: null,
      children: [
        { 
          text: 'General Settings', 
          link: '/admin/settings/general',
          icon: 'uil uil-cog'
        },
        { 
          text: 'User Permissions', 
          link: '/admin/settings/permissions',
          icon: 'uil uil-shield-check'
        },
        { 
          text: 'System Logs', 
          link: '/admin/settings/logs',
          icon: 'uil uil-file-info-alt'
        }
      ]
    }
  ])

  // Computed
  const flatMenuItems = computed(() => {
    const flat = []
    menuItems.value.forEach(item => {
      flat.push(item)
      if (item.children) {
        flat.push(...item.children)
      }
    })
    return flat
  })

  // Actions
  const setActiveMenu = (menuText) => {
    activeMenu.value = menuText
  }

  const toggleSubmenu = (menuText) => {
    if (openSubmenus.value.has(menuText)) {
      openSubmenus.value.delete(menuText)
    } else {
      openSubmenus.value.add(menuText)
    }
  }

  const isSubmenuOpen = (menuText) => {
    return openSubmenus.value.has(menuText)
  }

  const isMenuActive = (item) => {
    return activeMenu.value === item.text
  }

  const initializeFromRoute = (routePath) => {
    // Find menu item by route path
    const menuItem = flatMenuItems.value.find(item => item.link === routePath)
    if (menuItem) {
      setActiveMenu(menuItem.text)
      
      // If it's a child menu, open its parent submenu
      const parentMenu = menuItems.value.find(parent => 
        parent.children && parent.children.some(child => child.link === routePath)
      )
      if (parentMenu) {
        openSubmenus.value.add(parentMenu.text)
      }
    }
  }

  const addMenuItem = (item, parentText = null) => {
    if (parentText) {
      // Add as child to existing parent
      const parent = menuItems.value.find(menu => menu.text === parentText)
      if (parent) {
        if (!parent.children) {
          parent.children = []
        }
        parent.children.push(item)
      }
    } else {
      // Add as main menu item
      menuItems.value.push(item)
    }
  }

  const removeMenuItem = (itemText, parentText = null) => {
    if (parentText) {
      // Remove from parent's children
      const parent = menuItems.value.find(menu => menu.text === parentText)
      if (parent && parent.children) {
        parent.children = parent.children.filter(child => child.text !== itemText)
      }
    } else {
      // Remove main menu item
      menuItems.value = menuItems.value.filter(item => item.text !== itemText)
    }
  }

  const updateMenuItem = (itemText, updates, parentText = null) => {
    let targetItem
    
    if (parentText) {
      // Update child item
      const parent = menuItems.value.find(menu => menu.text === parentText)
      if (parent && parent.children) {
        targetItem = parent.children.find(child => child.text === itemText)
      }
    } else {
      // Update main menu item
      targetItem = menuItems.value.find(item => item.text === itemText)
    }
    
    if (targetItem) {
      Object.assign(targetItem, updates)
    }
  }

  const getMenuByText = (itemText, parentText = null) => {
    if (parentText) {
      const parent = menuItems.value.find(menu => menu.text === parentText)
      if (parent && parent.children) {
        return parent.children.find(child => child.text === itemText)
      }
    } else {
      return menuItems.value.find(item => item.text === itemText)
    }
    return null
  }

  const setBadge = (itemText, badge, parentText = null) => {
    const item = getMenuByText(itemText, parentText)
    if (item) {
      item.badge = badge
    }
  }

  const clearAllBadges = () => {
    menuItems.value.forEach(item => {
      item.badge = null
      if (item.children) {
        item.children.forEach(child => {
          child.badge = null
        })
      }
    })
  }

  const closeAllSubmenus = () => {
    openSubmenus.value.clear()
  }

  const openSubmenu = (menuText) => {
    openSubmenus.value.add(menuText)
  }

  const closeSubmenu = (menuText) => {
    openSubmenus.value.delete(menuText)
  }

  // Reset store
  const reset = () => {
    activeMenu.value = ''
    openSubmenus.value.clear()
  }

  return {
    // State
    activeMenu,
    openSubmenus,
    menuItems,
    
    // Computed
    flatMenuItems,
    
    // Actions
    setActiveMenu,
    toggleSubmenu,
    isSubmenuOpen,
    isMenuActive,
    initializeFromRoute,
    addMenuItem,
    removeMenuItem,
    updateMenuItem,
    getMenuByText,
    setBadge,
    clearAllBadges,
    closeAllSubmenus,
    openSubmenu,
    closeSubmenu,
    reset
  }
})

// Helper functions for menu management
export const menuUtils = {
  // Create a new menu item
  createMenuItem: (text, icon, link = null, children = null, badge = null) => ({
    text,
    icon,
    link,
    children,
    badge
  }),

  // Create a child menu item
  createChildMenuItem: (text, link, icon = 'uil uil-circle') => ({
    text,
    link,
    icon
  }),

  // Validate menu item structure
  validateMenuItem: (item) => {
    const required = ['text', 'icon']
    const valid = required.every(field => item.hasOwnProperty(field))
    
    if (!valid) {
      console.warn('Menu item missing required fields:', required)
      return false
    }
    
    if (!item.link && !item.children) {
      console.warn('Menu item must have either a link or children')
      return false
    }
    
    return true
  },

  // Generate breadcrumb from current route
  generateBreadcrumb: (routePath, menuItems) => {
    const breadcrumb = []
    
    // Find the menu item
    for (const item of menuItems) {
      if (item.link === routePath) {
        breadcrumb.push(item.text)
        break
      }
      
      if (item.children) {
        const child = item.children.find(child => child.link === routePath)
        if (child) {
          breadcrumb.push(item.text, child.text)
          break
        }
      }
    }
    
    return breadcrumb
  }
}
