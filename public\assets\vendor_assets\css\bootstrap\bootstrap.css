/* Custom bootstrap classes */

/*!
 * Bootstrap v5.0.2 (https://getbootstrap.com/)
 * Copyright 2011-2021 The Bootstrap Authors
 * Copyright 2011-2021 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
 */

:root {
	--bs-blue: #0d6efd;
	--bs-indigo: #6610f2;
	--bs-purple: #6f42c1;
	--bs-pink: #d63384;
	--bs-red: #dc3545;
	--bs-orange: #fd7e14;
	--bs-yellow: #ffc107;
	--bs-green: #198754;
	--bs-teal: #20c997;
	--bs-cyan: #0dcaf0;
	--bs-white: #fff;
	--bs-gray: #6c757d;
	--bs-gray-dark: #343a40;
	--bs-primary: #8231D3;
	--bs-secondary: #5840FF;
	--bs-success: #01B81A;
	--bs-info: #00AAFF;
	--bs-infos: #00E4EC;
	--bs-warning: #FA8B0C;
	--bs-warnings: #FFBB00;
	--bs-danger: #FF0F0F;
	--bs-dangers: #FF0F0F;
	--bs-purple: #A722F6;
	--bs-dark: #0A0A0A;
	--bs-light: #8C90A4;
	--bs-lighten: #A0A0A0;
	--bs-light-gray: #8C90A4;
	--bs-text: #666d92;
	--bs-gray: #404040;
	--bs-third: #8231D3;
	--bs-white: #ffffff;
	--bs-font-sans-serif: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
	--bs-font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
	--bs-gradient: linear-gradient(180deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
}

*,
*::before,
*::after {
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
}

body {
	margin: 0;
	font-family: "Jost", sans-serif;
	font-size: 16px;
	font-weight: 400;
	line-height: 1.5;
	color: var(--body-color);
	background-color: #fff;
	-webkit-text-size-adjust: 100%;
	-webkit-tap-highlight-color: rgba(var(--color-dark), 0);
}

hr {
	margin: 4.67rem 0;
	color: inherit;
	background-color: currentColor;
	border: 0;
	opacity: 0.25;
}

hr:not([size]) {
	height: 1px;
}

h6,
.h6,
h5,
.h5,
h4,
.h4,
h3,
.h3,
h2,
.h2,
h1,
.h1 {
	margin-top: 0;
	margin-bottom: 0;
	font-family: "Jost", sans-serif;
	font-weight: 600;
	line-height: 1.2;
	color: var(--color-dark);
}

h1,
.h1 {
	font-size: calc(17.4px + 1.05vw);
}

h2,
.h2 {
	font-size: calc(16.8px + 0.6vw);
}

h3,
.h3 {
	font-size: calc(16.6px + 0.45vw);
}

h4,
.h4 {
	font-size: calc(16.4px + 0.3vw);
}

h5,
.h5 {
	font-size: calc(16.2px + 0.15vw);
}

h6,
.h6 {
	font-size: 16px;
}

p {
	margin-top: 0;
	margin-bottom: 1rem;
}

abbr[title],
abbr[data-bs-original-title] {
	-webkit-text-decoration: underline dotted;
	text-decoration: underline dotted;
	cursor: help;
	text-decoration-skip-ink: none;
}

address {
	margin-bottom: 1rem;
	font-style: normal;
	line-height: inherit;
}

ol,
ul {
	padding-left: 2rem;
}

ol,
ul,
dl {
	margin-top: 0;
	margin-bottom: 1rem;
}

ol ol,
ul ul,
ol ul,
ul ol {
	margin-bottom: 0;
}

dt {
	font-weight: 700;
}

dd {
	margin-bottom: 0.5rem;
	margin-left: 0;
}

blockquote {
	margin: 0 0 1rem;
}

b,
strong {
	font-weight: bolder;
}

small,
.small {
	font-size: 0.875em;
}

mark,
.mark {
	padding: 0.2em;
	background-color: #fcf8e3;
}

sub,
sup {
	position: relative;
	font-size: 0.75em;
	line-height: 0;
	vertical-align: baseline;
}

sub {
	bottom: -0.25em;
}

sup {
	top: -0.5em;
}

a {
	color: var(--color-primary);
	text-decoration: none;
}

a:hover {
	color: #0a58ca;
	text-decoration: none;
}

a:not([href]):not([class]),
a:not([href]):not([class]):hover {
	color: inherit;
	text-decoration: none;
}

pre,
code,
kbd,
samp {
	font-family: var(--bs-font-monospace);
	font-size: 1em;
	direction: ltr /* rtl:ignore */;
	unicode-bidi: bidi-override;
}

pre {
	display: block;
	margin-top: 0;
	margin-bottom: 1rem;
	overflow: auto;
	font-size: 0.875em;
}

pre code {
	font-size: inherit;
	color: inherit;
	word-break: normal;
}

code {
	font-size: 0.875em;
	color: #d63384;
	word-wrap: break-word;
}

a > code {
	color: inherit;
}

kbd {
	padding: 0.2rem 0.4rem;
	font-size: 0.875em;
	color: #fff;
	background-color: #212529;
	-webkit-border-radius: 0.2rem;
	border-radius: 0.2rem;
}

kbd kbd {
	padding: 0;
	font-size: 1em;
	font-weight: 700;
}

figure {
	margin: 0 0 1rem;
}

img,
svg {
	vertical-align: middle;
}

table {
	caption-side: bottom;
	border-collapse: collapse;
}

caption {
	padding-top: 0.5rem;
	padding-bottom: 0.5rem;
	color: #6c757d;
	text-align: left;
}

th {
	text-align: inherit;
	text-align: -webkit-match-parent;
}

thead,
tbody,
tfoot,
tr,
td,
th {
	border-color: inherit;
	border-style: solid;
	border-width: 0;
}

label {
	display: inline-block;
}

button {
	-webkit-border-radius: 0;
	border-radius: 0;
}

button:focus:not(:focus-visible) {
	outline: 0;
}

input,
button,
select,
optgroup,
textarea {
	margin: 0;
	font-family: inherit;
	font-size: inherit;
	line-height: inherit;
}

button,
select {
	text-transform: none;
}

[role=button] {
	cursor: pointer;
}

select {
	word-wrap: normal;
}

select:disabled {
	opacity: 1;
}

[list]::-webkit-calendar-picker-indicator {
	display: none;
}

button,
[type=button],
[type=reset],
[type=submit] {
	-webkit-appearance: button;
}

button:not(:disabled),
[type=button]:not(:disabled),
[type=reset]:not(:disabled),
[type=submit]:not(:disabled) {
	cursor: pointer;
}

::-moz-focus-inner {
	padding: 0;
	border-style: none;
}

textarea {
	resize: vertical;
}

fieldset {
	min-width: 0;
	padding: 0;
	margin: 0;
	border: 0;
}

legend {
	float: left;
	width: 100%;
	padding: 0;
	margin-bottom: 0.5rem;
	font-size: calc(16.8px + 0.6vw);
	line-height: inherit;
}

legend + * {
	clear: left;
}

::-webkit-datetime-edit-fields-wrapper,
::-webkit-datetime-edit-text,
::-webkit-datetime-edit-minute,
::-webkit-datetime-edit-hour-field,
::-webkit-datetime-edit-day-field,
::-webkit-datetime-edit-month-field,
::-webkit-datetime-edit-year-field {
	padding: 0;
}

::-webkit-inner-spin-button {
	height: auto;
}

[type=search] {
	outline-offset: -2px;
	-webkit-appearance: textfield;
}

/* rtl:raw:
[type="tel"],
[type="url"],
[type="email"],
[type="number"] {
  direction: ltr;
}
*/

::-webkit-search-decoration {
	-webkit-appearance: none;
}

::-webkit-color-swatch-wrapper {
	padding: 0;
}

::file-selector-button {
	font: inherit;
}

::-webkit-file-upload-button {
	font: inherit;
	-webkit-appearance: button;
}

output {
	display: inline-block;
}

iframe {
	border: 0;
}

summary {
	display: list-item;
	cursor: pointer;
}

progress {
	vertical-align: baseline;
}

[hidden] {
	display: none !important;
}

.lead {
	font-size: calc(16.528px + 0.396vw);
	font-weight: 400;
}

.display-1 {
	font-size: calc(19.2px + 2.4vw);
	font-weight: 500;
	line-height: 1.2;
}

.display-2 {
	font-size: calc(18.4px + 1.8vw);
	font-weight: 500;
	line-height: 1.2;
}

.display-3 {
	font-size: calc(18px + 1.5vw);
	font-weight: 500;
	line-height: 1.2;
}

.display-4 {
	font-size: calc(17.4px + 1.05vw);
	font-weight: 500;
	line-height: 1.2;
}

.list-unstyled {
	padding-left: 0;
	list-style: none;
}

.list-inline {
	padding-left: 0;
	list-style: none;
}

.list-inline-item {
	display: inline-block;
}

.list-inline-item:not(:last-child) {
	margin-right: 0.5rem;
}

.initialism {
	font-size: 0.875em;
	text-transform: uppercase;
}

.blockquote {
	margin-bottom: 1rem;
	font-size: calc(16.4px + 0.3vw);
}

.blockquote > :last-child {
	margin-bottom: 0;
}

.blockquote-footer {
	margin-top: -1rem;
	margin-bottom: 1rem;
	font-size: 0.875em;
	color: #6c757d;
}

.blockquote-footer::before {
	content: "— ";
}

.img-fluid {
	max-width: 100%;
	height: auto;
}

.img-thumbnail {
	padding: 0.25rem;
	background-color: #fff;
	border: 1px solid #dee2e6;
	-webkit-border-radius: 0.25rem;
	border-radius: 0.25rem;
	-webkit-box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
	box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
	max-width: 100%;
	height: auto;
}

.figure {
	display: inline-block;
}

.figure-img {
	margin-bottom: 0.5rem;
	line-height: 1;
}

.figure-caption {
	font-size: 0.875em;
	color: #6c757d;
}

.container,
.container-fluid,
.container-xxl,
.container-xl,
.container-lg,
.container-md,
.container-sm {
	width: 100%;
	padding-right: var(--bs-gutter-x, 0.75rem);
	padding-left: var(--bs-gutter-x, 0.75rem);
	margin-right: auto;
	margin-left: auto;
}

.row {
	--bs-gutter-x: 1.5rem;
	--bs-gutter-y: 0;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
	margin-top: calc(var(--bs-gutter-y) * -1);
	margin-right: calc(var(--bs-gutter-x) * -0.5);
	margin-left: calc(var(--bs-gutter-x) * -0.5);
}

.row > * {
	-ms-flex-negative: 0;
	flex-shrink: 0;
	width: 100%;
	max-width: 100%;
	padding-right: calc(var(--bs-gutter-x) * 0.5);
	padding-left: calc(var(--bs-gutter-x) * 0.5);
	margin-top: var(--bs-gutter-y);
}

.col {
	-webkit-box-flex: 1;
	-ms-flex: 1 0 0%;
	flex: 1 0 0%;
}

.row-cols-auto > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: auto;
}

.row-cols-1 > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 100%;
}

.row-cols-2 > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 50%;
}

.row-cols-3 > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 33.3333333333%;
}

.row-cols-4 > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 25%;
}

.row-cols-5 > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 20%;
}

.row-cols-6 > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 16.6666666667%;
}

.col-auto {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: auto;
}

.col-1 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 8.33333333%;
}

.col-2 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 16.66666667%;
}

.col-3 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 25%;
}

.col-4 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 33.33333333%;
}

.col-5 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 41.66666667%;
}

.col-6 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 50%;
}

.col-7 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 58.33333333%;
}

.col-8 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 66.66666667%;
}

.col-9 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 75%;
}

.col-10 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 83.33333333%;
}

.col-11 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 91.66666667%;
}

.col-12 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 100%;
}

.offset-1 {
	margin-left: 8.33333333%;
}

.offset-2 {
	margin-left: 16.66666667%;
}

.offset-3 {
	margin-left: 25%;
}

.offset-4 {
	margin-left: 33.33333333%;
}

.offset-5 {
	margin-left: 41.66666667%;
}

.offset-6 {
	margin-left: 50%;
}

.offset-7 {
	margin-left: 58.33333333%;
}

.offset-8 {
	margin-left: 66.66666667%;
}

.offset-9 {
	margin-left: 75%;
}

.offset-10 {
	margin-left: 83.33333333%;
}

.offset-11 {
	margin-left: 91.66666667%;
}

.g-0,
.gx-0 {
	--bs-gutter-x: 0;
}

.g-0,
.gy-0 {
	--bs-gutter-y: 0;
}

.g-1,
.gx-1 {
	--bs-gutter-x: 0.25rem;
}

.g-1,
.gy-1 {
	--bs-gutter-y: 0.25rem;
}

.g-2,
.gx-2 {
	--bs-gutter-x: 0.5rem;
}

.g-2,
.gy-2 {
	--bs-gutter-y: 0.5rem;
}

.g-3,
.gx-3 {
	--bs-gutter-x: 1rem;
}

.g-3,
.gy-3 {
	--bs-gutter-y: 1rem;
}

.g-4,
.gx-4 {
	--bs-gutter-x: 1.5rem;
}

.g-4,
.gy-4 {
	--bs-gutter-y: 1.5rem;
}

.g-5,
.gx-5 {
	--bs-gutter-x: 3rem;
}

.g-5,
.gy-5 {
	--bs-gutter-y: 3rem;
}

.table {
	--bs-table-bg: transparent;
	--bs-table-accent-bg: transparent;
	--bs-table-striped-color: #212529;
	--bs-table-striped-bg: rgba(0, 0, 0, 0.05);
	--bs-table-active-color: #212529;
	--bs-table-active-bg: rgba(0, 0, 0, 0.1);
	--bs-table-hover-color: #212529;
	--bs-table-hover-bg: rgba(0, 0, 0, 0.075);
	width: 100%;
	margin-bottom: 1rem;
	color: var(--color-dark);
	vertical-align: top;
	border-color: var(--border-color);
}

.table > :not(caption) > * > * {
	padding: 0.5rem 0.5rem;
	background-color: var(--bs-table-bg);
	border-bottom-width: 1px;
	-webkit-box-shadow: inset 0 0 0 9999px var(--bs-table-accent-bg);
	box-shadow: inset 0 0 0 9999px var(--bs-table-accent-bg);
}

.table > tbody {
	vertical-align: inherit;
}

.table > thead {
	vertical-align: bottom;
}

.table > :not(:last-child) > :last-child > * {
	border-bottom-color: currentColor;
}

.caption-top {
	caption-side: top;
}

.table-sm > :not(caption) > * > * {
	padding: 0.25rem 0.25rem;
}

.table-bordered > :not(caption) > * {
	border-width: 1px 0;
}

.table-bordered > :not(caption) > * > * {
	border-width: 0 1px;
}

.table-borderless > :not(caption) > * > * {
	border-bottom-width: 0;
}

.table-striped > tbody > tr:nth-of-type(odd) {
	--bs-table-accent-bg: var(--bs-table-striped-bg);
	color: var(--bs-table-striped-color);
}

.table-active {
	--bs-table-accent-bg: var(--bs-table-active-bg);
	color: var(--bs-table-active-color);
}

.table-hover > tbody > tr:hover {
	--bs-table-accent-bg: var(--bs-table-hover-bg);
	color: var(--bs-table-hover-color);
}

.table-primary {
	--bs-table-bg: #cfe2ff;
	--bs-table-striped-bg: #c5d7f2;
	--bs-table-striped-color: #000;
	--bs-table-active-bg: #bacbe6;
	--bs-table-active-color: #000;
	--bs-table-hover-bg: #bfd1ec;
	--bs-table-hover-color: #000;
	color: #000;
	border-color: #bacbe6;
}

.table-secondary {
	--bs-table-bg: #e2e3e5;
	--bs-table-striped-bg: #d7d8da;
	--bs-table-striped-color: #000;
	--bs-table-active-bg: #cbccce;
	--bs-table-active-color: #000;
	--bs-table-hover-bg: #d1d2d4;
	--bs-table-hover-color: #000;
	color: #000;
	border-color: #cbccce;
}

.table-success {
	--bs-table-bg: #d1e7dd;
	--bs-table-striped-bg: #c7dbd2;
	--bs-table-striped-color: #000;
	--bs-table-active-bg: #bcd0c7;
	--bs-table-active-color: #000;
	--bs-table-hover-bg: #c1d6cc;
	--bs-table-hover-color: #000;
	color: #000;
	border-color: #bcd0c7;
}

.table-info {
	--bs-table-bg: #cff4fc;
	--bs-table-striped-bg: #c5e8ef;
	--bs-table-striped-color: #000;
	--bs-table-active-bg: #badce3;
	--bs-table-active-color: #000;
	--bs-table-hover-bg: #bfe2e9;
	--bs-table-hover-color: #000;
	color: #000;
	border-color: #badce3;
}

.table-warning {
	--bs-table-bg: #fff3cd;
	--bs-table-striped-bg: #f2e7c3;
	--bs-table-striped-color: #000;
	--bs-table-active-bg: #e6dbb9;
	--bs-table-active-color: #000;
	--bs-table-hover-bg: #ece1be;
	--bs-table-hover-color: #000;
	color: #000;
	border-color: #e6dbb9;
}

.table-danger {
	--bs-table-bg: #f8d7da;
	--bs-table-striped-bg: #eccccf;
	--bs-table-striped-color: #000;
	--bs-table-active-bg: #dfc2c4;
	--bs-table-active-color: #000;
	--bs-table-hover-bg: #e5c7ca;
	--bs-table-hover-color: #000;
	color: #000;
	border-color: #dfc2c4;
}

.table-light {
	--bs-table-bg: #f8f9fa;
	--bs-table-striped-bg: #ecedee;
	--bs-table-striped-color: #000;
	--bs-table-active-bg: #dfe0e1;
	--bs-table-active-color: #000;
	--bs-table-hover-bg: #e5e6e7;
	--bs-table-hover-color: #000;
	color: #000;
	border-color: #dfe0e1;
}

.table-dark {
	--bs-table-bg: #212529;
	--bs-table-striped-bg: #2c3034;
	--bs-table-striped-color: #fff;
	--bs-table-active-bg: #373b3e;
	--bs-table-active-color: #fff;
	--bs-table-hover-bg: #323539;
	--bs-table-hover-color: #fff;
	color: #fff;
	border-color: #373b3e;
}

.table-responsive {
	overflow-x: auto;
	-webkit-overflow-scrolling: touch;
}

.form-label {
	margin-bottom: 0.5rem;
}

.col-form-label {
	padding-top: calc(0.375rem + 1px);
	padding-bottom: calc(0.375rem + 1px);
	margin-bottom: 0;
	font-size: inherit;
	line-height: 1.5;
}

.col-form-label-lg {
	padding-top: calc(0.5rem + 1px);
	padding-bottom: calc(0.5rem + 1px);
	font-size: 15px;
}

.col-form-label-sm {
	padding-top: calc(0.25rem + 1px);
	padding-bottom: calc(0.25rem + 1px);
	font-size: 12px;
}

.form-text {
	margin-top: 0.66rem;
	font-size: 0.875em;
	color: #6c757d;
}

.form-control {
	display: block;
	width: 100%;
	padding: 0.375rem 1.2rem;
	font-size: 14px;
	font-weight: 400;
	line-height: 1.5;
	color: var(--color-dark);
	background-color: var(--input-bg);
	-webkit-background-clip: padding-box;
	background-clip: padding-box;
	border: 1px solid var(--border-light);
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;
	-webkit-border-radius: 5px;
	border-radius: 5px;
	-webkit-box-shadow: 0 0;
	box-shadow: 0 0;
	-webkit-transition: border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
	transition: border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
	-o-transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
	transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
	transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
}

.form-control[type=file] {
	overflow: hidden;
}

.form-control[type=file]:not(:disabled):not([readonly]) {
	cursor: pointer;
}

.form-control:focus {
	color: var(--color-dark);
	background-color: var(--input-focus-bg);
	border-color: var(--color-primary);
	outline: 0;
	-webkit-box-shadow: 0 0, 0px 5px 20px rgba(95, 99, 242, 0.1019607843);
	box-shadow: 0 0, 0px 5px 20px rgba(95, 99, 242, 0.1019607843);
}

.form-control::-webkit-date-and-time-value {
	height: 1.5em;
}

.form-control::-webkit-input-placeholder {
	color: #6c757d;
	opacity: 1;
}

.form-control::-moz-placeholder {
	color: #6c757d;
	opacity: 1;
}

.form-control::-ms-input-placeholder {
	color: #6c757d;
	opacity: 1;
}

.form-control::placeholder {
	color: #6c757d;
	opacity: 1;
}

.form-control:disabled,
.form-control[readonly] {
	background-color: var(--color-lighter);
	opacity: 1;
}

.form-control::file-selector-button {
	padding: 0.375rem 1.2rem;
	margin: -0.375rem -1.2rem;
	-webkit-margin-end: 1.2rem;
	margin-inline-end: 1.2rem;
	color: #212529;
	background-color: #e9ecef;
	pointer-events: none;
	border-color: inherit;
	border-style: solid;
	border-width: 0;
	border-inline-end-width: 1px;
	-webkit-border-radius: 0;
	border-radius: 0;
	-webkit-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
	transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
	-o-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
	transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
	transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
}

.form-control:hover:not(:disabled):not([readonly])::file-selector-button {
	background-color: #dde0e3;
}

.form-control::-webkit-file-upload-button {
	padding: 0.375rem 1.2rem;
	margin: -0.375rem -1.2rem;
	-webkit-margin-end: 1.2rem;
	margin-inline-end: 1.2rem;
	color: #212529;
	background-color: #e9ecef;
	pointer-events: none;
	border-color: inherit;
	border-style: solid;
	border-width: 0;
	border-inline-end-width: 1px;
	-webkit-border-radius: 0;
	border-radius: 0;
	-webkit-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
	transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
	-o-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
	transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
	transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
}

.form-control:hover:not(:disabled):not([readonly])::-webkit-file-upload-button {
	background-color: #dde0e3;
}

.form-control-plaintext {
	display: block;
	width: 100%;
	padding: 0.375rem 0;
	margin-bottom: 0;
	line-height: 1.5;
	color: #212529;
	background-color: transparent;
	border: solid transparent;
	border-width: 1px 0;
}

.form-control-plaintext.form-control-sm,
.form-control-plaintext.form-control-lg {
	padding-right: 0;
	padding-left: 0;
}

.form-control-sm {
	min-height: 1.875rem;
	padding: 0.25rem 0.5rem;
	font-size: 12px;
	-webkit-border-radius: 0.2rem;
	border-radius: 0.2rem;
}

.form-control-sm::file-selector-button {
	padding: 0.25rem 0.5rem;
	margin: -0.25rem -0.5rem;
	-webkit-margin-end: 0.5rem;
	margin-inline-end: 0.5rem;
}

.form-control-sm::-webkit-file-upload-button {
	padding: 0.25rem 0.5rem;
	margin: -0.25rem -0.5rem;
	-webkit-margin-end: 0.5rem;
	margin-inline-end: 0.5rem;
}

.form-control-lg {
	min-height: 3.125rem;
	padding: 0.5rem 1rem;
	font-size: 15px;
	-webkit-border-radius: 0.3rem;
	border-radius: 0.3rem;
}

.form-control-lg::file-selector-button {
	padding: 0.5rem 1rem;
	margin: -0.5rem -1rem;
	-webkit-margin-end: 1rem;
	margin-inline-end: 1rem;
}

.form-control-lg::-webkit-file-upload-button {
	padding: 0.5rem 1rem;
	margin: -0.5rem -1rem;
	-webkit-margin-end: 1rem;
	margin-inline-end: 1rem;
}

textarea.form-control {
	min-height: 2.625rem;
}

textarea.form-control-sm {
	min-height: 1.875rem;
}

textarea.form-control-lg {
	min-height: 3.125rem;
}

.form-control-color {
	max-width: 3rem;
	height: auto;
	padding: 0.375rem;
}

.form-control-color:not(:disabled):not([readonly]) {
	cursor: pointer;
}

.form-control-color::-moz-color-swatch {
	height: 1.5em;
	border-radius: 5px;
}

.form-control-color::-webkit-color-swatch {
	height: 1.5em;
	-webkit-border-radius: 5px;
	border-radius: 5px;
}

.form-select {
	display: block;
	width: 100%;
	padding: 0.375rem 2.25rem 0.375rem 0.75rem;
	-moz-padding-start: calc(0.75rem - 3px);
	font-size: 16px;
	font-weight: 400;
	line-height: 1.5;
	color: #212529;
	background-color: #fff;
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
	background-repeat: no-repeat;
	background-position: right 0.75rem center;
	-webkit-background-size: 16px 12px;
	background-size: 16px 12px;
	border: 1px solid #ced4da;
	-webkit-border-radius: 0.25rem;
	border-radius: 0.25rem;
	-webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.075);
	box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.075);
	-webkit-transition: border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
	transition: border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
	-o-transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
	transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
	transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;
}

.form-select:focus {
	border-color: #86b7fe;
	outline: 0;
	-webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.075), 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
	box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.075), 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-select[multiple],
.form-select[size]:not([size="1"]) {
	padding-right: 0.75rem;
	background-image: none;
}

.form-select:disabled {
	background-color: #e9ecef;
}

.form-select:-moz-focusring {
	color: transparent;
	text-shadow: 0 0 0 #212529;
}

.form-select-sm {
	padding-top: 0.25rem;
	padding-bottom: 0.25rem;
	padding-left: 0.5rem;
	font-size: 14px;
}

.form-select-lg {
	padding-top: 0.5rem;
	padding-bottom: 0.5rem;
	padding-left: 1rem;
	font-size: calc(16.4px + 0.3vw);
}

.form-check {
	display: block;
	min-height: 1.5rem;
	padding-left: 1.5em;
	margin-bottom: 0.125rem;
}

.form-check .form-check-input {
	float: left;
	margin-left: -1.5em;
}

.form-check-input {
	width: 1em;
	height: 1em;
	margin-top: 0.25em;
	vertical-align: top;
	background-color: #fff;
	background-repeat: no-repeat;
	background-position: center;
	-webkit-background-size: contain;
	background-size: contain;
	border: 1px solid rgba(0, 0, 0, 0.25);
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;
	color-adjust: exact;
}

.form-check-input[type=checkbox] {
	-webkit-border-radius: 0.25em;
	border-radius: 0.25em;
}

.form-check-input[type=radio] {
	-webkit-border-radius: 50%;
	border-radius: 50%;
}

.form-check-input:active {
	-webkit-filter: brightness(90%);
	filter: brightness(90%);
}

.form-check-input:focus {
	border-color: #86b7fe;
	outline: 0;
	-webkit-box-shadow: none;
	box-shadow: none;
}

.form-check-input:checked {
	background-color: #0d6efd;
	border-color: #0d6efd;
}

.form-check-input:checked[type=checkbox] {
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");
}

.form-check-input:checked[type=radio] {
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23fff'/%3e%3c/svg%3e");
}

.form-check-input[type=checkbox]:indeterminate {
	background-color: #0d6efd;
	border-color: #0d6efd;
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3e%3c/svg%3e");
}

.form-check-input:disabled {
	pointer-events: none;
	-webkit-filter: none;
	filter: none;
	opacity: 0.5;
}

.form-check-input[disabled] ~ .form-check-label,
.form-check-input:disabled ~ .form-check-label {
	opacity: 0.5;
}

.form-switch {
	padding-left: 2.5em;
}

.form-switch .form-check-input {
	width: 2em;
	margin-left: -2.5em;
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280, 0, 0, 0.25%29'/%3e%3c/svg%3e");
	background-position: left center;
	-webkit-border-radius: 2em;
	border-radius: 2em;
	-webkit-transition: background-position 0.15s ease-in-out;
	-o-transition: background-position 0.15s ease-in-out;
	transition: background-position 0.15s ease-in-out;
}

.form-switch .form-check-input:focus {
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%2386b7fe'/%3e%3c/svg%3e");
}

.form-switch .form-check-input:checked {
	background-position: right center;
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
}

.form-check-inline {
	display: inline-block;
	margin-right: 1rem;
}

.btn-check {
	position: absolute;
	clip: rect(0, 0, 0, 0);
	pointer-events: none;
}

.btn-check[disabled] + .btn,
.btn-check:disabled + .btn {
	pointer-events: none;
	-webkit-filter: none;
	filter: none;
	opacity: 0.65;
}

.form-range {
	width: 100%;
	height: 1.5rem;
	padding: 0;
	background-color: transparent;
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;
}

.form-range:focus {
	outline: 0;
}

.form-range:focus::-webkit-slider-thumb {
	-webkit-box-shadow: 0 0 0 1px #fff, 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
	box-shadow: 0 0 0 1px #fff, 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-range:focus::-moz-range-thumb {
	box-shadow: 0 0 0 1px #fff, 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-range::-moz-focus-outer {
	border: 0;
}

.form-range::-webkit-slider-thumb {
	width: 1rem;
	height: 1rem;
	margin-top: -0.25rem;
	background-color: #0d6efd;
	border: 0;
	-webkit-border-radius: 1rem;
	border-radius: 1rem;
	-webkit-box-shadow: 0 0.1rem 0.25rem rgba(0, 0, 0, 0.1);
	box-shadow: 0 0.1rem 0.25rem rgba(0, 0, 0, 0.1);
	-webkit-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
	transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
	-o-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
	transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
	transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
	-webkit-appearance: none;
	appearance: none;
}

.form-range::-webkit-slider-thumb:active {
	background-color: #b6d4fe;
}

.form-range::-webkit-slider-runnable-track {
	width: 100%;
	height: 0.5rem;
	color: transparent;
	cursor: pointer;
	background-color: #dee2e6;
	border-color: transparent;
	-webkit-border-radius: 1rem;
	border-radius: 1rem;
	-webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.075);
	box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.075);
}

.form-range::-moz-range-thumb {
	width: 1rem;
	height: 1rem;
	background-color: #0d6efd;
	border: 0;
	border-radius: 1rem;
	box-shadow: 0 0.1rem 0.25rem rgba(0, 0, 0, 0.1);
	-webkit-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
	transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
	-o-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
	transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
	transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
	-moz-appearance: none;
	appearance: none;
}

.form-range::-moz-range-thumb:active {
	background-color: #b6d4fe;
}

.form-range::-moz-range-track {
	width: 100%;
	height: 0.5rem;
	color: transparent;
	cursor: pointer;
	background-color: #dee2e6;
	border-color: transparent;
	border-radius: 1rem;
	box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.075);
}

.form-range:disabled {
	pointer-events: none;
}

.form-range:disabled::-webkit-slider-thumb {
	background-color: #adb5bd;
}

.form-range:disabled::-moz-range-thumb {
	background-color: #adb5bd;
}

.form-floating {
	position: relative;
}

.form-floating > .form-control,
.form-floating > .form-select {
	height: calc(3.5rem + 2px);
	line-height: 1.25;
}

.form-floating > label {
	position: absolute;
	top: 0;
	left: 0;
	height: 100%;
	padding: 1rem 0.75rem;
	pointer-events: none;
	border: 1px solid transparent;
	-webkit-transform-origin: 0 0;
	-ms-transform-origin: 0 0;
	transform-origin: 0 0;
	-webkit-transition: opacity 0.1s ease-in-out, -webkit-transform 0.1s ease-in-out;
	transition: opacity 0.1s ease-in-out, -webkit-transform 0.1s ease-in-out;
	-o-transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
	transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
	transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out, -webkit-transform 0.1s ease-in-out;
}

.form-floating > .form-control {
	padding: 1rem 0.75rem;
}

.form-floating > .form-control::-webkit-input-placeholder {
	color: transparent;
}

.form-floating > .form-control::-moz-placeholder {
	color: transparent;
}

.form-floating > .form-control::-ms-input-placeholder {
	color: transparent;
}

.form-floating > .form-control::placeholder {
	color: transparent;
}

.form-floating > .form-control:focus,
.form-floating > .form-control:not(:placeholder-shown) {
	padding-top: 1.625rem;
	padding-bottom: 0.625rem;
}

.form-floating > .form-control:-webkit-autofill {
	padding-top: 1.625rem;
	padding-bottom: 0.625rem;
}

.form-floating > .form-select {
	padding-top: 1.625rem;
	padding-bottom: 0.625rem;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label,
.form-floating > .form-select ~ label {
	opacity: 0.65;
	-webkit-transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
	-ms-transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
	transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

.form-floating > .form-control:-webkit-autofill ~ label {
	opacity: 0.65;
	-webkit-transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
	transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

.input-group {
	position: relative;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
	-webkit-box-align: stretch;
	-ms-flex-align: stretch;
	align-items: stretch;
	width: 100%;
}

.input-group > .form-control,
.input-group > .form-select {
	position: relative;
	-webkit-box-flex: 1;
	-ms-flex: 1 1 auto;
	flex: 1 1 auto;
	width: 1%;
	min-width: 0;
}

.input-group > .form-control:focus,
.input-group > .form-select:focus {
	z-index: 3;
}

.input-group .btn {
	position: relative;
	z-index: 2;
}

.input-group .btn:focus {
	z-index: 3;
}

.input-group-text {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	padding: 0.375rem 0.75rem;
	font-size: 14px;
	font-weight: 400;
	line-height: 1.5;
	color: #212529;
	text-align: center;
	white-space: nowrap;
	background-color: #e9ecef;
	border: 1px solid #ced4da;
	-webkit-border-radius: 5px;
	border-radius: 5px;
}

.input-group-lg > .form-control,
.input-group-lg > .form-select,
.input-group-lg > .input-group-text,
.input-group-lg > .btn {
	padding: 0.5rem 1rem;
	font-size: 15px;
	-webkit-border-radius: 0.3rem;
	border-radius: 0.3rem;
}

.input-group-sm > .form-control,
.input-group-sm > .form-select,
.input-group-sm > .input-group-text,
.input-group-sm > .btn {
	padding: 0.25rem 0.5rem;
	font-size: 12px;
	-webkit-border-radius: 0.2rem;
	border-radius: 0.2rem;
}

.input-group-lg > .form-select,
.input-group-sm > .form-select {
	padding-right: 3rem;
}

.input-group:not(.has-validation) > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu),
.input-group:not(.has-validation) > .dropdown-toggle:nth-last-child(n+3) {
	-webkit-border-top-right-radius: 0;
	border-top-right-radius: 0;
	-webkit-border-bottom-right-radius: 0;
	border-bottom-right-radius: 0;
}

.input-group.has-validation > :nth-last-child(n+3):not(.dropdown-toggle):not(.dropdown-menu),
.input-group.has-validation > .dropdown-toggle:nth-last-child(n+4) {
	-webkit-border-top-right-radius: 0;
	border-top-right-radius: 0;
	-webkit-border-bottom-right-radius: 0;
	border-bottom-right-radius: 0;
}

.input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
	margin-left: -1px;
	-webkit-border-top-left-radius: 0;
	border-top-left-radius: 0;
	-webkit-border-bottom-left-radius: 0;
	border-bottom-left-radius: 0;
}

.valid-feedback {
	display: none;
	width: 100%;
	margin-top: 0.25rem;
	font-size: 0.875em;
	color: #198754;
}

.valid-tooltip {
	position: absolute;
	top: 100%;
	z-index: 5;
	display: none;
	max-width: 100%;
	padding: 0.25rem 0.5rem;
	margin-top: 0.1rem;
	font-size: 14px;
	color: #fff;
	background-color: rgba(25, 135, 84, 0.9);
	-webkit-border-radius: 0.25rem;
	border-radius: 0.25rem;
}

.was-validated :valid ~ .valid-feedback,
.was-validated :valid ~ .valid-tooltip,
.is-valid ~ .valid-feedback,
.is-valid ~ .valid-tooltip {
	display: block;
}

.was-validated .form-control:valid,
.form-control.is-valid {
	border-color: #198754;
	padding-right: calc(1.5em + 0.75rem);
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
	background-repeat: no-repeat;
	background-position: right calc(0.375em + 0.1875rem) center;
	-webkit-background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
	background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.was-validated .form-control:valid:focus,
.form-control.is-valid:focus {
	border-color: #198754;
	-webkit-box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25);
	box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25);
}

.was-validated textarea.form-control:valid,
textarea.form-control.is-valid {
	padding-right: calc(1.5em + 0.75rem);
	background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);
}

.was-validated .form-select:valid,
.form-select.is-valid {
	border-color: #198754;
}

.was-validated .form-select:valid:not([multiple]):not([size]),
.was-validated .form-select:valid:not([multiple])[size="1"],
.form-select.is-valid:not([multiple]):not([size]),
.form-select.is-valid:not([multiple])[size="1"] {
	padding-right: 4.125rem;
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
	background-position: right 0.75rem center, center right 2.25rem;
	-webkit-background-size: 16px 12px, calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
	background-size: 16px 12px, calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.was-validated .form-select:valid:focus,
.form-select.is-valid:focus {
	border-color: #198754;
	-webkit-box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25);
	box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25);
}

.was-validated .form-check-input:valid,
.form-check-input.is-valid {
	border-color: #198754;
}

.was-validated .form-check-input:valid:checked,
.form-check-input.is-valid:checked {
	background-color: #198754;
}

.was-validated .form-check-input:valid:focus,
.form-check-input.is-valid:focus {
	-webkit-box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25);
	box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25);
}

.was-validated .form-check-input:valid ~ .form-check-label,
.form-check-input.is-valid ~ .form-check-label {
	color: #198754;
}

.form-check-inline .form-check-input ~ .valid-feedback {
	margin-left: 0.5em;
}

.was-validated .input-group .form-control:valid,
.input-group .form-control.is-valid,
.was-validated .input-group .form-select:valid,
.input-group .form-select.is-valid {
	z-index: 1;
}

.was-validated .input-group .form-control:valid:focus,
.input-group .form-control.is-valid:focus,
.was-validated .input-group .form-select:valid:focus,
.input-group .form-select.is-valid:focus {
	z-index: 3;
}

.invalid-feedback {
	display: none;
	width: 100%;
	margin-top: 0.25rem;
	font-size: 0.875em;
	color: #dc3545;
}

.invalid-tooltip {
	position: absolute;
	top: 100%;
	z-index: 5;
	display: none;
	max-width: 100%;
	padding: 0.25rem 0.5rem;
	margin-top: 0.1rem;
	font-size: 14px;
	color: #fff;
	background-color: rgba(220, 53, 69, 0.9);
	-webkit-border-radius: 0.25rem;
	border-radius: 0.25rem;
}

.was-validated :invalid ~ .invalid-feedback,
.was-validated :invalid ~ .invalid-tooltip,
.is-invalid ~ .invalid-feedback,
.is-invalid ~ .invalid-tooltip {
	display: block;
}

.was-validated .form-control:invalid,
.form-control.is-invalid {
	border-color: #dc3545;
	padding-right: calc(1.5em + 0.75rem);
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
	background-repeat: no-repeat;
	background-position: right calc(0.375em + 0.1875rem) center;
	-webkit-background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
	background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.was-validated .form-control:invalid:focus,
.form-control.is-invalid:focus {
	border-color: #dc3545;
	-webkit-box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
	box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
}

.was-validated textarea.form-control:invalid,
textarea.form-control.is-invalid {
	padding-right: calc(1.5em + 0.75rem);
	background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);
}

.was-validated .form-select:invalid,
.form-select.is-invalid {
	border-color: #dc3545;
}

.was-validated .form-select:invalid:not([multiple]):not([size]),
.was-validated .form-select:invalid:not([multiple])[size="1"],
.form-select.is-invalid:not([multiple]):not([size]),
.form-select.is-invalid:not([multiple])[size="1"] {
	padding-right: 4.125rem;
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
	background-position: right 0.75rem center, center right 2.25rem;
	-webkit-background-size: 16px 12px, calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
	background-size: 16px 12px, calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.was-validated .form-select:invalid:focus,
.form-select.is-invalid:focus {
	border-color: #dc3545;
	-webkit-box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
	box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
}

.was-validated .form-check-input:invalid,
.form-check-input.is-invalid {
	border-color: #dc3545;
}

.was-validated .form-check-input:invalid:checked,
.form-check-input.is-invalid:checked {
	background-color: #dc3545;
}

.was-validated .form-check-input:invalid:focus,
.form-check-input.is-invalid:focus {
	-webkit-box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
	box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
}

.was-validated .form-check-input:invalid ~ .form-check-label,
.form-check-input.is-invalid ~ .form-check-label {
	color: #dc3545;
}

.form-check-inline .form-check-input ~ .invalid-feedback {
	margin-left: 0.5em;
}

.was-validated .input-group .form-control:invalid,
.input-group .form-control.is-invalid,
.was-validated .input-group .form-select:invalid,
.input-group .form-select.is-invalid {
	z-index: 2;
}

.was-validated .input-group .form-control:invalid:focus,
.input-group .form-control.is-invalid:focus,
.was-validated .input-group .form-select:invalid:focus,
.input-group .form-select.is-invalid:focus {
	z-index: 3;
}

.btn {
	display: inline-block;
	-ms-flex-item-align: center;
	align-self: center;
	font-weight: 500;
	line-height: 2.65rem;
	color: var(--body-color);
	text-align: center;
	vertical-align: middle;
	cursor: pointer;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	background-color: transparent;
	border: 1px solid transparent;
	padding: 0 20px;
	font-size: 14px;
	-webkit-border-radius: 5px;
	border-radius: 5px;
	-webkit-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
	transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
	-o-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
	transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
	transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
}

.btn:hover {
	color: var(--body-color);
}

.btn-check:focus + .btn,
.btn:focus {
	outline: 0;
	-webkit-box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
	box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.btn-check:checked + .btn,
.btn-check:active + .btn,
.btn:active,
.btn.active {
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}

.btn-check:checked + .btn:focus,
.btn-check:active + .btn:focus,
.btn:active:focus,
.btn.active:focus {
	-webkit-box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25), inset 0 3px 5px rgba(0, 0, 0, 0.125);
	box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25), inset 0 3px 5px rgba(0, 0, 0, 0.125);
}

.btn:disabled,
.btn.disabled,
fieldset:disabled .btn {
	pointer-events: none;
	opacity: 0.65;
	-webkit-box-shadow: none;
	box-shadow: none;
}

.btn-primary {
	color: #fff;
	background-color: #8231D3;
	border-color: #8231D3;
	-webkit-box-shadow: none;
	box-shadow: none;
}

.btn-primary:hover {
	background-color: #6f2ab3;
	border-color: #6827a9;
}

.btn-check:focus + .btn-primary,
.btn-primary:focus {
	color: #fff;
	background-color: #6f2ab3;
	border-color: #6827a9;
	-webkit-box-shadow: none, 0 0 0 0.25rem rgba(149, 80, 218, 0.5);
	box-shadow: none, 0 0 0 0.25rem rgba(149, 80, 218, 0.5);
}

.btn-check:checked + .btn-primary,
.btn-check:active + .btn-primary,
.btn-primary:active,
.btn-primary.active,
.show > .btn-primary.dropdown-toggle {
	color: #fff;
	background-color: #6827a9;
	border-color: #62259e;
}

.btn-check:checked + .btn-primary:focus,
.btn-check:active + .btn-primary:focus,
.btn-primary:active:focus,
.btn-primary.active:focus,
.show > .btn-primary.dropdown-toggle:focus {
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(149, 80, 218, 0.5);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(149, 80, 218, 0.5);
}

.btn-primary:disabled,
.btn-primary.disabled {
	color: #fff;
	background-color: #8231D3;
	border-color: #8231D3;
}

.btn-secondary {
	color: #fff;
	background-color: #5840FF;
	border-color: #5840FF;
	-webkit-box-shadow: none;
	box-shadow: none;
}

.btn-secondary:hover {
	background-color: #4b36d9;
	border-color: #4633cc;
}

.btn-check:focus + .btn-secondary,
.btn-secondary:focus {
	color: #fff;
	background-color: #4b36d9;
	border-color: #4633cc;
	-webkit-box-shadow: none, 0 0 0 0.25rem rgba(113, 93, 255, 0.5);
	box-shadow: none, 0 0 0 0.25rem rgba(113, 93, 255, 0.5);
}

.btn-check:checked + .btn-secondary,
.btn-check:active + .btn-secondary,
.btn-secondary:active,
.btn-secondary.active,
.show > .btn-secondary.dropdown-toggle {
	color: #fff;
	background-color: #4633cc;
	border-color: #4230bf;
}

.btn-check:checked + .btn-secondary:focus,
.btn-check:active + .btn-secondary:focus,
.btn-secondary:active:focus,
.btn-secondary.active:focus,
.show > .btn-secondary.dropdown-toggle:focus {
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(113, 93, 255, 0.5);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(113, 93, 255, 0.5);
}

.btn-secondary:disabled,
.btn-secondary.disabled {
	color: #fff;
	background-color: #5840FF;
	border-color: #5840FF;
}

.btn-success {
	color: #000;
	background-color: #01B81A;
	border-color: #01B81A;
	-webkit-box-shadow: none;
	box-shadow: none;
}

.btn-success:hover {
	background-color: #27c33c;
	border-color: #1abf31;
}

.btn-check:focus + .btn-success,
.btn-success:focus {
	color: #000;
	background-color: #27c33c;
	border-color: #1abf31;
	-webkit-box-shadow: none, 0 0 0 0.25rem rgba(1, 156, 22, 0.5);
	box-shadow: none, 0 0 0 0.25rem rgba(1, 156, 22, 0.5);
}

.btn-check:checked + .btn-success,
.btn-check:active + .btn-success,
.btn-success:active,
.btn-success.active,
.show > .btn-success.dropdown-toggle {
	color: #000;
	background-color: #34c648;
	border-color: #1abf31;
}

.btn-check:checked + .btn-success:focus,
.btn-check:active + .btn-success:focus,
.btn-success:active:focus,
.btn-success.active:focus,
.show > .btn-success.dropdown-toggle:focus {
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(1, 156, 22, 0.5);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(1, 156, 22, 0.5);
}

.btn-success:disabled,
.btn-success.disabled {
	color: #000;
	background-color: #01B81A;
	border-color: #01B81A;
}

.btn-info {
	color: #000;
	background-color: #00AAFF;
	border-color: #00AAFF;
	-webkit-box-shadow: none;
	box-shadow: none;
}

.btn-info:hover {
	background-color: #26b7ff;
	border-color: #1ab3ff;
}

.btn-check:focus + .btn-info,
.btn-info:focus {
	color: #000;
	background-color: #26b7ff;
	border-color: #1ab3ff;
	-webkit-box-shadow: none, 0 0 0 0.25rem rgba(0, 145, 217, 0.5);
	box-shadow: none, 0 0 0 0.25rem rgba(0, 145, 217, 0.5);
}

.btn-check:checked + .btn-info,
.btn-check:active + .btn-info,
.btn-info:active,
.btn-info.active,
.show > .btn-info.dropdown-toggle {
	color: #000;
	background-color: #33bbff;
	border-color: #1ab3ff;
}

.btn-check:checked + .btn-info:focus,
.btn-check:active + .btn-info:focus,
.btn-info:active:focus,
.btn-info.active:focus,
.show > .btn-info.dropdown-toggle:focus {
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(0, 145, 217, 0.5);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(0, 145, 217, 0.5);
}

.btn-info:disabled,
.btn-info.disabled {
	color: #000;
	background-color: #00AAFF;
	border-color: #00AAFF;
}

.btn-infos {
	color: #000;
	background-color: #00E4EC;
	border-color: #00E4EC;
	-webkit-box-shadow: none;
	box-shadow: none;
}

.btn-infos:hover {
	background-color: #26e8ef;
	border-color: #1ae7ee;
}

.btn-check:focus + .btn-infos,
.btn-infos:focus {
	color: #000;
	background-color: #26e8ef;
	border-color: #1ae7ee;
	-webkit-box-shadow: none, 0 0 0 0.25rem rgba(0, 194, 201, 0.5);
	box-shadow: none, 0 0 0 0.25rem rgba(0, 194, 201, 0.5);
}

.btn-check:checked + .btn-infos,
.btn-check:active + .btn-infos,
.btn-infos:active,
.btn-infos.active,
.show > .btn-infos.dropdown-toggle {
	color: #000;
	background-color: #33e9f0;
	border-color: #1ae7ee;
}

.btn-check:checked + .btn-infos:focus,
.btn-check:active + .btn-infos:focus,
.btn-infos:active:focus,
.btn-infos.active:focus,
.show > .btn-infos.dropdown-toggle:focus {
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(0, 194, 201, 0.5);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(0, 194, 201, 0.5);
}

.btn-infos:disabled,
.btn-infos.disabled {
	color: #000;
	background-color: #00E4EC;
	border-color: #00E4EC;
}

.btn-warning {
	color: #000;
	background-color: #FA8B0C;
	border-color: #FA8B0C;
	-webkit-box-shadow: none;
	box-shadow: none;
}

.btn-warning:hover {
	background-color: #fb9c30;
	border-color: #fb9724;
}

.btn-check:focus + .btn-warning,
.btn-warning:focus {
	color: #000;
	background-color: #fb9c30;
	border-color: #fb9724;
	-webkit-box-shadow: none, 0 0 0 0.25rem rgba(213, 118, 10, 0.5);
	box-shadow: none, 0 0 0 0.25rem rgba(213, 118, 10, 0.5);
}

.btn-check:checked + .btn-warning,
.btn-check:active + .btn-warning,
.btn-warning:active,
.btn-warning.active,
.show > .btn-warning.dropdown-toggle {
	color: #000;
	background-color: #fba23d;
	border-color: #fb9724;
}

.btn-check:checked + .btn-warning:focus,
.btn-check:active + .btn-warning:focus,
.btn-warning:active:focus,
.btn-warning.active:focus,
.show > .btn-warning.dropdown-toggle:focus {
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(213, 118, 10, 0.5);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(213, 118, 10, 0.5);
}

.btn-warning:disabled,
.btn-warning.disabled {
	color: #000;
	background-color: #FA8B0C;
	border-color: #FA8B0C;
}

.btn-warnings {
	color: #000;
	background-color: #FFBB00;
	border-color: #FFBB00;
	-webkit-box-shadow: none;
	box-shadow: none;
}

.btn-warnings:hover {
	background-color: #ffc526;
	border-color: #ffc21a;
}

.btn-check:focus + .btn-warnings,
.btn-warnings:focus {
	color: #000;
	background-color: #ffc526;
	border-color: #ffc21a;
	-webkit-box-shadow: none, 0 0 0 0.25rem rgba(217, 159, 0, 0.5);
	box-shadow: none, 0 0 0 0.25rem rgba(217, 159, 0, 0.5);
}

.btn-check:checked + .btn-warnings,
.btn-check:active + .btn-warnings,
.btn-warnings:active,
.btn-warnings.active,
.show > .btn-warnings.dropdown-toggle {
	color: #000;
	background-color: #ffc933;
	border-color: #ffc21a;
}

.btn-check:checked + .btn-warnings:focus,
.btn-check:active + .btn-warnings:focus,
.btn-warnings:active:focus,
.btn-warnings.active:focus,
.show > .btn-warnings.dropdown-toggle:focus {
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(217, 159, 0, 0.5);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(217, 159, 0, 0.5);
}

.btn-warnings:disabled,
.btn-warnings.disabled {
	color: #000;
	background-color: #FFBB00;
	border-color: #FFBB00;
}

.btn-danger {
	color: #000;
	background-color: #FF0F0F;
	border-color: #FF0F0F;
	-webkit-box-shadow: none;
	box-shadow: none;
}

.btn-danger:hover {
	background-color: #ff3333;
	border-color: #ff2727;
}

.btn-check:focus + .btn-danger,
.btn-danger:focus {
	color: #000;
	background-color: #ff3333;
	border-color: #ff2727;
	-webkit-box-shadow: none, 0 0 0 0.25rem rgba(217, 13, 13, 0.5);
	box-shadow: none, 0 0 0 0.25rem rgba(217, 13, 13, 0.5);
}

.btn-check:checked + .btn-danger,
.btn-check:active + .btn-danger,
.btn-danger:active,
.btn-danger.active,
.show > .btn-danger.dropdown-toggle {
	color: #000;
	background-color: #ff3f3f;
	border-color: #ff2727;
}

.btn-check:checked + .btn-danger:focus,
.btn-check:active + .btn-danger:focus,
.btn-danger:active:focus,
.btn-danger.active:focus,
.show > .btn-danger.dropdown-toggle:focus {
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(217, 13, 13, 0.5);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(217, 13, 13, 0.5);
}

.btn-danger:disabled,
.btn-danger.disabled {
	color: #000;
	background-color: #FF0F0F;
	border-color: #FF0F0F;
}

.btn-dangers {
	color: #000;
	background-color: #FF0F0F;
	border-color: #FF0F0F;
	-webkit-box-shadow: none;
	box-shadow: none;
}

.btn-dangers:hover {
	background-color: #ff3333;
	border-color: #ff2727;
}

.btn-check:focus + .btn-dangers,
.btn-dangers:focus {
	color: #000;
	background-color: #ff3333;
	border-color: #ff2727;
	-webkit-box-shadow: none, 0 0 0 0.25rem rgba(217, 13, 13, 0.5);
	box-shadow: none, 0 0 0 0.25rem rgba(217, 13, 13, 0.5);
}

.btn-check:checked + .btn-dangers,
.btn-check:active + .btn-dangers,
.btn-dangers:active,
.btn-dangers.active,
.show > .btn-dangers.dropdown-toggle {
	color: #000;
	background-color: #ff3f3f;
	border-color: #ff2727;
}

.btn-check:checked + .btn-dangers:focus,
.btn-check:active + .btn-dangers:focus,
.btn-dangers:active:focus,
.btn-dangers.active:focus,
.show > .btn-dangers.dropdown-toggle:focus {
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(217, 13, 13, 0.5);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(217, 13, 13, 0.5);
}

.btn-dangers:disabled,
.btn-dangers.disabled {
	color: #000;
	background-color: #FF0F0F;
	border-color: #FF0F0F;
}

.btn-purple {
	color: #fff;
	background-color: #A722F6;
	border-color: #A722F6;
	-webkit-box-shadow: none;
	box-shadow: none;
}

.btn-purple:hover {
	background-color: #8e1dd1;
	border-color: #861bc5;
}

.btn-check:focus + .btn-purple,
.btn-purple:focus {
	color: #fff;
	background-color: #8e1dd1;
	border-color: #861bc5;
	-webkit-box-shadow: none, 0 0 0 0.25rem rgba(180, 67, 247, 0.5);
	box-shadow: none, 0 0 0 0.25rem rgba(180, 67, 247, 0.5);
}

.btn-check:checked + .btn-purple,
.btn-check:active + .btn-purple,
.btn-purple:active,
.btn-purple.active,
.show > .btn-purple.dropdown-toggle {
	color: #fff;
	background-color: #861bc5;
	border-color: #7d1ab9;
}

.btn-check:checked + .btn-purple:focus,
.btn-check:active + .btn-purple:focus,
.btn-purple:active:focus,
.btn-purple.active:focus,
.show > .btn-purple.dropdown-toggle:focus {
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(180, 67, 247, 0.5);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(180, 67, 247, 0.5);
}

.btn-purple:disabled,
.btn-purple.disabled {
	color: #fff;
	background-color: #A722F6;
	border-color: #A722F6;
}

.btn-dark {
	color: #fff;
	background-color: #0A0A0A;
	border-color: #0A0A0A;
	-webkit-box-shadow: none;
	box-shadow: none;
}

.btn-dark:hover {
	background-color: #090909;
	border-color: #080808;
}

.btn-check:focus + .btn-dark,
.btn-dark:focus {
	color: #fff;
	background-color: #090909;
	border-color: #080808;
	-webkit-box-shadow: none, 0 0 0 0.25rem rgba(47, 47, 47, 0.5);
	box-shadow: none, 0 0 0 0.25rem rgba(47, 47, 47, 0.5);
}

.btn-check:checked + .btn-dark,
.btn-check:active + .btn-dark,
.btn-dark:active,
.btn-dark.active,
.show > .btn-dark.dropdown-toggle {
	color: #fff;
	background-color: #080808;
	border-color: #080808;
}

.btn-check:checked + .btn-dark:focus,
.btn-check:active + .btn-dark:focus,
.btn-dark:active:focus,
.btn-dark.active:focus,
.show > .btn-dark.dropdown-toggle:focus {
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(47, 47, 47, 0.5);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(47, 47, 47, 0.5);
}

.btn-dark:disabled,
.btn-dark.disabled {
	color: #fff;
	background-color: #0A0A0A;
	border-color: #0A0A0A;
}

.btn-light {
	color: #000;
	background-color: #8C90A4;
	border-color: #8C90A4;
	-webkit-box-shadow: none;
	box-shadow: none;
}

.btn-light:hover {
	background-color: #9da1b2;
	border-color: #989bad;
}

.btn-check:focus + .btn-light,
.btn-light:focus {
	color: #000;
	background-color: #9da1b2;
	border-color: #989bad;
	-webkit-box-shadow: none, 0 0 0 0.25rem rgba(119, 122, 139, 0.5);
	box-shadow: none, 0 0 0 0.25rem rgba(119, 122, 139, 0.5);
}

.btn-check:checked + .btn-light,
.btn-check:active + .btn-light,
.btn-light:active,
.btn-light.active,
.show > .btn-light.dropdown-toggle {
	color: #000;
	background-color: #a3a6b6;
	border-color: #989bad;
}

.btn-check:checked + .btn-light:focus,
.btn-check:active + .btn-light:focus,
.btn-light:active:focus,
.btn-light.active:focus,
.show > .btn-light.dropdown-toggle:focus {
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(119, 122, 139, 0.5);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(119, 122, 139, 0.5);
}

.btn-light:disabled,
.btn-light.disabled {
	color: #000;
	background-color: #8C90A4;
	border-color: #8C90A4;
}

.btn-lighten {
	color: #000;
	background-color: #A0A0A0;
	border-color: #A0A0A0;
	-webkit-box-shadow: none;
	box-shadow: none;
}

.btn-lighten:hover {
	background-color: #aeaeae;
	border-color: #aaaaaa;
}

.btn-check:focus + .btn-lighten,
.btn-lighten:focus {
	color: #000;
	background-color: #aeaeae;
	border-color: #aaaaaa;
	-webkit-box-shadow: none, 0 0 0 0.25rem rgba(136, 136, 136, 0.5);
	box-shadow: none, 0 0 0 0.25rem rgba(136, 136, 136, 0.5);
}

.btn-check:checked + .btn-lighten,
.btn-check:active + .btn-lighten,
.btn-lighten:active,
.btn-lighten.active,
.show > .btn-lighten.dropdown-toggle {
	color: #000;
	background-color: #b3b3b3;
	border-color: #aaaaaa;
}

.btn-check:checked + .btn-lighten:focus,
.btn-check:active + .btn-lighten:focus,
.btn-lighten:active:focus,
.btn-lighten.active:focus,
.show > .btn-lighten.dropdown-toggle:focus {
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(136, 136, 136, 0.5);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(136, 136, 136, 0.5);
}

.btn-lighten:disabled,
.btn-lighten.disabled {
	color: #000;
	background-color: #A0A0A0;
	border-color: #A0A0A0;
}

.btn-light-gray {
	color: #000;
	background-color: #8C90A4;
	border-color: #8C90A4;
	-webkit-box-shadow: none;
	box-shadow: none;
}

.btn-light-gray:hover {
	background-color: #9da1b2;
	border-color: #989bad;
}

.btn-check:focus + .btn-light-gray,
.btn-light-gray:focus {
	color: #000;
	background-color: #9da1b2;
	border-color: #989bad;
	-webkit-box-shadow: none, 0 0 0 0.25rem rgba(119, 122, 139, 0.5);
	box-shadow: none, 0 0 0 0.25rem rgba(119, 122, 139, 0.5);
}

.btn-check:checked + .btn-light-gray,
.btn-check:active + .btn-light-gray,
.btn-light-gray:active,
.btn-light-gray.active,
.show > .btn-light-gray.dropdown-toggle {
	color: #000;
	background-color: #a3a6b6;
	border-color: #989bad;
}

.btn-check:checked + .btn-light-gray:focus,
.btn-check:active + .btn-light-gray:focus,
.btn-light-gray:active:focus,
.btn-light-gray.active:focus,
.show > .btn-light-gray.dropdown-toggle:focus {
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(119, 122, 139, 0.5);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(119, 122, 139, 0.5);
}

.btn-light-gray:disabled,
.btn-light-gray.disabled {
	color: #000;
	background-color: #8C90A4;
	border-color: #8C90A4;
}

.btn-text {
	color: #fff;
	background-color: #666d92;
	border-color: #666d92;
	-webkit-box-shadow: none;
	box-shadow: none;
}

.btn-text:hover {
	background-color: #575d7c;
	border-color: #525775;
}

.btn-check:focus + .btn-text,
.btn-text:focus {
	color: #fff;
	background-color: #575d7c;
	border-color: #525775;
	-webkit-box-shadow: none, 0 0 0 0.25rem rgba(125, 131, 162, 0.5);
	box-shadow: none, 0 0 0 0.25rem rgba(125, 131, 162, 0.5);
}

.btn-check:checked + .btn-text,
.btn-check:active + .btn-text,
.btn-text:active,
.btn-text.active,
.show > .btn-text.dropdown-toggle {
	color: #fff;
	background-color: #525775;
	border-color: #4d526e;
}

.btn-check:checked + .btn-text:focus,
.btn-check:active + .btn-text:focus,
.btn-text:active:focus,
.btn-text.active:focus,
.show > .btn-text.dropdown-toggle:focus {
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(125, 131, 162, 0.5);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(125, 131, 162, 0.5);
}

.btn-text:disabled,
.btn-text.disabled {
	color: #fff;
	background-color: #666d92;
	border-color: #666d92;
}

.btn-gray {
	color: #fff;
	background-color: #404040;
	border-color: #404040;
	-webkit-box-shadow: none;
	box-shadow: none;
}

.btn-gray:hover {
	background-color: #363636;
	border-color: #333333;
}

.btn-check:focus + .btn-gray,
.btn-gray:focus {
	color: #fff;
	background-color: #363636;
	border-color: #333333;
	-webkit-box-shadow: none, 0 0 0 0.25rem rgba(93, 93, 93, 0.5);
	box-shadow: none, 0 0 0 0.25rem rgba(93, 93, 93, 0.5);
}

.btn-check:checked + .btn-gray,
.btn-check:active + .btn-gray,
.btn-gray:active,
.btn-gray.active,
.show > .btn-gray.dropdown-toggle {
	color: #fff;
	background-color: #333333;
	border-color: #303030;
}

.btn-check:checked + .btn-gray:focus,
.btn-check:active + .btn-gray:focus,
.btn-gray:active:focus,
.btn-gray.active:focus,
.show > .btn-gray.dropdown-toggle:focus {
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(93, 93, 93, 0.5);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(93, 93, 93, 0.5);
}

.btn-gray:disabled,
.btn-gray.disabled {
	color: #fff;
	background-color: #404040;
	border-color: #404040;
}

.btn-third {
	color: #fff;
	background-color: #8231D3;
	border-color: #8231D3;
	-webkit-box-shadow: none;
	box-shadow: none;
}

.btn-third:hover {
	background-color: #6f2ab3;
	border-color: #6827a9;
}

.btn-check:focus + .btn-third,
.btn-third:focus {
	color: #fff;
	background-color: #6f2ab3;
	border-color: #6827a9;
	-webkit-box-shadow: none, 0 0 0 0.25rem rgba(149, 80, 218, 0.5);
	box-shadow: none, 0 0 0 0.25rem rgba(149, 80, 218, 0.5);
}

.btn-check:checked + .btn-third,
.btn-check:active + .btn-third,
.btn-third:active,
.btn-third.active,
.show > .btn-third.dropdown-toggle {
	color: #fff;
	background-color: #6827a9;
	border-color: #62259e;
}

.btn-check:checked + .btn-third:focus,
.btn-check:active + .btn-third:focus,
.btn-third:active:focus,
.btn-third.active:focus,
.show > .btn-third.dropdown-toggle:focus {
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(149, 80, 218, 0.5);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(149, 80, 218, 0.5);
}

.btn-third:disabled,
.btn-third.disabled {
	color: #fff;
	background-color: #8231D3;
	border-color: #8231D3;
}

.btn-white {
	color: #000;
	background-color: #ffffff;
	border-color: #ffffff;
	-webkit-box-shadow: none;
	box-shadow: none;
}

.btn-white:hover {
	background-color: white;
	border-color: white;
}

.btn-check:focus + .btn-white,
.btn-white:focus {
	color: #000;
	background-color: white;
	border-color: white;
	-webkit-box-shadow: none, 0 0 0 0.25rem rgba(217, 217, 217, 0.5);
	box-shadow: none, 0 0 0 0.25rem rgba(217, 217, 217, 0.5);
}

.btn-check:checked + .btn-white,
.btn-check:active + .btn-white,
.btn-white:active,
.btn-white.active,
.show > .btn-white.dropdown-toggle {
	color: #000;
	background-color: white;
	border-color: white;
}

.btn-check:checked + .btn-white:focus,
.btn-check:active + .btn-white:focus,
.btn-white:active:focus,
.btn-white.active:focus,
.show > .btn-white.dropdown-toggle:focus {
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(217, 217, 217, 0.5);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(217, 217, 217, 0.5);
}

.btn-white:disabled,
.btn-white.disabled {
	color: #000;
	background-color: #ffffff;
	border-color: #ffffff;
}

.btn-outline-primary {
	color: #8231D3;
	border-color: #8231D3;
}

.btn-outline-primary:hover {
	color: #fff;
	background-color: #8231D3;
	border-color: #8231D3;
}

.btn-check:focus + .btn-outline-primary,
.btn-outline-primary:focus {
	-webkit-box-shadow: 0 0 0 0.25rem rgba(130, 49, 211, 0.5);
	box-shadow: 0 0 0 0.25rem rgba(130, 49, 211, 0.5);
}

.btn-check:checked + .btn-outline-primary,
.btn-check:active + .btn-outline-primary,
.btn-outline-primary:active,
.btn-outline-primary.active,
.btn-outline-primary.dropdown-toggle.show {
	color: #fff;
	background-color: #8231D3;
	border-color: #8231D3;
}

.btn-check:checked + .btn-outline-primary:focus,
.btn-check:active + .btn-outline-primary:focus,
.btn-outline-primary:active:focus,
.btn-outline-primary.active:focus,
.btn-outline-primary.dropdown-toggle.show:focus {
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(130, 49, 211, 0.5);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(130, 49, 211, 0.5);
}

.btn-outline-primary:disabled,
.btn-outline-primary.disabled {
	color: #8231D3;
	background-color: transparent;
}

.btn-outline-secondary {
	color: #5840FF;
	border-color: #5840FF;
}

.btn-outline-secondary:hover {
	color: #fff;
	background-color: #5840FF;
	border-color: #5840FF;
}

.btn-check:focus + .btn-outline-secondary,
.btn-outline-secondary:focus {
	-webkit-box-shadow: 0 0 0 0.25rem rgba(88, 64, 255, 0.5);
	box-shadow: 0 0 0 0.25rem rgba(88, 64, 255, 0.5);
}

.btn-check:checked + .btn-outline-secondary,
.btn-check:active + .btn-outline-secondary,
.btn-outline-secondary:active,
.btn-outline-secondary.active,
.btn-outline-secondary.dropdown-toggle.show {
	color: #fff;
	background-color: #5840FF;
	border-color: #5840FF;
}

.btn-check:checked + .btn-outline-secondary:focus,
.btn-check:active + .btn-outline-secondary:focus,
.btn-outline-secondary:active:focus,
.btn-outline-secondary.active:focus,
.btn-outline-secondary.dropdown-toggle.show:focus {
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(88, 64, 255, 0.5);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(88, 64, 255, 0.5);
}

.btn-outline-secondary:disabled,
.btn-outline-secondary.disabled {
	color: #5840FF;
	background-color: transparent;
}

.btn-outline-success {
	color: #01B81A;
	border-color: #01B81A;
}

.btn-outline-success:hover {
	color: #000;
	background-color: #01B81A;
	border-color: #01B81A;
}

.btn-check:focus + .btn-outline-success,
.btn-outline-success:focus {
	-webkit-box-shadow: 0 0 0 0.25rem rgba(1, 184, 26, 0.5);
	box-shadow: 0 0 0 0.25rem rgba(1, 184, 26, 0.5);
}

.btn-check:checked + .btn-outline-success,
.btn-check:active + .btn-outline-success,
.btn-outline-success:active,
.btn-outline-success.active,
.btn-outline-success.dropdown-toggle.show {
	color: #000;
	background-color: #01B81A;
	border-color: #01B81A;
}

.btn-check:checked + .btn-outline-success:focus,
.btn-check:active + .btn-outline-success:focus,
.btn-outline-success:active:focus,
.btn-outline-success.active:focus,
.btn-outline-success.dropdown-toggle.show:focus {
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(1, 184, 26, 0.5);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(1, 184, 26, 0.5);
}

.btn-outline-success:disabled,
.btn-outline-success.disabled {
	color: #01B81A;
	background-color: transparent;
}

.btn-outline-info {
	color: #00AAFF;
	border-color: #00AAFF;
}

.btn-outline-info:hover {
	color: #000;
	background-color: #00AAFF;
	border-color: #00AAFF;
}

.btn-check:focus + .btn-outline-info,
.btn-outline-info:focus {
	-webkit-box-shadow: 0 0 0 0.25rem rgba(0, 170, 255, 0.5);
	box-shadow: 0 0 0 0.25rem rgba(0, 170, 255, 0.5);
}

.btn-check:checked + .btn-outline-info,
.btn-check:active + .btn-outline-info,
.btn-outline-info:active,
.btn-outline-info.active,
.btn-outline-info.dropdown-toggle.show {
	color: #000;
	background-color: #00AAFF;
	border-color: #00AAFF;
}

.btn-check:checked + .btn-outline-info:focus,
.btn-check:active + .btn-outline-info:focus,
.btn-outline-info:active:focus,
.btn-outline-info.active:focus,
.btn-outline-info.dropdown-toggle.show:focus {
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(0, 170, 255, 0.5);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(0, 170, 255, 0.5);
}

.btn-outline-info:disabled,
.btn-outline-info.disabled {
	color: #00AAFF;
	background-color: transparent;
}

.btn-outline-infos {
	color: #00E4EC;
	border-color: #00E4EC;
}

.btn-outline-infos:hover {
	color: #000;
	background-color: #00E4EC;
	border-color: #00E4EC;
}

.btn-check:focus + .btn-outline-infos,
.btn-outline-infos:focus {
	-webkit-box-shadow: 0 0 0 0.25rem rgba(0, 228, 236, 0.5);
	box-shadow: 0 0 0 0.25rem rgba(0, 228, 236, 0.5);
}

.btn-check:checked + .btn-outline-infos,
.btn-check:active + .btn-outline-infos,
.btn-outline-infos:active,
.btn-outline-infos.active,
.btn-outline-infos.dropdown-toggle.show {
	color: #000;
	background-color: #00E4EC;
	border-color: #00E4EC;
}

.btn-check:checked + .btn-outline-infos:focus,
.btn-check:active + .btn-outline-infos:focus,
.btn-outline-infos:active:focus,
.btn-outline-infos.active:focus,
.btn-outline-infos.dropdown-toggle.show:focus {
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(0, 228, 236, 0.5);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(0, 228, 236, 0.5);
}

.btn-outline-infos:disabled,
.btn-outline-infos.disabled {
	color: #00E4EC;
	background-color: transparent;
}

.btn-outline-warning {
	color: #FA8B0C;
	border-color: #FA8B0C;
}

.btn-outline-warning:hover {
	color: #000;
	background-color: #FA8B0C;
	border-color: #FA8B0C;
}

.btn-check:focus + .btn-outline-warning,
.btn-outline-warning:focus {
	-webkit-box-shadow: 0 0 0 0.25rem rgba(250, 139, 12, 0.5);
	box-shadow: 0 0 0 0.25rem rgba(250, 139, 12, 0.5);
}

.btn-check:checked + .btn-outline-warning,
.btn-check:active + .btn-outline-warning,
.btn-outline-warning:active,
.btn-outline-warning.active,
.btn-outline-warning.dropdown-toggle.show {
	color: #000;
	background-color: #FA8B0C;
	border-color: #FA8B0C;
}

.btn-check:checked + .btn-outline-warning:focus,
.btn-check:active + .btn-outline-warning:focus,
.btn-outline-warning:active:focus,
.btn-outline-warning.active:focus,
.btn-outline-warning.dropdown-toggle.show:focus {
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(250, 139, 12, 0.5);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(250, 139, 12, 0.5);
}

.btn-outline-warning:disabled,
.btn-outline-warning.disabled {
	color: #FA8B0C;
	background-color: transparent;
}

.btn-outline-warnings {
	color: #FFBB00;
	border-color: #FFBB00;
}

.btn-outline-warnings:hover {
	color: #000;
	background-color: #FFBB00;
	border-color: #FFBB00;
}

.btn-check:focus + .btn-outline-warnings,
.btn-outline-warnings:focus {
	-webkit-box-shadow: 0 0 0 0.25rem rgba(255, 187, 0, 0.5);
	box-shadow: 0 0 0 0.25rem rgba(255, 187, 0, 0.5);
}

.btn-check:checked + .btn-outline-warnings,
.btn-check:active + .btn-outline-warnings,
.btn-outline-warnings:active,
.btn-outline-warnings.active,
.btn-outline-warnings.dropdown-toggle.show {
	color: #000;
	background-color: #FFBB00;
	border-color: #FFBB00;
}

.btn-check:checked + .btn-outline-warnings:focus,
.btn-check:active + .btn-outline-warnings:focus,
.btn-outline-warnings:active:focus,
.btn-outline-warnings.active:focus,
.btn-outline-warnings.dropdown-toggle.show:focus {
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(255, 187, 0, 0.5);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(255, 187, 0, 0.5);
}

.btn-outline-warnings:disabled,
.btn-outline-warnings.disabled {
	color: #FFBB00;
	background-color: transparent;
}

.btn-outline-danger {
	color: #FF0F0F;
	border-color: #FF0F0F;
}

.btn-outline-danger:hover {
	color: #000;
	background-color: #FF0F0F;
	border-color: #FF0F0F;
}

.btn-check:focus + .btn-outline-danger,
.btn-outline-danger:focus {
	-webkit-box-shadow: 0 0 0 0.25rem rgba(255, 15, 15, 0.5);
	box-shadow: 0 0 0 0.25rem rgba(255, 15, 15, 0.5);
}

.btn-check:checked + .btn-outline-danger,
.btn-check:active + .btn-outline-danger,
.btn-outline-danger:active,
.btn-outline-danger.active,
.btn-outline-danger.dropdown-toggle.show {
	color: #000;
	background-color: #FF0F0F;
	border-color: #FF0F0F;
}

.btn-check:checked + .btn-outline-danger:focus,
.btn-check:active + .btn-outline-danger:focus,
.btn-outline-danger:active:focus,
.btn-outline-danger.active:focus,
.btn-outline-danger.dropdown-toggle.show:focus {
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(255, 15, 15, 0.5);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(255, 15, 15, 0.5);
}

.btn-outline-danger:disabled,
.btn-outline-danger.disabled {
	color: #FF0F0F;
	background-color: transparent;
}

.btn-outline-dangers {
	color: #FF0F0F;
	border-color: #FF0F0F;
}

.btn-outline-dangers:hover {
	color: #000;
	background-color: #FF0F0F;
	border-color: #FF0F0F;
}

.btn-check:focus + .btn-outline-dangers,
.btn-outline-dangers:focus {
	-webkit-box-shadow: 0 0 0 0.25rem rgba(255, 15, 15, 0.5);
	box-shadow: 0 0 0 0.25rem rgba(255, 15, 15, 0.5);
}

.btn-check:checked + .btn-outline-dangers,
.btn-check:active + .btn-outline-dangers,
.btn-outline-dangers:active,
.btn-outline-dangers.active,
.btn-outline-dangers.dropdown-toggle.show {
	color: #000;
	background-color: #FF0F0F;
	border-color: #FF0F0F;
}

.btn-check:checked + .btn-outline-dangers:focus,
.btn-check:active + .btn-outline-dangers:focus,
.btn-outline-dangers:active:focus,
.btn-outline-dangers.active:focus,
.btn-outline-dangers.dropdown-toggle.show:focus {
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(255, 15, 15, 0.5);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(255, 15, 15, 0.5);
}

.btn-outline-dangers:disabled,
.btn-outline-dangers.disabled {
	color: #FF0F0F;
	background-color: transparent;
}

.btn-outline-purple {
	color: #A722F6;
	border-color: #A722F6;
}

.btn-outline-purple:hover {
	color: #fff;
	background-color: #A722F6;
	border-color: #A722F6;
}

.btn-check:focus + .btn-outline-purple,
.btn-outline-purple:focus {
	-webkit-box-shadow: 0 0 0 0.25rem rgba(167, 34, 246, 0.5);
	box-shadow: 0 0 0 0.25rem rgba(167, 34, 246, 0.5);
}

.btn-check:checked + .btn-outline-purple,
.btn-check:active + .btn-outline-purple,
.btn-outline-purple:active,
.btn-outline-purple.active,
.btn-outline-purple.dropdown-toggle.show {
	color: #fff;
	background-color: #A722F6;
	border-color: #A722F6;
}

.btn-check:checked + .btn-outline-purple:focus,
.btn-check:active + .btn-outline-purple:focus,
.btn-outline-purple:active:focus,
.btn-outline-purple.active:focus,
.btn-outline-purple.dropdown-toggle.show:focus {
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(167, 34, 246, 0.5);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(167, 34, 246, 0.5);
}

.btn-outline-purple:disabled,
.btn-outline-purple.disabled {
	color: #A722F6;
	background-color: transparent;
}

.btn-outline-dark {
	color: #0A0A0A;
	border-color: #0A0A0A;
}

.btn-outline-dark:hover {
	color: #fff;
	background-color: #0A0A0A;
	border-color: #0A0A0A;
}

.btn-check:focus + .btn-outline-dark,
.btn-outline-dark:focus {
	-webkit-box-shadow: 0 0 0 0.25rem rgba(10, 10, 10, 0.5);
	box-shadow: 0 0 0 0.25rem rgba(10, 10, 10, 0.5);
}

.btn-check:checked + .btn-outline-dark,
.btn-check:active + .btn-outline-dark,
.btn-outline-dark:active,
.btn-outline-dark.active,
.btn-outline-dark.dropdown-toggle.show {
	color: #fff;
	background-color: #0A0A0A;
	border-color: #0A0A0A;
}

.btn-check:checked + .btn-outline-dark:focus,
.btn-check:active + .btn-outline-dark:focus,
.btn-outline-dark:active:focus,
.btn-outline-dark.active:focus,
.btn-outline-dark.dropdown-toggle.show:focus {
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(10, 10, 10, 0.5);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(10, 10, 10, 0.5);
}

.btn-outline-dark:disabled,
.btn-outline-dark.disabled {
	color: #0A0A0A;
	background-color: transparent;
}

.btn-outline-light {
	color: #8C90A4;
	border-color: #8C90A4;
}

.btn-outline-light:hover {
	color: #000;
	background-color: #8C90A4;
	border-color: #8C90A4;
}

.btn-check:focus + .btn-outline-light,
.btn-outline-light:focus {
	-webkit-box-shadow: 0 0 0 0.25rem rgba(140, 144, 164, 0.5);
	box-shadow: 0 0 0 0.25rem rgba(140, 144, 164, 0.5);
}

.btn-check:checked + .btn-outline-light,
.btn-check:active + .btn-outline-light,
.btn-outline-light:active,
.btn-outline-light.active,
.btn-outline-light.dropdown-toggle.show {
	color: #000;
	background-color: #8C90A4;
	border-color: #8C90A4;
}

.btn-check:checked + .btn-outline-light:focus,
.btn-check:active + .btn-outline-light:focus,
.btn-outline-light:active:focus,
.btn-outline-light.active:focus,
.btn-outline-light.dropdown-toggle.show:focus {
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(140, 144, 164, 0.5);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(140, 144, 164, 0.5);
}

.btn-outline-light:disabled,
.btn-outline-light.disabled {
	color: #8C90A4;
	background-color: transparent;
}

.btn-outline-lighten {
	color: #A0A0A0;
	border-color: #A0A0A0;
}

.btn-outline-lighten:hover {
	color: #000;
	background-color: #A0A0A0;
	border-color: #A0A0A0;
}

.btn-check:focus + .btn-outline-lighten,
.btn-outline-lighten:focus {
	-webkit-box-shadow: 0 0 0 0.25rem rgba(160, 160, 160, 0.5);
	box-shadow: 0 0 0 0.25rem rgba(160, 160, 160, 0.5);
}

.btn-check:checked + .btn-outline-lighten,
.btn-check:active + .btn-outline-lighten,
.btn-outline-lighten:active,
.btn-outline-lighten.active,
.btn-outline-lighten.dropdown-toggle.show {
	color: #000;
	background-color: #A0A0A0;
	border-color: #A0A0A0;
}

.btn-check:checked + .btn-outline-lighten:focus,
.btn-check:active + .btn-outline-lighten:focus,
.btn-outline-lighten:active:focus,
.btn-outline-lighten.active:focus,
.btn-outline-lighten.dropdown-toggle.show:focus {
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(160, 160, 160, 0.5);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(160, 160, 160, 0.5);
}

.btn-outline-lighten:disabled,
.btn-outline-lighten.disabled {
	color: #A0A0A0;
	background-color: transparent;
}

.btn-outline-light-gray {
	color: #8C90A4;
	border-color: #8C90A4;
}

.btn-outline-light-gray:hover {
	color: #000;
	background-color: #8C90A4;
	border-color: #8C90A4;
}

.btn-check:focus + .btn-outline-light-gray,
.btn-outline-light-gray:focus {
	-webkit-box-shadow: 0 0 0 0.25rem rgba(140, 144, 164, 0.5);
	box-shadow: 0 0 0 0.25rem rgba(140, 144, 164, 0.5);
}

.btn-check:checked + .btn-outline-light-gray,
.btn-check:active + .btn-outline-light-gray,
.btn-outline-light-gray:active,
.btn-outline-light-gray.active,
.btn-outline-light-gray.dropdown-toggle.show {
	color: #000;
	background-color: #8C90A4;
	border-color: #8C90A4;
}

.btn-check:checked + .btn-outline-light-gray:focus,
.btn-check:active + .btn-outline-light-gray:focus,
.btn-outline-light-gray:active:focus,
.btn-outline-light-gray.active:focus,
.btn-outline-light-gray.dropdown-toggle.show:focus {
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(140, 144, 164, 0.5);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(140, 144, 164, 0.5);
}

.btn-outline-light-gray:disabled,
.btn-outline-light-gray.disabled {
	color: #8C90A4;
	background-color: transparent;
}

.btn-outline-text {
	color: #666d92;
	border-color: #666d92;
}

.btn-outline-text:hover {
	color: #fff;
	background-color: #666d92;
	border-color: #666d92;
}

.btn-check:focus + .btn-outline-text,
.btn-outline-text:focus {
	-webkit-box-shadow: 0 0 0 0.25rem rgba(102, 109, 146, 0.5);
	box-shadow: 0 0 0 0.25rem rgba(102, 109, 146, 0.5);
}

.btn-check:checked + .btn-outline-text,
.btn-check:active + .btn-outline-text,
.btn-outline-text:active,
.btn-outline-text.active,
.btn-outline-text.dropdown-toggle.show {
	color: #fff;
	background-color: #666d92;
	border-color: #666d92;
}

.btn-check:checked + .btn-outline-text:focus,
.btn-check:active + .btn-outline-text:focus,
.btn-outline-text:active:focus,
.btn-outline-text.active:focus,
.btn-outline-text.dropdown-toggle.show:focus {
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(102, 109, 146, 0.5);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(102, 109, 146, 0.5);
}

.btn-outline-text:disabled,
.btn-outline-text.disabled {
	color: #666d92;
	background-color: transparent;
}

.btn-outline-gray {
	color: #404040;
	border-color: #404040;
}

.btn-outline-gray:hover {
	color: #fff;
	background-color: #404040;
	border-color: #404040;
}

.btn-check:focus + .btn-outline-gray,
.btn-outline-gray:focus {
	-webkit-box-shadow: 0 0 0 0.25rem rgba(64, 64, 64, 0.5);
	box-shadow: 0 0 0 0.25rem rgba(64, 64, 64, 0.5);
}

.btn-check:checked + .btn-outline-gray,
.btn-check:active + .btn-outline-gray,
.btn-outline-gray:active,
.btn-outline-gray.active,
.btn-outline-gray.dropdown-toggle.show {
	color: #fff;
	background-color: #404040;
	border-color: #404040;
}

.btn-check:checked + .btn-outline-gray:focus,
.btn-check:active + .btn-outline-gray:focus,
.btn-outline-gray:active:focus,
.btn-outline-gray.active:focus,
.btn-outline-gray.dropdown-toggle.show:focus {
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(64, 64, 64, 0.5);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(64, 64, 64, 0.5);
}

.btn-outline-gray:disabled,
.btn-outline-gray.disabled {
	color: #404040;
	background-color: transparent;
}

.btn-outline-third {
	color: #8231D3;
	border-color: #8231D3;
}

.btn-outline-third:hover {
	color: #fff;
	background-color: #8231D3;
	border-color: #8231D3;
}

.btn-check:focus + .btn-outline-third,
.btn-outline-third:focus {
	-webkit-box-shadow: 0 0 0 0.25rem rgba(130, 49, 211, 0.5);
	box-shadow: 0 0 0 0.25rem rgba(130, 49, 211, 0.5);
}

.btn-check:checked + .btn-outline-third,
.btn-check:active + .btn-outline-third,
.btn-outline-third:active,
.btn-outline-third.active,
.btn-outline-third.dropdown-toggle.show {
	color: #fff;
	background-color: #8231D3;
	border-color: #8231D3;
}

.btn-check:checked + .btn-outline-third:focus,
.btn-check:active + .btn-outline-third:focus,
.btn-outline-third:active:focus,
.btn-outline-third.active:focus,
.btn-outline-third.dropdown-toggle.show:focus {
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(130, 49, 211, 0.5);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(130, 49, 211, 0.5);
}

.btn-outline-third:disabled,
.btn-outline-third.disabled {
	color: #8231D3;
	background-color: transparent;
}

.btn-outline-white {
	color: #ffffff;
	border-color: #ffffff;
}

.btn-outline-white:hover {
	color: #000;
	background-color: #ffffff;
	border-color: #ffffff;
}

.btn-check:focus + .btn-outline-white,
.btn-outline-white:focus {
	-webkit-box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.5);
	box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.5);
}

.btn-check:checked + .btn-outline-white,
.btn-check:active + .btn-outline-white,
.btn-outline-white:active,
.btn-outline-white.active,
.btn-outline-white.dropdown-toggle.show {
	color: #000;
	background-color: #ffffff;
	border-color: #ffffff;
}

.btn-check:checked + .btn-outline-white:focus,
.btn-check:active + .btn-outline-white:focus,
.btn-outline-white:active:focus,
.btn-outline-white.active:focus,
.btn-outline-white.dropdown-toggle.show:focus {
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(255, 255, 255, 0.5);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.25rem rgba(255, 255, 255, 0.5);
}

.btn-outline-white:disabled,
.btn-outline-white.disabled {
	color: #ffffff;
	background-color: transparent;
}

.btn-link {
	font-weight: 400;
	color: #0d6efd;
	text-decoration: none;
}

.btn-link:hover {
	color: #0a58ca;
	text-decoration: none;
}

.btn-link:focus {
	text-decoration: none;
}

.btn-link:disabled,
.btn-link.disabled {
	color: #6c757d;
}

.btn-lg,
.btn-group-lg > .btn {
	padding: 0.5rem 1rem;
	font-size: calc(16.4px + 0.3vw);
	-webkit-border-radius: 0.3rem;
	border-radius: 0.3rem;
}

.btn-sm,
.btn-group-sm > .btn {
	padding: 0.25rem 0.5rem;
	font-size: 14px;
	-webkit-border-radius: 0.2rem;
	border-radius: 0.2rem;
}

.fade {
	-webkit-transition: opacity 0.15s linear;
	-o-transition: opacity 0.15s linear;
	transition: opacity 0.15s linear;
}

.fade:not(.show) {
	opacity: 0;
}

.collapse:not(.show) {
	display: none;
}

.collapsing {
	height: 0;
	overflow: hidden;
	-webkit-transition: height 0.35s ease;
	-o-transition: height 0.35s ease;
	transition: height 0.35s ease;
}

.dropup,
.dropend,
.dropdown,
.dropstart {
	position: relative;
}

.dropdown-toggle {
	white-space: nowrap;
}

.dropdown-menu {
	position: absolute;
	z-index: 1000;
	display: none;
	min-width: 10rem;
	padding: 0.5rem 0;
	margin: 0;
	font-size: 16px;
	color: #212529;
	text-align: left;
	list-style: none;
	background-color: #fff;
	-webkit-background-clip: padding-box;
	background-clip: padding-box;
	border: 0 solid rgba(0, 0, 0, 0.15);
	-webkit-border-radius: 0.25rem;
	border-radius: 0.25rem;
	-webkit-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
	box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.dropdown-menu[data-bs-popper] {
	top: 100%;
	left: 0;
	margin-top: 0.125rem;
}

.dropdown-menu-start {
	--bs-position: start;
}

.dropdown-menu-start[data-bs-popper] {
	right: auto;
	left: 0;
}

.dropdown-menu-end {
	--bs-position: end;
}

.dropdown-menu-end[data-bs-popper] {
	right: 0;
	left: auto;
}

.dropup .dropdown-menu[data-bs-popper] {
	top: auto;
	bottom: 100%;
	margin-top: 0;
	margin-bottom: 0.125rem;
}

.dropend .dropdown-menu[data-bs-popper] {
	top: 0;
	right: auto;
	left: 100%;
	margin-top: 0;
	margin-left: 0.125rem;
}

.dropend .dropdown-toggle::after {
	vertical-align: 0;
}

.dropstart .dropdown-menu[data-bs-popper] {
	top: 0;
	right: 100%;
	left: auto;
	margin-top: 0;
	margin-right: 0.125rem;
}

.dropstart .dropdown-toggle::before {
	vertical-align: 0;
}

.dropdown-divider {
	height: 0;
	margin: 0.5rem 0;
	overflow: hidden;
	border-top: 1px solid rgba(0, 0, 0, 0.15);
}

.dropdown-item {
	display: block;
	width: 100%;
	padding: 0.25rem 1rem;
	clear: both;
	font-weight: 400;
	color: #212529;
	text-align: inherit;
	white-space: nowrap;
	background-color: transparent;
	border: 0;
}

.dropdown-item:hover,
.dropdown-item:focus {
	color: #1e2125;
	background-color: #e9ecef;
}

.dropdown-item.active,
.dropdown-item:active {
	color: #fff;
	text-decoration: none;
	background-color: #0d6efd;
}

.dropdown-item.disabled,
.dropdown-item:disabled {
	color: #adb5bd;
	pointer-events: none;
	background-color: transparent;
}

.dropdown-menu.show {
	display: block;
}

.dropdown-header {
	display: block;
	padding: 0.5rem 1rem;
	margin-bottom: 0;
	font-size: 14px;
	color: #6c757d;
	white-space: nowrap;
}

.dropdown-item-text {
	display: block;
	padding: 0.25rem 1rem;
	color: #212529;
}

.dropdown-menu-dark {
	color: #dee2e6;
	background-color: #343a40;
	border-color: rgba(0, 0, 0, 0.15);
}

.dropdown-menu-dark .dropdown-item {
	color: #dee2e6;
}

.dropdown-menu-dark .dropdown-item:hover,
.dropdown-menu-dark .dropdown-item:focus {
	color: #fff;
	background-color: rgba(255, 255, 255, 0.15);
}

.dropdown-menu-dark .dropdown-item.active,
.dropdown-menu-dark .dropdown-item:active {
	color: #fff;
	background-color: #0d6efd;
}

.dropdown-menu-dark .dropdown-item.disabled,
.dropdown-menu-dark .dropdown-item:disabled {
	color: #adb5bd;
}

.dropdown-menu-dark .dropdown-divider {
	border-color: rgba(0, 0, 0, 0.15);
}

.dropdown-menu-dark .dropdown-item-text {
	color: #dee2e6;
}

.dropdown-menu-dark .dropdown-header {
	color: #adb5bd;
}

.btn-group,
.btn-group-vertical {
	position: relative;
	display: -webkit-inline-box;
	display: -ms-inline-flexbox;
	display: inline-flex;
	vertical-align: middle;
}

.btn-group > .btn,
.btn-group-vertical > .btn {
	position: relative;
	-webkit-box-flex: 1;
	-ms-flex: 1 1 auto;
	flex: 1 1 auto;
}

.btn-group > .btn-check:checked + .btn,
.btn-group > .btn-check:focus + .btn,
.btn-group > .btn:hover,
.btn-group > .btn:focus,
.btn-group > .btn:active,
.btn-group > .btn.active,
.btn-group-vertical > .btn-check:checked + .btn,
.btn-group-vertical > .btn-check:focus + .btn,
.btn-group-vertical > .btn:hover,
.btn-group-vertical > .btn:focus,
.btn-group-vertical > .btn:active,
.btn-group-vertical > .btn.active {
	z-index: 1;
}

.btn-toolbar {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
	-webkit-box-pack: start;
	-ms-flex-pack: start;
	justify-content: flex-start;
}

.btn-toolbar .input-group {
	width: auto;
}

.btn-group > .btn:not(:first-child),
.btn-group > .btn-group:not(:first-child) {
	margin-left: -1px;
}

.btn-group > .btn:not(:last-child):not(.dropdown-toggle),
.btn-group > .btn-group:not(:last-child) > .btn {
	-webkit-border-top-right-radius: 0;
	border-top-right-radius: 0;
	-webkit-border-bottom-right-radius: 0;
	border-bottom-right-radius: 0;
}

.btn-group > .btn:nth-child(n+3),
.btn-group > :not(.btn-check) + .btn,
.btn-group > .btn-group:not(:first-child) > .btn {
	-webkit-border-top-left-radius: 0;
	border-top-left-radius: 0;
	-webkit-border-bottom-left-radius: 0;
	border-bottom-left-radius: 0;
}

.dropdown-toggle-split {
	padding-right: 15px;
	padding-left: 15px;
}

.dropdown-toggle-split::after,
.dropup .dropdown-toggle-split::after,
.dropend .dropdown-toggle-split::after {
	margin-left: 0;
}

.dropstart .dropdown-toggle-split::before {
	margin-right: 0;
}

.btn-sm + .dropdown-toggle-split,
.btn-group-sm > .btn + .dropdown-toggle-split {
	padding-right: 0.375rem;
	padding-left: 0.375rem;
}

.btn-lg + .dropdown-toggle-split,
.btn-group-lg > .btn + .dropdown-toggle-split {
	padding-right: 0.75rem;
	padding-left: 0.75rem;
}

.btn-group.show .dropdown-toggle {
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}

.btn-group.show .dropdown-toggle.btn-link {
	-webkit-box-shadow: none;
	box-shadow: none;
}

.btn-group-vertical {
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	-ms-flex-direction: column;
	flex-direction: column;
	-webkit-box-align: start;
	-ms-flex-align: start;
	align-items: flex-start;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
}

.btn-group-vertical > .btn,
.btn-group-vertical > .btn-group {
	width: 100%;
}

.btn-group-vertical > .btn:not(:first-child),
.btn-group-vertical > .btn-group:not(:first-child) {
	margin-top: -1px;
}

.btn-group-vertical > .btn:not(:last-child):not(.dropdown-toggle),
.btn-group-vertical > .btn-group:not(:last-child) > .btn {
	-webkit-border-bottom-right-radius: 0;
	border-bottom-right-radius: 0;
	-webkit-border-bottom-left-radius: 0;
	border-bottom-left-radius: 0;
}

.btn-group-vertical > .btn ~ .btn,
.btn-group-vertical > .btn-group:not(:first-child) > .btn {
	-webkit-border-top-left-radius: 0;
	border-top-left-radius: 0;
	-webkit-border-top-right-radius: 0;
	border-top-right-radius: 0;
}

.nav {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
	padding-left: 0;
	margin-bottom: 0;
	list-style: none;
}

.nav-link {
	display: block;
	padding: 10px 12px;
	color: #0d6efd;
	-webkit-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
	-o-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
	transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
}

.nav-link:hover,
.nav-link:focus {
	color: #0a58ca;
}

.nav-link.disabled {
	color: #6c757d;
	pointer-events: none;
	cursor: default;
}

.nav-tabs {
	border-bottom: 0px solid #dee2e6;
}

.nav-tabs .nav-link {
	margin-bottom: 0px;
	background: none;
	border: 0px solid transparent;
	-webkit-border-top-left-radius: 0;
	border-top-left-radius: 0;
	-webkit-border-top-right-radius: 0;
	border-top-right-radius: 0;
}

.nav-tabs .nav-link:hover,
.nav-tabs .nav-link:focus {
	border-color: #e9ecef #e9ecef #dee2e6;
	isolation: isolate;
}

.nav-tabs .nav-link.disabled {
	color: #6c757d;
	background-color: transparent;
	border-color: transparent;
}

.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
	color: var(--color-primary);
	background-color: var(--color-white);
	border-color: #dee2e6 #dee2e6 #fff;
}

.nav-tabs .dropdown-menu {
	margin-top: 0px;
	-webkit-border-top-left-radius: 0;
	border-top-left-radius: 0;
	-webkit-border-top-right-radius: 0;
	border-top-right-radius: 0;
}

.nav-pills .nav-link {
	background: none;
	border: 0;
	-webkit-border-radius: 13.33rem;
	border-radius: 13.33rem;
}

.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
	color: #fff;
	background-color: var(--color-primary);
}

.nav-fill > .nav-link,
.nav-fill .nav-item {
	-webkit-box-flex: 1;
	-ms-flex: 1 1 auto;
	flex: 1 1 auto;
	text-align: center;
}

.nav-justified > .nav-link,
.nav-justified .nav-item {
	-ms-flex-preferred-size: 0;
	flex-basis: 0;
	-webkit-box-flex: 1;
	-ms-flex-positive: 1;
	flex-grow: 1;
	text-align: center;
}

.nav-fill .nav-item .nav-link,
.nav-justified .nav-item .nav-link {
	width: 100%;
}

.tab-content > .tab-pane {
	display: none;
}

.tab-content > .active {
	display: block;
}

.navbar {
	position: relative;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
	padding-top: 1.0313rem;
	padding-right: 2rem;
	padding-bottom: 1.0313rem;
	padding-left: 2rem;
}

.navbar > .container,
.navbar > .container-fluid,
.navbar > .container-sm,
.navbar > .container-md,
.navbar > .container-lg,
.navbar > .container-xl,
.navbar > .container-xxl {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-ms-flex-wrap: inherit;
	flex-wrap: inherit;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
}

.navbar-brand {
	padding-top: 0.3125rem;
	padding-bottom: 0.3125rem;
	margin-right: 1rem;
	font-size: calc(16.4px + 0.3vw);
	white-space: nowrap;
}

.navbar-nav {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	-ms-flex-direction: column;
	flex-direction: column;
	padding-left: 0;
	margin-bottom: 0;
	list-style: none;
}

.navbar-nav .nav-link {
	padding-right: 0;
	padding-left: 0;
}

.navbar-nav .dropdown-menu {
	position: static;
}

.navbar-text {
	padding-top: 10px;
	padding-bottom: 10px;
}

.navbar-collapse {
	-ms-flex-preferred-size: 100%;
	flex-basis: 100%;
	-webkit-box-flex: 1;
	-ms-flex-positive: 1;
	flex-grow: 1;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
}

.navbar-toggler {
	padding: 0.25rem 0.75rem;
	font-size: calc(16.4px + 0.3vw);
	line-height: 1;
	background-color: transparent;
	border: 1px solid transparent;
	-webkit-border-radius: 0.25rem;
	border-radius: 0.25rem;
	-webkit-transition: -webkit-box-shadow 0.15s ease-in-out;
	transition: -webkit-box-shadow 0.15s ease-in-out;
	-o-transition: box-shadow 0.15s ease-in-out;
	transition: box-shadow 0.15s ease-in-out;
	transition: box-shadow 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
}

.navbar-toggler:hover {
	text-decoration: none;
}

.navbar-toggler:focus {
	text-decoration: none;
	outline: 0;
	-webkit-box-shadow: 0 0 0 0.25rem;
	box-shadow: 0 0 0 0.25rem;
}

.navbar-toggler-icon {
	display: inline-block;
	width: 1.5em;
	height: 1.5em;
	vertical-align: middle;
	background-repeat: no-repeat;
	background-position: center;
	-webkit-background-size: 100% 100%;
	background-size: 100%;
}

.navbar-nav-scroll {
	max-height: var(--bs-scroll-height, 75vh);
	overflow-y: auto;
}

.navbar-expand {
	-ms-flex-wrap: nowrap;
	flex-wrap: nowrap;
	-webkit-box-pack: start;
	-ms-flex-pack: start;
	justify-content: flex-start;
}

.navbar-expand .navbar-nav {
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-ms-flex-direction: row;
	flex-direction: row;
}

.navbar-expand .navbar-nav .dropdown-menu {
	position: absolute;
}

.navbar-expand .navbar-nav .nav-link {
	padding-right: 0.5rem;
	padding-left: 0.5rem;
}

.navbar-expand .navbar-nav-scroll {
	overflow: visible;
}

.navbar-expand .navbar-collapse {
	display: -webkit-box !important;
	display: -ms-flexbox !important;
	display: flex !important;
	-ms-flex-preferred-size: auto;
	flex-basis: auto;
}

.navbar-expand .navbar-toggler {
	display: none;
}

.navbar-light .navbar-brand {
	color: rgba(0, 0, 0, 0.9);
}

.navbar-light .navbar-brand:hover,
.navbar-light .navbar-brand:focus {
	color: rgba(0, 0, 0, 0.9);
}

.navbar-light .navbar-nav .nav-link {
	color: #202428;
}

.navbar-light .navbar-nav .nav-link:hover,
.navbar-light .navbar-nav .nav-link:focus {
	color: var(--color-primary);
}

.navbar-light .navbar-nav .nav-link.disabled {
	color: rgba(0, 0, 0, 0.3);
}

.navbar-light .navbar-nav .show > .nav-link,
.navbar-light .navbar-nav .nav-link.active {
	color: var(--color-primary);
}

.navbar-light .navbar-toggler {
	color: #202428;
	border-color: rgba(0, 0, 0, 0.1);
}

.navbar-light .navbar-toggler-icon {
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%280, 0, 0, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

.navbar-light .navbar-text {
	color: #202428;
}

.navbar-light .navbar-text a,
.navbar-light .navbar-text a:hover,
.navbar-light .navbar-text a:focus {
	color: var(--color-primary);
}

.navbar-dark .navbar-brand {
	color: #fff;
}

.navbar-dark .navbar-brand:hover,
.navbar-dark .navbar-brand:focus {
	color: #fff;
}

.navbar-dark .navbar-nav .nav-link {
	color: rgba(var(--color-white-rgba), 0.7);
}

.navbar-dark .navbar-nav .nav-link:hover,
.navbar-dark .navbar-nav .nav-link:focus {
	color: var(--color-white);
}

.navbar-dark .navbar-nav .nav-link.disabled {
	color: rgba(255, 255, 255, 0.25);
}

.navbar-dark .navbar-nav .show > .nav-link,
.navbar-dark .navbar-nav .nav-link.active {
	color: #fff;
}

.navbar-dark .navbar-toggler {
	color: rgba(var(--color-white-rgba), 0.7);
	border-color: rgba(255, 255, 255, 0.1);
}

.navbar-dark .navbar-toggler-icon {
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

.navbar-dark .navbar-text {
	color: rgba(var(--color-white-rgba), 0.7);
}

.navbar-dark .navbar-text a,
.navbar-dark .navbar-text a:hover,
.navbar-dark .navbar-text a:focus {
	color: #fff;
}

.card {
	position: relative;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	-ms-flex-direction: column;
	flex-direction: column;
	min-width: 0;
	word-wrap: break-word;
	background-color: var(--bg-white);
	-webkit-background-clip: border-box;
	background-clip: border-box;
	border: 1px solid var(--border-color);
	-webkit-border-radius: 10px;
	border-radius: 10px;
}

.card > hr {
	margin-right: 0;
	margin-left: 0;
}

.card > .list-group {
	border-top: inherit;
	border-bottom: inherit;
}

.card > .list-group:first-child {
	border-top-width: 0;
	-webkit-border-top-left-radius: calc(0.25rem - 1px);
	border-top-left-radius: calc(0.25rem - 1px);
	-webkit-border-top-right-radius: calc(0.25rem - 1px);
	border-top-right-radius: calc(0.25rem - 1px);
}

.card > .list-group:last-child {
	border-bottom-width: 0;
	-webkit-border-bottom-right-radius: calc(0.25rem - 1px);
	border-bottom-right-radius: calc(0.25rem - 1px);
	-webkit-border-bottom-left-radius: calc(0.25rem - 1px);
	border-bottom-left-radius: calc(0.25rem - 1px);
}

.card > .card-header + .list-group,
.card > .list-group + .card-footer {
	border-top: 0;
}

.card-body {
	-webkit-box-flex: 1;
	-ms-flex: 1 1 auto;
	flex: 1 1 auto;
	padding: 1rem 1.56rem;
}

.card-title {
	margin-bottom: 0.5rem;
}

.card-subtitle {
	margin-top: -0.25rem;
	margin-bottom: 0;
}

.card-text:last-child {
	margin-bottom: 0;
}

.card-link:hover {
	text-decoration: none;
}

.card-link + .card-link {
	margin-left: 1.56rem;
}

.card-header {
	padding: 0.5rem 1rem;
	margin-bottom: 0;
	background-color: var(--color-white);
	border-bottom: 1px solid var(--border-color);
}

.card-header:first-child {
	-webkit-border-radius: calc(0.25rem - 1px) calc(0.25rem - 1px) 0 0;
	border-radius: calc(0.25rem - 1px) calc(0.25rem - 1px) 0 0;
}

.card-footer {
	padding: 0.5rem 1rem;
	background-color: var(--color-white);
	border-top: 1px solid var(--border-color);
}

.card-footer:last-child {
	-webkit-border-radius: 0 0 calc(0.25rem - 1px) calc(0.25rem - 1px);
	border-radius: 0 0 calc(0.25rem - 1px) calc(0.25rem - 1px);
}

.card-header-tabs {
	margin-right: -0.5rem;
	margin-bottom: -0.5rem;
	margin-left: -0.5rem;
	border-bottom: 0;
}

.card-header-tabs .nav-link.active {
	background-color: var(--bg-white);
	border-bottom-color: var(--bg-white);
}

.card-header-pills {
	margin-right: -0.5rem;
	margin-left: -0.5rem;
}

.card-img-overlay {
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	padding: 1rem;
	-webkit-border-radius: calc(0.25rem - 1px);
	border-radius: calc(0.25rem - 1px);
}

.card-img,
.card-img-top,
.card-img-bottom {
	width: 100%;
}

.card-img,
.card-img-top {
	-webkit-border-top-left-radius: calc(0.25rem - 1px);
	border-top-left-radius: calc(0.25rem - 1px);
	-webkit-border-top-right-radius: calc(0.25rem - 1px);
	border-top-right-radius: calc(0.25rem - 1px);
}

.card-img,
.card-img-bottom {
	-webkit-border-bottom-right-radius: calc(0.25rem - 1px);
	border-bottom-right-radius: calc(0.25rem - 1px);
	-webkit-border-bottom-left-radius: calc(0.25rem - 1px);
	border-bottom-left-radius: calc(0.25rem - 1px);
}

.card-group > .card {
	margin-bottom: 0.75rem;
}

.accordion-button {
	position: relative;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	width: 100%;
	padding: 1rem 1.25rem;
	font-size: 16px;
	color: #212529;
	text-align: left;
	background-color: #fff;
	border: 0;
	-webkit-border-radius: 0;
	border-radius: 0;
	overflow-anchor: none;
	-webkit-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out, -webkit-border-radius 0.15s ease;
	transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out, -webkit-border-radius 0.15s ease;
	-o-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, border-radius 0.15s ease;
	transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, border-radius 0.15s ease;
	transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, border-radius 0.15s ease, -webkit-box-shadow 0.15s ease-in-out, -webkit-border-radius 0.15s ease;
}

.accordion-button:not(.collapsed) {
	color: #0c63e4;
	background-color: #e7f1ff;
	-webkit-box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.125);
	box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.125);
}

.accordion-button:not(.collapsed)::after {
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%230c63e4'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
	-webkit-transform: rotate(-180deg);
	-ms-transform: rotate(-180deg);
	transform: rotate(-180deg);
}

.accordion-button::after {
	-ms-flex-negative: 0;
	flex-shrink: 0;
	width: 1.25rem;
	height: 1.25rem;
	margin-left: auto;
	content: "";
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23212529'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
	background-repeat: no-repeat;
	-webkit-background-size: 1.25rem 1.25rem;
	background-size: 1.25rem;
	-webkit-transition: -webkit-transform 0.2s ease-in-out;
	transition: -webkit-transform 0.2s ease-in-out;
	-o-transition: transform 0.2s ease-in-out;
	transition: transform 0.2s ease-in-out;
	transition: transform 0.2s ease-in-out, -webkit-transform 0.2s ease-in-out;
}

.accordion-button:hover {
	z-index: 2;
}

.accordion-button:focus {
	z-index: 3;
	border-color: #86b7fe;
	outline: 0;
	-webkit-box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
	box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.accordion-header {
	margin-bottom: 0;
}

.accordion-item {
	background-color: #fff;
	border: 1px solid rgba(0, 0, 0, 0.125);
}

.accordion-item:first-of-type {
	-webkit-border-top-left-radius: 0.25rem;
	border-top-left-radius: 0.25rem;
	-webkit-border-top-right-radius: 0.25rem;
	border-top-right-radius: 0.25rem;
}

.accordion-item:first-of-type .accordion-button {
	-webkit-border-top-left-radius: calc(0.25rem - 1px);
	border-top-left-radius: calc(0.25rem - 1px);
	-webkit-border-top-right-radius: calc(0.25rem - 1px);
	border-top-right-radius: calc(0.25rem - 1px);
}

.accordion-item:not(:first-of-type) {
	border-top: 0;
}

.accordion-item:last-of-type {
	-webkit-border-bottom-right-radius: 0.25rem;
	border-bottom-right-radius: 0.25rem;
	-webkit-border-bottom-left-radius: 0.25rem;
	border-bottom-left-radius: 0.25rem;
}

.accordion-item:last-of-type .accordion-button.collapsed {
	-webkit-border-bottom-right-radius: calc(0.25rem - 1px);
	border-bottom-right-radius: calc(0.25rem - 1px);
	-webkit-border-bottom-left-radius: calc(0.25rem - 1px);
	border-bottom-left-radius: calc(0.25rem - 1px);
}

.accordion-item:last-of-type .accordion-collapse {
	-webkit-border-bottom-right-radius: 0.25rem;
	border-bottom-right-radius: 0.25rem;
	-webkit-border-bottom-left-radius: 0.25rem;
	border-bottom-left-radius: 0.25rem;
}

.accordion-body {
	padding: 1rem 1.25rem;
}

.accordion-flush .accordion-collapse {
	border-width: 0;
}

.accordion-flush .accordion-item {
	border-right: 0;
	border-left: 0;
	-webkit-border-radius: 0;
	border-radius: 0;
}

.accordion-flush .accordion-item:first-child {
	border-top: 0;
}

.accordion-flush .accordion-item:last-child {
	border-bottom: 0;
}

.accordion-flush .accordion-item .accordion-button {
	-webkit-border-radius: 0;
	border-radius: 0;
}

.breadcrumb {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
	padding: 0 0;
	margin-bottom: 0;
	list-style: none;
	background-color: none;
}

.breadcrumb-item + .breadcrumb-item {
	padding-left: 0.5rem;
}

.breadcrumb-item + .breadcrumb-item::before {
	float: left;
	padding-right: 0.5rem;
	color: #8C90A4;
	content: var(--bs-breadcrumb-divider, "\f112") /* rtl: var(--bs-breadcrumb-divider, "/") */;
}

.breadcrumb-item.active {
	color: #c9cfe4;
}

.pagination {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	padding-left: 0;
	list-style: none;
}

.page-link {
	position: relative;
	display: block;
	color: #0d6efd;
	background-color: #fff;
	border: 1px solid #dee2e6;
	-webkit-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
	transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
	-o-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
	transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
	transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
}

.page-link:hover {
	z-index: 2;
	color: #0a58ca;
	background-color: #e9ecef;
	border-color: #dee2e6;
}

.page-link:focus {
	z-index: 3;
	color: #0a58ca;
	background-color: #e9ecef;
	outline: 0;
	-webkit-box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
	box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.page-item:not(:first-child) .page-link {
	margin-left: -1px;
}

.page-item.active .page-link {
	z-index: 3;
	color: #fff;
	background-color: #0d6efd;
	border-color: #0d6efd;
}

.page-item.disabled .page-link {
	color: #6c757d;
	pointer-events: none;
	background-color: #fff;
	border-color: #dee2e6;
}

.page-link {
	padding: 0.8rem 1rem;
}

.page-item:first-child .page-link {
	-webkit-border-top-left-radius: 0.25rem;
	border-top-left-radius: 0.25rem;
	-webkit-border-bottom-left-radius: 0.25rem;
	border-bottom-left-radius: 0.25rem;
}

.page-item:last-child .page-link {
	-webkit-border-top-right-radius: 0.25rem;
	border-top-right-radius: 0.25rem;
	-webkit-border-bottom-right-radius: 0.25rem;
	border-bottom-right-radius: 0.25rem;
}

.pagination-lg .page-link {
	padding: 0.75rem 1.5rem;
	font-size: calc(16.4px + 0.3vw);
}

.pagination-lg .page-item:first-child .page-link {
	-webkit-border-top-left-radius: 0.3rem;
	border-top-left-radius: 0.3rem;
	-webkit-border-bottom-left-radius: 0.3rem;
	border-bottom-left-radius: 0.3rem;
}

.pagination-lg .page-item:last-child .page-link {
	-webkit-border-top-right-radius: 0.3rem;
	border-top-right-radius: 0.3rem;
	-webkit-border-bottom-right-radius: 0.3rem;
	border-bottom-right-radius: 0.3rem;
}

.pagination-sm .page-link {
	padding: 0.25rem 0.5rem;
	font-size: 14px;
}

.pagination-sm .page-item:first-child .page-link {
	-webkit-border-top-left-radius: 0.2rem;
	border-top-left-radius: 0.2rem;
	-webkit-border-bottom-left-radius: 0.2rem;
	border-bottom-left-radius: 0.2rem;
}

.pagination-sm .page-item:last-child .page-link {
	-webkit-border-top-right-radius: 0.2rem;
	border-top-right-radius: 0.2rem;
	-webkit-border-bottom-right-radius: 0.2rem;
	border-bottom-right-radius: 0.2rem;
}

.badge {
	display: inline-block;
	padding: 4.5px 6.58px;
	font-size: 10px;
	font-weight: 600;
	line-height: 1;
	color: #fff;
	text-align: center;
	white-space: nowrap;
	vertical-align: baseline;
	-webkit-border-radius: 10px;
	border-radius: 10px;
}

.badge:empty {
	display: none;
}

.btn .badge {
	position: relative;
	top: -1px;
}

.alert {
	position: relative;
	padding: 0.659rem 1.2rem;
	margin-bottom: 15px;
	border: 1px solid transparent;
	-webkit-border-radius: 5px;
	border-radius: 5px;
}

.alert-heading {
	color: inherit;
}

.alert-link {
	font-weight: 700;
}

.alert-dismissible {
	padding-right: 3rem;
}

.alert-dismissible .btn-close {
	position: absolute;
	top: 0;
	right: 0;
	z-index: 2;
	padding: 0.82375rem 1.2rem;
}

.alert-primary {
	color: #4e1d7f;
	background-color: #e6d6f6;
	border-color: #dac1f2;
}

.alert-primary .alert-link {
	color: #3e1766;
}

.alert-secondary {
	color: #352699;
	background-color: #ded9ff;
	border-color: #cdc6ff;
}

.alert-secondary .alert-link {
	color: #2a1e7a;
}

.alert-success {
	color: #016e10;
	background-color: #ccf1d1;
	border-color: #b3eaba;
}

.alert-success .alert-link {
	color: #01580d;
}

.alert-info {
	color: #006699;
	background-color: #cceeff;
	border-color: #b3e6ff;
}

.alert-info .alert-link {
	color: #00527a;
}

.alert-infos {
	color: #005b5e;
	background-color: #ccfafb;
	border-color: #b3f7f9;
}

.alert-infos .alert-link {
	color: #00494b;
}

.alert-warning {
	color: #965307;
	background-color: #fee8ce;
	border-color: #fedcb6;
}

.alert-warning .alert-link {
	color: #784206;
}

.alert-warnings {
	color: #664b00;
	background-color: #fff1cc;
	border-color: #ffebb3;
}

.alert-warnings .alert-link {
	color: #523c00;
}

.alert-danger {
	color: #990909;
	background-color: #ffcfcf;
	border-color: #ffb7b7;
}

.alert-danger .alert-link {
	color: #7a0707;
}

.alert-dangers {
	color: #990909;
	background-color: #ffcfcf;
	border-color: #ffb7b7;
}

.alert-dangers .alert-link {
	color: #7a0707;
}

.alert-purple {
	color: #641494;
	background-color: #edd3fd;
	border-color: #e5bdfc;
}

.alert-purple .alert-link {
	color: #501076;
}

.alert-dark {
	color: #060606;
	background-color: #cecece;
	border-color: #b6b6b6;
}

.alert-dark .alert-link {
	color: #050505;
}

.alert-light {
	color: #545662;
	background-color: #e8e9ed;
	border-color: #dddee4;
}

.alert-light .alert-link {
	color: #43454e;
}

.alert-lighten {
	color: #606060;
	background-color: #ececec;
	border-color: #e3e3e3;
}

.alert-lighten .alert-link {
	color: #4d4d4d;
}

.alert-light-gray {
	color: #545662;
	background-color: #e8e9ed;
	border-color: #dddee4;
}

.alert-light-gray .alert-link {
	color: #43454e;
}

.alert-text {
	color: #3d4158;
	background-color: #e0e2e9;
	border-color: #d1d3de;
}

.alert-text .alert-link {
	color: #313446;
}

.alert-gray {
	color: #262626;
	background-color: #d9d9d9;
	border-color: #c6c6c6;
}

.alert-gray .alert-link {
	color: #1e1e1e;
}

.alert-third {
	color: #4e1d7f;
	background-color: #e6d6f6;
	border-color: #dac1f2;
}

.alert-third .alert-link {
	color: #3e1766;
}

.alert-white {
	color: #666666;
	background-color: white;
	border-color: white;
}

.alert-white .alert-link {
	color: #525252;
}

.progress {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	height: 8px;
	overflow: hidden;
	font-size: 12px;
	background-color: var(--bg-deep);
	-webkit-border-radius: 10px;
	border-radius: 10px;
	-webkit-box-shadow: 0 0;
	box-shadow: 0 0;
}

.progress-bar {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	-ms-flex-direction: column;
	flex-direction: column;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	overflow: hidden;
	color: #fff;
	text-align: center;
	white-space: nowrap;
	background-color: #0d6efd;
	-webkit-transition: width 0.6s ease;
	-o-transition: width 0.6s ease;
	transition: width 0.6s ease;
}

.progress-bar-striped {
	background-image: -webkit-linear-gradient(45deg, rgba(var(--color-white-rgba), 0.15) 25%, transparent 25%, transparent 50%, rgba(var(--color-white-rgba), 0.15) 50%, rgba(var(--color-white-rgba), 0.15) 75%, transparent 75%, transparent);
	background-image: -o-linear-gradient(45deg, rgba(var(--color-white-rgba), 0.15) 25%, transparent 25%, transparent 50%, rgba(var(--color-white-rgba), 0.15) 50%, rgba(var(--color-white-rgba), 0.15) 75%, transparent 75%, transparent);
	background-image: linear-gradient(45deg, rgba(var(--color-white-rgba), 0.15) 25%, transparent 25%, transparent 50%, rgba(var(--color-white-rgba), 0.15) 50%, rgba(var(--color-white-rgba), 0.15) 75%, transparent 75%, transparent);
	-webkit-background-size: 8px 8px;
	background-size: 8px 8px;
}

.progress-bar-animated {
	-webkit-animation: 1s linear infinite progress-bar-stripes;
	animation: 1s linear infinite progress-bar-stripes;
}

.media {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: start;
	-ms-flex-align: start;
	align-items: flex-start;
}

.media-body {
	-webkit-box-flex: 1;
	-ms-flex: 1;
	flex: 1;
}

.list-group {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	-ms-flex-direction: column;
	flex-direction: column;
	padding-left: 0;
	margin-bottom: 0;
	-webkit-border-radius: 0.25rem;
	border-radius: 0.25rem;
}

.list-group-numbered {
	list-style-type: none;
	counter-reset: section;
}

.list-group-numbered > li::before {
	content: counters(section, ".") ". ";
	counter-increment: section;
}

.list-group-item-action {
	width: 100%;
	color: #495057;
	text-align: inherit;
}

.list-group-item-action:hover,
.list-group-item-action:focus {
	z-index: 1;
	color: #495057;
	text-decoration: none;
	background-color: #f8f9fa;
}

.list-group-item-action:active {
	color: #212529;
	background-color: #e9ecef;
}

.list-group-item {
	position: relative;
	display: block;
	padding: 0.5rem 1rem;
	color: #212529;
	background-color: #fff;
	border: 1px solid rgba(0, 0, 0, 0.125);
}

.list-group-item:first-child {
	-webkit-border-top-left-radius: inherit;
	border-top-left-radius: inherit;
	-webkit-border-top-right-radius: inherit;
	border-top-right-radius: inherit;
}

.list-group-item:last-child {
	-webkit-border-bottom-right-radius: inherit;
	border-bottom-right-radius: inherit;
	-webkit-border-bottom-left-radius: inherit;
	border-bottom-left-radius: inherit;
}

.list-group-item.disabled,
.list-group-item:disabled {
	color: #6c757d;
	pointer-events: none;
	background-color: #fff;
}

.list-group-item.active {
	z-index: 2;
	color: #fff;
	background-color: #0d6efd;
	border-color: #0d6efd;
}

.list-group-item + .list-group-item {
	border-top-width: 0;
}

.list-group-item + .list-group-item.active {
	margin-top: -1px;
	border-top-width: 1px;
}

.list-group-horizontal {
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-ms-flex-direction: row;
	flex-direction: row;
}

.list-group-horizontal > .list-group-item:first-child {
	-webkit-border-bottom-left-radius: 0.25rem;
	border-bottom-left-radius: 0.25rem;
	-webkit-border-top-right-radius: 0;
	border-top-right-radius: 0;
}

.list-group-horizontal > .list-group-item:last-child {
	-webkit-border-top-right-radius: 0.25rem;
	border-top-right-radius: 0.25rem;
	-webkit-border-bottom-left-radius: 0;
	border-bottom-left-radius: 0;
}

.list-group-horizontal > .list-group-item.active {
	margin-top: 0;
}

.list-group-horizontal > .list-group-item + .list-group-item {
	border-top-width: 1px;
	border-left-width: 0;
}

.list-group-horizontal > .list-group-item + .list-group-item.active {
	margin-left: -1px;
	border-left-width: 1px;
}

.list-group-flush {
	-webkit-border-radius: 0;
	border-radius: 0;
}

.list-group-flush > .list-group-item {
	border-width: 0 0 1px;
}

.list-group-flush > .list-group-item:last-child {
	border-bottom-width: 0;
}

.list-group-item-primary {
	color: #4e1d7f;
	background-color: #e6d6f6;
}

.list-group-item-primary.list-group-item-action:hover,
.list-group-item-primary.list-group-item-action:focus {
	color: #4e1d7f;
	background-color: #cfc1dd;
}

.list-group-item-primary.list-group-item-action.active {
	color: var(--color-white);
	background-color: #4e1d7f;
	border-color: #4e1d7f;
}

.list-group-item-secondary {
	color: #352699;
	background-color: #ded9ff;
}

.list-group-item-secondary.list-group-item-action:hover,
.list-group-item-secondary.list-group-item-action:focus {
	color: #352699;
	background-color: #c8c3e6;
}

.list-group-item-secondary.list-group-item-action.active {
	color: var(--color-white);
	background-color: #352699;
	border-color: #352699;
}

.list-group-item-success {
	color: #016e10;
	background-color: #ccf1d1;
}

.list-group-item-success.list-group-item-action:hover,
.list-group-item-success.list-group-item-action:focus {
	color: #016e10;
	background-color: #b8d9bc;
}

.list-group-item-success.list-group-item-action.active {
	color: var(--color-white);
	background-color: #016e10;
	border-color: #016e10;
}

.list-group-item-info {
	color: #006699;
	background-color: #cceeff;
}

.list-group-item-info.list-group-item-action:hover,
.list-group-item-info.list-group-item-action:focus {
	color: #006699;
	background-color: #b8d6e6;
}

.list-group-item-info.list-group-item-action.active {
	color: var(--color-white);
	background-color: #006699;
	border-color: #006699;
}

.list-group-item-infos {
	color: #005b5e;
	background-color: #ccfafb;
}

.list-group-item-infos.list-group-item-action:hover,
.list-group-item-infos.list-group-item-action:focus {
	color: #005b5e;
	background-color: #b8e1e2;
}

.list-group-item-infos.list-group-item-action.active {
	color: var(--color-white);
	background-color: #005b5e;
	border-color: #005b5e;
}

.list-group-item-warning {
	color: #965307;
	background-color: #fee8ce;
}

.list-group-item-warning.list-group-item-action:hover,
.list-group-item-warning.list-group-item-action:focus {
	color: #965307;
	background-color: #e5d1b9;
}

.list-group-item-warning.list-group-item-action.active {
	color: var(--color-white);
	background-color: #965307;
	border-color: #965307;
}

.list-group-item-warnings {
	color: #664b00;
	background-color: #fff1cc;
}

.list-group-item-warnings.list-group-item-action:hover,
.list-group-item-warnings.list-group-item-action:focus {
	color: #664b00;
	background-color: #e6d9b8;
}

.list-group-item-warnings.list-group-item-action.active {
	color: var(--color-white);
	background-color: #664b00;
	border-color: #664b00;
}

.list-group-item-danger {
	color: #990909;
	background-color: #ffcfcf;
}

.list-group-item-danger.list-group-item-action:hover,
.list-group-item-danger.list-group-item-action:focus {
	color: #990909;
	background-color: #e6baba;
}

.list-group-item-danger.list-group-item-action.active {
	color: var(--color-white);
	background-color: #990909;
	border-color: #990909;
}

.list-group-item-dangers {
	color: #990909;
	background-color: #ffcfcf;
}

.list-group-item-dangers.list-group-item-action:hover,
.list-group-item-dangers.list-group-item-action:focus {
	color: #990909;
	background-color: #e6baba;
}

.list-group-item-dangers.list-group-item-action.active {
	color: var(--color-white);
	background-color: #990909;
	border-color: #990909;
}

.list-group-item-purple {
	color: #641494;
	background-color: #edd3fd;
}

.list-group-item-purple.list-group-item-action:hover,
.list-group-item-purple.list-group-item-action:focus {
	color: #641494;
	background-color: #d5bee4;
}

.list-group-item-purple.list-group-item-action.active {
	color: var(--color-white);
	background-color: #641494;
	border-color: #641494;
}

.list-group-item-dark {
	color: #060606;
	background-color: #cecece;
}

.list-group-item-dark.list-group-item-action:hover,
.list-group-item-dark.list-group-item-action:focus {
	color: #060606;
	background-color: #b9b9b9;
}

.list-group-item-dark.list-group-item-action.active {
	color: var(--color-white);
	background-color: #060606;
	border-color: #060606;
}

.list-group-item-light {
	color: #545662;
	background-color: #e8e9ed;
}

.list-group-item-light.list-group-item-action:hover,
.list-group-item-light.list-group-item-action:focus {
	color: #545662;
	background-color: #d1d2d5;
}

.list-group-item-light.list-group-item-action.active {
	color: var(--color-white);
	background-color: #545662;
	border-color: #545662;
}

.list-group-item-lighten {
	color: #606060;
	background-color: #ececec;
}

.list-group-item-lighten.list-group-item-action:hover,
.list-group-item-lighten.list-group-item-action:focus {
	color: #606060;
	background-color: #d4d4d4;
}

.list-group-item-lighten.list-group-item-action.active {
	color: var(--color-white);
	background-color: #606060;
	border-color: #606060;
}

.list-group-item-light-gray {
	color: #545662;
	background-color: #e8e9ed;
}

.list-group-item-light-gray.list-group-item-action:hover,
.list-group-item-light-gray.list-group-item-action:focus {
	color: #545662;
	background-color: #d1d2d5;
}

.list-group-item-light-gray.list-group-item-action.active {
	color: var(--color-white);
	background-color: #545662;
	border-color: #545662;
}

.list-group-item-text {
	color: #3d4158;
	background-color: #e0e2e9;
}

.list-group-item-text.list-group-item-action:hover,
.list-group-item-text.list-group-item-action:focus {
	color: #3d4158;
	background-color: #cacbd2;
}

.list-group-item-text.list-group-item-action.active {
	color: var(--color-white);
	background-color: #3d4158;
	border-color: #3d4158;
}

.list-group-item-gray {
	color: #262626;
	background-color: #d9d9d9;
}

.list-group-item-gray.list-group-item-action:hover,
.list-group-item-gray.list-group-item-action:focus {
	color: #262626;
	background-color: #c3c3c3;
}

.list-group-item-gray.list-group-item-action.active {
	color: var(--color-white);
	background-color: #262626;
	border-color: #262626;
}

.list-group-item-third {
	color: #4e1d7f;
	background-color: #e6d6f6;
}

.list-group-item-third.list-group-item-action:hover,
.list-group-item-third.list-group-item-action:focus {
	color: #4e1d7f;
	background-color: #cfc1dd;
}

.list-group-item-third.list-group-item-action.active {
	color: var(--color-white);
	background-color: #4e1d7f;
	border-color: #4e1d7f;
}

.list-group-item-white {
	color: #666666;
	background-color: white;
}

.list-group-item-white.list-group-item-action:hover,
.list-group-item-white.list-group-item-action:focus {
	color: #666666;
	background-color: #e6e6e6;
}

.list-group-item-white.list-group-item-action.active {
	color: var(--color-white);
	background-color: #666666;
	border-color: #666666;
}

.btn-close {
	-webkit-box-sizing: content-box;
	box-sizing: content-box;
	width: 1em;
	height: 1em;
	padding: 0.25em 0.25em;
	color: #000;
	background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e") center/1em auto no-repeat;
	border: 0;
	-webkit-border-radius: 5px;
	border-radius: 5px;
	opacity: 0.5;
}

.btn-close:hover {
	color: #000;
	text-decoration: none;
	opacity: 0.75;
}

.btn-close:focus {
	outline: 0;
	-webkit-box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
	box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
	opacity: 1;
}

.btn-close:disabled,
.btn-close.disabled {
	pointer-events: none;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	opacity: 0.25;
}

.btn-close-white {
	-webkit-filter: invert(1) grayscale(100%) brightness(200%);
	filter: invert(1) grayscale(100%) brightness(200%);
}

.toast {
	width: 350px;
	max-width: 100%;
	font-size: 14px;
	pointer-events: auto;
	background-color: rgba(255, 255, 255, 0.85);
	-webkit-background-clip: padding-box;
	background-clip: padding-box;
	border: 1px solid rgba(0, 0, 0, 0.1);
	-webkit-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
	box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
	-webkit-border-radius: 0.25rem;
	border-radius: 0.25rem;
}

.toast:not(.showing):not(.show) {
	opacity: 0;
}

.toast.hide {
	display: none;
}

.toast-container {
	width: -webkit-max-content;
	width: -moz-max-content;
	width: max-content;
	max-width: 100%;
	pointer-events: none;
}

.toast-container > :not(:last-child) {
	margin-bottom: 0.75rem;
}

.toast-header {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	padding: 0.5rem 0.75rem;
	color: #6c757d;
	background-color: rgba(255, 255, 255, 0.85);
	-webkit-background-clip: padding-box;
	background-clip: padding-box;
	border-bottom: 1px solid rgba(0, 0, 0, 0.05);
	-webkit-border-top-left-radius: calc(0.25rem - 1px);
	border-top-left-radius: calc(0.25rem - 1px);
	-webkit-border-top-right-radius: calc(0.25rem - 1px);
	border-top-right-radius: calc(0.25rem - 1px);
}

.toast-header .btn-close {
	margin-right: -0.375rem;
	margin-left: 0.75rem;
}

.toast-body {
	padding: 0.75rem;
	word-wrap: break-word;
}

.modal {
	position: fixed;
	top: 0;
	left: 0;
	z-index: 9999;
	display: none;
	width: 100%;
	height: 100%;
	overflow-x: hidden;
	overflow-y: auto;
	outline: 0;
}

.modal-dialog {
	position: relative;
	width: auto;
	margin: 0.5rem;
	pointer-events: none;
}

.modal.fade .modal-dialog {
	-webkit-transition: -webkit-transform 0.3s ease-out;
	transition: -webkit-transform 0.3s ease-out;
	-o-transition: transform 0.3s ease-out;
	transition: transform 0.3s ease-out;
	transition: transform 0.3s ease-out, -webkit-transform 0.3s ease-out;
	-webkit-transform: translate(0, -50px);
	-ms-transform: translate(0, -50px);
	transform: translate(0, -50px);
}

.modal.show .modal-dialog {
	-webkit-transform: none;
	-ms-transform: none;
	transform: none;
}

.modal.modal-static .modal-dialog {
	-webkit-transform: scale(1.02);
	-ms-transform: scale(1.02);
	transform: scale(1.02);
}

.modal-dialog-scrollable {
	height: calc(100% - 1rem);
}

.modal-dialog-scrollable .modal-content {
	max-height: 100%;
	overflow: hidden;
}

.modal-dialog-scrollable .modal-body {
	overflow-y: auto;
}

.modal-dialog-centered {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	min-height: calc(100% - 1rem);
}

.modal-content {
	position: relative;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	-ms-flex-direction: column;
	flex-direction: column;
	width: 100%;
	pointer-events: auto;
	background-color: #fff;
	-webkit-background-clip: padding-box;
	background-clip: padding-box;
	border: 0 solid rgba(0, 0, 0, 0.2);
	-webkit-border-radius: 4px;
	border-radius: 4px;
	-webkit-box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
	box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
	outline: 0;
}

.modal-backdrop {
	position: fixed;
	top: 0;
	left: 0;
	z-index: 9998;
	width: 100vw;
	height: 100vh;
	background-color: #000;
}

.modal-backdrop.fade {
	opacity: 0;
}

.modal-backdrop.show {
	opacity: 0.5;
}

.modal-header {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-ms-flex-negative: 0;
	flex-shrink: 0;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
	padding: 1.125rem 1.75rem;
	border-bottom: 1px solid var(--border-color);
	-webkit-border-top-left-radius: calc(0.3rem - 1px);
	border-top-left-radius: calc(0.3rem - 1px);
	-webkit-border-top-right-radius: calc(0.3rem - 1px);
	border-top-right-radius: calc(0.3rem - 1px);
}

.modal-header .btn-close {
	padding: 0.5rem 0.5rem;
	margin: -0.5rem -0.5rem -0.5rem auto;
}

.modal-title {
	margin-bottom: 0;
	line-height: 1.5;
}

.modal-body {
	position: relative;
	-webkit-box-flex: 1;
	-ms-flex: 1 1 auto;
	flex: 1 1 auto;
	padding: 1.75rem;
}

.modal-footer {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
	-ms-flex-negative: 0;
	flex-shrink: 0;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: end;
	-ms-flex-pack: end;
	justify-content: flex-end;
	padding: 1.5rem;
	border-top: 1px solid var(--border-color);
	-webkit-border-bottom-right-radius: calc(0.3rem - 1px);
	border-bottom-right-radius: calc(0.3rem - 1px);
	-webkit-border-bottom-left-radius: calc(0.3rem - 1px);
	border-bottom-left-radius: calc(0.3rem - 1px);
}

.modal-footer > * {
	margin: 0.25rem;
}

.modal-fullscreen {
	width: 100vw;
	max-width: none;
	height: 100%;
	margin: 0;
}

.modal-fullscreen .modal-content {
	height: 100%;
	border: 0;
	-webkit-border-radius: 0;
	border-radius: 0;
}

.modal-fullscreen .modal-header {
	-webkit-border-radius: 0;
	border-radius: 0;
}

.modal-fullscreen .modal-body {
	overflow-y: auto;
}

.modal-fullscreen .modal-footer {
	-webkit-border-radius: 0;
	border-radius: 0;
}

.tooltip {
	position: absolute;
	z-index: 1080;
	display: block;
	margin: 0;
	font-family: "Jost", sans-serif;
	font-style: normal;
	font-weight: 400;
	line-height: 1.5;
	text-align: left;
	text-align: start;
	text-decoration: none;
	text-shadow: none;
	text-transform: none;
	letter-spacing: normal;
	word-break: normal;
	word-spacing: normal;
	white-space: normal;
	line-break: auto;
	font-size: 14px;
	word-wrap: break-word;
	opacity: 0;
}

.tooltip.show {
	opacity: 0.9;
}

.tooltip .tooltip-arrow {
	position: absolute;
	display: block;
	width: 0.8rem;
	height: 0.4rem;
}

.tooltip .tooltip-arrow::before {
	position: absolute;
	content: "";
	border-color: transparent;
	border-style: solid;
}

.bs-tooltip-top,
.bs-tooltip-auto[data-popper-placement^=top] {
	padding: 0.4rem 0;
}

.bs-tooltip-top .tooltip-arrow,
.bs-tooltip-auto[data-popper-placement^=top] .tooltip-arrow {
	bottom: 0;
}

.bs-tooltip-top .tooltip-arrow::before,
.bs-tooltip-auto[data-popper-placement^=top] .tooltip-arrow::before {
	top: -1px;
	border-width: 0.4rem 0.4rem 0;
	border-top-color: var(--tooltip-arrow-color);
}

.bs-tooltip-end,
.bs-tooltip-auto[data-popper-placement^=right] {
	padding: 0 0.4rem;
}

.bs-tooltip-end .tooltip-arrow,
.bs-tooltip-auto[data-popper-placement^=right] .tooltip-arrow {
	left: 0;
	width: 0.4rem;
	height: 0.8rem;
}

.bs-tooltip-end .tooltip-arrow::before,
.bs-tooltip-auto[data-popper-placement^=right] .tooltip-arrow::before {
	right: -1px;
	border-width: 0.4rem 0.4rem 0.4rem 0;
	border-right-color: var(--tooltip-arrow-color);
}

.bs-tooltip-bottom,
.bs-tooltip-auto[data-popper-placement^=bottom] {
	padding: 0.4rem 0;
}

.bs-tooltip-bottom .tooltip-arrow,
.bs-tooltip-auto[data-popper-placement^=bottom] .tooltip-arrow {
	top: 0;
}

.bs-tooltip-bottom .tooltip-arrow::before,
.bs-tooltip-auto[data-popper-placement^=bottom] .tooltip-arrow::before {
	bottom: -1px;
	border-width: 0 0.4rem 0.4rem;
	border-bottom-color: var(--tooltip-arrow-color);
}

.bs-tooltip-start,
.bs-tooltip-auto[data-popper-placement^=left] {
	padding: 0 0.4rem;
}

.bs-tooltip-start .tooltip-arrow,
.bs-tooltip-auto[data-popper-placement^=left] .tooltip-arrow {
	right: 0;
	width: 0.4rem;
	height: 0.8rem;
}

.bs-tooltip-start .tooltip-arrow::before,
.bs-tooltip-auto[data-popper-placement^=left] .tooltip-arrow::before {
	left: -1px;
	border-width: 0.4rem 0 0.4rem 0.4rem;
	border-left-color: var(--tooltip-arrow-color);
}

.tooltip-inner {
	max-width: 200px;
	padding: 0.41rem 1.165rem;
	color: var(--color-gray);
	text-align: center;
	background-color: var(--tooltip-bg);
	-webkit-border-radius: 0.25rem;
	border-radius: 0.25rem;
}

.popover {
	position: absolute;
	top: 0;
	left: 0 /* rtl:ignore */;
	z-index: 9998;
	display: block;
	max-width: 235px;
	font-family: "Jost", sans-serif;
	font-style: normal;
	font-weight: 400;
	line-height: 1.5;
	text-align: left;
	text-align: start;
	text-decoration: none;
	text-shadow: none;
	text-transform: none;
	letter-spacing: normal;
	word-break: normal;
	word-spacing: normal;
	white-space: normal;
	line-break: auto;
	font-size: 14px;
	word-wrap: break-word;
	background-color: #fff;
	-webkit-background-clip: padding-box;
	background-clip: padding-box;
	border: 0px solid rgba(0, 0, 0, 0.2);
	-webkit-border-radius: 0.3rem;
	border-radius: 0.3rem;
	-webkit-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
	box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.popover .popover-arrow {
	position: absolute;
	display: block;
	width: 1rem;
	height: 0.5rem;
}

.popover .popover-arrow::before,
.popover .popover-arrow::after {
	position: absolute;
	display: block;
	content: "";
	border-color: transparent;
	border-style: solid;
}

.bs-popover-top > .popover-arrow,
.bs-popover-auto[data-popper-placement^=top] > .popover-arrow {
	bottom: calc(-0.5rem - 0px);
}

.bs-popover-top > .popover-arrow::before,
.bs-popover-auto[data-popper-placement^=top] > .popover-arrow::before {
	bottom: 0;
	border-width: 0.5rem 0.5rem 0;
	border-top-color: rgba(0, 0, 0, 0.25);
}

.bs-popover-top > .popover-arrow::after,
.bs-popover-auto[data-popper-placement^=top] > .popover-arrow::after {
	bottom: 0px;
	border-width: 0.5rem 0.5rem 0;
	border-top-color: #fff;
}

.bs-popover-end > .popover-arrow,
.bs-popover-auto[data-popper-placement^=right] > .popover-arrow {
	left: calc(-0.5rem - 0px);
	width: 0.5rem;
	height: 1rem;
}

.bs-popover-end > .popover-arrow::before,
.bs-popover-auto[data-popper-placement^=right] > .popover-arrow::before {
	left: 0;
	border-width: 0.5rem 0.5rem 0.5rem 0;
	border-right-color: rgba(0, 0, 0, 0.25);
}

.bs-popover-end > .popover-arrow::after,
.bs-popover-auto[data-popper-placement^=right] > .popover-arrow::after {
	left: 0px;
	border-width: 0.5rem 0.5rem 0.5rem 0;
	border-right-color: #fff;
}

.bs-popover-bottom > .popover-arrow,
.bs-popover-auto[data-popper-placement^=bottom] > .popover-arrow {
	top: calc(-0.5rem - 0px);
}

.bs-popover-bottom > .popover-arrow::before,
.bs-popover-auto[data-popper-placement^=bottom] > .popover-arrow::before {
	top: 0;
	border-width: 0 0.5rem 0.5rem 0.5rem;
	border-bottom-color: rgba(0, 0, 0, 0.25);
}

.bs-popover-bottom > .popover-arrow::after,
.bs-popover-auto[data-popper-placement^=bottom] > .popover-arrow::after {
	top: 0px;
	border-width: 0 0.5rem 0.5rem 0.5rem;
	border-bottom-color: #fff;
}

.bs-popover-bottom .popover-header::before,
.bs-popover-auto[data-popper-placement^=bottom] .popover-header::before {
	position: absolute;
	top: 0;
	left: 50%;
	display: block;
	width: 1rem;
	margin-left: -0.5rem;
	content: "";
	border-bottom: 0px solid #f0f0f0;
}

.bs-popover-start > .popover-arrow,
.bs-popover-auto[data-popper-placement^=left] > .popover-arrow {
	right: calc(-0.5rem - 0px);
	width: 0.5rem;
	height: 1rem;
}

.bs-popover-start > .popover-arrow::before,
.bs-popover-auto[data-popper-placement^=left] > .popover-arrow::before {
	right: 0;
	border-width: 0.5rem 0 0.5rem 0.5rem;
	border-left-color: rgba(0, 0, 0, 0.25);
}

.bs-popover-start > .popover-arrow::after,
.bs-popover-auto[data-popper-placement^=left] > .popover-arrow::after {
	right: 0px;
	border-width: 0.5rem 0 0.5rem 0.5rem;
	border-left-color: #fff;
}

.popover-header {
	padding: 0.5rem 1rem;
	margin-bottom: 0;
	font-size: 16px;
	color: var(--color-dark);
	background-color: #f0f0f0;
	border-bottom: 0px solid rgba(0, 0, 0, 0.2);
	-webkit-border-top-left-radius: calc(0.3rem - 1px);
	border-top-left-radius: calc(0.3rem - 1px);
	-webkit-border-top-right-radius: calc(0.3rem - 1px);
	border-top-right-radius: calc(0.3rem - 1px);
}

.popover-header:empty {
	display: none;
}

.popover-body {
	padding: 1rem 1rem;
	color: #212529;
}

.carousel {
	position: relative;
}

.carousel.pointer-event {
	-ms-touch-action: pan-y;
	touch-action: pan-y;
}

.carousel-inner {
	position: relative;
	width: 100%;
	overflow: hidden;
}

.carousel-inner::after {
	display: block;
	clear: both;
	content: "";
}

.carousel-item {
	position: relative;
	display: none;
	float: left;
	width: 100%;
	margin-right: -100%;
	-webkit-backface-visibility: hidden;
	backface-visibility: hidden;
	-webkit-transition: -webkit-transform 0.6s ease-in-out;
	transition: -webkit-transform 0.6s ease-in-out;
	-o-transition: transform 0.6s ease-in-out;
	transition: transform 0.6s ease-in-out;
	transition: transform 0.6s ease-in-out, -webkit-transform 0.6s ease-in-out;
}

.carousel-item.active,
.carousel-item-next,
.carousel-item-prev {
	display: block;
}

/* rtl:begin:ignore */

.carousel-item-next:not(.carousel-item-start),
.active.carousel-item-end {
	-webkit-transform: translateX(100%);
	-ms-transform: translateX(100%);
	transform: translateX(100%);
}

.carousel-item-prev:not(.carousel-item-end),
.active.carousel-item-start {
	-webkit-transform: translateX(-100%);
	-ms-transform: translateX(-100%);
	transform: translateX(-100%);
}

/* rtl:end:ignore */

.carousel-fade .carousel-item {
	opacity: 0;
	-webkit-transition-property: opacity;
	-o-transition-property: opacity;
	transition-property: opacity;
	-webkit-transform: none;
	-ms-transform: none;
	transform: none;
}

.carousel-fade .carousel-item.active,
.carousel-fade .carousel-item-next.carousel-item-start,
.carousel-fade .carousel-item-prev.carousel-item-end {
	z-index: 1;
	opacity: 1;
}

.carousel-fade .active.carousel-item-start,
.carousel-fade .active.carousel-item-end {
	z-index: 0;
	opacity: 0;
	-webkit-transition: opacity 0s 0.6s;
	-o-transition: opacity 0s 0.6s;
	transition: opacity 0s 0.6s;
}

.carousel-control-prev,
.carousel-control-next {
	position: absolute;
	top: 0;
	bottom: 0;
	z-index: 1;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	width: 15%;
	padding: 0;
	color: #fff;
	text-align: center;
	background: none;
	border: 0;
	opacity: 0.5;
	-webkit-transition: opacity 0.15s ease;
	-o-transition: opacity 0.15s ease;
	transition: opacity 0.15s ease;
}

.carousel-control-prev:hover,
.carousel-control-prev:focus,
.carousel-control-next:hover,
.carousel-control-next:focus {
	color: #fff;
	text-decoration: none;
	outline: 0;
	opacity: 0.9;
}

.carousel-control-prev {
	left: 0;
}

.carousel-control-next {
	right: 0;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
	display: inline-block;
	width: 2rem;
	height: 2rem;
	background-repeat: no-repeat;
	background-position: 50%;
	-webkit-background-size: 100% 100%;
	background-size: 100% 100%;
}

/* rtl:options: {
  "autoRename": true,
  "stringMap":[ {
    "name"    : "prev-next",
    "search"  : "prev",
    "replace" : "next"
  } ]
} */

.carousel-control-prev-icon {
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/%3e%3c/svg%3e");
}

.carousel-control-next-icon {
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}

.carousel-indicators {
	position: absolute;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 2;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	padding: 0;
	margin-right: 15%;
	margin-bottom: 1rem;
	margin-left: 15%;
	list-style: none;
}

.carousel-indicators [data-bs-target] {
	-webkit-box-sizing: content-box;
	box-sizing: content-box;
	-webkit-box-flex: 0;
	-ms-flex: 0 1 auto;
	flex: 0 1 auto;
	width: 30px;
	height: 3px;
	padding: 0;
	margin-right: 3px;
	margin-left: 3px;
	text-indent: -999px;
	cursor: pointer;
	background-color: #fff;
	-webkit-background-clip: padding-box;
	background-clip: padding-box;
	border: 0;
	border-top: 10px solid transparent;
	border-bottom: 10px solid transparent;
	opacity: 0.5;
	-webkit-transition: opacity 0.6s ease;
	-o-transition: opacity 0.6s ease;
	transition: opacity 0.6s ease;
}

.carousel-indicators .active {
	opacity: 1;
}

.carousel-caption {
	position: absolute;
	right: 15%;
	bottom: 1.25rem;
	left: 15%;
	padding-top: 1.25rem;
	padding-bottom: 1.25rem;
	color: #fff;
	text-align: center;
}

.carousel-dark .carousel-control-prev-icon,
.carousel-dark .carousel-control-next-icon {
	-webkit-filter: invert(1) grayscale(100);
	filter: invert(1) grayscale(100);
}

.carousel-dark .carousel-indicators [data-bs-target] {
	background-color: #000;
}

.carousel-dark .carousel-caption {
	color: #000;
}

.spinner-border {
	display: inline-block;
	width: 2rem;
	height: 2rem;
	vertical-align: -0.125em;
	border: 0.25em solid currentColor;
	border-right-color: transparent;
	-webkit-border-radius: 50%;
	border-radius: 50%;
	-webkit-animation: 0.75s linear infinite spinner-border;
	animation: 0.75s linear infinite spinner-border;
}

.spinner-border-sm {
	width: 1rem;
	height: 1rem;
	border-width: 0.2em;
}

.spinner-grow {
	display: inline-block;
	width: 2rem;
	height: 2rem;
	vertical-align: -0.125em;
	background-color: currentColor;
	-webkit-border-radius: 50%;
	border-radius: 50%;
	opacity: 0;
	-webkit-animation: 0.75s linear infinite spinner-grow;
	animation: 0.75s linear infinite spinner-grow;
}

.spinner-grow-sm {
	width: 1rem;
	height: 1rem;
}

.offcanvas {
	position: fixed;
	bottom: 0;
	z-index: 1050;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	-ms-flex-direction: column;
	flex-direction: column;
	max-width: 100%;
	visibility: hidden;
	background-color: #fff;
	-webkit-background-clip: padding-box;
	background-clip: padding-box;
	outline: 0;
	-webkit-box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
	box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
	-webkit-transition: -webkit-transform 0.3s ease-in-out;
	transition: -webkit-transform 0.3s ease-in-out;
	-o-transition: transform 0.3s ease-in-out;
	transition: transform 0.3s ease-in-out;
	transition: transform 0.3s ease-in-out, -webkit-transform 0.3s ease-in-out;
}

.offcanvas-header {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
	padding: 1rem 1rem;
}

.offcanvas-header .btn-close {
	padding: 0.5rem 0.5rem;
	margin-top: -0.5rem;
	margin-right: -0.5rem;
	margin-bottom: -0.5rem;
}

.offcanvas-title {
	margin-bottom: 0;
	line-height: 1.5;
}

.offcanvas-body {
	-webkit-box-flex: 1;
	-ms-flex-positive: 1;
	flex-grow: 1;
	padding: 1rem 1rem;
	overflow-y: auto;
}

.offcanvas-start {
	top: 0;
	left: 0;
	width: 400px;
	border-right: 1px solid rgba(0, 0, 0, 0.2);
	-webkit-transform: translateX(-100%);
	-ms-transform: translateX(-100%);
	transform: translateX(-100%);
}

.offcanvas-end {
	top: 0;
	right: 0;
	width: 400px;
	border-left: 1px solid rgba(0, 0, 0, 0.2);
	-webkit-transform: translateX(100%);
	-ms-transform: translateX(100%);
	transform: translateX(100%);
}

.offcanvas-top {
	top: 0;
	right: 0;
	left: 0;
	height: 30vh;
	max-height: 100%;
	border-bottom: 1px solid rgba(0, 0, 0, 0.2);
	-webkit-transform: translateY(-100%);
	-ms-transform: translateY(-100%);
	transform: translateY(-100%);
}

.offcanvas-bottom {
	right: 0;
	left: 0;
	height: 30vh;
	max-height: 100%;
	border-top: 1px solid rgba(0, 0, 0, 0.2);
	-webkit-transform: translateY(100%);
	-ms-transform: translateY(100%);
	transform: translateY(100%);
}

.offcanvas.show {
	-webkit-transform: none;
	-ms-transform: none;
	transform: none;
}

.clearfix::after {
	display: block;
	clear: both;
	content: "";
}

.link-primary {
	color: #8231D3;
}

.link-primary:hover,
.link-primary:focus {
	color: #6827a9;
}

.link-secondary {
	color: #5840FF;
}

.link-secondary:hover,
.link-secondary:focus {
	color: #4633cc;
}

.link-success {
	color: #01B81A;
}

.link-success:hover,
.link-success:focus {
	color: #34c648;
}

.link-info {
	color: #00AAFF;
}

.link-info:hover,
.link-info:focus {
	color: #33bbff;
}

.link-infos {
	color: #00E4EC;
}

.link-infos:hover,
.link-infos:focus {
	color: #33e9f0;
}

.link-warning {
	color: #FA8B0C;
}

.link-warning:hover,
.link-warning:focus {
	color: #fba23d;
}

.link-warnings {
	color: #FFBB00;
}

.link-warnings:hover,
.link-warnings:focus {
	color: #ffc933;
}

.link-danger {
	color: #FF0F0F;
}

.link-danger:hover,
.link-danger:focus {
	color: #ff3f3f;
}

.link-dangers {
	color: #FF0F0F;
}

.link-dangers:hover,
.link-dangers:focus {
	color: #ff3f3f;
}

.link-purple {
	color: #A722F6;
}

.link-purple:hover,
.link-purple:focus {
	color: #861bc5;
}

.link-dark {
	color: #0A0A0A;
}

.link-dark:hover,
.link-dark:focus {
	color: #080808;
}

.link-light {
	color: #8C90A4;
}

.link-light:hover,
.link-light:focus {
	color: #a3a6b6;
}

.link-lighten {
	color: #A0A0A0;
}

.link-lighten:hover,
.link-lighten:focus {
	color: #b3b3b3;
}

.link-light-gray {
	color: #8C90A4;
}

.link-light-gray:hover,
.link-light-gray:focus {
	color: #a3a6b6;
}

.link-text {
	color: #666d92;
}

.link-text:hover,
.link-text:focus {
	color: #525775;
}

.link-gray {
	color: #404040;
}

.link-gray:hover,
.link-gray:focus {
	color: #333333;
}

.link-third {
	color: #8231D3;
}

.link-third:hover,
.link-third:focus {
	color: #6827a9;
}

.link-white {
	color: #ffffff;
}

.link-white:hover,
.link-white:focus {
	color: white;
}

.ratio {
	position: relative;
	width: 100%;
}

.ratio::before {
	display: block;
	padding-top: var(--bs-aspect-ratio);
	content: "";
}

.ratio > * {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}

.ratio-1x1 {
	--bs-aspect-ratio: 100%;
}

.ratio-4x3 {
	--bs-aspect-ratio: 75%;
}

.ratio-16x9 {
	--bs-aspect-ratio: 56.25%;
}

.ratio-21x9 {
	--bs-aspect-ratio: 42.8571428571%;
}

.fixed-top {
	position: fixed;
	top: 0;
	right: 0;
	left: 0;
	z-index: 1030;
}

.fixed-bottom {
	position: fixed;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 1030;
}

.sticky-top {
	position: sticky;
	top: 0;
	z-index: 1020;
}

.visually-hidden,
.visually-hidden-focusable:not(:focus):not(:focus-within) {
	position: absolute !important;
	width: 1px !important;
	height: 1px !important;
	padding: 0 !important;
	margin: -1px !important;
	overflow: hidden !important;
	clip: rect(0, 0, 0, 0) !important;
	white-space: nowrap !important;
	border: 0 !important;
}

.stretched-link::after {
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 1;
	content: "";
}

.text-truncate {
	overflow: hidden;
	-o-text-overflow: ellipsis;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.align-baseline {
	vertical-align: baseline !important;
}

.align-top {
	vertical-align: top !important;
}

.align-middle {
	vertical-align: middle !important;
}

.align-bottom {
	vertical-align: bottom !important;
}

.align-text-bottom {
	vertical-align: text-bottom !important;
}

.align-text-top {
	vertical-align: text-top !important;
}

.float-start {
	float: left !important;
}

.float-end {
	float: right !important;
}

.float-none {
	float: none !important;
}

.overflow-auto {
	overflow: auto !important;
}

.overflow-hidden {
	overflow: hidden !important;
}

.overflow-visible {
	overflow: visible !important;
}

.overflow-scroll {
	overflow: scroll !important;
}

.d-inline {
	display: inline !important;
}

.d-inline-block {
	display: inline-block !important;
}

.d-block {
	display: block !important;
}

.d-grid {
	display: grid !important;
}

.d-table {
	display: table !important;
}

.d-table-row {
	display: table-row !important;
}

.d-table-cell {
	display: table-cell !important;
}

.d-flex {
	display: -webkit-box !important;
	display: -ms-flexbox !important;
	display: flex !important;
}

.d-inline-flex {
	display: -webkit-inline-box !important;
	display: -ms-inline-flexbox !important;
	display: inline-flex !important;
}

.d-none {
	display: none !important;
}

.shadow {
	-webkit-box-shadow: 0 0.67rem 0.67rem rgba(var(--color-gray), 0.08) !important;
	box-shadow: 0 0.67rem 0.67rem rgba(var(--color-gray), 0.08) !important;
}

.shadow-sm {
	-webkit-box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
	box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.shadow-lg {
	-webkit-box-shadow: 0 0.67rem 0.67rem rgba(var(--color-gray), 0.1) !important;
	box-shadow: 0 0.67rem 0.67rem rgba(var(--color-gray), 0.1) !important;
}

.shadow-none {
	-webkit-box-shadow: none !important;
	box-shadow: none !important;
}

.position-static {
	position: static !important;
}

.position-relative {
	position: relative !important;
}

.position-absolute {
	position: absolute !important;
}

.position-fixed {
	position: fixed !important;
}

.position-sticky {
	position: sticky !important;
}

.top-0 {
	top: 0 !important;
}

.top-50 {
	top: 50% !important;
}

.top-100 {
	top: 100% !important;
}

.bottom-0 {
	bottom: 0 !important;
}

.bottom-50 {
	bottom: 50% !important;
}

.bottom-100 {
	bottom: 100% !important;
}

.start-0 {
	left: 0 !important;
}

.start-50 {
	left: 50% !important;
}

.start-100 {
	left: 100% !important;
}

.end-0 {
	right: 0 !important;
}

.end-50 {
	right: 50% !important;
}

.end-100 {
	right: 100% !important;
}

.translate-middle {
	-webkit-transform: translate(-50%, -50%) !important;
	-ms-transform: translate(-50%, -50%) !important;
	transform: translate(-50%, -50%) !important;
}

.translate-middle-x {
	-webkit-transform: translateX(-50%) !important;
	-ms-transform: translateX(-50%) !important;
	transform: translateX(-50%) !important;
}

.translate-middle-y {
	-webkit-transform: translateY(-50%) !important;
	-ms-transform: translateY(-50%) !important;
	transform: translateY(-50%) !important;
}

.border {
	border: 1px solid var(--border-color) !important;
}

.border-0 {
	border: 0 !important;
}

.border-top {
	border-top: 1px solid var(--border-color) !important;
}

.border-top-0 {
	border-top: 0 !important;
}

.border-end {
	border-right: 1px solid var(--border-color) !important;
}

.border-end-0 {
	border-right: 0 !important;
}

.border-bottom {
	border-bottom: 1px solid var(--border-color) !important;
}

.border-bottom-0 {
	border-bottom: 0 !important;
}

.border-start {
	border-left: 1px solid var(--border-color) !important;
}

.border-start-0 {
	border-left: 0 !important;
}

.border-primary {
	border-color: #8231D3 !important;
}

.border-secondary {
	border-color: #5840FF !important;
}

.border-success {
	border-color: #01B81A !important;
}

.border-info {
	border-color: #00AAFF !important;
}

.border-infos {
	border-color: #00E4EC !important;
}

.border-warning {
	border-color: #FA8B0C !important;
}

.border-warnings {
	border-color: #FFBB00 !important;
}

.border-danger {
	border-color: #FF0F0F !important;
}

.border-dangers {
	border-color: #FF0F0F !important;
}

.border-purple {
	border-color: #A722F6 !important;
}

.border-dark {
	border-color: #0A0A0A !important;
}

.border-light {
	border-color: #8C90A4 !important;
}

.border-lighten {
	border-color: #A0A0A0 !important;
}

.border-light-gray {
	border-color: #8C90A4 !important;
}

.border-text {
	border-color: #666d92 !important;
}

.border-gray {
	border-color: #404040 !important;
}

.border-third {
	border-color: #8231D3 !important;
}

.border-white {
	border-color: var(--color-white) !important;
}

.border-1 {
	border-width: 1px !important;
}

.border-2 {
	border-width: 2px !important;
}

.border-3 {
	border-width: 3px !important;
}

.border-4 {
	border-width: 4px !important;
}

.border-5 {
	border-width: 5px !important;
}

.w-25 {
	width: 25% !important;
}

.w-50 {
	width: 50% !important;
}

.w-75 {
	width: 75% !important;
}

.w-100 {
	width: 100% !important;
}

.w-auto {
	width: auto !important;
}

.mw-100 {
	max-width: 100% !important;
}

.vw-100 {
	width: 100vw !important;
}

.min-vw-100 {
	min-width: 100vw !important;
}

.h-25 {
	height: 25% !important;
}

.h-50 {
	height: 50% !important;
}

.h-75 {
	height: 75% !important;
}

.h-100 {
	height: 100% !important;
}

.h-auto {
	height: auto !important;
}

.mh-100 {
	max-height: 100% !important;
}

.vh-100 {
	height: 100vh !important;
}

.min-vh-100 {
	min-height: 100vh !important;
}

.flex-fill {
	-webkit-box-flex: 1 !important;
	-ms-flex: 1 1 auto !important;
	flex: 1 1 auto !important;
}

.flex-row {
	-webkit-box-orient: horizontal !important;
	-webkit-box-direction: normal !important;
	-ms-flex-direction: row !important;
	flex-direction: row !important;
}

.flex-column {
	-webkit-box-orient: vertical !important;
	-webkit-box-direction: normal !important;
	-ms-flex-direction: column !important;
	flex-direction: column !important;
}

.flex-row-reverse {
	-webkit-box-orient: horizontal !important;
	-webkit-box-direction: reverse !important;
	-ms-flex-direction: row-reverse !important;
	flex-direction: row-reverse !important;
}

.flex-column-reverse {
	-webkit-box-orient: vertical !important;
	-webkit-box-direction: reverse !important;
	-ms-flex-direction: column-reverse !important;
	flex-direction: column-reverse !important;
}

.flex-grow-0 {
	-webkit-box-flex: 0 !important;
	-ms-flex-positive: 0 !important;
	flex-grow: 0 !important;
}

.flex-grow-1 {
	-webkit-box-flex: 1 !important;
	-ms-flex-positive: 1 !important;
	flex-grow: 1 !important;
}

.flex-shrink-0 {
	-ms-flex-negative: 0 !important;
	flex-shrink: 0 !important;
}

.flex-shrink-1 {
	-ms-flex-negative: 1 !important;
	flex-shrink: 1 !important;
}

.flex-wrap {
	-ms-flex-wrap: wrap !important;
	flex-wrap: wrap !important;
}

.flex-nowrap {
	-ms-flex-wrap: nowrap !important;
	flex-wrap: nowrap !important;
}

.flex-wrap-reverse {
	-ms-flex-wrap: wrap-reverse !important;
	flex-wrap: wrap-reverse !important;
}

.gap-0 {
	gap: 0 !important;
}

.gap-1 {
	gap: 0.25rem !important;
}

.gap-2 {
	gap: 0.5rem !important;
}

.gap-3 {
	gap: 1rem !important;
}

.gap-4 {
	gap: 1.5rem !important;
}

.gap-5 {
	gap: 3rem !important;
}

.gap-10 {
	gap: 0.625rem !important;
}

.gap-15 {
	gap: 0.9375rem !important;
}

.gap-20 {
	gap: 1.25rem !important;
}

.gap-25 {
	gap: 1.5625rem !important;
}

.gap-30 {
	gap: 1.875rem !important;
}

.gap-35 {
	gap: 2.18753rem !important;
}

.gap-40 {
	gap: 2.5rem !important;
}

.gap-45 {
	gap: 2.8125rem !important;
}

.gap-50 {
	gap: 3.125rem !important;
}

.justify-content-start {
	-webkit-box-pack: start !important;
	-ms-flex-pack: start !important;
	justify-content: flex-start !important;
}

.justify-content-end {
	-webkit-box-pack: end !important;
	-ms-flex-pack: end !important;
	justify-content: flex-end !important;
}

.justify-content-center {
	-webkit-box-pack: center !important;
	-ms-flex-pack: center !important;
	justify-content: center !important;
}

.justify-content-between {
	-webkit-box-pack: justify !important;
	-ms-flex-pack: justify !important;
	justify-content: space-between !important;
}

.justify-content-around {
	-ms-flex-pack: distribute !important;
	justify-content: space-around !important;
}

.justify-content-evenly {
	-webkit-box-pack: space-evenly !important;
	-ms-flex-pack: space-evenly !important;
	justify-content: space-evenly !important;
}

.align-items-start {
	-webkit-box-align: start !important;
	-ms-flex-align: start !important;
	align-items: flex-start !important;
}

.align-items-end {
	-webkit-box-align: end !important;
	-ms-flex-align: end !important;
	align-items: flex-end !important;
}

.align-items-center {
	-webkit-box-align: center !important;
	-ms-flex-align: center !important;
	align-items: center !important;
}

.align-items-baseline {
	-webkit-box-align: baseline !important;
	-ms-flex-align: baseline !important;
	align-items: baseline !important;
}

.align-items-stretch {
	-webkit-box-align: stretch !important;
	-ms-flex-align: stretch !important;
	align-items: stretch !important;
}

.align-content-start {
	-ms-flex-line-pack: start !important;
	align-content: flex-start !important;
}

.align-content-end {
	-ms-flex-line-pack: end !important;
	align-content: flex-end !important;
}

.align-content-center {
	-ms-flex-line-pack: center !important;
	align-content: center !important;
}

.align-content-between {
	-ms-flex-line-pack: justify !important;
	align-content: space-between !important;
}

.align-content-around {
	-ms-flex-line-pack: distribute !important;
	align-content: space-around !important;
}

.align-content-stretch {
	-ms-flex-line-pack: stretch !important;
	align-content: stretch !important;
}

.align-self-auto {
	-ms-flex-item-align: auto !important;
	align-self: auto !important;
}

.align-self-start {
	-ms-flex-item-align: start !important;
	align-self: flex-start !important;
}

.align-self-end {
	-ms-flex-item-align: end !important;
	align-self: flex-end !important;
}

.align-self-center {
	-ms-flex-item-align: center !important;
	align-self: center !important;
}

.align-self-baseline {
	-ms-flex-item-align: baseline !important;
	align-self: baseline !important;
}

.align-self-stretch {
	-ms-flex-item-align: stretch !important;
	align-self: stretch !important;
}

.order-first {
	-webkit-box-ordinal-group: 0 !important;
	-ms-flex-order: -1 !important;
	order: -1 !important;
}

.order-0 {
	-webkit-box-ordinal-group: 1 !important;
	-ms-flex-order: 0 !important;
	order: 0 !important;
}

.order-1 {
	-webkit-box-ordinal-group: 2 !important;
	-ms-flex-order: 1 !important;
	order: 1 !important;
}

.order-2 {
	-webkit-box-ordinal-group: 3 !important;
	-ms-flex-order: 2 !important;
	order: 2 !important;
}

.order-3 {
	-webkit-box-ordinal-group: 4 !important;
	-ms-flex-order: 3 !important;
	order: 3 !important;
}

.order-4 {
	-webkit-box-ordinal-group: 5 !important;
	-ms-flex-order: 4 !important;
	order: 4 !important;
}

.order-5 {
	-webkit-box-ordinal-group: 6 !important;
	-ms-flex-order: 5 !important;
	order: 5 !important;
}

.order-last {
	-webkit-box-ordinal-group: 7 !important;
	-ms-flex-order: 6 !important;
	order: 6 !important;
}

.m-0 {
	margin: 0 !important;
}

.m-1 {
	margin: 0.25rem !important;
}

.m-2 {
	margin: 0.5rem !important;
}

.m-3 {
	margin: 1rem !important;
}

.m-4 {
	margin: 1.5rem !important;
}

.m-5 {
	margin: 3rem !important;
}

.m-10 {
	margin: 0.625rem !important;
}

.m-15 {
	margin: 0.9375rem !important;
}

.m-20 {
	margin: 1.25rem !important;
}

.m-25 {
	margin: 1.5625rem !important;
}

.m-30 {
	margin: 1.875rem !important;
}

.m-35 {
	margin: 2.18753rem !important;
}

.m-40 {
	margin: 2.5rem !important;
}

.m-45 {
	margin: 2.8125rem !important;
}

.m-50 {
	margin: 3.125rem !important;
}

.m-auto {
	margin: auto !important;
}

.mx-0 {
	margin-right: 0 !important;
	margin-left: 0 !important;
}

.mx-1 {
	margin-right: 0.25rem !important;
	margin-left: 0.25rem !important;
}

.mx-2 {
	margin-right: 0.5rem !important;
	margin-left: 0.5rem !important;
}

.mx-3 {
	margin-right: 1rem !important;
	margin-left: 1rem !important;
}

.mx-4 {
	margin-right: 1.5rem !important;
	margin-left: 1.5rem !important;
}

.mx-5 {
	margin-right: 3rem !important;
	margin-left: 3rem !important;
}

.mx-10 {
	margin-right: 0.625rem !important;
	margin-left: 0.625rem !important;
}

.mx-15 {
	margin-right: 0.9375rem !important;
	margin-left: 0.9375rem !important;
}

.mx-20 {
	margin-right: 1.25rem !important;
	margin-left: 1.25rem !important;
}

.mx-25 {
	margin-right: 1.5625rem !important;
	margin-left: 1.5625rem !important;
}

.mx-30 {
	margin-right: 1.875rem !important;
	margin-left: 1.875rem !important;
}

.mx-35 {
	margin-right: 2.18753rem !important;
	margin-left: 2.18753rem !important;
}

.mx-40 {
	margin-right: 2.5rem !important;
	margin-left: 2.5rem !important;
}

.mx-45 {
	margin-right: 2.8125rem !important;
	margin-left: 2.8125rem !important;
}

.mx-50 {
	margin-right: 3.125rem !important;
	margin-left: 3.125rem !important;
}

.mx-auto {
	margin-right: auto !important;
	margin-left: auto !important;
}

.my-0 {
	margin-top: 0 !important;
	margin-bottom: 0 !important;
}

.my-1 {
	margin-top: 0.25rem !important;
	margin-bottom: 0.25rem !important;
}

.my-2 {
	margin-top: 0.5rem !important;
	margin-bottom: 0.5rem !important;
}

.my-3 {
	margin-top: 1rem !important;
	margin-bottom: 1rem !important;
}

.my-4 {
	margin-top: 1.5rem !important;
	margin-bottom: 1.5rem !important;
}

.my-5 {
	margin-top: 3rem !important;
	margin-bottom: 3rem !important;
}

.my-10 {
	margin-top: 0.625rem !important;
	margin-bottom: 0.625rem !important;
}

.my-15 {
	margin-top: 0.9375rem !important;
	margin-bottom: 0.9375rem !important;
}

.my-20 {
	margin-top: 1.25rem !important;
	margin-bottom: 1.25rem !important;
}

.my-25 {
	margin-top: 1.5625rem !important;
	margin-bottom: 1.5625rem !important;
}

.my-30 {
	margin-top: 1.875rem !important;
	margin-bottom: 1.875rem !important;
}

.my-35 {
	margin-top: 2.18753rem !important;
	margin-bottom: 2.18753rem !important;
}

.my-40 {
	margin-top: 2.5rem !important;
	margin-bottom: 2.5rem !important;
}

.my-45 {
	margin-top: 2.8125rem !important;
	margin-bottom: 2.8125rem !important;
}

.my-50 {
	margin-top: 3.125rem !important;
	margin-bottom: 3.125rem !important;
}

.my-auto {
	margin-top: auto !important;
	margin-bottom: auto !important;
}

.mt-0 {
	margin-top: 0 !important;
}

.mt-1 {
	margin-top: 0.25rem !important;
}

.mt-2 {
	margin-top: 0.5rem !important;
}

.mt-3 {
	margin-top: 1rem !important;
}

.mt-4 {
	margin-top: 1.5rem !important;
}

.mt-5 {
	margin-top: 3rem !important;
}

.mt-10 {
	margin-top: 0.625rem !important;
}

.mt-15 {
	margin-top: 0.9375rem !important;
}

.mt-20 {
	margin-top: 1.25rem !important;
}

.mt-25 {
	margin-top: 1.5625rem !important;
}

.mt-30 {
	margin-top: 1.875rem !important;
}

.mt-35 {
	margin-top: 2.18753rem !important;
}

.mt-40 {
	margin-top: 2.5rem !important;
}

.mt-45 {
	margin-top: 2.8125rem !important;
}

.mt-50 {
	margin-top: 3.125rem !important;
}

.mt-auto {
	margin-top: auto !important;
}

.me-0 {
	margin-right: 0 !important;
}

.me-1 {
	margin-right: 0.25rem !important;
}

.me-2 {
	margin-right: 0.5rem !important;
}

.me-3 {
	margin-right: 1rem !important;
}

.me-4 {
	margin-right: 1.5rem !important;
}

.me-5 {
	margin-right: 3rem !important;
}

.me-10 {
	margin-right: 0.625rem !important;
}

.me-15 {
	margin-right: 0.9375rem !important;
}

.me-20 {
	margin-right: 1.25rem !important;
}

.me-25 {
	margin-right: 1.5625rem !important;
}

.me-30 {
	margin-right: 1.875rem !important;
}

.me-35 {
	margin-right: 2.18753rem !important;
}

.me-40 {
	margin-right: 2.5rem !important;
}

.me-45 {
	margin-right: 2.8125rem !important;
}

.me-50 {
	margin-right: 3.125rem !important;
}

.me-auto {
	margin-right: auto !important;
}

.mb-0 {
	margin-bottom: 0 !important;
}

.mb-1 {
	margin-bottom: 0.25rem !important;
}

.mb-2 {
	margin-bottom: 0.5rem !important;
}

.mb-3 {
	margin-bottom: 1rem !important;
}

.mb-4 {
	margin-bottom: 1.5rem !important;
}

.mb-5 {
	margin-bottom: 3rem !important;
}

.mb-10 {
	margin-bottom: 0.625rem !important;
}

.mb-15 {
	margin-bottom: 0.9375rem !important;
}

.mb-20 {
	margin-bottom: 1.25rem !important;
}

.mb-25 {
	margin-bottom: 1.5625rem !important;
}

.mb-30 {
	margin-bottom: 1.875rem !important;
}

.mb-35 {
	margin-bottom: 2.18753rem !important;
}

.mb-40 {
	margin-bottom: 2.5rem !important;
}

.mb-45 {
	margin-bottom: 2.8125rem !important;
}

.mb-50 {
	margin-bottom: 3.125rem !important;
}

.mb-auto {
	margin-bottom: auto !important;
}

.ms-0 {
	margin-left: 0 !important;
}

.ms-1 {
	margin-left: 0.25rem !important;
}

.ms-2 {
	margin-left: 0.5rem !important;
}

.ms-3 {
	margin-left: 1rem !important;
}

.ms-4 {
	margin-left: 1.5rem !important;
}

.ms-5 {
	margin-left: 3rem !important;
}

.ms-10 {
	margin-left: 0.625rem !important;
}

.ms-15 {
	margin-left: 0.9375rem !important;
}

.ms-20 {
	margin-left: 1.25rem !important;
}

.ms-25 {
	margin-left: 1.5625rem !important;
}

.ms-30 {
	margin-left: 1.875rem !important;
}

.ms-35 {
	margin-left: 2.18753rem !important;
}

.ms-40 {
	margin-left: 2.5rem !important;
}

.ms-45 {
	margin-left: 2.8125rem !important;
}

.ms-50 {
	margin-left: 3.125rem !important;
}

.ms-auto {
	margin-left: auto !important;
}

.m-n1 {
	margin: -0.25rem !important;
}

.m-n2 {
	margin: -0.5rem !important;
}

.m-n3 {
	margin: -1rem !important;
}

.m-n4 {
	margin: -1.5rem !important;
}

.m-n5 {
	margin: -3rem !important;
}

.m-n10 {
	margin: -0.625rem !important;
}

.m-n15 {
	margin: -0.9375rem !important;
}

.m-n20 {
	margin: -1.25rem !important;
}

.m-n25 {
	margin: -1.5625rem !important;
}

.m-n30 {
	margin: -1.875rem !important;
}

.m-n35 {
	margin: -2.18753rem !important;
}

.m-n40 {
	margin: -2.5rem !important;
}

.m-n45 {
	margin: -2.8125rem !important;
}

.m-n50 {
	margin: -3.125rem !important;
}

.mx-n1 {
	margin-right: -0.25rem !important;
	margin-left: -0.25rem !important;
}

.mx-n2 {
	margin-right: -0.5rem !important;
	margin-left: -0.5rem !important;
}

.mx-n3 {
	margin-right: -1rem !important;
	margin-left: -1rem !important;
}

.mx-n4 {
	margin-right: -1.5rem !important;
	margin-left: -1.5rem !important;
}

.mx-n5 {
	margin-right: -3rem !important;
	margin-left: -3rem !important;
}

.mx-n10 {
	margin-right: -0.625rem !important;
	margin-left: -0.625rem !important;
}

.mx-n15 {
	margin-right: -0.9375rem !important;
	margin-left: -0.9375rem !important;
}

.mx-n20 {
	margin-right: -1.25rem !important;
	margin-left: -1.25rem !important;
}

.mx-n25 {
	margin-right: -1.5625rem !important;
	margin-left: -1.5625rem !important;
}

.mx-n30 {
	margin-right: -1.875rem !important;
	margin-left: -1.875rem !important;
}

.mx-n35 {
	margin-right: -2.18753rem !important;
	margin-left: -2.18753rem !important;
}

.mx-n40 {
	margin-right: -2.5rem !important;
	margin-left: -2.5rem !important;
}

.mx-n45 {
	margin-right: -2.8125rem !important;
	margin-left: -2.8125rem !important;
}

.mx-n50 {
	margin-right: -3.125rem !important;
	margin-left: -3.125rem !important;
}

.my-n1 {
	margin-top: -0.25rem !important;
	margin-bottom: -0.25rem !important;
}

.my-n2 {
	margin-top: -0.5rem !important;
	margin-bottom: -0.5rem !important;
}

.my-n3 {
	margin-top: -1rem !important;
	margin-bottom: -1rem !important;
}

.my-n4 {
	margin-top: -1.5rem !important;
	margin-bottom: -1.5rem !important;
}

.my-n5 {
	margin-top: -3rem !important;
	margin-bottom: -3rem !important;
}

.my-n10 {
	margin-top: -0.625rem !important;
	margin-bottom: -0.625rem !important;
}

.my-n15 {
	margin-top: -0.9375rem !important;
	margin-bottom: -0.9375rem !important;
}

.my-n20 {
	margin-top: -1.25rem !important;
	margin-bottom: -1.25rem !important;
}

.my-n25 {
	margin-top: -1.5625rem !important;
	margin-bottom: -1.5625rem !important;
}

.my-n30 {
	margin-top: -1.875rem !important;
	margin-bottom: -1.875rem !important;
}

.my-n35 {
	margin-top: -2.18753rem !important;
	margin-bottom: -2.18753rem !important;
}

.my-n40 {
	margin-top: -2.5rem !important;
	margin-bottom: -2.5rem !important;
}

.my-n45 {
	margin-top: -2.8125rem !important;
	margin-bottom: -2.8125rem !important;
}

.my-n50 {
	margin-top: -3.125rem !important;
	margin-bottom: -3.125rem !important;
}

.mt-n1 {
	margin-top: -0.25rem !important;
}

.mt-n2 {
	margin-top: -0.5rem !important;
}

.mt-n3 {
	margin-top: -1rem !important;
}

.mt-n4 {
	margin-top: -1.5rem !important;
}

.mt-n5 {
	margin-top: -3rem !important;
}

.mt-n10 {
	margin-top: -0.625rem !important;
}

.mt-n15 {
	margin-top: -0.9375rem !important;
}

.mt-n20 {
	margin-top: -1.25rem !important;
}

.mt-n25 {
	margin-top: -1.5625rem !important;
}

.mt-n30 {
	margin-top: -1.875rem !important;
}

.mt-n35 {
	margin-top: -2.18753rem !important;
}

.mt-n40 {
	margin-top: -2.5rem !important;
}

.mt-n45 {
	margin-top: -2.8125rem !important;
}

.mt-n50 {
	margin-top: -3.125rem !important;
}

.me-n1 {
	margin-right: -0.25rem !important;
}

.me-n2 {
	margin-right: -0.5rem !important;
}

.me-n3 {
	margin-right: -1rem !important;
}

.me-n4 {
	margin-right: -1.5rem !important;
}

.me-n5 {
	margin-right: -3rem !important;
}

.me-n10 {
	margin-right: -0.625rem !important;
}

.me-n15 {
	margin-right: -0.9375rem !important;
}

.me-n20 {
	margin-right: -1.25rem !important;
}

.me-n25 {
	margin-right: -1.5625rem !important;
}

.me-n30 {
	margin-right: -1.875rem !important;
}

.me-n35 {
	margin-right: -2.18753rem !important;
}

.me-n40 {
	margin-right: -2.5rem !important;
}

.me-n45 {
	margin-right: -2.8125rem !important;
}

.me-n50 {
	margin-right: -3.125rem !important;
}

.mb-n1 {
	margin-bottom: -0.25rem !important;
}

.mb-n2 {
	margin-bottom: -0.5rem !important;
}

.mb-n3 {
	margin-bottom: -1rem !important;
}

.mb-n4 {
	margin-bottom: -1.5rem !important;
}

.mb-n5 {
	margin-bottom: -3rem !important;
}

.mb-n10 {
	margin-bottom: -0.625rem !important;
}

.mb-n15 {
	margin-bottom: -0.9375rem !important;
}

.mb-n20 {
	margin-bottom: -1.25rem !important;
}

.mb-n25 {
	margin-bottom: -1.5625rem !important;
}

.mb-n30 {
	margin-bottom: -1.875rem !important;
}

.mb-n35 {
	margin-bottom: -2.18753rem !important;
}

.mb-n40 {
	margin-bottom: -2.5rem !important;
}

.mb-n45 {
	margin-bottom: -2.8125rem !important;
}

.mb-n50 {
	margin-bottom: -3.125rem !important;
}

.ms-n1 {
	margin-left: -0.25rem !important;
}

.ms-n2 {
	margin-left: -0.5rem !important;
}

.ms-n3 {
	margin-left: -1rem !important;
}

.ms-n4 {
	margin-left: -1.5rem !important;
}

.ms-n5 {
	margin-left: -3rem !important;
}

.ms-n10 {
	margin-left: -0.625rem !important;
}

.ms-n15 {
	margin-left: -0.9375rem !important;
}

.ms-n20 {
	margin-left: -1.25rem !important;
}

.ms-n25 {
	margin-left: -1.5625rem !important;
}

.ms-n30 {
	margin-left: -1.875rem !important;
}

.ms-n35 {
	margin-left: -2.18753rem !important;
}

.ms-n40 {
	margin-left: -2.5rem !important;
}

.ms-n45 {
	margin-left: -2.8125rem !important;
}

.ms-n50 {
	margin-left: -3.125rem !important;
}

.p-0 {
	padding: 0 !important;
}

.p-1 {
	padding: 0.25rem !important;
}

.p-2 {
	padding: 0.5rem !important;
}

.p-3 {
	padding: 1rem !important;
}

.p-4 {
	padding: 1.5rem !important;
}

.p-5 {
	padding: 3rem !important;
}

.p-10 {
	padding: 0.625rem !important;
}

.p-15 {
	padding: 0.9375rem !important;
}

.p-20 {
	padding: 1.25rem !important;
}

.p-25 {
	padding: 1.5625rem !important;
}

.p-30 {
	padding: 1.875rem !important;
}

.p-35 {
	padding: 2.18753rem !important;
}

.p-40 {
	padding: 2.5rem !important;
}

.p-45 {
	padding: 2.8125rem !important;
}

.p-50 {
	padding: 3.125rem !important;
}

.px-0 {
	padding-right: 0 !important;
	padding-left: 0 !important;
}

.px-1 {
	padding-right: 0.25rem !important;
	padding-left: 0.25rem !important;
}

.px-2 {
	padding-right: 0.5rem !important;
	padding-left: 0.5rem !important;
}

.px-3 {
	padding-right: 1rem !important;
	padding-left: 1rem !important;
}

.px-4 {
	padding-right: 1.5rem !important;
	padding-left: 1.5rem !important;
}

.px-5 {
	padding-right: 3rem !important;
	padding-left: 3rem !important;
}

.px-10 {
	padding-right: 0.625rem !important;
	padding-left: 0.625rem !important;
}

.px-15 {
	padding-right: 0.9375rem !important;
	padding-left: 0.9375rem !important;
}

.px-20 {
	padding-right: 1.25rem !important;
	padding-left: 1.25rem !important;
}

.px-25 {
	padding-right: 1.5625rem !important;
	padding-left: 1.5625rem !important;
}

.px-30 {
	padding-right: 1.875rem !important;
	padding-left: 1.875rem !important;
}

.px-35 {
	padding-right: 2.18753rem !important;
	padding-left: 2.18753rem !important;
}

.px-40 {
	padding-right: 2.5rem !important;
	padding-left: 2.5rem !important;
}

.px-45 {
	padding-right: 2.8125rem !important;
	padding-left: 2.8125rem !important;
}

.px-50 {
	padding-right: 3.125rem !important;
	padding-left: 3.125rem !important;
}

.py-0 {
	padding-top: 0 !important;
	padding-bottom: 0 !important;
}

.py-1 {
	padding-top: 0.25rem !important;
	padding-bottom: 0.25rem !important;
}

.py-2 {
	padding-top: 0.5rem !important;
	padding-bottom: 0.5rem !important;
}

.py-3 {
	padding-top: 1rem !important;
	padding-bottom: 1rem !important;
}

.py-4 {
	padding-top: 1.5rem !important;
	padding-bottom: 1.5rem !important;
}

.py-5 {
	padding-top: 3rem !important;
	padding-bottom: 3rem !important;
}

.py-10 {
	padding-top: 0.625rem !important;
	padding-bottom: 0.625rem !important;
}

.py-15 {
	padding-top: 0.9375rem !important;
	padding-bottom: 0.9375rem !important;
}

.py-20 {
	padding-top: 1.25rem !important;
	padding-bottom: 1.25rem !important;
}

.py-25 {
	padding-top: 1.5625rem !important;
	padding-bottom: 1.5625rem !important;
}

.py-30 {
	padding-top: 1.875rem !important;
	padding-bottom: 1.875rem !important;
}

.py-35 {
	padding-top: 2.18753rem !important;
	padding-bottom: 2.18753rem !important;
}

.py-40 {
	padding-top: 2.5rem !important;
	padding-bottom: 2.5rem !important;
}

.py-45 {
	padding-top: 2.8125rem !important;
	padding-bottom: 2.8125rem !important;
}

.py-50 {
	padding-top: 3.125rem !important;
	padding-bottom: 3.125rem !important;
}

.pt-0 {
	padding-top: 0 !important;
}

.pt-1 {
	padding-top: 0.25rem !important;
}

.pt-2 {
	padding-top: 0.5rem !important;
}

.pt-3 {
	padding-top: 1rem !important;
}

.pt-4 {
	padding-top: 1.5rem !important;
}

.pt-5 {
	padding-top: 3rem !important;
}

.pt-10 {
	padding-top: 0.625rem !important;
}

.pt-15 {
	padding-top: 0.9375rem !important;
}

.pt-20 {
	padding-top: 1.25rem !important;
}

.pt-25 {
	padding-top: 1.5625rem !important;
}

.pt-30 {
	padding-top: 1.875rem !important;
}

.pt-35 {
	padding-top: 2.18753rem !important;
}

.pt-40 {
	padding-top: 2.5rem !important;
}

.pt-45 {
	padding-top: 2.8125rem !important;
}

.pt-50 {
	padding-top: 3.125rem !important;
}

.pe-0 {
	padding-right: 0 !important;
}

.pe-1 {
	padding-right: 0.25rem !important;
}

.pe-2 {
	padding-right: 0.5rem !important;
}

.pe-3 {
	padding-right: 1rem !important;
}

.pe-4 {
	padding-right: 1.5rem !important;
}

.pe-5 {
	padding-right: 3rem !important;
}

.pe-10 {
	padding-right: 0.625rem !important;
}

.pe-15 {
	padding-right: 0.9375rem !important;
}

.pe-20 {
	padding-right: 1.25rem !important;
}

.pe-25 {
	padding-right: 1.5625rem !important;
}

.pe-30 {
	padding-right: 1.875rem !important;
}

.pe-35 {
	padding-right: 2.18753rem !important;
}

.pe-40 {
	padding-right: 2.5rem !important;
}

.pe-45 {
	padding-right: 2.8125rem !important;
}

.pe-50 {
	padding-right: 3.125rem !important;
}

.pb-0 {
	padding-bottom: 0 !important;
}

.pb-1 {
	padding-bottom: 0.25rem !important;
}

.pb-2 {
	padding-bottom: 0.5rem !important;
}

.pb-3 {
	padding-bottom: 1rem !important;
}

.pb-4 {
	padding-bottom: 1.5rem !important;
}

.pb-5 {
	padding-bottom: 3rem !important;
}

.pb-10 {
	padding-bottom: 0.625rem !important;
}

.pb-15 {
	padding-bottom: 0.9375rem !important;
}

.pb-20 {
	padding-bottom: 1.25rem !important;
}

.pb-25 {
	padding-bottom: 1.5625rem !important;
}

.pb-30 {
	padding-bottom: 1.875rem !important;
}

.pb-35 {
	padding-bottom: 2.18753rem !important;
}

.pb-40 {
	padding-bottom: 2.5rem !important;
}

.pb-45 {
	padding-bottom: 2.8125rem !important;
}

.pb-50 {
	padding-bottom: 3.125rem !important;
}

.ps-0 {
	padding-left: 0 !important;
}

.ps-1 {
	padding-left: 0.25rem !important;
}

.ps-2 {
	padding-left: 0.5rem !important;
}

.ps-3 {
	padding-left: 1rem !important;
}

.ps-4 {
	padding-left: 1.5rem !important;
}

.ps-5 {
	padding-left: 3rem !important;
}

.ps-10 {
	padding-left: 0.625rem !important;
}

.ps-15 {
	padding-left: 0.9375rem !important;
}

.ps-20 {
	padding-left: 1.25rem !important;
}

.ps-25 {
	padding-left: 1.5625rem !important;
}

.ps-30 {
	padding-left: 1.875rem !important;
}

.ps-35 {
	padding-left: 2.18753rem !important;
}

.ps-40 {
	padding-left: 2.5rem !important;
}

.ps-45 {
	padding-left: 2.8125rem !important;
}

.ps-50 {
	padding-left: 3.125rem !important;
}

.font-monospace {
	font-family: var(--bs-font-monospace) !important;
}

.fs-1 {
	font-size: calc(18.4px + 1.8vw) !important;
}

.fs-2 {
	font-size: calc(17.6px + 1.2vw) !important;
}

.fs-3 {
	font-size: calc(17.2px + 0.9vw) !important;
}

.fs-4 {
	font-size: calc(16.8px + 0.6vw) !important;
}

.fs-5 {
	font-size: calc(16.4px + 0.3vw) !important;
}

.fs-6 {
	font-size: 16px !important;
}

.fst-italic {
	font-style: italic !important;
}

.fst-normal {
	font-style: normal !important;
}

.fw-light {
	font-weight: 300 !important;
}

.fw-lighter {
	font-weight: lighter !important;
}

.fw-normal {
	font-weight: 400 !important;
}

.fw-bold {
	font-weight: 700 !important;
}

.fw-bolder {
	font-weight: bolder !important;
}

.lh-1 {
	line-height: 1 !important;
}

.lh-sm {
	line-height: 1.25 !important;
}

.lh-base {
	line-height: 1.5 !important;
}

.lh-lg {
	line-height: 2 !important;
}

.text-start {
	text-align: left !important;
}

.text-end {
	text-align: right !important;
}

.text-center {
	text-align: center !important;
}

.text-decoration-none {
	text-decoration: none !important;
}

.text-decoration-underline {
	text-decoration: underline !important;
}

.text-decoration-line-through {
	text-decoration: line-through !important;
}

.text-lowercase {
	text-transform: lowercase !important;
}

.text-uppercase {
	text-transform: uppercase !important;
}

.text-capitalize {
	text-transform: capitalize !important;
}

.text-wrap {
	white-space: normal !important;
}

.text-nowrap {
	white-space: nowrap !important;
}

/* rtl:begin:remove */

.text-break {
	word-wrap: break-word !important;
	word-break: break-word !important;
}

/* rtl:end:remove */

.text-primary {
	color: #8231D3 !important;
}

.text-secondary {
	color: #5840FF !important;
}

.text-success {
	color: #01B81A !important;
}

.text-info {
	color: #00AAFF !important;
}

.text-infos {
	color: #00E4EC !important;
}

.text-warning {
	color: #FA8B0C !important;
}

.text-warnings {
	color: #FFBB00 !important;
}

.text-danger {
	color: #FF0F0F !important;
}

.text-dangers {
	color: #FF0F0F !important;
}

.text-purple {
	color: #A722F6 !important;
}

.text-dark {
	color: #0A0A0A !important;
}

.text-light {
	color: #8C90A4 !important;
}

.text-lighten {
	color: #A0A0A0 !important;
}

.text-light-gray {
	color: #8C90A4 !important;
}

.text-text {
	color: #666d92 !important;
}

.text-gray {
	color: #404040 !important;
}

.text-third {
	color: #8231D3 !important;
}

.text-white {
	color: var(--color-white) !important;
}

.text-body {
	color: var(--body-color) !important;
}

.text-muted {
	color: #6c757d !important;
}

.text-black-50 {
	color: rgba(var(--color-dark), 0.5) !important;
}

.text-white-50 {
	color: rgba(var(--color-white), 0.5) !important;
}

.text-reset {
	color: inherit !important;
}

.bg-primary {
	background-color: #8231D3 !important;
}

.bg-secondary {
	background-color: #5840FF !important;
}

.bg-success {
	background-color: #01B81A !important;
}

.bg-info {
	background-color: #00AAFF !important;
}

.bg-infos {
	background-color: #00E4EC !important;
}

.bg-warning {
	background-color: #FA8B0C !important;
}

.bg-warnings {
	background-color: #FFBB00 !important;
}

.bg-danger {
	background-color: #FF0F0F !important;
}

.bg-dangers {
	background-color: #FF0F0F !important;
}

.bg-purple {
	background-color: #A722F6 !important;
}

.bg-dark {
	background-color: #0A0A0A !important;
}

.bg-light {
	background-color: #8C90A4 !important;
}

.bg-lighten {
	background-color: #A0A0A0 !important;
}

.bg-light-gray {
	background-color: #8C90A4 !important;
}

.bg-text {
	background-color: #666d92 !important;
}

.bg-gray {
	background-color: #404040 !important;
}

.bg-third {
	background-color: #8231D3 !important;
}

.bg-white {
	background-color: var(--color-white) !important;
}

.bg-body {
	background-color: #fff !important;
}

.bg-transparent {
	background-color: transparent !important;
}

.bg-gradient {
	background-image: var(--bs-gradient) !important;
}

.user-select-all {
	-webkit-user-select: all !important;
	-moz-user-select: all !important;
	-ms-user-select: all !important;
	user-select: all !important;
}

.user-select-auto {
	-webkit-user-select: auto !important;
	-moz-user-select: auto !important;
	-ms-user-select: auto !important;
	user-select: auto !important;
}

.user-select-none {
	-webkit-user-select: none !important;
	-moz-user-select: none !important;
	-ms-user-select: none !important;
	user-select: none !important;
}

.pe-none {
	pointer-events: none !important;
}

.pe-auto {
	pointer-events: auto !important;
}

.rounded {
	-webkit-border-radius: 5px !important;
	border-radius: 5px !important;
}

.rounded-0 {
	-webkit-border-radius: 0 !important;
	border-radius: 0 !important;
}

.rounded-1 {
	-webkit-border-radius: 0.2rem !important;
	border-radius: 0.2rem !important;
}

.rounded-2 {
	-webkit-border-radius: 5px !important;
	border-radius: 5px !important;
}

.rounded-3 {
	-webkit-border-radius: 6px !important;
	border-radius: 6px !important;
}

.rounded-circle {
	-webkit-border-radius: 50% !important;
	border-radius: 50% !important;
}

.rounded-pill {
	-webkit-border-radius: 50rem !important;
	border-radius: 50rem !important;
}

.rounded-top {
	-webkit-border-top-left-radius: 5px !important;
	border-top-left-radius: 5px !important;
	-webkit-border-top-right-radius: 5px !important;
	border-top-right-radius: 5px !important;
}

.rounded-end {
	-webkit-border-top-right-radius: 5px !important;
	border-top-right-radius: 5px !important;
	-webkit-border-bottom-right-radius: 5px !important;
	border-bottom-right-radius: 5px !important;
}

.rounded-bottom {
	-webkit-border-bottom-right-radius: 5px !important;
	border-bottom-right-radius: 5px !important;
	-webkit-border-bottom-left-radius: 5px !important;
	border-bottom-left-radius: 5px !important;
}

.rounded-start {
	-webkit-border-bottom-left-radius: 5px !important;
	border-bottom-left-radius: 5px !important;
	-webkit-border-top-left-radius: 5px !important;
	border-top-left-radius: 5px !important;
}

.visible {
	visibility: visible !important;
}

.invisible {
	visibility: hidden !important;
}

@media (prefers-reduced-motion: no-preference) {

:root {
	scroll-behavior: smooth;
}

}

@media (prefers-reduced-motion: reduce) {

.form-control {
	-webkit-transition: none;
	-o-transition: none;
	transition: none;
}

.form-control::file-selector-button {
	-webkit-transition: none;
	-o-transition: none;
	transition: none;
}

.form-control::-webkit-file-upload-button {
	-webkit-transition: none;
	-o-transition: none;
	transition: none;
}

.form-select {
	-webkit-transition: none;
	-o-transition: none;
	transition: none;
}

.form-switch .form-check-input {
	-webkit-transition: none;
	-o-transition: none;
	transition: none;
}

.form-range::-webkit-slider-thumb {
	-webkit-transition: none;
	-o-transition: none;
	transition: none;
}

.form-range::-moz-range-thumb {
	-webkit-transition: none;
	-o-transition: none;
	transition: none;
}

.form-floating > label {
	-webkit-transition: none;
	-o-transition: none;
	transition: none;
}

.btn {
	-webkit-transition: none;
	-o-transition: none;
	transition: none;
}

.fade {
	-webkit-transition: none;
	-o-transition: none;
	transition: none;
}

.collapsing {
	-webkit-transition: none;
	-o-transition: none;
	transition: none;
}

.nav-link {
	-webkit-transition: none;
	-o-transition: none;
	transition: none;
}

.navbar-toggler {
	-webkit-transition: none;
	-o-transition: none;
	transition: none;
}

.accordion-button {
	-webkit-transition: none;
	-o-transition: none;
	transition: none;
}

.accordion-button::after {
	-webkit-transition: none;
	-o-transition: none;
	transition: none;
}

.page-link {
	-webkit-transition: none;
	-o-transition: none;
	transition: none;
}

.progress-bar {
	-webkit-transition: none;
	-o-transition: none;
	transition: none;
}

.progress-bar-animated {
	-webkit-animation: none;
	animation: none;
}

.modal.fade .modal-dialog {
	-webkit-transition: none;
	-o-transition: none;
	transition: none;
}

.carousel-item {
	-webkit-transition: none;
	-o-transition: none;
	transition: none;
}

.carousel-fade .active.carousel-item-start,
.carousel-fade .active.carousel-item-end {
	-webkit-transition: none;
	-o-transition: none;
	transition: none;
}

.carousel-control-prev,
.carousel-control-next {
	-webkit-transition: none;
	-o-transition: none;
	transition: none;
}

.carousel-indicators [data-bs-target] {
	-webkit-transition: none;
	-o-transition: none;
	transition: none;
}

.spinner-border,
.spinner-grow {
	-webkit-animation-duration: 1.5s;
	animation-duration: 1.5s;
}

.offcanvas {
	-webkit-transition: none;
	-o-transition: none;
	transition: none;
}

}

@media (min-width: 576px) {

.container-sm,
.container {
	max-width: 540px;
}

.col-sm {
	-webkit-box-flex: 1;
	-ms-flex: 1 0 0%;
	flex: 1 0 0%;
}

.row-cols-sm-auto > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: auto;
}

.row-cols-sm-1 > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 100%;
}

.row-cols-sm-2 > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 50%;
}

.row-cols-sm-3 > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 33.3333333333%;
}

.row-cols-sm-4 > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 25%;
}

.row-cols-sm-5 > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 20%;
}

.row-cols-sm-6 > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 16.6666666667%;
}

.col-sm-auto {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: auto;
}

.col-sm-1 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 8.33333333%;
}

.col-sm-2 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 16.66666667%;
}

.col-sm-3 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 25%;
}

.col-sm-4 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 33.33333333%;
}

.col-sm-5 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 41.66666667%;
}

.col-sm-6 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 50%;
}

.col-sm-7 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 58.33333333%;
}

.col-sm-8 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 66.66666667%;
}

.col-sm-9 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 75%;
}

.col-sm-10 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 83.33333333%;
}

.col-sm-11 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 91.66666667%;
}

.col-sm-12 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 100%;
}

.offset-sm-0 {
	margin-left: 0;
}

.offset-sm-1 {
	margin-left: 8.33333333%;
}

.offset-sm-2 {
	margin-left: 16.66666667%;
}

.offset-sm-3 {
	margin-left: 25%;
}

.offset-sm-4 {
	margin-left: 33.33333333%;
}

.offset-sm-5 {
	margin-left: 41.66666667%;
}

.offset-sm-6 {
	margin-left: 50%;
}

.offset-sm-7 {
	margin-left: 58.33333333%;
}

.offset-sm-8 {
	margin-left: 66.66666667%;
}

.offset-sm-9 {
	margin-left: 75%;
}

.offset-sm-10 {
	margin-left: 83.33333333%;
}

.offset-sm-11 {
	margin-left: 91.66666667%;
}

.g-sm-0,
.gx-sm-0 {
	--bs-gutter-x: 0;
}

.g-sm-0,
.gy-sm-0 {
	--bs-gutter-y: 0;
}

.g-sm-1,
.gx-sm-1 {
	--bs-gutter-x: 0.25rem;
}

.g-sm-1,
.gy-sm-1 {
	--bs-gutter-y: 0.25rem;
}

.g-sm-2,
.gx-sm-2 {
	--bs-gutter-x: 0.5rem;
}

.g-sm-2,
.gy-sm-2 {
	--bs-gutter-y: 0.5rem;
}

.g-sm-3,
.gx-sm-3 {
	--bs-gutter-x: 1rem;
}

.g-sm-3,
.gy-sm-3 {
	--bs-gutter-y: 1rem;
}

.g-sm-4,
.gx-sm-4 {
	--bs-gutter-x: 1.5rem;
}

.g-sm-4,
.gy-sm-4 {
	--bs-gutter-y: 1.5rem;
}

.g-sm-5,
.gx-sm-5 {
	--bs-gutter-x: 3rem;
}

.g-sm-5,
.gy-sm-5 {
	--bs-gutter-y: 3rem;
}

.dropdown-menu-sm-start {
	--bs-position: start;
}

.dropdown-menu-sm-start[data-bs-popper] {
	right: auto;
	left: 0;
}

.dropdown-menu-sm-end {
	--bs-position: end;
}

.dropdown-menu-sm-end[data-bs-popper] {
	right: 0;
	left: auto;
}

.navbar-expand-sm {
	-ms-flex-wrap: nowrap;
	flex-wrap: nowrap;
	-webkit-box-pack: start;
	-ms-flex-pack: start;
	justify-content: flex-start;
}

.navbar-expand-sm .navbar-nav {
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-ms-flex-direction: row;
	flex-direction: row;
}

.navbar-expand-sm .navbar-nav .dropdown-menu {
	position: absolute;
}

.navbar-expand-sm .navbar-nav .nav-link {
	padding-right: 0.5rem;
	padding-left: 0.5rem;
}

.navbar-expand-sm .navbar-nav-scroll {
	overflow: visible;
}

.navbar-expand-sm .navbar-collapse {
	display: -webkit-box !important;
	display: -ms-flexbox !important;
	display: flex !important;
	-ms-flex-preferred-size: auto;
	flex-basis: auto;
}

.navbar-expand-sm .navbar-toggler {
	display: none;
}

.card-group {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-ms-flex-flow: row wrap;
	flex-flow: row wrap;
}

.card-group > .card {
	-webkit-box-flex: 1;
	-ms-flex: 1 0 0%;
	flex: 1 0 0%;
	margin-bottom: 0;
}

.card-group > .card + .card {
	margin-left: 0;
	border-left: 0;
}

.card-group > .card:not(:last-child) {
	-webkit-border-top-right-radius: 0;
	border-top-right-radius: 0;
	-webkit-border-bottom-right-radius: 0;
	border-bottom-right-radius: 0;
}

.card-group > .card:not(:last-child) .card-img-top,
.card-group > .card:not(:last-child) .card-header {
	-webkit-border-top-right-radius: 0;
	border-top-right-radius: 0;
}

.card-group > .card:not(:last-child) .card-img-bottom,
.card-group > .card:not(:last-child) .card-footer {
	-webkit-border-bottom-right-radius: 0;
	border-bottom-right-radius: 0;
}

.card-group > .card:not(:first-child) {
	-webkit-border-top-left-radius: 0;
	border-top-left-radius: 0;
	-webkit-border-bottom-left-radius: 0;
	border-bottom-left-radius: 0;
}

.card-group > .card:not(:first-child) .card-img-top,
.card-group > .card:not(:first-child) .card-header {
	-webkit-border-top-left-radius: 0;
	border-top-left-radius: 0;
}

.card-group > .card:not(:first-child) .card-img-bottom,
.card-group > .card:not(:first-child) .card-footer {
	-webkit-border-bottom-left-radius: 0;
	border-bottom-left-radius: 0;
}

.list-group-horizontal-sm {
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-ms-flex-direction: row;
	flex-direction: row;
}

.list-group-horizontal-sm > .list-group-item:first-child {
	-webkit-border-bottom-left-radius: 0.25rem;
	border-bottom-left-radius: 0.25rem;
	-webkit-border-top-right-radius: 0;
	border-top-right-radius: 0;
}

.list-group-horizontal-sm > .list-group-item:last-child {
	-webkit-border-top-right-radius: 0.25rem;
	border-top-right-radius: 0.25rem;
	-webkit-border-bottom-left-radius: 0;
	border-bottom-left-radius: 0;
}

.list-group-horizontal-sm > .list-group-item.active {
	margin-top: 0;
}

.list-group-horizontal-sm > .list-group-item + .list-group-item {
	border-top-width: 1px;
	border-left-width: 0;
}

.list-group-horizontal-sm > .list-group-item + .list-group-item.active {
	margin-left: -1px;
	border-left-width: 1px;
}

.modal-dialog {
	max-width: 620px;
	margin: 1.75rem auto;
}

.modal-dialog-scrollable {
	height: calc(100% - 3.5rem);
}

.modal-dialog-centered {
	min-height: calc(100% - 3.5rem);
}

.modal-content {
	-webkit-box-shadow: 0 3px 6px -4px rgba(var(--color-dark-rgba), 0.12), 0 6px 16px 0 rgba(var(--color-dark-rgba), 0.08), 0 9px 28px 8px rgba(var(--color-dark-rgba), 0.05);
	box-shadow: 0 3px 6px -4px rgba(var(--color-dark-rgba), 0.12), 0 6px 16px 0 rgba(var(--color-dark-rgba), 0.08), 0 9px 28px 8px rgba(var(--color-dark-rgba), 0.05);
}

.modal-sm {
	max-width: 450px;
}

.sticky-sm-top {
	position: sticky;
	top: 0;
	z-index: 1020;
}

.float-sm-start {
	float: left !important;
}

.float-sm-end {
	float: right !important;
}

.float-sm-none {
	float: none !important;
}

.d-sm-inline {
	display: inline !important;
}

.d-sm-inline-block {
	display: inline-block !important;
}

.d-sm-block {
	display: block !important;
}

.d-sm-grid {
	display: grid !important;
}

.d-sm-table {
	display: table !important;
}

.d-sm-table-row {
	display: table-row !important;
}

.d-sm-table-cell {
	display: table-cell !important;
}

.d-sm-flex {
	display: -webkit-box !important;
	display: -ms-flexbox !important;
	display: flex !important;
}

.d-sm-inline-flex {
	display: -webkit-inline-box !important;
	display: -ms-inline-flexbox !important;
	display: inline-flex !important;
}

.d-sm-none {
	display: none !important;
}

.flex-sm-fill {
	-webkit-box-flex: 1 !important;
	-ms-flex: 1 1 auto !important;
	flex: 1 1 auto !important;
}

.flex-sm-row {
	-webkit-box-orient: horizontal !important;
	-webkit-box-direction: normal !important;
	-ms-flex-direction: row !important;
	flex-direction: row !important;
}

.flex-sm-column {
	-webkit-box-orient: vertical !important;
	-webkit-box-direction: normal !important;
	-ms-flex-direction: column !important;
	flex-direction: column !important;
}

.flex-sm-row-reverse {
	-webkit-box-orient: horizontal !important;
	-webkit-box-direction: reverse !important;
	-ms-flex-direction: row-reverse !important;
	flex-direction: row-reverse !important;
}

.flex-sm-column-reverse {
	-webkit-box-orient: vertical !important;
	-webkit-box-direction: reverse !important;
	-ms-flex-direction: column-reverse !important;
	flex-direction: column-reverse !important;
}

.flex-sm-grow-0 {
	-webkit-box-flex: 0 !important;
	-ms-flex-positive: 0 !important;
	flex-grow: 0 !important;
}

.flex-sm-grow-1 {
	-webkit-box-flex: 1 !important;
	-ms-flex-positive: 1 !important;
	flex-grow: 1 !important;
}

.flex-sm-shrink-0 {
	-ms-flex-negative: 0 !important;
	flex-shrink: 0 !important;
}

.flex-sm-shrink-1 {
	-ms-flex-negative: 1 !important;
	flex-shrink: 1 !important;
}

.flex-sm-wrap {
	-ms-flex-wrap: wrap !important;
	flex-wrap: wrap !important;
}

.flex-sm-nowrap {
	-ms-flex-wrap: nowrap !important;
	flex-wrap: nowrap !important;
}

.flex-sm-wrap-reverse {
	-ms-flex-wrap: wrap-reverse !important;
	flex-wrap: wrap-reverse !important;
}

.gap-sm-0 {
	gap: 0 !important;
}

.gap-sm-1 {
	gap: 0.25rem !important;
}

.gap-sm-2 {
	gap: 0.5rem !important;
}

.gap-sm-3 {
	gap: 1rem !important;
}

.gap-sm-4 {
	gap: 1.5rem !important;
}

.gap-sm-5 {
	gap: 3rem !important;
}

.gap-sm-10 {
	gap: 0.625rem !important;
}

.gap-sm-15 {
	gap: 0.9375rem !important;
}

.gap-sm-20 {
	gap: 1.25rem !important;
}

.gap-sm-25 {
	gap: 1.5625rem !important;
}

.gap-sm-30 {
	gap: 1.875rem !important;
}

.gap-sm-35 {
	gap: 2.18753rem !important;
}

.gap-sm-40 {
	gap: 2.5rem !important;
}

.gap-sm-45 {
	gap: 2.8125rem !important;
}

.gap-sm-50 {
	gap: 3.125rem !important;
}

.justify-content-sm-start {
	-webkit-box-pack: start !important;
	-ms-flex-pack: start !important;
	justify-content: flex-start !important;
}

.justify-content-sm-end {
	-webkit-box-pack: end !important;
	-ms-flex-pack: end !important;
	justify-content: flex-end !important;
}

.justify-content-sm-center {
	-webkit-box-pack: center !important;
	-ms-flex-pack: center !important;
	justify-content: center !important;
}

.justify-content-sm-between {
	-webkit-box-pack: justify !important;
	-ms-flex-pack: justify !important;
	justify-content: space-between !important;
}

.justify-content-sm-around {
	-ms-flex-pack: distribute !important;
	justify-content: space-around !important;
}

.justify-content-sm-evenly {
	-webkit-box-pack: space-evenly !important;
	-ms-flex-pack: space-evenly !important;
	justify-content: space-evenly !important;
}

.align-items-sm-start {
	-webkit-box-align: start !important;
	-ms-flex-align: start !important;
	align-items: flex-start !important;
}

.align-items-sm-end {
	-webkit-box-align: end !important;
	-ms-flex-align: end !important;
	align-items: flex-end !important;
}

.align-items-sm-center {
	-webkit-box-align: center !important;
	-ms-flex-align: center !important;
	align-items: center !important;
}

.align-items-sm-baseline {
	-webkit-box-align: baseline !important;
	-ms-flex-align: baseline !important;
	align-items: baseline !important;
}

.align-items-sm-stretch {
	-webkit-box-align: stretch !important;
	-ms-flex-align: stretch !important;
	align-items: stretch !important;
}

.align-content-sm-start {
	-ms-flex-line-pack: start !important;
	align-content: flex-start !important;
}

.align-content-sm-end {
	-ms-flex-line-pack: end !important;
	align-content: flex-end !important;
}

.align-content-sm-center {
	-ms-flex-line-pack: center !important;
	align-content: center !important;
}

.align-content-sm-between {
	-ms-flex-line-pack: justify !important;
	align-content: space-between !important;
}

.align-content-sm-around {
	-ms-flex-line-pack: distribute !important;
	align-content: space-around !important;
}

.align-content-sm-stretch {
	-ms-flex-line-pack: stretch !important;
	align-content: stretch !important;
}

.align-self-sm-auto {
	-ms-flex-item-align: auto !important;
	align-self: auto !important;
}

.align-self-sm-start {
	-ms-flex-item-align: start !important;
	align-self: flex-start !important;
}

.align-self-sm-end {
	-ms-flex-item-align: end !important;
	align-self: flex-end !important;
}

.align-self-sm-center {
	-ms-flex-item-align: center !important;
	align-self: center !important;
}

.align-self-sm-baseline {
	-ms-flex-item-align: baseline !important;
	align-self: baseline !important;
}

.align-self-sm-stretch {
	-ms-flex-item-align: stretch !important;
	align-self: stretch !important;
}

.order-sm-first {
	-webkit-box-ordinal-group: 0 !important;
	-ms-flex-order: -1 !important;
	order: -1 !important;
}

.order-sm-0 {
	-webkit-box-ordinal-group: 1 !important;
	-ms-flex-order: 0 !important;
	order: 0 !important;
}

.order-sm-1 {
	-webkit-box-ordinal-group: 2 !important;
	-ms-flex-order: 1 !important;
	order: 1 !important;
}

.order-sm-2 {
	-webkit-box-ordinal-group: 3 !important;
	-ms-flex-order: 2 !important;
	order: 2 !important;
}

.order-sm-3 {
	-webkit-box-ordinal-group: 4 !important;
	-ms-flex-order: 3 !important;
	order: 3 !important;
}

.order-sm-4 {
	-webkit-box-ordinal-group: 5 !important;
	-ms-flex-order: 4 !important;
	order: 4 !important;
}

.order-sm-5 {
	-webkit-box-ordinal-group: 6 !important;
	-ms-flex-order: 5 !important;
	order: 5 !important;
}

.order-sm-last {
	-webkit-box-ordinal-group: 7 !important;
	-ms-flex-order: 6 !important;
	order: 6 !important;
}

.m-sm-0 {
	margin: 0 !important;
}

.m-sm-1 {
	margin: 0.25rem !important;
}

.m-sm-2 {
	margin: 0.5rem !important;
}

.m-sm-3 {
	margin: 1rem !important;
}

.m-sm-4 {
	margin: 1.5rem !important;
}

.m-sm-5 {
	margin: 3rem !important;
}

.m-sm-10 {
	margin: 0.625rem !important;
}

.m-sm-15 {
	margin: 0.9375rem !important;
}

.m-sm-20 {
	margin: 1.25rem !important;
}

.m-sm-25 {
	margin: 1.5625rem !important;
}

.m-sm-30 {
	margin: 1.875rem !important;
}

.m-sm-35 {
	margin: 2.18753rem !important;
}

.m-sm-40 {
	margin: 2.5rem !important;
}

.m-sm-45 {
	margin: 2.8125rem !important;
}

.m-sm-50 {
	margin: 3.125rem !important;
}

.m-sm-auto {
	margin: auto !important;
}

.mx-sm-0 {
	margin-right: 0 !important;
	margin-left: 0 !important;
}

.mx-sm-1 {
	margin-right: 0.25rem !important;
	margin-left: 0.25rem !important;
}

.mx-sm-2 {
	margin-right: 0.5rem !important;
	margin-left: 0.5rem !important;
}

.mx-sm-3 {
	margin-right: 1rem !important;
	margin-left: 1rem !important;
}

.mx-sm-4 {
	margin-right: 1.5rem !important;
	margin-left: 1.5rem !important;
}

.mx-sm-5 {
	margin-right: 3rem !important;
	margin-left: 3rem !important;
}

.mx-sm-10 {
	margin-right: 0.625rem !important;
	margin-left: 0.625rem !important;
}

.mx-sm-15 {
	margin-right: 0.9375rem !important;
	margin-left: 0.9375rem !important;
}

.mx-sm-20 {
	margin-right: 1.25rem !important;
	margin-left: 1.25rem !important;
}

.mx-sm-25 {
	margin-right: 1.5625rem !important;
	margin-left: 1.5625rem !important;
}

.mx-sm-30 {
	margin-right: 1.875rem !important;
	margin-left: 1.875rem !important;
}

.mx-sm-35 {
	margin-right: 2.18753rem !important;
	margin-left: 2.18753rem !important;
}

.mx-sm-40 {
	margin-right: 2.5rem !important;
	margin-left: 2.5rem !important;
}

.mx-sm-45 {
	margin-right: 2.8125rem !important;
	margin-left: 2.8125rem !important;
}

.mx-sm-50 {
	margin-right: 3.125rem !important;
	margin-left: 3.125rem !important;
}

.mx-sm-auto {
	margin-right: auto !important;
	margin-left: auto !important;
}

.my-sm-0 {
	margin-top: 0 !important;
	margin-bottom: 0 !important;
}

.my-sm-1 {
	margin-top: 0.25rem !important;
	margin-bottom: 0.25rem !important;
}

.my-sm-2 {
	margin-top: 0.5rem !important;
	margin-bottom: 0.5rem !important;
}

.my-sm-3 {
	margin-top: 1rem !important;
	margin-bottom: 1rem !important;
}

.my-sm-4 {
	margin-top: 1.5rem !important;
	margin-bottom: 1.5rem !important;
}

.my-sm-5 {
	margin-top: 3rem !important;
	margin-bottom: 3rem !important;
}

.my-sm-10 {
	margin-top: 0.625rem !important;
	margin-bottom: 0.625rem !important;
}

.my-sm-15 {
	margin-top: 0.9375rem !important;
	margin-bottom: 0.9375rem !important;
}

.my-sm-20 {
	margin-top: 1.25rem !important;
	margin-bottom: 1.25rem !important;
}

.my-sm-25 {
	margin-top: 1.5625rem !important;
	margin-bottom: 1.5625rem !important;
}

.my-sm-30 {
	margin-top: 1.875rem !important;
	margin-bottom: 1.875rem !important;
}

.my-sm-35 {
	margin-top: 2.18753rem !important;
	margin-bottom: 2.18753rem !important;
}

.my-sm-40 {
	margin-top: 2.5rem !important;
	margin-bottom: 2.5rem !important;
}

.my-sm-45 {
	margin-top: 2.8125rem !important;
	margin-bottom: 2.8125rem !important;
}

.my-sm-50 {
	margin-top: 3.125rem !important;
	margin-bottom: 3.125rem !important;
}

.my-sm-auto {
	margin-top: auto !important;
	margin-bottom: auto !important;
}

.mt-sm-0 {
	margin-top: 0 !important;
}

.mt-sm-1 {
	margin-top: 0.25rem !important;
}

.mt-sm-2 {
	margin-top: 0.5rem !important;
}

.mt-sm-3 {
	margin-top: 1rem !important;
}

.mt-sm-4 {
	margin-top: 1.5rem !important;
}

.mt-sm-5 {
	margin-top: 3rem !important;
}

.mt-sm-10 {
	margin-top: 0.625rem !important;
}

.mt-sm-15 {
	margin-top: 0.9375rem !important;
}

.mt-sm-20 {
	margin-top: 1.25rem !important;
}

.mt-sm-25 {
	margin-top: 1.5625rem !important;
}

.mt-sm-30 {
	margin-top: 1.875rem !important;
}

.mt-sm-35 {
	margin-top: 2.18753rem !important;
}

.mt-sm-40 {
	margin-top: 2.5rem !important;
}

.mt-sm-45 {
	margin-top: 2.8125rem !important;
}

.mt-sm-50 {
	margin-top: 3.125rem !important;
}

.mt-sm-auto {
	margin-top: auto !important;
}

.me-sm-0 {
	margin-right: 0 !important;
}

.me-sm-1 {
	margin-right: 0.25rem !important;
}

.me-sm-2 {
	margin-right: 0.5rem !important;
}

.me-sm-3 {
	margin-right: 1rem !important;
}

.me-sm-4 {
	margin-right: 1.5rem !important;
}

.me-sm-5 {
	margin-right: 3rem !important;
}

.me-sm-10 {
	margin-right: 0.625rem !important;
}

.me-sm-15 {
	margin-right: 0.9375rem !important;
}

.me-sm-20 {
	margin-right: 1.25rem !important;
}

.me-sm-25 {
	margin-right: 1.5625rem !important;
}

.me-sm-30 {
	margin-right: 1.875rem !important;
}

.me-sm-35 {
	margin-right: 2.18753rem !important;
}

.me-sm-40 {
	margin-right: 2.5rem !important;
}

.me-sm-45 {
	margin-right: 2.8125rem !important;
}

.me-sm-50 {
	margin-right: 3.125rem !important;
}

.me-sm-auto {
	margin-right: auto !important;
}

.mb-sm-0 {
	margin-bottom: 0 !important;
}

.mb-sm-1 {
	margin-bottom: 0.25rem !important;
}

.mb-sm-2 {
	margin-bottom: 0.5rem !important;
}

.mb-sm-3 {
	margin-bottom: 1rem !important;
}

.mb-sm-4 {
	margin-bottom: 1.5rem !important;
}

.mb-sm-5 {
	margin-bottom: 3rem !important;
}

.mb-sm-10 {
	margin-bottom: 0.625rem !important;
}

.mb-sm-15 {
	margin-bottom: 0.9375rem !important;
}

.mb-sm-20 {
	margin-bottom: 1.25rem !important;
}

.mb-sm-25 {
	margin-bottom: 1.5625rem !important;
}

.mb-sm-30 {
	margin-bottom: 1.875rem !important;
}

.mb-sm-35 {
	margin-bottom: 2.18753rem !important;
}

.mb-sm-40 {
	margin-bottom: 2.5rem !important;
}

.mb-sm-45 {
	margin-bottom: 2.8125rem !important;
}

.mb-sm-50 {
	margin-bottom: 3.125rem !important;
}

.mb-sm-auto {
	margin-bottom: auto !important;
}

.ms-sm-0 {
	margin-left: 0 !important;
}

.ms-sm-1 {
	margin-left: 0.25rem !important;
}

.ms-sm-2 {
	margin-left: 0.5rem !important;
}

.ms-sm-3 {
	margin-left: 1rem !important;
}

.ms-sm-4 {
	margin-left: 1.5rem !important;
}

.ms-sm-5 {
	margin-left: 3rem !important;
}

.ms-sm-10 {
	margin-left: 0.625rem !important;
}

.ms-sm-15 {
	margin-left: 0.9375rem !important;
}

.ms-sm-20 {
	margin-left: 1.25rem !important;
}

.ms-sm-25 {
	margin-left: 1.5625rem !important;
}

.ms-sm-30 {
	margin-left: 1.875rem !important;
}

.ms-sm-35 {
	margin-left: 2.18753rem !important;
}

.ms-sm-40 {
	margin-left: 2.5rem !important;
}

.ms-sm-45 {
	margin-left: 2.8125rem !important;
}

.ms-sm-50 {
	margin-left: 3.125rem !important;
}

.ms-sm-auto {
	margin-left: auto !important;
}

.m-sm-n1 {
	margin: -0.25rem !important;
}

.m-sm-n2 {
	margin: -0.5rem !important;
}

.m-sm-n3 {
	margin: -1rem !important;
}

.m-sm-n4 {
	margin: -1.5rem !important;
}

.m-sm-n5 {
	margin: -3rem !important;
}

.m-sm-n10 {
	margin: -0.625rem !important;
}

.m-sm-n15 {
	margin: -0.9375rem !important;
}

.m-sm-n20 {
	margin: -1.25rem !important;
}

.m-sm-n25 {
	margin: -1.5625rem !important;
}

.m-sm-n30 {
	margin: -1.875rem !important;
}

.m-sm-n35 {
	margin: -2.18753rem !important;
}

.m-sm-n40 {
	margin: -2.5rem !important;
}

.m-sm-n45 {
	margin: -2.8125rem !important;
}

.m-sm-n50 {
	margin: -3.125rem !important;
}

.mx-sm-n1 {
	margin-right: -0.25rem !important;
	margin-left: -0.25rem !important;
}

.mx-sm-n2 {
	margin-right: -0.5rem !important;
	margin-left: -0.5rem !important;
}

.mx-sm-n3 {
	margin-right: -1rem !important;
	margin-left: -1rem !important;
}

.mx-sm-n4 {
	margin-right: -1.5rem !important;
	margin-left: -1.5rem !important;
}

.mx-sm-n5 {
	margin-right: -3rem !important;
	margin-left: -3rem !important;
}

.mx-sm-n10 {
	margin-right: -0.625rem !important;
	margin-left: -0.625rem !important;
}

.mx-sm-n15 {
	margin-right: -0.9375rem !important;
	margin-left: -0.9375rem !important;
}

.mx-sm-n20 {
	margin-right: -1.25rem !important;
	margin-left: -1.25rem !important;
}

.mx-sm-n25 {
	margin-right: -1.5625rem !important;
	margin-left: -1.5625rem !important;
}

.mx-sm-n30 {
	margin-right: -1.875rem !important;
	margin-left: -1.875rem !important;
}

.mx-sm-n35 {
	margin-right: -2.18753rem !important;
	margin-left: -2.18753rem !important;
}

.mx-sm-n40 {
	margin-right: -2.5rem !important;
	margin-left: -2.5rem !important;
}

.mx-sm-n45 {
	margin-right: -2.8125rem !important;
	margin-left: -2.8125rem !important;
}

.mx-sm-n50 {
	margin-right: -3.125rem !important;
	margin-left: -3.125rem !important;
}

.my-sm-n1 {
	margin-top: -0.25rem !important;
	margin-bottom: -0.25rem !important;
}

.my-sm-n2 {
	margin-top: -0.5rem !important;
	margin-bottom: -0.5rem !important;
}

.my-sm-n3 {
	margin-top: -1rem !important;
	margin-bottom: -1rem !important;
}

.my-sm-n4 {
	margin-top: -1.5rem !important;
	margin-bottom: -1.5rem !important;
}

.my-sm-n5 {
	margin-top: -3rem !important;
	margin-bottom: -3rem !important;
}

.my-sm-n10 {
	margin-top: -0.625rem !important;
	margin-bottom: -0.625rem !important;
}

.my-sm-n15 {
	margin-top: -0.9375rem !important;
	margin-bottom: -0.9375rem !important;
}

.my-sm-n20 {
	margin-top: -1.25rem !important;
	margin-bottom: -1.25rem !important;
}

.my-sm-n25 {
	margin-top: -1.5625rem !important;
	margin-bottom: -1.5625rem !important;
}

.my-sm-n30 {
	margin-top: -1.875rem !important;
	margin-bottom: -1.875rem !important;
}

.my-sm-n35 {
	margin-top: -2.18753rem !important;
	margin-bottom: -2.18753rem !important;
}

.my-sm-n40 {
	margin-top: -2.5rem !important;
	margin-bottom: -2.5rem !important;
}

.my-sm-n45 {
	margin-top: -2.8125rem !important;
	margin-bottom: -2.8125rem !important;
}

.my-sm-n50 {
	margin-top: -3.125rem !important;
	margin-bottom: -3.125rem !important;
}

.mt-sm-n1 {
	margin-top: -0.25rem !important;
}

.mt-sm-n2 {
	margin-top: -0.5rem !important;
}

.mt-sm-n3 {
	margin-top: -1rem !important;
}

.mt-sm-n4 {
	margin-top: -1.5rem !important;
}

.mt-sm-n5 {
	margin-top: -3rem !important;
}

.mt-sm-n10 {
	margin-top: -0.625rem !important;
}

.mt-sm-n15 {
	margin-top: -0.9375rem !important;
}

.mt-sm-n20 {
	margin-top: -1.25rem !important;
}

.mt-sm-n25 {
	margin-top: -1.5625rem !important;
}

.mt-sm-n30 {
	margin-top: -1.875rem !important;
}

.mt-sm-n35 {
	margin-top: -2.18753rem !important;
}

.mt-sm-n40 {
	margin-top: -2.5rem !important;
}

.mt-sm-n45 {
	margin-top: -2.8125rem !important;
}

.mt-sm-n50 {
	margin-top: -3.125rem !important;
}

.me-sm-n1 {
	margin-right: -0.25rem !important;
}

.me-sm-n2 {
	margin-right: -0.5rem !important;
}

.me-sm-n3 {
	margin-right: -1rem !important;
}

.me-sm-n4 {
	margin-right: -1.5rem !important;
}

.me-sm-n5 {
	margin-right: -3rem !important;
}

.me-sm-n10 {
	margin-right: -0.625rem !important;
}

.me-sm-n15 {
	margin-right: -0.9375rem !important;
}

.me-sm-n20 {
	margin-right: -1.25rem !important;
}

.me-sm-n25 {
	margin-right: -1.5625rem !important;
}

.me-sm-n30 {
	margin-right: -1.875rem !important;
}

.me-sm-n35 {
	margin-right: -2.18753rem !important;
}

.me-sm-n40 {
	margin-right: -2.5rem !important;
}

.me-sm-n45 {
	margin-right: -2.8125rem !important;
}

.me-sm-n50 {
	margin-right: -3.125rem !important;
}

.mb-sm-n1 {
	margin-bottom: -0.25rem !important;
}

.mb-sm-n2 {
	margin-bottom: -0.5rem !important;
}

.mb-sm-n3 {
	margin-bottom: -1rem !important;
}

.mb-sm-n4 {
	margin-bottom: -1.5rem !important;
}

.mb-sm-n5 {
	margin-bottom: -3rem !important;
}

.mb-sm-n10 {
	margin-bottom: -0.625rem !important;
}

.mb-sm-n15 {
	margin-bottom: -0.9375rem !important;
}

.mb-sm-n20 {
	margin-bottom: -1.25rem !important;
}

.mb-sm-n25 {
	margin-bottom: -1.5625rem !important;
}

.mb-sm-n30 {
	margin-bottom: -1.875rem !important;
}

.mb-sm-n35 {
	margin-bottom: -2.18753rem !important;
}

.mb-sm-n40 {
	margin-bottom: -2.5rem !important;
}

.mb-sm-n45 {
	margin-bottom: -2.8125rem !important;
}

.mb-sm-n50 {
	margin-bottom: -3.125rem !important;
}

.ms-sm-n1 {
	margin-left: -0.25rem !important;
}

.ms-sm-n2 {
	margin-left: -0.5rem !important;
}

.ms-sm-n3 {
	margin-left: -1rem !important;
}

.ms-sm-n4 {
	margin-left: -1.5rem !important;
}

.ms-sm-n5 {
	margin-left: -3rem !important;
}

.ms-sm-n10 {
	margin-left: -0.625rem !important;
}

.ms-sm-n15 {
	margin-left: -0.9375rem !important;
}

.ms-sm-n20 {
	margin-left: -1.25rem !important;
}

.ms-sm-n25 {
	margin-left: -1.5625rem !important;
}

.ms-sm-n30 {
	margin-left: -1.875rem !important;
}

.ms-sm-n35 {
	margin-left: -2.18753rem !important;
}

.ms-sm-n40 {
	margin-left: -2.5rem !important;
}

.ms-sm-n45 {
	margin-left: -2.8125rem !important;
}

.ms-sm-n50 {
	margin-left: -3.125rem !important;
}

.p-sm-0 {
	padding: 0 !important;
}

.p-sm-1 {
	padding: 0.25rem !important;
}

.p-sm-2 {
	padding: 0.5rem !important;
}

.p-sm-3 {
	padding: 1rem !important;
}

.p-sm-4 {
	padding: 1.5rem !important;
}

.p-sm-5 {
	padding: 3rem !important;
}

.p-sm-10 {
	padding: 0.625rem !important;
}

.p-sm-15 {
	padding: 0.9375rem !important;
}

.p-sm-20 {
	padding: 1.25rem !important;
}

.p-sm-25 {
	padding: 1.5625rem !important;
}

.p-sm-30 {
	padding: 1.875rem !important;
}

.p-sm-35 {
	padding: 2.18753rem !important;
}

.p-sm-40 {
	padding: 2.5rem !important;
}

.p-sm-45 {
	padding: 2.8125rem !important;
}

.p-sm-50 {
	padding: 3.125rem !important;
}

.px-sm-0 {
	padding-right: 0 !important;
	padding-left: 0 !important;
}

.px-sm-1 {
	padding-right: 0.25rem !important;
	padding-left: 0.25rem !important;
}

.px-sm-2 {
	padding-right: 0.5rem !important;
	padding-left: 0.5rem !important;
}

.px-sm-3 {
	padding-right: 1rem !important;
	padding-left: 1rem !important;
}

.px-sm-4 {
	padding-right: 1.5rem !important;
	padding-left: 1.5rem !important;
}

.px-sm-5 {
	padding-right: 3rem !important;
	padding-left: 3rem !important;
}

.px-sm-10 {
	padding-right: 0.625rem !important;
	padding-left: 0.625rem !important;
}

.px-sm-15 {
	padding-right: 0.9375rem !important;
	padding-left: 0.9375rem !important;
}

.px-sm-20 {
	padding-right: 1.25rem !important;
	padding-left: 1.25rem !important;
}

.px-sm-25 {
	padding-right: 1.5625rem !important;
	padding-left: 1.5625rem !important;
}

.px-sm-30 {
	padding-right: 1.875rem !important;
	padding-left: 1.875rem !important;
}

.px-sm-35 {
	padding-right: 2.18753rem !important;
	padding-left: 2.18753rem !important;
}

.px-sm-40 {
	padding-right: 2.5rem !important;
	padding-left: 2.5rem !important;
}

.px-sm-45 {
	padding-right: 2.8125rem !important;
	padding-left: 2.8125rem !important;
}

.px-sm-50 {
	padding-right: 3.125rem !important;
	padding-left: 3.125rem !important;
}

.py-sm-0 {
	padding-top: 0 !important;
	padding-bottom: 0 !important;
}

.py-sm-1 {
	padding-top: 0.25rem !important;
	padding-bottom: 0.25rem !important;
}

.py-sm-2 {
	padding-top: 0.5rem !important;
	padding-bottom: 0.5rem !important;
}

.py-sm-3 {
	padding-top: 1rem !important;
	padding-bottom: 1rem !important;
}

.py-sm-4 {
	padding-top: 1.5rem !important;
	padding-bottom: 1.5rem !important;
}

.py-sm-5 {
	padding-top: 3rem !important;
	padding-bottom: 3rem !important;
}

.py-sm-10 {
	padding-top: 0.625rem !important;
	padding-bottom: 0.625rem !important;
}

.py-sm-15 {
	padding-top: 0.9375rem !important;
	padding-bottom: 0.9375rem !important;
}

.py-sm-20 {
	padding-top: 1.25rem !important;
	padding-bottom: 1.25rem !important;
}

.py-sm-25 {
	padding-top: 1.5625rem !important;
	padding-bottom: 1.5625rem !important;
}

.py-sm-30 {
	padding-top: 1.875rem !important;
	padding-bottom: 1.875rem !important;
}

.py-sm-35 {
	padding-top: 2.18753rem !important;
	padding-bottom: 2.18753rem !important;
}

.py-sm-40 {
	padding-top: 2.5rem !important;
	padding-bottom: 2.5rem !important;
}

.py-sm-45 {
	padding-top: 2.8125rem !important;
	padding-bottom: 2.8125rem !important;
}

.py-sm-50 {
	padding-top: 3.125rem !important;
	padding-bottom: 3.125rem !important;
}

.pt-sm-0 {
	padding-top: 0 !important;
}

.pt-sm-1 {
	padding-top: 0.25rem !important;
}

.pt-sm-2 {
	padding-top: 0.5rem !important;
}

.pt-sm-3 {
	padding-top: 1rem !important;
}

.pt-sm-4 {
	padding-top: 1.5rem !important;
}

.pt-sm-5 {
	padding-top: 3rem !important;
}

.pt-sm-10 {
	padding-top: 0.625rem !important;
}

.pt-sm-15 {
	padding-top: 0.9375rem !important;
}

.pt-sm-20 {
	padding-top: 1.25rem !important;
}

.pt-sm-25 {
	padding-top: 1.5625rem !important;
}

.pt-sm-30 {
	padding-top: 1.875rem !important;
}

.pt-sm-35 {
	padding-top: 2.18753rem !important;
}

.pt-sm-40 {
	padding-top: 2.5rem !important;
}

.pt-sm-45 {
	padding-top: 2.8125rem !important;
}

.pt-sm-50 {
	padding-top: 3.125rem !important;
}

.pe-sm-0 {
	padding-right: 0 !important;
}

.pe-sm-1 {
	padding-right: 0.25rem !important;
}

.pe-sm-2 {
	padding-right: 0.5rem !important;
}

.pe-sm-3 {
	padding-right: 1rem !important;
}

.pe-sm-4 {
	padding-right: 1.5rem !important;
}

.pe-sm-5 {
	padding-right: 3rem !important;
}

.pe-sm-10 {
	padding-right: 0.625rem !important;
}

.pe-sm-15 {
	padding-right: 0.9375rem !important;
}

.pe-sm-20 {
	padding-right: 1.25rem !important;
}

.pe-sm-25 {
	padding-right: 1.5625rem !important;
}

.pe-sm-30 {
	padding-right: 1.875rem !important;
}

.pe-sm-35 {
	padding-right: 2.18753rem !important;
}

.pe-sm-40 {
	padding-right: 2.5rem !important;
}

.pe-sm-45 {
	padding-right: 2.8125rem !important;
}

.pe-sm-50 {
	padding-right: 3.125rem !important;
}

.pb-sm-0 {
	padding-bottom: 0 !important;
}

.pb-sm-1 {
	padding-bottom: 0.25rem !important;
}

.pb-sm-2 {
	padding-bottom: 0.5rem !important;
}

.pb-sm-3 {
	padding-bottom: 1rem !important;
}

.pb-sm-4 {
	padding-bottom: 1.5rem !important;
}

.pb-sm-5 {
	padding-bottom: 3rem !important;
}

.pb-sm-10 {
	padding-bottom: 0.625rem !important;
}

.pb-sm-15 {
	padding-bottom: 0.9375rem !important;
}

.pb-sm-20 {
	padding-bottom: 1.25rem !important;
}

.pb-sm-25 {
	padding-bottom: 1.5625rem !important;
}

.pb-sm-30 {
	padding-bottom: 1.875rem !important;
}

.pb-sm-35 {
	padding-bottom: 2.18753rem !important;
}

.pb-sm-40 {
	padding-bottom: 2.5rem !important;
}

.pb-sm-45 {
	padding-bottom: 2.8125rem !important;
}

.pb-sm-50 {
	padding-bottom: 3.125rem !important;
}

.ps-sm-0 {
	padding-left: 0 !important;
}

.ps-sm-1 {
	padding-left: 0.25rem !important;
}

.ps-sm-2 {
	padding-left: 0.5rem !important;
}

.ps-sm-3 {
	padding-left: 1rem !important;
}

.ps-sm-4 {
	padding-left: 1.5rem !important;
}

.ps-sm-5 {
	padding-left: 3rem !important;
}

.ps-sm-10 {
	padding-left: 0.625rem !important;
}

.ps-sm-15 {
	padding-left: 0.9375rem !important;
}

.ps-sm-20 {
	padding-left: 1.25rem !important;
}

.ps-sm-25 {
	padding-left: 1.5625rem !important;
}

.ps-sm-30 {
	padding-left: 1.875rem !important;
}

.ps-sm-35 {
	padding-left: 2.18753rem !important;
}

.ps-sm-40 {
	padding-left: 2.5rem !important;
}

.ps-sm-45 {
	padding-left: 2.8125rem !important;
}

.ps-sm-50 {
	padding-left: 3.125rem !important;
}

.text-sm-start {
	text-align: left !important;
}

.text-sm-end {
	text-align: right !important;
}

.text-sm-center {
	text-align: center !important;
}

}

@media (min-width: 768px) {

.container-md,
.container-sm,
.container {
	max-width: 720px;
}

.col-md {
	-webkit-box-flex: 1;
	-ms-flex: 1 0 0%;
	flex: 1 0 0%;
}

.row-cols-md-auto > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: auto;
}

.row-cols-md-1 > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 100%;
}

.row-cols-md-2 > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 50%;
}

.row-cols-md-3 > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 33.3333333333%;
}

.row-cols-md-4 > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 25%;
}

.row-cols-md-5 > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 20%;
}

.row-cols-md-6 > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 16.6666666667%;
}

.col-md-auto {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: auto;
}

.col-md-1 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 8.33333333%;
}

.col-md-2 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 16.66666667%;
}

.col-md-3 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 25%;
}

.col-md-4 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 33.33333333%;
}

.col-md-5 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 41.66666667%;
}

.col-md-6 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 50%;
}

.col-md-7 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 58.33333333%;
}

.col-md-8 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 66.66666667%;
}

.col-md-9 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 75%;
}

.col-md-10 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 83.33333333%;
}

.col-md-11 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 91.66666667%;
}

.col-md-12 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 100%;
}

.offset-md-0 {
	margin-left: 0;
}

.offset-md-1 {
	margin-left: 8.33333333%;
}

.offset-md-2 {
	margin-left: 16.66666667%;
}

.offset-md-3 {
	margin-left: 25%;
}

.offset-md-4 {
	margin-left: 33.33333333%;
}

.offset-md-5 {
	margin-left: 41.66666667%;
}

.offset-md-6 {
	margin-left: 50%;
}

.offset-md-7 {
	margin-left: 58.33333333%;
}

.offset-md-8 {
	margin-left: 66.66666667%;
}

.offset-md-9 {
	margin-left: 75%;
}

.offset-md-10 {
	margin-left: 83.33333333%;
}

.offset-md-11 {
	margin-left: 91.66666667%;
}

.g-md-0,
.gx-md-0 {
	--bs-gutter-x: 0;
}

.g-md-0,
.gy-md-0 {
	--bs-gutter-y: 0;
}

.g-md-1,
.gx-md-1 {
	--bs-gutter-x: 0.25rem;
}

.g-md-1,
.gy-md-1 {
	--bs-gutter-y: 0.25rem;
}

.g-md-2,
.gx-md-2 {
	--bs-gutter-x: 0.5rem;
}

.g-md-2,
.gy-md-2 {
	--bs-gutter-y: 0.5rem;
}

.g-md-3,
.gx-md-3 {
	--bs-gutter-x: 1rem;
}

.g-md-3,
.gy-md-3 {
	--bs-gutter-y: 1rem;
}

.g-md-4,
.gx-md-4 {
	--bs-gutter-x: 1.5rem;
}

.g-md-4,
.gy-md-4 {
	--bs-gutter-y: 1.5rem;
}

.g-md-5,
.gx-md-5 {
	--bs-gutter-x: 3rem;
}

.g-md-5,
.gy-md-5 {
	--bs-gutter-y: 3rem;
}

.dropdown-menu-md-start {
	--bs-position: start;
}

.dropdown-menu-md-start[data-bs-popper] {
	right: auto;
	left: 0;
}

.dropdown-menu-md-end {
	--bs-position: end;
}

.dropdown-menu-md-end[data-bs-popper] {
	right: 0;
	left: auto;
}

.navbar-expand-md {
	-ms-flex-wrap: nowrap;
	flex-wrap: nowrap;
	-webkit-box-pack: start;
	-ms-flex-pack: start;
	justify-content: flex-start;
}

.navbar-expand-md .navbar-nav {
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-ms-flex-direction: row;
	flex-direction: row;
}

.navbar-expand-md .navbar-nav .dropdown-menu {
	position: absolute;
}

.navbar-expand-md .navbar-nav .nav-link {
	padding-right: 0.5rem;
	padding-left: 0.5rem;
}

.navbar-expand-md .navbar-nav-scroll {
	overflow: visible;
}

.navbar-expand-md .navbar-collapse {
	display: -webkit-box !important;
	display: -ms-flexbox !important;
	display: flex !important;
	-ms-flex-preferred-size: auto;
	flex-basis: auto;
}

.navbar-expand-md .navbar-toggler {
	display: none;
}

.list-group-horizontal-md {
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-ms-flex-direction: row;
	flex-direction: row;
}

.list-group-horizontal-md > .list-group-item:first-child {
	-webkit-border-bottom-left-radius: 0.25rem;
	border-bottom-left-radius: 0.25rem;
	-webkit-border-top-right-radius: 0;
	border-top-right-radius: 0;
}

.list-group-horizontal-md > .list-group-item:last-child {
	-webkit-border-top-right-radius: 0.25rem;
	border-top-right-radius: 0.25rem;
	-webkit-border-bottom-left-radius: 0;
	border-bottom-left-radius: 0;
}

.list-group-horizontal-md > .list-group-item.active {
	margin-top: 0;
}

.list-group-horizontal-md > .list-group-item + .list-group-item {
	border-top-width: 1px;
	border-left-width: 0;
}

.list-group-horizontal-md > .list-group-item + .list-group-item.active {
	margin-left: -1px;
	border-left-width: 1px;
}

.sticky-md-top {
	position: sticky;
	top: 0;
	z-index: 1020;
}

.float-md-start {
	float: left !important;
}

.float-md-end {
	float: right !important;
}

.float-md-none {
	float: none !important;
}

.d-md-inline {
	display: inline !important;
}

.d-md-inline-block {
	display: inline-block !important;
}

.d-md-block {
	display: block !important;
}

.d-md-grid {
	display: grid !important;
}

.d-md-table {
	display: table !important;
}

.d-md-table-row {
	display: table-row !important;
}

.d-md-table-cell {
	display: table-cell !important;
}

.d-md-flex {
	display: -webkit-box !important;
	display: -ms-flexbox !important;
	display: flex !important;
}

.d-md-inline-flex {
	display: -webkit-inline-box !important;
	display: -ms-inline-flexbox !important;
	display: inline-flex !important;
}

.d-md-none {
	display: none !important;
}

.flex-md-fill {
	-webkit-box-flex: 1 !important;
	-ms-flex: 1 1 auto !important;
	flex: 1 1 auto !important;
}

.flex-md-row {
	-webkit-box-orient: horizontal !important;
	-webkit-box-direction: normal !important;
	-ms-flex-direction: row !important;
	flex-direction: row !important;
}

.flex-md-column {
	-webkit-box-orient: vertical !important;
	-webkit-box-direction: normal !important;
	-ms-flex-direction: column !important;
	flex-direction: column !important;
}

.flex-md-row-reverse {
	-webkit-box-orient: horizontal !important;
	-webkit-box-direction: reverse !important;
	-ms-flex-direction: row-reverse !important;
	flex-direction: row-reverse !important;
}

.flex-md-column-reverse {
	-webkit-box-orient: vertical !important;
	-webkit-box-direction: reverse !important;
	-ms-flex-direction: column-reverse !important;
	flex-direction: column-reverse !important;
}

.flex-md-grow-0 {
	-webkit-box-flex: 0 !important;
	-ms-flex-positive: 0 !important;
	flex-grow: 0 !important;
}

.flex-md-grow-1 {
	-webkit-box-flex: 1 !important;
	-ms-flex-positive: 1 !important;
	flex-grow: 1 !important;
}

.flex-md-shrink-0 {
	-ms-flex-negative: 0 !important;
	flex-shrink: 0 !important;
}

.flex-md-shrink-1 {
	-ms-flex-negative: 1 !important;
	flex-shrink: 1 !important;
}

.flex-md-wrap {
	-ms-flex-wrap: wrap !important;
	flex-wrap: wrap !important;
}

.flex-md-nowrap {
	-ms-flex-wrap: nowrap !important;
	flex-wrap: nowrap !important;
}

.flex-md-wrap-reverse {
	-ms-flex-wrap: wrap-reverse !important;
	flex-wrap: wrap-reverse !important;
}

.gap-md-0 {
	gap: 0 !important;
}

.gap-md-1 {
	gap: 0.25rem !important;
}

.gap-md-2 {
	gap: 0.5rem !important;
}

.gap-md-3 {
	gap: 1rem !important;
}

.gap-md-4 {
	gap: 1.5rem !important;
}

.gap-md-5 {
	gap: 3rem !important;
}

.gap-md-10 {
	gap: 0.625rem !important;
}

.gap-md-15 {
	gap: 0.9375rem !important;
}

.gap-md-20 {
	gap: 1.25rem !important;
}

.gap-md-25 {
	gap: 1.5625rem !important;
}

.gap-md-30 {
	gap: 1.875rem !important;
}

.gap-md-35 {
	gap: 2.18753rem !important;
}

.gap-md-40 {
	gap: 2.5rem !important;
}

.gap-md-45 {
	gap: 2.8125rem !important;
}

.gap-md-50 {
	gap: 3.125rem !important;
}

.justify-content-md-start {
	-webkit-box-pack: start !important;
	-ms-flex-pack: start !important;
	justify-content: flex-start !important;
}

.justify-content-md-end {
	-webkit-box-pack: end !important;
	-ms-flex-pack: end !important;
	justify-content: flex-end !important;
}

.justify-content-md-center {
	-webkit-box-pack: center !important;
	-ms-flex-pack: center !important;
	justify-content: center !important;
}

.justify-content-md-between {
	-webkit-box-pack: justify !important;
	-ms-flex-pack: justify !important;
	justify-content: space-between !important;
}

.justify-content-md-around {
	-ms-flex-pack: distribute !important;
	justify-content: space-around !important;
}

.justify-content-md-evenly {
	-webkit-box-pack: space-evenly !important;
	-ms-flex-pack: space-evenly !important;
	justify-content: space-evenly !important;
}

.align-items-md-start {
	-webkit-box-align: start !important;
	-ms-flex-align: start !important;
	align-items: flex-start !important;
}

.align-items-md-end {
	-webkit-box-align: end !important;
	-ms-flex-align: end !important;
	align-items: flex-end !important;
}

.align-items-md-center {
	-webkit-box-align: center !important;
	-ms-flex-align: center !important;
	align-items: center !important;
}

.align-items-md-baseline {
	-webkit-box-align: baseline !important;
	-ms-flex-align: baseline !important;
	align-items: baseline !important;
}

.align-items-md-stretch {
	-webkit-box-align: stretch !important;
	-ms-flex-align: stretch !important;
	align-items: stretch !important;
}

.align-content-md-start {
	-ms-flex-line-pack: start !important;
	align-content: flex-start !important;
}

.align-content-md-end {
	-ms-flex-line-pack: end !important;
	align-content: flex-end !important;
}

.align-content-md-center {
	-ms-flex-line-pack: center !important;
	align-content: center !important;
}

.align-content-md-between {
	-ms-flex-line-pack: justify !important;
	align-content: space-between !important;
}

.align-content-md-around {
	-ms-flex-line-pack: distribute !important;
	align-content: space-around !important;
}

.align-content-md-stretch {
	-ms-flex-line-pack: stretch !important;
	align-content: stretch !important;
}

.align-self-md-auto {
	-ms-flex-item-align: auto !important;
	align-self: auto !important;
}

.align-self-md-start {
	-ms-flex-item-align: start !important;
	align-self: flex-start !important;
}

.align-self-md-end {
	-ms-flex-item-align: end !important;
	align-self: flex-end !important;
}

.align-self-md-center {
	-ms-flex-item-align: center !important;
	align-self: center !important;
}

.align-self-md-baseline {
	-ms-flex-item-align: baseline !important;
	align-self: baseline !important;
}

.align-self-md-stretch {
	-ms-flex-item-align: stretch !important;
	align-self: stretch !important;
}

.order-md-first {
	-webkit-box-ordinal-group: 0 !important;
	-ms-flex-order: -1 !important;
	order: -1 !important;
}

.order-md-0 {
	-webkit-box-ordinal-group: 1 !important;
	-ms-flex-order: 0 !important;
	order: 0 !important;
}

.order-md-1 {
	-webkit-box-ordinal-group: 2 !important;
	-ms-flex-order: 1 !important;
	order: 1 !important;
}

.order-md-2 {
	-webkit-box-ordinal-group: 3 !important;
	-ms-flex-order: 2 !important;
	order: 2 !important;
}

.order-md-3 {
	-webkit-box-ordinal-group: 4 !important;
	-ms-flex-order: 3 !important;
	order: 3 !important;
}

.order-md-4 {
	-webkit-box-ordinal-group: 5 !important;
	-ms-flex-order: 4 !important;
	order: 4 !important;
}

.order-md-5 {
	-webkit-box-ordinal-group: 6 !important;
	-ms-flex-order: 5 !important;
	order: 5 !important;
}

.order-md-last {
	-webkit-box-ordinal-group: 7 !important;
	-ms-flex-order: 6 !important;
	order: 6 !important;
}

.m-md-0 {
	margin: 0 !important;
}

.m-md-1 {
	margin: 0.25rem !important;
}

.m-md-2 {
	margin: 0.5rem !important;
}

.m-md-3 {
	margin: 1rem !important;
}

.m-md-4 {
	margin: 1.5rem !important;
}

.m-md-5 {
	margin: 3rem !important;
}

.m-md-10 {
	margin: 0.625rem !important;
}

.m-md-15 {
	margin: 0.9375rem !important;
}

.m-md-20 {
	margin: 1.25rem !important;
}

.m-md-25 {
	margin: 1.5625rem !important;
}

.m-md-30 {
	margin: 1.875rem !important;
}

.m-md-35 {
	margin: 2.18753rem !important;
}

.m-md-40 {
	margin: 2.5rem !important;
}

.m-md-45 {
	margin: 2.8125rem !important;
}

.m-md-50 {
	margin: 3.125rem !important;
}

.m-md-auto {
	margin: auto !important;
}

.mx-md-0 {
	margin-right: 0 !important;
	margin-left: 0 !important;
}

.mx-md-1 {
	margin-right: 0.25rem !important;
	margin-left: 0.25rem !important;
}

.mx-md-2 {
	margin-right: 0.5rem !important;
	margin-left: 0.5rem !important;
}

.mx-md-3 {
	margin-right: 1rem !important;
	margin-left: 1rem !important;
}

.mx-md-4 {
	margin-right: 1.5rem !important;
	margin-left: 1.5rem !important;
}

.mx-md-5 {
	margin-right: 3rem !important;
	margin-left: 3rem !important;
}

.mx-md-10 {
	margin-right: 0.625rem !important;
	margin-left: 0.625rem !important;
}

.mx-md-15 {
	margin-right: 0.9375rem !important;
	margin-left: 0.9375rem !important;
}

.mx-md-20 {
	margin-right: 1.25rem !important;
	margin-left: 1.25rem !important;
}

.mx-md-25 {
	margin-right: 1.5625rem !important;
	margin-left: 1.5625rem !important;
}

.mx-md-30 {
	margin-right: 1.875rem !important;
	margin-left: 1.875rem !important;
}

.mx-md-35 {
	margin-right: 2.18753rem !important;
	margin-left: 2.18753rem !important;
}

.mx-md-40 {
	margin-right: 2.5rem !important;
	margin-left: 2.5rem !important;
}

.mx-md-45 {
	margin-right: 2.8125rem !important;
	margin-left: 2.8125rem !important;
}

.mx-md-50 {
	margin-right: 3.125rem !important;
	margin-left: 3.125rem !important;
}

.mx-md-auto {
	margin-right: auto !important;
	margin-left: auto !important;
}

.my-md-0 {
	margin-top: 0 !important;
	margin-bottom: 0 !important;
}

.my-md-1 {
	margin-top: 0.25rem !important;
	margin-bottom: 0.25rem !important;
}

.my-md-2 {
	margin-top: 0.5rem !important;
	margin-bottom: 0.5rem !important;
}

.my-md-3 {
	margin-top: 1rem !important;
	margin-bottom: 1rem !important;
}

.my-md-4 {
	margin-top: 1.5rem !important;
	margin-bottom: 1.5rem !important;
}

.my-md-5 {
	margin-top: 3rem !important;
	margin-bottom: 3rem !important;
}

.my-md-10 {
	margin-top: 0.625rem !important;
	margin-bottom: 0.625rem !important;
}

.my-md-15 {
	margin-top: 0.9375rem !important;
	margin-bottom: 0.9375rem !important;
}

.my-md-20 {
	margin-top: 1.25rem !important;
	margin-bottom: 1.25rem !important;
}

.my-md-25 {
	margin-top: 1.5625rem !important;
	margin-bottom: 1.5625rem !important;
}

.my-md-30 {
	margin-top: 1.875rem !important;
	margin-bottom: 1.875rem !important;
}

.my-md-35 {
	margin-top: 2.18753rem !important;
	margin-bottom: 2.18753rem !important;
}

.my-md-40 {
	margin-top: 2.5rem !important;
	margin-bottom: 2.5rem !important;
}

.my-md-45 {
	margin-top: 2.8125rem !important;
	margin-bottom: 2.8125rem !important;
}

.my-md-50 {
	margin-top: 3.125rem !important;
	margin-bottom: 3.125rem !important;
}

.my-md-auto {
	margin-top: auto !important;
	margin-bottom: auto !important;
}

.mt-md-0 {
	margin-top: 0 !important;
}

.mt-md-1 {
	margin-top: 0.25rem !important;
}

.mt-md-2 {
	margin-top: 0.5rem !important;
}

.mt-md-3 {
	margin-top: 1rem !important;
}

.mt-md-4 {
	margin-top: 1.5rem !important;
}

.mt-md-5 {
	margin-top: 3rem !important;
}

.mt-md-10 {
	margin-top: 0.625rem !important;
}

.mt-md-15 {
	margin-top: 0.9375rem !important;
}

.mt-md-20 {
	margin-top: 1.25rem !important;
}

.mt-md-25 {
	margin-top: 1.5625rem !important;
}

.mt-md-30 {
	margin-top: 1.875rem !important;
}

.mt-md-35 {
	margin-top: 2.18753rem !important;
}

.mt-md-40 {
	margin-top: 2.5rem !important;
}

.mt-md-45 {
	margin-top: 2.8125rem !important;
}

.mt-md-50 {
	margin-top: 3.125rem !important;
}

.mt-md-auto {
	margin-top: auto !important;
}

.me-md-0 {
	margin-right: 0 !important;
}

.me-md-1 {
	margin-right: 0.25rem !important;
}

.me-md-2 {
	margin-right: 0.5rem !important;
}

.me-md-3 {
	margin-right: 1rem !important;
}

.me-md-4 {
	margin-right: 1.5rem !important;
}

.me-md-5 {
	margin-right: 3rem !important;
}

.me-md-10 {
	margin-right: 0.625rem !important;
}

.me-md-15 {
	margin-right: 0.9375rem !important;
}

.me-md-20 {
	margin-right: 1.25rem !important;
}

.me-md-25 {
	margin-right: 1.5625rem !important;
}

.me-md-30 {
	margin-right: 1.875rem !important;
}

.me-md-35 {
	margin-right: 2.18753rem !important;
}

.me-md-40 {
	margin-right: 2.5rem !important;
}

.me-md-45 {
	margin-right: 2.8125rem !important;
}

.me-md-50 {
	margin-right: 3.125rem !important;
}

.me-md-auto {
	margin-right: auto !important;
}

.mb-md-0 {
	margin-bottom: 0 !important;
}

.mb-md-1 {
	margin-bottom: 0.25rem !important;
}

.mb-md-2 {
	margin-bottom: 0.5rem !important;
}

.mb-md-3 {
	margin-bottom: 1rem !important;
}

.mb-md-4 {
	margin-bottom: 1.5rem !important;
}

.mb-md-5 {
	margin-bottom: 3rem !important;
}

.mb-md-10 {
	margin-bottom: 0.625rem !important;
}

.mb-md-15 {
	margin-bottom: 0.9375rem !important;
}

.mb-md-20 {
	margin-bottom: 1.25rem !important;
}

.mb-md-25 {
	margin-bottom: 1.5625rem !important;
}

.mb-md-30 {
	margin-bottom: 1.875rem !important;
}

.mb-md-35 {
	margin-bottom: 2.18753rem !important;
}

.mb-md-40 {
	margin-bottom: 2.5rem !important;
}

.mb-md-45 {
	margin-bottom: 2.8125rem !important;
}

.mb-md-50 {
	margin-bottom: 3.125rem !important;
}

.mb-md-auto {
	margin-bottom: auto !important;
}

.ms-md-0 {
	margin-left: 0 !important;
}

.ms-md-1 {
	margin-left: 0.25rem !important;
}

.ms-md-2 {
	margin-left: 0.5rem !important;
}

.ms-md-3 {
	margin-left: 1rem !important;
}

.ms-md-4 {
	margin-left: 1.5rem !important;
}

.ms-md-5 {
	margin-left: 3rem !important;
}

.ms-md-10 {
	margin-left: 0.625rem !important;
}

.ms-md-15 {
	margin-left: 0.9375rem !important;
}

.ms-md-20 {
	margin-left: 1.25rem !important;
}

.ms-md-25 {
	margin-left: 1.5625rem !important;
}

.ms-md-30 {
	margin-left: 1.875rem !important;
}

.ms-md-35 {
	margin-left: 2.18753rem !important;
}

.ms-md-40 {
	margin-left: 2.5rem !important;
}

.ms-md-45 {
	margin-left: 2.8125rem !important;
}

.ms-md-50 {
	margin-left: 3.125rem !important;
}

.ms-md-auto {
	margin-left: auto !important;
}

.m-md-n1 {
	margin: -0.25rem !important;
}

.m-md-n2 {
	margin: -0.5rem !important;
}

.m-md-n3 {
	margin: -1rem !important;
}

.m-md-n4 {
	margin: -1.5rem !important;
}

.m-md-n5 {
	margin: -3rem !important;
}

.m-md-n10 {
	margin: -0.625rem !important;
}

.m-md-n15 {
	margin: -0.9375rem !important;
}

.m-md-n20 {
	margin: -1.25rem !important;
}

.m-md-n25 {
	margin: -1.5625rem !important;
}

.m-md-n30 {
	margin: -1.875rem !important;
}

.m-md-n35 {
	margin: -2.18753rem !important;
}

.m-md-n40 {
	margin: -2.5rem !important;
}

.m-md-n45 {
	margin: -2.8125rem !important;
}

.m-md-n50 {
	margin: -3.125rem !important;
}

.mx-md-n1 {
	margin-right: -0.25rem !important;
	margin-left: -0.25rem !important;
}

.mx-md-n2 {
	margin-right: -0.5rem !important;
	margin-left: -0.5rem !important;
}

.mx-md-n3 {
	margin-right: -1rem !important;
	margin-left: -1rem !important;
}

.mx-md-n4 {
	margin-right: -1.5rem !important;
	margin-left: -1.5rem !important;
}

.mx-md-n5 {
	margin-right: -3rem !important;
	margin-left: -3rem !important;
}

.mx-md-n10 {
	margin-right: -0.625rem !important;
	margin-left: -0.625rem !important;
}

.mx-md-n15 {
	margin-right: -0.9375rem !important;
	margin-left: -0.9375rem !important;
}

.mx-md-n20 {
	margin-right: -1.25rem !important;
	margin-left: -1.25rem !important;
}

.mx-md-n25 {
	margin-right: -1.5625rem !important;
	margin-left: -1.5625rem !important;
}

.mx-md-n30 {
	margin-right: -1.875rem !important;
	margin-left: -1.875rem !important;
}

.mx-md-n35 {
	margin-right: -2.18753rem !important;
	margin-left: -2.18753rem !important;
}

.mx-md-n40 {
	margin-right: -2.5rem !important;
	margin-left: -2.5rem !important;
}

.mx-md-n45 {
	margin-right: -2.8125rem !important;
	margin-left: -2.8125rem !important;
}

.mx-md-n50 {
	margin-right: -3.125rem !important;
	margin-left: -3.125rem !important;
}

.my-md-n1 {
	margin-top: -0.25rem !important;
	margin-bottom: -0.25rem !important;
}

.my-md-n2 {
	margin-top: -0.5rem !important;
	margin-bottom: -0.5rem !important;
}

.my-md-n3 {
	margin-top: -1rem !important;
	margin-bottom: -1rem !important;
}

.my-md-n4 {
	margin-top: -1.5rem !important;
	margin-bottom: -1.5rem !important;
}

.my-md-n5 {
	margin-top: -3rem !important;
	margin-bottom: -3rem !important;
}

.my-md-n10 {
	margin-top: -0.625rem !important;
	margin-bottom: -0.625rem !important;
}

.my-md-n15 {
	margin-top: -0.9375rem !important;
	margin-bottom: -0.9375rem !important;
}

.my-md-n20 {
	margin-top: -1.25rem !important;
	margin-bottom: -1.25rem !important;
}

.my-md-n25 {
	margin-top: -1.5625rem !important;
	margin-bottom: -1.5625rem !important;
}

.my-md-n30 {
	margin-top: -1.875rem !important;
	margin-bottom: -1.875rem !important;
}

.my-md-n35 {
	margin-top: -2.18753rem !important;
	margin-bottom: -2.18753rem !important;
}

.my-md-n40 {
	margin-top: -2.5rem !important;
	margin-bottom: -2.5rem !important;
}

.my-md-n45 {
	margin-top: -2.8125rem !important;
	margin-bottom: -2.8125rem !important;
}

.my-md-n50 {
	margin-top: -3.125rem !important;
	margin-bottom: -3.125rem !important;
}

.mt-md-n1 {
	margin-top: -0.25rem !important;
}

.mt-md-n2 {
	margin-top: -0.5rem !important;
}

.mt-md-n3 {
	margin-top: -1rem !important;
}

.mt-md-n4 {
	margin-top: -1.5rem !important;
}

.mt-md-n5 {
	margin-top: -3rem !important;
}

.mt-md-n10 {
	margin-top: -0.625rem !important;
}

.mt-md-n15 {
	margin-top: -0.9375rem !important;
}

.mt-md-n20 {
	margin-top: -1.25rem !important;
}

.mt-md-n25 {
	margin-top: -1.5625rem !important;
}

.mt-md-n30 {
	margin-top: -1.875rem !important;
}

.mt-md-n35 {
	margin-top: -2.18753rem !important;
}

.mt-md-n40 {
	margin-top: -2.5rem !important;
}

.mt-md-n45 {
	margin-top: -2.8125rem !important;
}

.mt-md-n50 {
	margin-top: -3.125rem !important;
}

.me-md-n1 {
	margin-right: -0.25rem !important;
}

.me-md-n2 {
	margin-right: -0.5rem !important;
}

.me-md-n3 {
	margin-right: -1rem !important;
}

.me-md-n4 {
	margin-right: -1.5rem !important;
}

.me-md-n5 {
	margin-right: -3rem !important;
}

.me-md-n10 {
	margin-right: -0.625rem !important;
}

.me-md-n15 {
	margin-right: -0.9375rem !important;
}

.me-md-n20 {
	margin-right: -1.25rem !important;
}

.me-md-n25 {
	margin-right: -1.5625rem !important;
}

.me-md-n30 {
	margin-right: -1.875rem !important;
}

.me-md-n35 {
	margin-right: -2.18753rem !important;
}

.me-md-n40 {
	margin-right: -2.5rem !important;
}

.me-md-n45 {
	margin-right: -2.8125rem !important;
}

.me-md-n50 {
	margin-right: -3.125rem !important;
}

.mb-md-n1 {
	margin-bottom: -0.25rem !important;
}

.mb-md-n2 {
	margin-bottom: -0.5rem !important;
}

.mb-md-n3 {
	margin-bottom: -1rem !important;
}

.mb-md-n4 {
	margin-bottom: -1.5rem !important;
}

.mb-md-n5 {
	margin-bottom: -3rem !important;
}

.mb-md-n10 {
	margin-bottom: -0.625rem !important;
}

.mb-md-n15 {
	margin-bottom: -0.9375rem !important;
}

.mb-md-n20 {
	margin-bottom: -1.25rem !important;
}

.mb-md-n25 {
	margin-bottom: -1.5625rem !important;
}

.mb-md-n30 {
	margin-bottom: -1.875rem !important;
}

.mb-md-n35 {
	margin-bottom: -2.18753rem !important;
}

.mb-md-n40 {
	margin-bottom: -2.5rem !important;
}

.mb-md-n45 {
	margin-bottom: -2.8125rem !important;
}

.mb-md-n50 {
	margin-bottom: -3.125rem !important;
}

.ms-md-n1 {
	margin-left: -0.25rem !important;
}

.ms-md-n2 {
	margin-left: -0.5rem !important;
}

.ms-md-n3 {
	margin-left: -1rem !important;
}

.ms-md-n4 {
	margin-left: -1.5rem !important;
}

.ms-md-n5 {
	margin-left: -3rem !important;
}

.ms-md-n10 {
	margin-left: -0.625rem !important;
}

.ms-md-n15 {
	margin-left: -0.9375rem !important;
}

.ms-md-n20 {
	margin-left: -1.25rem !important;
}

.ms-md-n25 {
	margin-left: -1.5625rem !important;
}

.ms-md-n30 {
	margin-left: -1.875rem !important;
}

.ms-md-n35 {
	margin-left: -2.18753rem !important;
}

.ms-md-n40 {
	margin-left: -2.5rem !important;
}

.ms-md-n45 {
	margin-left: -2.8125rem !important;
}

.ms-md-n50 {
	margin-left: -3.125rem !important;
}

.p-md-0 {
	padding: 0 !important;
}

.p-md-1 {
	padding: 0.25rem !important;
}

.p-md-2 {
	padding: 0.5rem !important;
}

.p-md-3 {
	padding: 1rem !important;
}

.p-md-4 {
	padding: 1.5rem !important;
}

.p-md-5 {
	padding: 3rem !important;
}

.p-md-10 {
	padding: 0.625rem !important;
}

.p-md-15 {
	padding: 0.9375rem !important;
}

.p-md-20 {
	padding: 1.25rem !important;
}

.p-md-25 {
	padding: 1.5625rem !important;
}

.p-md-30 {
	padding: 1.875rem !important;
}

.p-md-35 {
	padding: 2.18753rem !important;
}

.p-md-40 {
	padding: 2.5rem !important;
}

.p-md-45 {
	padding: 2.8125rem !important;
}

.p-md-50 {
	padding: 3.125rem !important;
}

.px-md-0 {
	padding-right: 0 !important;
	padding-left: 0 !important;
}

.px-md-1 {
	padding-right: 0.25rem !important;
	padding-left: 0.25rem !important;
}

.px-md-2 {
	padding-right: 0.5rem !important;
	padding-left: 0.5rem !important;
}

.px-md-3 {
	padding-right: 1rem !important;
	padding-left: 1rem !important;
}

.px-md-4 {
	padding-right: 1.5rem !important;
	padding-left: 1.5rem !important;
}

.px-md-5 {
	padding-right: 3rem !important;
	padding-left: 3rem !important;
}

.px-md-10 {
	padding-right: 0.625rem !important;
	padding-left: 0.625rem !important;
}

.px-md-15 {
	padding-right: 0.9375rem !important;
	padding-left: 0.9375rem !important;
}

.px-md-20 {
	padding-right: 1.25rem !important;
	padding-left: 1.25rem !important;
}

.px-md-25 {
	padding-right: 1.5625rem !important;
	padding-left: 1.5625rem !important;
}

.px-md-30 {
	padding-right: 1.875rem !important;
	padding-left: 1.875rem !important;
}

.px-md-35 {
	padding-right: 2.18753rem !important;
	padding-left: 2.18753rem !important;
}

.px-md-40 {
	padding-right: 2.5rem !important;
	padding-left: 2.5rem !important;
}

.px-md-45 {
	padding-right: 2.8125rem !important;
	padding-left: 2.8125rem !important;
}

.px-md-50 {
	padding-right: 3.125rem !important;
	padding-left: 3.125rem !important;
}

.py-md-0 {
	padding-top: 0 !important;
	padding-bottom: 0 !important;
}

.py-md-1 {
	padding-top: 0.25rem !important;
	padding-bottom: 0.25rem !important;
}

.py-md-2 {
	padding-top: 0.5rem !important;
	padding-bottom: 0.5rem !important;
}

.py-md-3 {
	padding-top: 1rem !important;
	padding-bottom: 1rem !important;
}

.py-md-4 {
	padding-top: 1.5rem !important;
	padding-bottom: 1.5rem !important;
}

.py-md-5 {
	padding-top: 3rem !important;
	padding-bottom: 3rem !important;
}

.py-md-10 {
	padding-top: 0.625rem !important;
	padding-bottom: 0.625rem !important;
}

.py-md-15 {
	padding-top: 0.9375rem !important;
	padding-bottom: 0.9375rem !important;
}

.py-md-20 {
	padding-top: 1.25rem !important;
	padding-bottom: 1.25rem !important;
}

.py-md-25 {
	padding-top: 1.5625rem !important;
	padding-bottom: 1.5625rem !important;
}

.py-md-30 {
	padding-top: 1.875rem !important;
	padding-bottom: 1.875rem !important;
}

.py-md-35 {
	padding-top: 2.18753rem !important;
	padding-bottom: 2.18753rem !important;
}

.py-md-40 {
	padding-top: 2.5rem !important;
	padding-bottom: 2.5rem !important;
}

.py-md-45 {
	padding-top: 2.8125rem !important;
	padding-bottom: 2.8125rem !important;
}

.py-md-50 {
	padding-top: 3.125rem !important;
	padding-bottom: 3.125rem !important;
}

.pt-md-0 {
	padding-top: 0 !important;
}

.pt-md-1 {
	padding-top: 0.25rem !important;
}

.pt-md-2 {
	padding-top: 0.5rem !important;
}

.pt-md-3 {
	padding-top: 1rem !important;
}

.pt-md-4 {
	padding-top: 1.5rem !important;
}

.pt-md-5 {
	padding-top: 3rem !important;
}

.pt-md-10 {
	padding-top: 0.625rem !important;
}

.pt-md-15 {
	padding-top: 0.9375rem !important;
}

.pt-md-20 {
	padding-top: 1.25rem !important;
}

.pt-md-25 {
	padding-top: 1.5625rem !important;
}

.pt-md-30 {
	padding-top: 1.875rem !important;
}

.pt-md-35 {
	padding-top: 2.18753rem !important;
}

.pt-md-40 {
	padding-top: 2.5rem !important;
}

.pt-md-45 {
	padding-top: 2.8125rem !important;
}

.pt-md-50 {
	padding-top: 3.125rem !important;
}

.pe-md-0 {
	padding-right: 0 !important;
}

.pe-md-1 {
	padding-right: 0.25rem !important;
}

.pe-md-2 {
	padding-right: 0.5rem !important;
}

.pe-md-3 {
	padding-right: 1rem !important;
}

.pe-md-4 {
	padding-right: 1.5rem !important;
}

.pe-md-5 {
	padding-right: 3rem !important;
}

.pe-md-10 {
	padding-right: 0.625rem !important;
}

.pe-md-15 {
	padding-right: 0.9375rem !important;
}

.pe-md-20 {
	padding-right: 1.25rem !important;
}

.pe-md-25 {
	padding-right: 1.5625rem !important;
}

.pe-md-30 {
	padding-right: 1.875rem !important;
}

.pe-md-35 {
	padding-right: 2.18753rem !important;
}

.pe-md-40 {
	padding-right: 2.5rem !important;
}

.pe-md-45 {
	padding-right: 2.8125rem !important;
}

.pe-md-50 {
	padding-right: 3.125rem !important;
}

.pb-md-0 {
	padding-bottom: 0 !important;
}

.pb-md-1 {
	padding-bottom: 0.25rem !important;
}

.pb-md-2 {
	padding-bottom: 0.5rem !important;
}

.pb-md-3 {
	padding-bottom: 1rem !important;
}

.pb-md-4 {
	padding-bottom: 1.5rem !important;
}

.pb-md-5 {
	padding-bottom: 3rem !important;
}

.pb-md-10 {
	padding-bottom: 0.625rem !important;
}

.pb-md-15 {
	padding-bottom: 0.9375rem !important;
}

.pb-md-20 {
	padding-bottom: 1.25rem !important;
}

.pb-md-25 {
	padding-bottom: 1.5625rem !important;
}

.pb-md-30 {
	padding-bottom: 1.875rem !important;
}

.pb-md-35 {
	padding-bottom: 2.18753rem !important;
}

.pb-md-40 {
	padding-bottom: 2.5rem !important;
}

.pb-md-45 {
	padding-bottom: 2.8125rem !important;
}

.pb-md-50 {
	padding-bottom: 3.125rem !important;
}

.ps-md-0 {
	padding-left: 0 !important;
}

.ps-md-1 {
	padding-left: 0.25rem !important;
}

.ps-md-2 {
	padding-left: 0.5rem !important;
}

.ps-md-3 {
	padding-left: 1rem !important;
}

.ps-md-4 {
	padding-left: 1.5rem !important;
}

.ps-md-5 {
	padding-left: 3rem !important;
}

.ps-md-10 {
	padding-left: 0.625rem !important;
}

.ps-md-15 {
	padding-left: 0.9375rem !important;
}

.ps-md-20 {
	padding-left: 1.25rem !important;
}

.ps-md-25 {
	padding-left: 1.5625rem !important;
}

.ps-md-30 {
	padding-left: 1.875rem !important;
}

.ps-md-35 {
	padding-left: 2.18753rem !important;
}

.ps-md-40 {
	padding-left: 2.5rem !important;
}

.ps-md-45 {
	padding-left: 2.8125rem !important;
}

.ps-md-50 {
	padding-left: 3.125rem !important;
}

.text-md-start {
	text-align: left !important;
}

.text-md-end {
	text-align: right !important;
}

.text-md-center {
	text-align: center !important;
}

}

@media (min-width: 992px) {

.container-lg,
.container-md,
.container-sm,
.container {
	max-width: 960px;
}

.col-lg {
	-webkit-box-flex: 1;
	-ms-flex: 1 0 0%;
	flex: 1 0 0%;
}

.row-cols-lg-auto > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: auto;
}

.row-cols-lg-1 > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 100%;
}

.row-cols-lg-2 > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 50%;
}

.row-cols-lg-3 > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 33.3333333333%;
}

.row-cols-lg-4 > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 25%;
}

.row-cols-lg-5 > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 20%;
}

.row-cols-lg-6 > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 16.6666666667%;
}

.col-lg-auto {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: auto;
}

.col-lg-1 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 8.33333333%;
}

.col-lg-2 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 16.66666667%;
}

.col-lg-3 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 25%;
}

.col-lg-4 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 33.33333333%;
}

.col-lg-5 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 41.66666667%;
}

.col-lg-6 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 50%;
}

.col-lg-7 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 58.33333333%;
}

.col-lg-8 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 66.66666667%;
}

.col-lg-9 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 75%;
}

.col-lg-10 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 83.33333333%;
}

.col-lg-11 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 91.66666667%;
}

.col-lg-12 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 100%;
}

.offset-lg-0 {
	margin-left: 0;
}

.offset-lg-1 {
	margin-left: 8.33333333%;
}

.offset-lg-2 {
	margin-left: 16.66666667%;
}

.offset-lg-3 {
	margin-left: 25%;
}

.offset-lg-4 {
	margin-left: 33.33333333%;
}

.offset-lg-5 {
	margin-left: 41.66666667%;
}

.offset-lg-6 {
	margin-left: 50%;
}

.offset-lg-7 {
	margin-left: 58.33333333%;
}

.offset-lg-8 {
	margin-left: 66.66666667%;
}

.offset-lg-9 {
	margin-left: 75%;
}

.offset-lg-10 {
	margin-left: 83.33333333%;
}

.offset-lg-11 {
	margin-left: 91.66666667%;
}

.g-lg-0,
.gx-lg-0 {
	--bs-gutter-x: 0;
}

.g-lg-0,
.gy-lg-0 {
	--bs-gutter-y: 0;
}

.g-lg-1,
.gx-lg-1 {
	--bs-gutter-x: 0.25rem;
}

.g-lg-1,
.gy-lg-1 {
	--bs-gutter-y: 0.25rem;
}

.g-lg-2,
.gx-lg-2 {
	--bs-gutter-x: 0.5rem;
}

.g-lg-2,
.gy-lg-2 {
	--bs-gutter-y: 0.5rem;
}

.g-lg-3,
.gx-lg-3 {
	--bs-gutter-x: 1rem;
}

.g-lg-3,
.gy-lg-3 {
	--bs-gutter-y: 1rem;
}

.g-lg-4,
.gx-lg-4 {
	--bs-gutter-x: 1.5rem;
}

.g-lg-4,
.gy-lg-4 {
	--bs-gutter-y: 1.5rem;
}

.g-lg-5,
.gx-lg-5 {
	--bs-gutter-x: 3rem;
}

.g-lg-5,
.gy-lg-5 {
	--bs-gutter-y: 3rem;
}

.dropdown-menu-lg-start {
	--bs-position: start;
}

.dropdown-menu-lg-start[data-bs-popper] {
	right: auto;
	left: 0;
}

.dropdown-menu-lg-end {
	--bs-position: end;
}

.dropdown-menu-lg-end[data-bs-popper] {
	right: 0;
	left: auto;
}

.navbar-expand-lg {
	-ms-flex-wrap: nowrap;
	flex-wrap: nowrap;
	-webkit-box-pack: start;
	-ms-flex-pack: start;
	justify-content: flex-start;
}

.navbar-expand-lg .navbar-nav {
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-ms-flex-direction: row;
	flex-direction: row;
}

.navbar-expand-lg .navbar-nav .dropdown-menu {
	position: absolute;
}

.navbar-expand-lg .navbar-nav .nav-link {
	padding-right: 0.5rem;
	padding-left: 0.5rem;
}

.navbar-expand-lg .navbar-nav-scroll {
	overflow: visible;
}

.navbar-expand-lg .navbar-collapse {
	display: -webkit-box !important;
	display: -ms-flexbox !important;
	display: flex !important;
	-ms-flex-preferred-size: auto;
	flex-basis: auto;
}

.navbar-expand-lg .navbar-toggler {
	display: none;
}

.list-group-horizontal-lg {
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-ms-flex-direction: row;
	flex-direction: row;
}

.list-group-horizontal-lg > .list-group-item:first-child {
	-webkit-border-bottom-left-radius: 0.25rem;
	border-bottom-left-radius: 0.25rem;
	-webkit-border-top-right-radius: 0;
	border-top-right-radius: 0;
}

.list-group-horizontal-lg > .list-group-item:last-child {
	-webkit-border-top-right-radius: 0.25rem;
	border-top-right-radius: 0.25rem;
	-webkit-border-bottom-left-radius: 0;
	border-bottom-left-radius: 0;
}

.list-group-horizontal-lg > .list-group-item.active {
	margin-top: 0;
}

.list-group-horizontal-lg > .list-group-item + .list-group-item {
	border-top-width: 1px;
	border-left-width: 0;
}

.list-group-horizontal-lg > .list-group-item + .list-group-item.active {
	margin-left: -1px;
	border-left-width: 1px;
}

.modal-lg,
.modal-xl {
	max-width: 800px;
}

.sticky-lg-top {
	position: sticky;
	top: 0;
	z-index: 1020;
}

.float-lg-start {
	float: left !important;
}

.float-lg-end {
	float: right !important;
}

.float-lg-none {
	float: none !important;
}

.d-lg-inline {
	display: inline !important;
}

.d-lg-inline-block {
	display: inline-block !important;
}

.d-lg-block {
	display: block !important;
}

.d-lg-grid {
	display: grid !important;
}

.d-lg-table {
	display: table !important;
}

.d-lg-table-row {
	display: table-row !important;
}

.d-lg-table-cell {
	display: table-cell !important;
}

.d-lg-flex {
	display: -webkit-box !important;
	display: -ms-flexbox !important;
	display: flex !important;
}

.d-lg-inline-flex {
	display: -webkit-inline-box !important;
	display: -ms-inline-flexbox !important;
	display: inline-flex !important;
}

.d-lg-none {
	display: none !important;
}

.flex-lg-fill {
	-webkit-box-flex: 1 !important;
	-ms-flex: 1 1 auto !important;
	flex: 1 1 auto !important;
}

.flex-lg-row {
	-webkit-box-orient: horizontal !important;
	-webkit-box-direction: normal !important;
	-ms-flex-direction: row !important;
	flex-direction: row !important;
}

.flex-lg-column {
	-webkit-box-orient: vertical !important;
	-webkit-box-direction: normal !important;
	-ms-flex-direction: column !important;
	flex-direction: column !important;
}

.flex-lg-row-reverse {
	-webkit-box-orient: horizontal !important;
	-webkit-box-direction: reverse !important;
	-ms-flex-direction: row-reverse !important;
	flex-direction: row-reverse !important;
}

.flex-lg-column-reverse {
	-webkit-box-orient: vertical !important;
	-webkit-box-direction: reverse !important;
	-ms-flex-direction: column-reverse !important;
	flex-direction: column-reverse !important;
}

.flex-lg-grow-0 {
	-webkit-box-flex: 0 !important;
	-ms-flex-positive: 0 !important;
	flex-grow: 0 !important;
}

.flex-lg-grow-1 {
	-webkit-box-flex: 1 !important;
	-ms-flex-positive: 1 !important;
	flex-grow: 1 !important;
}

.flex-lg-shrink-0 {
	-ms-flex-negative: 0 !important;
	flex-shrink: 0 !important;
}

.flex-lg-shrink-1 {
	-ms-flex-negative: 1 !important;
	flex-shrink: 1 !important;
}

.flex-lg-wrap {
	-ms-flex-wrap: wrap !important;
	flex-wrap: wrap !important;
}

.flex-lg-nowrap {
	-ms-flex-wrap: nowrap !important;
	flex-wrap: nowrap !important;
}

.flex-lg-wrap-reverse {
	-ms-flex-wrap: wrap-reverse !important;
	flex-wrap: wrap-reverse !important;
}

.gap-lg-0 {
	gap: 0 !important;
}

.gap-lg-1 {
	gap: 0.25rem !important;
}

.gap-lg-2 {
	gap: 0.5rem !important;
}

.gap-lg-3 {
	gap: 1rem !important;
}

.gap-lg-4 {
	gap: 1.5rem !important;
}

.gap-lg-5 {
	gap: 3rem !important;
}

.gap-lg-10 {
	gap: 0.625rem !important;
}

.gap-lg-15 {
	gap: 0.9375rem !important;
}

.gap-lg-20 {
	gap: 1.25rem !important;
}

.gap-lg-25 {
	gap: 1.5625rem !important;
}

.gap-lg-30 {
	gap: 1.875rem !important;
}

.gap-lg-35 {
	gap: 2.18753rem !important;
}

.gap-lg-40 {
	gap: 2.5rem !important;
}

.gap-lg-45 {
	gap: 2.8125rem !important;
}

.gap-lg-50 {
	gap: 3.125rem !important;
}

.justify-content-lg-start {
	-webkit-box-pack: start !important;
	-ms-flex-pack: start !important;
	justify-content: flex-start !important;
}

.justify-content-lg-end {
	-webkit-box-pack: end !important;
	-ms-flex-pack: end !important;
	justify-content: flex-end !important;
}

.justify-content-lg-center {
	-webkit-box-pack: center !important;
	-ms-flex-pack: center !important;
	justify-content: center !important;
}

.justify-content-lg-between {
	-webkit-box-pack: justify !important;
	-ms-flex-pack: justify !important;
	justify-content: space-between !important;
}

.justify-content-lg-around {
	-ms-flex-pack: distribute !important;
	justify-content: space-around !important;
}

.justify-content-lg-evenly {
	-webkit-box-pack: space-evenly !important;
	-ms-flex-pack: space-evenly !important;
	justify-content: space-evenly !important;
}

.align-items-lg-start {
	-webkit-box-align: start !important;
	-ms-flex-align: start !important;
	align-items: flex-start !important;
}

.align-items-lg-end {
	-webkit-box-align: end !important;
	-ms-flex-align: end !important;
	align-items: flex-end !important;
}

.align-items-lg-center {
	-webkit-box-align: center !important;
	-ms-flex-align: center !important;
	align-items: center !important;
}

.align-items-lg-baseline {
	-webkit-box-align: baseline !important;
	-ms-flex-align: baseline !important;
	align-items: baseline !important;
}

.align-items-lg-stretch {
	-webkit-box-align: stretch !important;
	-ms-flex-align: stretch !important;
	align-items: stretch !important;
}

.align-content-lg-start {
	-ms-flex-line-pack: start !important;
	align-content: flex-start !important;
}

.align-content-lg-end {
	-ms-flex-line-pack: end !important;
	align-content: flex-end !important;
}

.align-content-lg-center {
	-ms-flex-line-pack: center !important;
	align-content: center !important;
}

.align-content-lg-between {
	-ms-flex-line-pack: justify !important;
	align-content: space-between !important;
}

.align-content-lg-around {
	-ms-flex-line-pack: distribute !important;
	align-content: space-around !important;
}

.align-content-lg-stretch {
	-ms-flex-line-pack: stretch !important;
	align-content: stretch !important;
}

.align-self-lg-auto {
	-ms-flex-item-align: auto !important;
	align-self: auto !important;
}

.align-self-lg-start {
	-ms-flex-item-align: start !important;
	align-self: flex-start !important;
}

.align-self-lg-end {
	-ms-flex-item-align: end !important;
	align-self: flex-end !important;
}

.align-self-lg-center {
	-ms-flex-item-align: center !important;
	align-self: center !important;
}

.align-self-lg-baseline {
	-ms-flex-item-align: baseline !important;
	align-self: baseline !important;
}

.align-self-lg-stretch {
	-ms-flex-item-align: stretch !important;
	align-self: stretch !important;
}

.order-lg-first {
	-webkit-box-ordinal-group: 0 !important;
	-ms-flex-order: -1 !important;
	order: -1 !important;
}

.order-lg-0 {
	-webkit-box-ordinal-group: 1 !important;
	-ms-flex-order: 0 !important;
	order: 0 !important;
}

.order-lg-1 {
	-webkit-box-ordinal-group: 2 !important;
	-ms-flex-order: 1 !important;
	order: 1 !important;
}

.order-lg-2 {
	-webkit-box-ordinal-group: 3 !important;
	-ms-flex-order: 2 !important;
	order: 2 !important;
}

.order-lg-3 {
	-webkit-box-ordinal-group: 4 !important;
	-ms-flex-order: 3 !important;
	order: 3 !important;
}

.order-lg-4 {
	-webkit-box-ordinal-group: 5 !important;
	-ms-flex-order: 4 !important;
	order: 4 !important;
}

.order-lg-5 {
	-webkit-box-ordinal-group: 6 !important;
	-ms-flex-order: 5 !important;
	order: 5 !important;
}

.order-lg-last {
	-webkit-box-ordinal-group: 7 !important;
	-ms-flex-order: 6 !important;
	order: 6 !important;
}

.m-lg-0 {
	margin: 0 !important;
}

.m-lg-1 {
	margin: 0.25rem !important;
}

.m-lg-2 {
	margin: 0.5rem !important;
}

.m-lg-3 {
	margin: 1rem !important;
}

.m-lg-4 {
	margin: 1.5rem !important;
}

.m-lg-5 {
	margin: 3rem !important;
}

.m-lg-10 {
	margin: 0.625rem !important;
}

.m-lg-15 {
	margin: 0.9375rem !important;
}

.m-lg-20 {
	margin: 1.25rem !important;
}

.m-lg-25 {
	margin: 1.5625rem !important;
}

.m-lg-30 {
	margin: 1.875rem !important;
}

.m-lg-35 {
	margin: 2.18753rem !important;
}

.m-lg-40 {
	margin: 2.5rem !important;
}

.m-lg-45 {
	margin: 2.8125rem !important;
}

.m-lg-50 {
	margin: 3.125rem !important;
}

.m-lg-auto {
	margin: auto !important;
}

.mx-lg-0 {
	margin-right: 0 !important;
	margin-left: 0 !important;
}

.mx-lg-1 {
	margin-right: 0.25rem !important;
	margin-left: 0.25rem !important;
}

.mx-lg-2 {
	margin-right: 0.5rem !important;
	margin-left: 0.5rem !important;
}

.mx-lg-3 {
	margin-right: 1rem !important;
	margin-left: 1rem !important;
}

.mx-lg-4 {
	margin-right: 1.5rem !important;
	margin-left: 1.5rem !important;
}

.mx-lg-5 {
	margin-right: 3rem !important;
	margin-left: 3rem !important;
}

.mx-lg-10 {
	margin-right: 0.625rem !important;
	margin-left: 0.625rem !important;
}

.mx-lg-15 {
	margin-right: 0.9375rem !important;
	margin-left: 0.9375rem !important;
}

.mx-lg-20 {
	margin-right: 1.25rem !important;
	margin-left: 1.25rem !important;
}

.mx-lg-25 {
	margin-right: 1.5625rem !important;
	margin-left: 1.5625rem !important;
}

.mx-lg-30 {
	margin-right: 1.875rem !important;
	margin-left: 1.875rem !important;
}

.mx-lg-35 {
	margin-right: 2.18753rem !important;
	margin-left: 2.18753rem !important;
}

.mx-lg-40 {
	margin-right: 2.5rem !important;
	margin-left: 2.5rem !important;
}

.mx-lg-45 {
	margin-right: 2.8125rem !important;
	margin-left: 2.8125rem !important;
}

.mx-lg-50 {
	margin-right: 3.125rem !important;
	margin-left: 3.125rem !important;
}

.mx-lg-auto {
	margin-right: auto !important;
	margin-left: auto !important;
}

.my-lg-0 {
	margin-top: 0 !important;
	margin-bottom: 0 !important;
}

.my-lg-1 {
	margin-top: 0.25rem !important;
	margin-bottom: 0.25rem !important;
}

.my-lg-2 {
	margin-top: 0.5rem !important;
	margin-bottom: 0.5rem !important;
}

.my-lg-3 {
	margin-top: 1rem !important;
	margin-bottom: 1rem !important;
}

.my-lg-4 {
	margin-top: 1.5rem !important;
	margin-bottom: 1.5rem !important;
}

.my-lg-5 {
	margin-top: 3rem !important;
	margin-bottom: 3rem !important;
}

.my-lg-10 {
	margin-top: 0.625rem !important;
	margin-bottom: 0.625rem !important;
}

.my-lg-15 {
	margin-top: 0.9375rem !important;
	margin-bottom: 0.9375rem !important;
}

.my-lg-20 {
	margin-top: 1.25rem !important;
	margin-bottom: 1.25rem !important;
}

.my-lg-25 {
	margin-top: 1.5625rem !important;
	margin-bottom: 1.5625rem !important;
}

.my-lg-30 {
	margin-top: 1.875rem !important;
	margin-bottom: 1.875rem !important;
}

.my-lg-35 {
	margin-top: 2.18753rem !important;
	margin-bottom: 2.18753rem !important;
}

.my-lg-40 {
	margin-top: 2.5rem !important;
	margin-bottom: 2.5rem !important;
}

.my-lg-45 {
	margin-top: 2.8125rem !important;
	margin-bottom: 2.8125rem !important;
}

.my-lg-50 {
	margin-top: 3.125rem !important;
	margin-bottom: 3.125rem !important;
}

.my-lg-auto {
	margin-top: auto !important;
	margin-bottom: auto !important;
}

.mt-lg-0 {
	margin-top: 0 !important;
}

.mt-lg-1 {
	margin-top: 0.25rem !important;
}

.mt-lg-2 {
	margin-top: 0.5rem !important;
}

.mt-lg-3 {
	margin-top: 1rem !important;
}

.mt-lg-4 {
	margin-top: 1.5rem !important;
}

.mt-lg-5 {
	margin-top: 3rem !important;
}

.mt-lg-10 {
	margin-top: 0.625rem !important;
}

.mt-lg-15 {
	margin-top: 0.9375rem !important;
}

.mt-lg-20 {
	margin-top: 1.25rem !important;
}

.mt-lg-25 {
	margin-top: 1.5625rem !important;
}

.mt-lg-30 {
	margin-top: 1.875rem !important;
}

.mt-lg-35 {
	margin-top: 2.18753rem !important;
}

.mt-lg-40 {
	margin-top: 2.5rem !important;
}

.mt-lg-45 {
	margin-top: 2.8125rem !important;
}

.mt-lg-50 {
	margin-top: 3.125rem !important;
}

.mt-lg-auto {
	margin-top: auto !important;
}

.me-lg-0 {
	margin-right: 0 !important;
}

.me-lg-1 {
	margin-right: 0.25rem !important;
}

.me-lg-2 {
	margin-right: 0.5rem !important;
}

.me-lg-3 {
	margin-right: 1rem !important;
}

.me-lg-4 {
	margin-right: 1.5rem !important;
}

.me-lg-5 {
	margin-right: 3rem !important;
}

.me-lg-10 {
	margin-right: 0.625rem !important;
}

.me-lg-15 {
	margin-right: 0.9375rem !important;
}

.me-lg-20 {
	margin-right: 1.25rem !important;
}

.me-lg-25 {
	margin-right: 1.5625rem !important;
}

.me-lg-30 {
	margin-right: 1.875rem !important;
}

.me-lg-35 {
	margin-right: 2.18753rem !important;
}

.me-lg-40 {
	margin-right: 2.5rem !important;
}

.me-lg-45 {
	margin-right: 2.8125rem !important;
}

.me-lg-50 {
	margin-right: 3.125rem !important;
}

.me-lg-auto {
	margin-right: auto !important;
}

.mb-lg-0 {
	margin-bottom: 0 !important;
}

.mb-lg-1 {
	margin-bottom: 0.25rem !important;
}

.mb-lg-2 {
	margin-bottom: 0.5rem !important;
}

.mb-lg-3 {
	margin-bottom: 1rem !important;
}

.mb-lg-4 {
	margin-bottom: 1.5rem !important;
}

.mb-lg-5 {
	margin-bottom: 3rem !important;
}

.mb-lg-10 {
	margin-bottom: 0.625rem !important;
}

.mb-lg-15 {
	margin-bottom: 0.9375rem !important;
}

.mb-lg-20 {
	margin-bottom: 1.25rem !important;
}

.mb-lg-25 {
	margin-bottom: 1.5625rem !important;
}

.mb-lg-30 {
	margin-bottom: 1.875rem !important;
}

.mb-lg-35 {
	margin-bottom: 2.18753rem !important;
}

.mb-lg-40 {
	margin-bottom: 2.5rem !important;
}

.mb-lg-45 {
	margin-bottom: 2.8125rem !important;
}

.mb-lg-50 {
	margin-bottom: 3.125rem !important;
}

.mb-lg-auto {
	margin-bottom: auto !important;
}

.ms-lg-0 {
	margin-left: 0 !important;
}

.ms-lg-1 {
	margin-left: 0.25rem !important;
}

.ms-lg-2 {
	margin-left: 0.5rem !important;
}

.ms-lg-3 {
	margin-left: 1rem !important;
}

.ms-lg-4 {
	margin-left: 1.5rem !important;
}

.ms-lg-5 {
	margin-left: 3rem !important;
}

.ms-lg-10 {
	margin-left: 0.625rem !important;
}

.ms-lg-15 {
	margin-left: 0.9375rem !important;
}

.ms-lg-20 {
	margin-left: 1.25rem !important;
}

.ms-lg-25 {
	margin-left: 1.5625rem !important;
}

.ms-lg-30 {
	margin-left: 1.875rem !important;
}

.ms-lg-35 {
	margin-left: 2.18753rem !important;
}

.ms-lg-40 {
	margin-left: 2.5rem !important;
}

.ms-lg-45 {
	margin-left: 2.8125rem !important;
}

.ms-lg-50 {
	margin-left: 3.125rem !important;
}

.ms-lg-auto {
	margin-left: auto !important;
}

.m-lg-n1 {
	margin: -0.25rem !important;
}

.m-lg-n2 {
	margin: -0.5rem !important;
}

.m-lg-n3 {
	margin: -1rem !important;
}

.m-lg-n4 {
	margin: -1.5rem !important;
}

.m-lg-n5 {
	margin: -3rem !important;
}

.m-lg-n10 {
	margin: -0.625rem !important;
}

.m-lg-n15 {
	margin: -0.9375rem !important;
}

.m-lg-n20 {
	margin: -1.25rem !important;
}

.m-lg-n25 {
	margin: -1.5625rem !important;
}

.m-lg-n30 {
	margin: -1.875rem !important;
}

.m-lg-n35 {
	margin: -2.18753rem !important;
}

.m-lg-n40 {
	margin: -2.5rem !important;
}

.m-lg-n45 {
	margin: -2.8125rem !important;
}

.m-lg-n50 {
	margin: -3.125rem !important;
}

.mx-lg-n1 {
	margin-right: -0.25rem !important;
	margin-left: -0.25rem !important;
}

.mx-lg-n2 {
	margin-right: -0.5rem !important;
	margin-left: -0.5rem !important;
}

.mx-lg-n3 {
	margin-right: -1rem !important;
	margin-left: -1rem !important;
}

.mx-lg-n4 {
	margin-right: -1.5rem !important;
	margin-left: -1.5rem !important;
}

.mx-lg-n5 {
	margin-right: -3rem !important;
	margin-left: -3rem !important;
}

.mx-lg-n10 {
	margin-right: -0.625rem !important;
	margin-left: -0.625rem !important;
}

.mx-lg-n15 {
	margin-right: -0.9375rem !important;
	margin-left: -0.9375rem !important;
}

.mx-lg-n20 {
	margin-right: -1.25rem !important;
	margin-left: -1.25rem !important;
}

.mx-lg-n25 {
	margin-right: -1.5625rem !important;
	margin-left: -1.5625rem !important;
}

.mx-lg-n30 {
	margin-right: -1.875rem !important;
	margin-left: -1.875rem !important;
}

.mx-lg-n35 {
	margin-right: -2.18753rem !important;
	margin-left: -2.18753rem !important;
}

.mx-lg-n40 {
	margin-right: -2.5rem !important;
	margin-left: -2.5rem !important;
}

.mx-lg-n45 {
	margin-right: -2.8125rem !important;
	margin-left: -2.8125rem !important;
}

.mx-lg-n50 {
	margin-right: -3.125rem !important;
	margin-left: -3.125rem !important;
}

.my-lg-n1 {
	margin-top: -0.25rem !important;
	margin-bottom: -0.25rem !important;
}

.my-lg-n2 {
	margin-top: -0.5rem !important;
	margin-bottom: -0.5rem !important;
}

.my-lg-n3 {
	margin-top: -1rem !important;
	margin-bottom: -1rem !important;
}

.my-lg-n4 {
	margin-top: -1.5rem !important;
	margin-bottom: -1.5rem !important;
}

.my-lg-n5 {
	margin-top: -3rem !important;
	margin-bottom: -3rem !important;
}

.my-lg-n10 {
	margin-top: -0.625rem !important;
	margin-bottom: -0.625rem !important;
}

.my-lg-n15 {
	margin-top: -0.9375rem !important;
	margin-bottom: -0.9375rem !important;
}

.my-lg-n20 {
	margin-top: -1.25rem !important;
	margin-bottom: -1.25rem !important;
}

.my-lg-n25 {
	margin-top: -1.5625rem !important;
	margin-bottom: -1.5625rem !important;
}

.my-lg-n30 {
	margin-top: -1.875rem !important;
	margin-bottom: -1.875rem !important;
}

.my-lg-n35 {
	margin-top: -2.18753rem !important;
	margin-bottom: -2.18753rem !important;
}

.my-lg-n40 {
	margin-top: -2.5rem !important;
	margin-bottom: -2.5rem !important;
}

.my-lg-n45 {
	margin-top: -2.8125rem !important;
	margin-bottom: -2.8125rem !important;
}

.my-lg-n50 {
	margin-top: -3.125rem !important;
	margin-bottom: -3.125rem !important;
}

.mt-lg-n1 {
	margin-top: -0.25rem !important;
}

.mt-lg-n2 {
	margin-top: -0.5rem !important;
}

.mt-lg-n3 {
	margin-top: -1rem !important;
}

.mt-lg-n4 {
	margin-top: -1.5rem !important;
}

.mt-lg-n5 {
	margin-top: -3rem !important;
}

.mt-lg-n10 {
	margin-top: -0.625rem !important;
}

.mt-lg-n15 {
	margin-top: -0.9375rem !important;
}

.mt-lg-n20 {
	margin-top: -1.25rem !important;
}

.mt-lg-n25 {
	margin-top: -1.5625rem !important;
}

.mt-lg-n30 {
	margin-top: -1.875rem !important;
}

.mt-lg-n35 {
	margin-top: -2.18753rem !important;
}

.mt-lg-n40 {
	margin-top: -2.5rem !important;
}

.mt-lg-n45 {
	margin-top: -2.8125rem !important;
}

.mt-lg-n50 {
	margin-top: -3.125rem !important;
}

.me-lg-n1 {
	margin-right: -0.25rem !important;
}

.me-lg-n2 {
	margin-right: -0.5rem !important;
}

.me-lg-n3 {
	margin-right: -1rem !important;
}

.me-lg-n4 {
	margin-right: -1.5rem !important;
}

.me-lg-n5 {
	margin-right: -3rem !important;
}

.me-lg-n10 {
	margin-right: -0.625rem !important;
}

.me-lg-n15 {
	margin-right: -0.9375rem !important;
}

.me-lg-n20 {
	margin-right: -1.25rem !important;
}

.me-lg-n25 {
	margin-right: -1.5625rem !important;
}

.me-lg-n30 {
	margin-right: -1.875rem !important;
}

.me-lg-n35 {
	margin-right: -2.18753rem !important;
}

.me-lg-n40 {
	margin-right: -2.5rem !important;
}

.me-lg-n45 {
	margin-right: -2.8125rem !important;
}

.me-lg-n50 {
	margin-right: -3.125rem !important;
}

.mb-lg-n1 {
	margin-bottom: -0.25rem !important;
}

.mb-lg-n2 {
	margin-bottom: -0.5rem !important;
}

.mb-lg-n3 {
	margin-bottom: -1rem !important;
}

.mb-lg-n4 {
	margin-bottom: -1.5rem !important;
}

.mb-lg-n5 {
	margin-bottom: -3rem !important;
}

.mb-lg-n10 {
	margin-bottom: -0.625rem !important;
}

.mb-lg-n15 {
	margin-bottom: -0.9375rem !important;
}

.mb-lg-n20 {
	margin-bottom: -1.25rem !important;
}

.mb-lg-n25 {
	margin-bottom: -1.5625rem !important;
}

.mb-lg-n30 {
	margin-bottom: -1.875rem !important;
}

.mb-lg-n35 {
	margin-bottom: -2.18753rem !important;
}

.mb-lg-n40 {
	margin-bottom: -2.5rem !important;
}

.mb-lg-n45 {
	margin-bottom: -2.8125rem !important;
}

.mb-lg-n50 {
	margin-bottom: -3.125rem !important;
}

.ms-lg-n1 {
	margin-left: -0.25rem !important;
}

.ms-lg-n2 {
	margin-left: -0.5rem !important;
}

.ms-lg-n3 {
	margin-left: -1rem !important;
}

.ms-lg-n4 {
	margin-left: -1.5rem !important;
}

.ms-lg-n5 {
	margin-left: -3rem !important;
}

.ms-lg-n10 {
	margin-left: -0.625rem !important;
}

.ms-lg-n15 {
	margin-left: -0.9375rem !important;
}

.ms-lg-n20 {
	margin-left: -1.25rem !important;
}

.ms-lg-n25 {
	margin-left: -1.5625rem !important;
}

.ms-lg-n30 {
	margin-left: -1.875rem !important;
}

.ms-lg-n35 {
	margin-left: -2.18753rem !important;
}

.ms-lg-n40 {
	margin-left: -2.5rem !important;
}

.ms-lg-n45 {
	margin-left: -2.8125rem !important;
}

.ms-lg-n50 {
	margin-left: -3.125rem !important;
}

.p-lg-0 {
	padding: 0 !important;
}

.p-lg-1 {
	padding: 0.25rem !important;
}

.p-lg-2 {
	padding: 0.5rem !important;
}

.p-lg-3 {
	padding: 1rem !important;
}

.p-lg-4 {
	padding: 1.5rem !important;
}

.p-lg-5 {
	padding: 3rem !important;
}

.p-lg-10 {
	padding: 0.625rem !important;
}

.p-lg-15 {
	padding: 0.9375rem !important;
}

.p-lg-20 {
	padding: 1.25rem !important;
}

.p-lg-25 {
	padding: 1.5625rem !important;
}

.p-lg-30 {
	padding: 1.875rem !important;
}

.p-lg-35 {
	padding: 2.18753rem !important;
}

.p-lg-40 {
	padding: 2.5rem !important;
}

.p-lg-45 {
	padding: 2.8125rem !important;
}

.p-lg-50 {
	padding: 3.125rem !important;
}

.px-lg-0 {
	padding-right: 0 !important;
	padding-left: 0 !important;
}

.px-lg-1 {
	padding-right: 0.25rem !important;
	padding-left: 0.25rem !important;
}

.px-lg-2 {
	padding-right: 0.5rem !important;
	padding-left: 0.5rem !important;
}

.px-lg-3 {
	padding-right: 1rem !important;
	padding-left: 1rem !important;
}

.px-lg-4 {
	padding-right: 1.5rem !important;
	padding-left: 1.5rem !important;
}

.px-lg-5 {
	padding-right: 3rem !important;
	padding-left: 3rem !important;
}

.px-lg-10 {
	padding-right: 0.625rem !important;
	padding-left: 0.625rem !important;
}

.px-lg-15 {
	padding-right: 0.9375rem !important;
	padding-left: 0.9375rem !important;
}

.px-lg-20 {
	padding-right: 1.25rem !important;
	padding-left: 1.25rem !important;
}

.px-lg-25 {
	padding-right: 1.5625rem !important;
	padding-left: 1.5625rem !important;
}

.px-lg-30 {
	padding-right: 1.875rem !important;
	padding-left: 1.875rem !important;
}

.px-lg-35 {
	padding-right: 2.18753rem !important;
	padding-left: 2.18753rem !important;
}

.px-lg-40 {
	padding-right: 2.5rem !important;
	padding-left: 2.5rem !important;
}

.px-lg-45 {
	padding-right: 2.8125rem !important;
	padding-left: 2.8125rem !important;
}

.px-lg-50 {
	padding-right: 3.125rem !important;
	padding-left: 3.125rem !important;
}

.py-lg-0 {
	padding-top: 0 !important;
	padding-bottom: 0 !important;
}

.py-lg-1 {
	padding-top: 0.25rem !important;
	padding-bottom: 0.25rem !important;
}

.py-lg-2 {
	padding-top: 0.5rem !important;
	padding-bottom: 0.5rem !important;
}

.py-lg-3 {
	padding-top: 1rem !important;
	padding-bottom: 1rem !important;
}

.py-lg-4 {
	padding-top: 1.5rem !important;
	padding-bottom: 1.5rem !important;
}

.py-lg-5 {
	padding-top: 3rem !important;
	padding-bottom: 3rem !important;
}

.py-lg-10 {
	padding-top: 0.625rem !important;
	padding-bottom: 0.625rem !important;
}

.py-lg-15 {
	padding-top: 0.9375rem !important;
	padding-bottom: 0.9375rem !important;
}

.py-lg-20 {
	padding-top: 1.25rem !important;
	padding-bottom: 1.25rem !important;
}

.py-lg-25 {
	padding-top: 1.5625rem !important;
	padding-bottom: 1.5625rem !important;
}

.py-lg-30 {
	padding-top: 1.875rem !important;
	padding-bottom: 1.875rem !important;
}

.py-lg-35 {
	padding-top: 2.18753rem !important;
	padding-bottom: 2.18753rem !important;
}

.py-lg-40 {
	padding-top: 2.5rem !important;
	padding-bottom: 2.5rem !important;
}

.py-lg-45 {
	padding-top: 2.8125rem !important;
	padding-bottom: 2.8125rem !important;
}

.py-lg-50 {
	padding-top: 3.125rem !important;
	padding-bottom: 3.125rem !important;
}

.pt-lg-0 {
	padding-top: 0 !important;
}

.pt-lg-1 {
	padding-top: 0.25rem !important;
}

.pt-lg-2 {
	padding-top: 0.5rem !important;
}

.pt-lg-3 {
	padding-top: 1rem !important;
}

.pt-lg-4 {
	padding-top: 1.5rem !important;
}

.pt-lg-5 {
	padding-top: 3rem !important;
}

.pt-lg-10 {
	padding-top: 0.625rem !important;
}

.pt-lg-15 {
	padding-top: 0.9375rem !important;
}

.pt-lg-20 {
	padding-top: 1.25rem !important;
}

.pt-lg-25 {
	padding-top: 1.5625rem !important;
}

.pt-lg-30 {
	padding-top: 1.875rem !important;
}

.pt-lg-35 {
	padding-top: 2.18753rem !important;
}

.pt-lg-40 {
	padding-top: 2.5rem !important;
}

.pt-lg-45 {
	padding-top: 2.8125rem !important;
}

.pt-lg-50 {
	padding-top: 3.125rem !important;
}

.pe-lg-0 {
	padding-right: 0 !important;
}

.pe-lg-1 {
	padding-right: 0.25rem !important;
}

.pe-lg-2 {
	padding-right: 0.5rem !important;
}

.pe-lg-3 {
	padding-right: 1rem !important;
}

.pe-lg-4 {
	padding-right: 1.5rem !important;
}

.pe-lg-5 {
	padding-right: 3rem !important;
}

.pe-lg-10 {
	padding-right: 0.625rem !important;
}

.pe-lg-15 {
	padding-right: 0.9375rem !important;
}

.pe-lg-20 {
	padding-right: 1.25rem !important;
}

.pe-lg-25 {
	padding-right: 1.5625rem !important;
}

.pe-lg-30 {
	padding-right: 1.875rem !important;
}

.pe-lg-35 {
	padding-right: 2.18753rem !important;
}

.pe-lg-40 {
	padding-right: 2.5rem !important;
}

.pe-lg-45 {
	padding-right: 2.8125rem !important;
}

.pe-lg-50 {
	padding-right: 3.125rem !important;
}

.pb-lg-0 {
	padding-bottom: 0 !important;
}

.pb-lg-1 {
	padding-bottom: 0.25rem !important;
}

.pb-lg-2 {
	padding-bottom: 0.5rem !important;
}

.pb-lg-3 {
	padding-bottom: 1rem !important;
}

.pb-lg-4 {
	padding-bottom: 1.5rem !important;
}

.pb-lg-5 {
	padding-bottom: 3rem !important;
}

.pb-lg-10 {
	padding-bottom: 0.625rem !important;
}

.pb-lg-15 {
	padding-bottom: 0.9375rem !important;
}

.pb-lg-20 {
	padding-bottom: 1.25rem !important;
}

.pb-lg-25 {
	padding-bottom: 1.5625rem !important;
}

.pb-lg-30 {
	padding-bottom: 1.875rem !important;
}

.pb-lg-35 {
	padding-bottom: 2.18753rem !important;
}

.pb-lg-40 {
	padding-bottom: 2.5rem !important;
}

.pb-lg-45 {
	padding-bottom: 2.8125rem !important;
}

.pb-lg-50 {
	padding-bottom: 3.125rem !important;
}

.ps-lg-0 {
	padding-left: 0 !important;
}

.ps-lg-1 {
	padding-left: 0.25rem !important;
}

.ps-lg-2 {
	padding-left: 0.5rem !important;
}

.ps-lg-3 {
	padding-left: 1rem !important;
}

.ps-lg-4 {
	padding-left: 1.5rem !important;
}

.ps-lg-5 {
	padding-left: 3rem !important;
}

.ps-lg-10 {
	padding-left: 0.625rem !important;
}

.ps-lg-15 {
	padding-left: 0.9375rem !important;
}

.ps-lg-20 {
	padding-left: 1.25rem !important;
}

.ps-lg-25 {
	padding-left: 1.5625rem !important;
}

.ps-lg-30 {
	padding-left: 1.875rem !important;
}

.ps-lg-35 {
	padding-left: 2.18753rem !important;
}

.ps-lg-40 {
	padding-left: 2.5rem !important;
}

.ps-lg-45 {
	padding-left: 2.8125rem !important;
}

.ps-lg-50 {
	padding-left: 3.125rem !important;
}

.text-lg-start {
	text-align: left !important;
}

.text-lg-end {
	text-align: right !important;
}

.text-lg-center {
	text-align: center !important;
}

}

@media (min-width: 1200px) {

h1,
.h1 {
	font-size: 30px;
}

h2,
.h2 {
	font-size: 24px;
}

h3,
.h3 {
	font-size: 22px;
}

h4,
.h4 {
	font-size: 20px;
}

h5,
.h5 {
	font-size: 18px;
}

legend {
	font-size: 24px;
}

.lead {
	font-size: 21.28px;
}

.display-1 {
	font-size: 48px;
}

.display-2 {
	font-size: 40px;
}

.display-3 {
	font-size: 36px;
}

.display-4 {
	font-size: 30px;
}

.blockquote {
	font-size: 20px;
}

.container-xl,
.container-lg,
.container-md,
.container-sm,
.container {
	max-width: 1140px;
}

.col-xl {
	-webkit-box-flex: 1;
	-ms-flex: 1 0 0%;
	flex: 1 0 0%;
}

.row-cols-xl-auto > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: auto;
}

.row-cols-xl-1 > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 100%;
}

.row-cols-xl-2 > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 50%;
}

.row-cols-xl-3 > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 33.3333333333%;
}

.row-cols-xl-4 > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 25%;
}

.row-cols-xl-5 > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 20%;
}

.row-cols-xl-6 > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 16.6666666667%;
}

.col-xl-auto {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: auto;
}

.col-xl-1 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 8.33333333%;
}

.col-xl-2 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 16.66666667%;
}

.col-xl-3 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 25%;
}

.col-xl-4 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 33.33333333%;
}

.col-xl-5 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 41.66666667%;
}

.col-xl-6 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 50%;
}

.col-xl-7 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 58.33333333%;
}

.col-xl-8 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 66.66666667%;
}

.col-xl-9 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 75%;
}

.col-xl-10 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 83.33333333%;
}

.col-xl-11 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 91.66666667%;
}

.col-xl-12 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 100%;
}

.offset-xl-0 {
	margin-left: 0;
}

.offset-xl-1 {
	margin-left: 8.33333333%;
}

.offset-xl-2 {
	margin-left: 16.66666667%;
}

.offset-xl-3 {
	margin-left: 25%;
}

.offset-xl-4 {
	margin-left: 33.33333333%;
}

.offset-xl-5 {
	margin-left: 41.66666667%;
}

.offset-xl-6 {
	margin-left: 50%;
}

.offset-xl-7 {
	margin-left: 58.33333333%;
}

.offset-xl-8 {
	margin-left: 66.66666667%;
}

.offset-xl-9 {
	margin-left: 75%;
}

.offset-xl-10 {
	margin-left: 83.33333333%;
}

.offset-xl-11 {
	margin-left: 91.66666667%;
}

.g-xl-0,
.gx-xl-0 {
	--bs-gutter-x: 0;
}

.g-xl-0,
.gy-xl-0 {
	--bs-gutter-y: 0;
}

.g-xl-1,
.gx-xl-1 {
	--bs-gutter-x: 0.25rem;
}

.g-xl-1,
.gy-xl-1 {
	--bs-gutter-y: 0.25rem;
}

.g-xl-2,
.gx-xl-2 {
	--bs-gutter-x: 0.5rem;
}

.g-xl-2,
.gy-xl-2 {
	--bs-gutter-y: 0.5rem;
}

.g-xl-3,
.gx-xl-3 {
	--bs-gutter-x: 1rem;
}

.g-xl-3,
.gy-xl-3 {
	--bs-gutter-y: 1rem;
}

.g-xl-4,
.gx-xl-4 {
	--bs-gutter-x: 1.5rem;
}

.g-xl-4,
.gy-xl-4 {
	--bs-gutter-y: 1.5rem;
}

.g-xl-5,
.gx-xl-5 {
	--bs-gutter-x: 3rem;
}

.g-xl-5,
.gy-xl-5 {
	--bs-gutter-y: 3rem;
}

.form-select-lg {
	font-size: 20px;
}

.btn-lg,
.btn-group-lg > .btn {
	font-size: 20px;
}

.dropdown-menu-xl-start {
	--bs-position: start;
}

.dropdown-menu-xl-start[data-bs-popper] {
	right: auto;
	left: 0;
}

.dropdown-menu-xl-end {
	--bs-position: end;
}

.dropdown-menu-xl-end[data-bs-popper] {
	right: 0;
	left: auto;
}

.navbar-brand {
	font-size: 20px;
}

.navbar-toggler {
	font-size: 20px;
}

.navbar-expand-xl {
	-ms-flex-wrap: nowrap;
	flex-wrap: nowrap;
	-webkit-box-pack: start;
	-ms-flex-pack: start;
	justify-content: flex-start;
}

.navbar-expand-xl .navbar-nav {
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-ms-flex-direction: row;
	flex-direction: row;
}

.navbar-expand-xl .navbar-nav .dropdown-menu {
	position: absolute;
}

.navbar-expand-xl .navbar-nav .nav-link {
	padding-right: 0.5rem;
	padding-left: 0.5rem;
}

.navbar-expand-xl .navbar-nav-scroll {
	overflow: visible;
}

.navbar-expand-xl .navbar-collapse {
	display: -webkit-box !important;
	display: -ms-flexbox !important;
	display: flex !important;
	-ms-flex-preferred-size: auto;
	flex-basis: auto;
}

.navbar-expand-xl .navbar-toggler {
	display: none;
}

.pagination-lg .page-link {
	font-size: 20px;
}

.list-group-horizontal-xl {
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-ms-flex-direction: row;
	flex-direction: row;
}

.list-group-horizontal-xl > .list-group-item:first-child {
	-webkit-border-bottom-left-radius: 0.25rem;
	border-bottom-left-radius: 0.25rem;
	-webkit-border-top-right-radius: 0;
	border-top-right-radius: 0;
}

.list-group-horizontal-xl > .list-group-item:last-child {
	-webkit-border-top-right-radius: 0.25rem;
	border-top-right-radius: 0.25rem;
	-webkit-border-bottom-left-radius: 0;
	border-bottom-left-radius: 0;
}

.list-group-horizontal-xl > .list-group-item.active {
	margin-top: 0;
}

.list-group-horizontal-xl > .list-group-item + .list-group-item {
	border-top-width: 1px;
	border-left-width: 0;
}

.list-group-horizontal-xl > .list-group-item + .list-group-item.active {
	margin-left: -1px;
	border-left-width: 1px;
}

.modal-xl {
	max-width: 1140px;
}

.sticky-xl-top {
	position: sticky;
	top: 0;
	z-index: 1020;
}

.float-xl-start {
	float: left !important;
}

.float-xl-end {
	float: right !important;
}

.float-xl-none {
	float: none !important;
}

.d-xl-inline {
	display: inline !important;
}

.d-xl-inline-block {
	display: inline-block !important;
}

.d-xl-block {
	display: block !important;
}

.d-xl-grid {
	display: grid !important;
}

.d-xl-table {
	display: table !important;
}

.d-xl-table-row {
	display: table-row !important;
}

.d-xl-table-cell {
	display: table-cell !important;
}

.d-xl-flex {
	display: -webkit-box !important;
	display: -ms-flexbox !important;
	display: flex !important;
}

.d-xl-inline-flex {
	display: -webkit-inline-box !important;
	display: -ms-inline-flexbox !important;
	display: inline-flex !important;
}

.d-xl-none {
	display: none !important;
}

.flex-xl-fill {
	-webkit-box-flex: 1 !important;
	-ms-flex: 1 1 auto !important;
	flex: 1 1 auto !important;
}

.flex-xl-row {
	-webkit-box-orient: horizontal !important;
	-webkit-box-direction: normal !important;
	-ms-flex-direction: row !important;
	flex-direction: row !important;
}

.flex-xl-column {
	-webkit-box-orient: vertical !important;
	-webkit-box-direction: normal !important;
	-ms-flex-direction: column !important;
	flex-direction: column !important;
}

.flex-xl-row-reverse {
	-webkit-box-orient: horizontal !important;
	-webkit-box-direction: reverse !important;
	-ms-flex-direction: row-reverse !important;
	flex-direction: row-reverse !important;
}

.flex-xl-column-reverse {
	-webkit-box-orient: vertical !important;
	-webkit-box-direction: reverse !important;
	-ms-flex-direction: column-reverse !important;
	flex-direction: column-reverse !important;
}

.flex-xl-grow-0 {
	-webkit-box-flex: 0 !important;
	-ms-flex-positive: 0 !important;
	flex-grow: 0 !important;
}

.flex-xl-grow-1 {
	-webkit-box-flex: 1 !important;
	-ms-flex-positive: 1 !important;
	flex-grow: 1 !important;
}

.flex-xl-shrink-0 {
	-ms-flex-negative: 0 !important;
	flex-shrink: 0 !important;
}

.flex-xl-shrink-1 {
	-ms-flex-negative: 1 !important;
	flex-shrink: 1 !important;
}

.flex-xl-wrap {
	-ms-flex-wrap: wrap !important;
	flex-wrap: wrap !important;
}

.flex-xl-nowrap {
	-ms-flex-wrap: nowrap !important;
	flex-wrap: nowrap !important;
}

.flex-xl-wrap-reverse {
	-ms-flex-wrap: wrap-reverse !important;
	flex-wrap: wrap-reverse !important;
}

.gap-xl-0 {
	gap: 0 !important;
}

.gap-xl-1 {
	gap: 0.25rem !important;
}

.gap-xl-2 {
	gap: 0.5rem !important;
}

.gap-xl-3 {
	gap: 1rem !important;
}

.gap-xl-4 {
	gap: 1.5rem !important;
}

.gap-xl-5 {
	gap: 3rem !important;
}

.gap-xl-10 {
	gap: 0.625rem !important;
}

.gap-xl-15 {
	gap: 0.9375rem !important;
}

.gap-xl-20 {
	gap: 1.25rem !important;
}

.gap-xl-25 {
	gap: 1.5625rem !important;
}

.gap-xl-30 {
	gap: 1.875rem !important;
}

.gap-xl-35 {
	gap: 2.18753rem !important;
}

.gap-xl-40 {
	gap: 2.5rem !important;
}

.gap-xl-45 {
	gap: 2.8125rem !important;
}

.gap-xl-50 {
	gap: 3.125rem !important;
}

.justify-content-xl-start {
	-webkit-box-pack: start !important;
	-ms-flex-pack: start !important;
	justify-content: flex-start !important;
}

.justify-content-xl-end {
	-webkit-box-pack: end !important;
	-ms-flex-pack: end !important;
	justify-content: flex-end !important;
}

.justify-content-xl-center {
	-webkit-box-pack: center !important;
	-ms-flex-pack: center !important;
	justify-content: center !important;
}

.justify-content-xl-between {
	-webkit-box-pack: justify !important;
	-ms-flex-pack: justify !important;
	justify-content: space-between !important;
}

.justify-content-xl-around {
	-ms-flex-pack: distribute !important;
	justify-content: space-around !important;
}

.justify-content-xl-evenly {
	-webkit-box-pack: space-evenly !important;
	-ms-flex-pack: space-evenly !important;
	justify-content: space-evenly !important;
}

.align-items-xl-start {
	-webkit-box-align: start !important;
	-ms-flex-align: start !important;
	align-items: flex-start !important;
}

.align-items-xl-end {
	-webkit-box-align: end !important;
	-ms-flex-align: end !important;
	align-items: flex-end !important;
}

.align-items-xl-center {
	-webkit-box-align: center !important;
	-ms-flex-align: center !important;
	align-items: center !important;
}

.align-items-xl-baseline {
	-webkit-box-align: baseline !important;
	-ms-flex-align: baseline !important;
	align-items: baseline !important;
}

.align-items-xl-stretch {
	-webkit-box-align: stretch !important;
	-ms-flex-align: stretch !important;
	align-items: stretch !important;
}

.align-content-xl-start {
	-ms-flex-line-pack: start !important;
	align-content: flex-start !important;
}

.align-content-xl-end {
	-ms-flex-line-pack: end !important;
	align-content: flex-end !important;
}

.align-content-xl-center {
	-ms-flex-line-pack: center !important;
	align-content: center !important;
}

.align-content-xl-between {
	-ms-flex-line-pack: justify !important;
	align-content: space-between !important;
}

.align-content-xl-around {
	-ms-flex-line-pack: distribute !important;
	align-content: space-around !important;
}

.align-content-xl-stretch {
	-ms-flex-line-pack: stretch !important;
	align-content: stretch !important;
}

.align-self-xl-auto {
	-ms-flex-item-align: auto !important;
	align-self: auto !important;
}

.align-self-xl-start {
	-ms-flex-item-align: start !important;
	align-self: flex-start !important;
}

.align-self-xl-end {
	-ms-flex-item-align: end !important;
	align-self: flex-end !important;
}

.align-self-xl-center {
	-ms-flex-item-align: center !important;
	align-self: center !important;
}

.align-self-xl-baseline {
	-ms-flex-item-align: baseline !important;
	align-self: baseline !important;
}

.align-self-xl-stretch {
	-ms-flex-item-align: stretch !important;
	align-self: stretch !important;
}

.order-xl-first {
	-webkit-box-ordinal-group: 0 !important;
	-ms-flex-order: -1 !important;
	order: -1 !important;
}

.order-xl-0 {
	-webkit-box-ordinal-group: 1 !important;
	-ms-flex-order: 0 !important;
	order: 0 !important;
}

.order-xl-1 {
	-webkit-box-ordinal-group: 2 !important;
	-ms-flex-order: 1 !important;
	order: 1 !important;
}

.order-xl-2 {
	-webkit-box-ordinal-group: 3 !important;
	-ms-flex-order: 2 !important;
	order: 2 !important;
}

.order-xl-3 {
	-webkit-box-ordinal-group: 4 !important;
	-ms-flex-order: 3 !important;
	order: 3 !important;
}

.order-xl-4 {
	-webkit-box-ordinal-group: 5 !important;
	-ms-flex-order: 4 !important;
	order: 4 !important;
}

.order-xl-5 {
	-webkit-box-ordinal-group: 6 !important;
	-ms-flex-order: 5 !important;
	order: 5 !important;
}

.order-xl-last {
	-webkit-box-ordinal-group: 7 !important;
	-ms-flex-order: 6 !important;
	order: 6 !important;
}

.m-xl-0 {
	margin: 0 !important;
}

.m-xl-1 {
	margin: 0.25rem !important;
}

.m-xl-2 {
	margin: 0.5rem !important;
}

.m-xl-3 {
	margin: 1rem !important;
}

.m-xl-4 {
	margin: 1.5rem !important;
}

.m-xl-5 {
	margin: 3rem !important;
}

.m-xl-10 {
	margin: 0.625rem !important;
}

.m-xl-15 {
	margin: 0.9375rem !important;
}

.m-xl-20 {
	margin: 1.25rem !important;
}

.m-xl-25 {
	margin: 1.5625rem !important;
}

.m-xl-30 {
	margin: 1.875rem !important;
}

.m-xl-35 {
	margin: 2.18753rem !important;
}

.m-xl-40 {
	margin: 2.5rem !important;
}

.m-xl-45 {
	margin: 2.8125rem !important;
}

.m-xl-50 {
	margin: 3.125rem !important;
}

.m-xl-auto {
	margin: auto !important;
}

.mx-xl-0 {
	margin-right: 0 !important;
	margin-left: 0 !important;
}

.mx-xl-1 {
	margin-right: 0.25rem !important;
	margin-left: 0.25rem !important;
}

.mx-xl-2 {
	margin-right: 0.5rem !important;
	margin-left: 0.5rem !important;
}

.mx-xl-3 {
	margin-right: 1rem !important;
	margin-left: 1rem !important;
}

.mx-xl-4 {
	margin-right: 1.5rem !important;
	margin-left: 1.5rem !important;
}

.mx-xl-5 {
	margin-right: 3rem !important;
	margin-left: 3rem !important;
}

.mx-xl-10 {
	margin-right: 0.625rem !important;
	margin-left: 0.625rem !important;
}

.mx-xl-15 {
	margin-right: 0.9375rem !important;
	margin-left: 0.9375rem !important;
}

.mx-xl-20 {
	margin-right: 1.25rem !important;
	margin-left: 1.25rem !important;
}

.mx-xl-25 {
	margin-right: 1.5625rem !important;
	margin-left: 1.5625rem !important;
}

.mx-xl-30 {
	margin-right: 1.875rem !important;
	margin-left: 1.875rem !important;
}

.mx-xl-35 {
	margin-right: 2.18753rem !important;
	margin-left: 2.18753rem !important;
}

.mx-xl-40 {
	margin-right: 2.5rem !important;
	margin-left: 2.5rem !important;
}

.mx-xl-45 {
	margin-right: 2.8125rem !important;
	margin-left: 2.8125rem !important;
}

.mx-xl-50 {
	margin-right: 3.125rem !important;
	margin-left: 3.125rem !important;
}

.mx-xl-auto {
	margin-right: auto !important;
	margin-left: auto !important;
}

.my-xl-0 {
	margin-top: 0 !important;
	margin-bottom: 0 !important;
}

.my-xl-1 {
	margin-top: 0.25rem !important;
	margin-bottom: 0.25rem !important;
}

.my-xl-2 {
	margin-top: 0.5rem !important;
	margin-bottom: 0.5rem !important;
}

.my-xl-3 {
	margin-top: 1rem !important;
	margin-bottom: 1rem !important;
}

.my-xl-4 {
	margin-top: 1.5rem !important;
	margin-bottom: 1.5rem !important;
}

.my-xl-5 {
	margin-top: 3rem !important;
	margin-bottom: 3rem !important;
}

.my-xl-10 {
	margin-top: 0.625rem !important;
	margin-bottom: 0.625rem !important;
}

.my-xl-15 {
	margin-top: 0.9375rem !important;
	margin-bottom: 0.9375rem !important;
}

.my-xl-20 {
	margin-top: 1.25rem !important;
	margin-bottom: 1.25rem !important;
}

.my-xl-25 {
	margin-top: 1.5625rem !important;
	margin-bottom: 1.5625rem !important;
}

.my-xl-30 {
	margin-top: 1.875rem !important;
	margin-bottom: 1.875rem !important;
}

.my-xl-35 {
	margin-top: 2.18753rem !important;
	margin-bottom: 2.18753rem !important;
}

.my-xl-40 {
	margin-top: 2.5rem !important;
	margin-bottom: 2.5rem !important;
}

.my-xl-45 {
	margin-top: 2.8125rem !important;
	margin-bottom: 2.8125rem !important;
}

.my-xl-50 {
	margin-top: 3.125rem !important;
	margin-bottom: 3.125rem !important;
}

.my-xl-auto {
	margin-top: auto !important;
	margin-bottom: auto !important;
}

.mt-xl-0 {
	margin-top: 0 !important;
}

.mt-xl-1 {
	margin-top: 0.25rem !important;
}

.mt-xl-2 {
	margin-top: 0.5rem !important;
}

.mt-xl-3 {
	margin-top: 1rem !important;
}

.mt-xl-4 {
	margin-top: 1.5rem !important;
}

.mt-xl-5 {
	margin-top: 3rem !important;
}

.mt-xl-10 {
	margin-top: 0.625rem !important;
}

.mt-xl-15 {
	margin-top: 0.9375rem !important;
}

.mt-xl-20 {
	margin-top: 1.25rem !important;
}

.mt-xl-25 {
	margin-top: 1.5625rem !important;
}

.mt-xl-30 {
	margin-top: 1.875rem !important;
}

.mt-xl-35 {
	margin-top: 2.18753rem !important;
}

.mt-xl-40 {
	margin-top: 2.5rem !important;
}

.mt-xl-45 {
	margin-top: 2.8125rem !important;
}

.mt-xl-50 {
	margin-top: 3.125rem !important;
}

.mt-xl-auto {
	margin-top: auto !important;
}

.me-xl-0 {
	margin-right: 0 !important;
}

.me-xl-1 {
	margin-right: 0.25rem !important;
}

.me-xl-2 {
	margin-right: 0.5rem !important;
}

.me-xl-3 {
	margin-right: 1rem !important;
}

.me-xl-4 {
	margin-right: 1.5rem !important;
}

.me-xl-5 {
	margin-right: 3rem !important;
}

.me-xl-10 {
	margin-right: 0.625rem !important;
}

.me-xl-15 {
	margin-right: 0.9375rem !important;
}

.me-xl-20 {
	margin-right: 1.25rem !important;
}

.me-xl-25 {
	margin-right: 1.5625rem !important;
}

.me-xl-30 {
	margin-right: 1.875rem !important;
}

.me-xl-35 {
	margin-right: 2.18753rem !important;
}

.me-xl-40 {
	margin-right: 2.5rem !important;
}

.me-xl-45 {
	margin-right: 2.8125rem !important;
}

.me-xl-50 {
	margin-right: 3.125rem !important;
}

.me-xl-auto {
	margin-right: auto !important;
}

.mb-xl-0 {
	margin-bottom: 0 !important;
}

.mb-xl-1 {
	margin-bottom: 0.25rem !important;
}

.mb-xl-2 {
	margin-bottom: 0.5rem !important;
}

.mb-xl-3 {
	margin-bottom: 1rem !important;
}

.mb-xl-4 {
	margin-bottom: 1.5rem !important;
}

.mb-xl-5 {
	margin-bottom: 3rem !important;
}

.mb-xl-10 {
	margin-bottom: 0.625rem !important;
}

.mb-xl-15 {
	margin-bottom: 0.9375rem !important;
}

.mb-xl-20 {
	margin-bottom: 1.25rem !important;
}

.mb-xl-25 {
	margin-bottom: 1.5625rem !important;
}

.mb-xl-30 {
	margin-bottom: 1.875rem !important;
}

.mb-xl-35 {
	margin-bottom: 2.18753rem !important;
}

.mb-xl-40 {
	margin-bottom: 2.5rem !important;
}

.mb-xl-45 {
	margin-bottom: 2.8125rem !important;
}

.mb-xl-50 {
	margin-bottom: 3.125rem !important;
}

.mb-xl-auto {
	margin-bottom: auto !important;
}

.ms-xl-0 {
	margin-left: 0 !important;
}

.ms-xl-1 {
	margin-left: 0.25rem !important;
}

.ms-xl-2 {
	margin-left: 0.5rem !important;
}

.ms-xl-3 {
	margin-left: 1rem !important;
}

.ms-xl-4 {
	margin-left: 1.5rem !important;
}

.ms-xl-5 {
	margin-left: 3rem !important;
}

.ms-xl-10 {
	margin-left: 0.625rem !important;
}

.ms-xl-15 {
	margin-left: 0.9375rem !important;
}

.ms-xl-20 {
	margin-left: 1.25rem !important;
}

.ms-xl-25 {
	margin-left: 1.5625rem !important;
}

.ms-xl-30 {
	margin-left: 1.875rem !important;
}

.ms-xl-35 {
	margin-left: 2.18753rem !important;
}

.ms-xl-40 {
	margin-left: 2.5rem !important;
}

.ms-xl-45 {
	margin-left: 2.8125rem !important;
}

.ms-xl-50 {
	margin-left: 3.125rem !important;
}

.ms-xl-auto {
	margin-left: auto !important;
}

.m-xl-n1 {
	margin: -0.25rem !important;
}

.m-xl-n2 {
	margin: -0.5rem !important;
}

.m-xl-n3 {
	margin: -1rem !important;
}

.m-xl-n4 {
	margin: -1.5rem !important;
}

.m-xl-n5 {
	margin: -3rem !important;
}

.m-xl-n10 {
	margin: -0.625rem !important;
}

.m-xl-n15 {
	margin: -0.9375rem !important;
}

.m-xl-n20 {
	margin: -1.25rem !important;
}

.m-xl-n25 {
	margin: -1.5625rem !important;
}

.m-xl-n30 {
	margin: -1.875rem !important;
}

.m-xl-n35 {
	margin: -2.18753rem !important;
}

.m-xl-n40 {
	margin: -2.5rem !important;
}

.m-xl-n45 {
	margin: -2.8125rem !important;
}

.m-xl-n50 {
	margin: -3.125rem !important;
}

.mx-xl-n1 {
	margin-right: -0.25rem !important;
	margin-left: -0.25rem !important;
}

.mx-xl-n2 {
	margin-right: -0.5rem !important;
	margin-left: -0.5rem !important;
}

.mx-xl-n3 {
	margin-right: -1rem !important;
	margin-left: -1rem !important;
}

.mx-xl-n4 {
	margin-right: -1.5rem !important;
	margin-left: -1.5rem !important;
}

.mx-xl-n5 {
	margin-right: -3rem !important;
	margin-left: -3rem !important;
}

.mx-xl-n10 {
	margin-right: -0.625rem !important;
	margin-left: -0.625rem !important;
}

.mx-xl-n15 {
	margin-right: -0.9375rem !important;
	margin-left: -0.9375rem !important;
}

.mx-xl-n20 {
	margin-right: -1.25rem !important;
	margin-left: -1.25rem !important;
}

.mx-xl-n25 {
	margin-right: -1.5625rem !important;
	margin-left: -1.5625rem !important;
}

.mx-xl-n30 {
	margin-right: -1.875rem !important;
	margin-left: -1.875rem !important;
}

.mx-xl-n35 {
	margin-right: -2.18753rem !important;
	margin-left: -2.18753rem !important;
}

.mx-xl-n40 {
	margin-right: -2.5rem !important;
	margin-left: -2.5rem !important;
}

.mx-xl-n45 {
	margin-right: -2.8125rem !important;
	margin-left: -2.8125rem !important;
}

.mx-xl-n50 {
	margin-right: -3.125rem !important;
	margin-left: -3.125rem !important;
}

.my-xl-n1 {
	margin-top: -0.25rem !important;
	margin-bottom: -0.25rem !important;
}

.my-xl-n2 {
	margin-top: -0.5rem !important;
	margin-bottom: -0.5rem !important;
}

.my-xl-n3 {
	margin-top: -1rem !important;
	margin-bottom: -1rem !important;
}

.my-xl-n4 {
	margin-top: -1.5rem !important;
	margin-bottom: -1.5rem !important;
}

.my-xl-n5 {
	margin-top: -3rem !important;
	margin-bottom: -3rem !important;
}

.my-xl-n10 {
	margin-top: -0.625rem !important;
	margin-bottom: -0.625rem !important;
}

.my-xl-n15 {
	margin-top: -0.9375rem !important;
	margin-bottom: -0.9375rem !important;
}

.my-xl-n20 {
	margin-top: -1.25rem !important;
	margin-bottom: -1.25rem !important;
}

.my-xl-n25 {
	margin-top: -1.5625rem !important;
	margin-bottom: -1.5625rem !important;
}

.my-xl-n30 {
	margin-top: -1.875rem !important;
	margin-bottom: -1.875rem !important;
}

.my-xl-n35 {
	margin-top: -2.18753rem !important;
	margin-bottom: -2.18753rem !important;
}

.my-xl-n40 {
	margin-top: -2.5rem !important;
	margin-bottom: -2.5rem !important;
}

.my-xl-n45 {
	margin-top: -2.8125rem !important;
	margin-bottom: -2.8125rem !important;
}

.my-xl-n50 {
	margin-top: -3.125rem !important;
	margin-bottom: -3.125rem !important;
}

.mt-xl-n1 {
	margin-top: -0.25rem !important;
}

.mt-xl-n2 {
	margin-top: -0.5rem !important;
}

.mt-xl-n3 {
	margin-top: -1rem !important;
}

.mt-xl-n4 {
	margin-top: -1.5rem !important;
}

.mt-xl-n5 {
	margin-top: -3rem !important;
}

.mt-xl-n10 {
	margin-top: -0.625rem !important;
}

.mt-xl-n15 {
	margin-top: -0.9375rem !important;
}

.mt-xl-n20 {
	margin-top: -1.25rem !important;
}

.mt-xl-n25 {
	margin-top: -1.5625rem !important;
}

.mt-xl-n30 {
	margin-top: -1.875rem !important;
}

.mt-xl-n35 {
	margin-top: -2.18753rem !important;
}

.mt-xl-n40 {
	margin-top: -2.5rem !important;
}

.mt-xl-n45 {
	margin-top: -2.8125rem !important;
}

.mt-xl-n50 {
	margin-top: -3.125rem !important;
}

.me-xl-n1 {
	margin-right: -0.25rem !important;
}

.me-xl-n2 {
	margin-right: -0.5rem !important;
}

.me-xl-n3 {
	margin-right: -1rem !important;
}

.me-xl-n4 {
	margin-right: -1.5rem !important;
}

.me-xl-n5 {
	margin-right: -3rem !important;
}

.me-xl-n10 {
	margin-right: -0.625rem !important;
}

.me-xl-n15 {
	margin-right: -0.9375rem !important;
}

.me-xl-n20 {
	margin-right: -1.25rem !important;
}

.me-xl-n25 {
	margin-right: -1.5625rem !important;
}

.me-xl-n30 {
	margin-right: -1.875rem !important;
}

.me-xl-n35 {
	margin-right: -2.18753rem !important;
}

.me-xl-n40 {
	margin-right: -2.5rem !important;
}

.me-xl-n45 {
	margin-right: -2.8125rem !important;
}

.me-xl-n50 {
	margin-right: -3.125rem !important;
}

.mb-xl-n1 {
	margin-bottom: -0.25rem !important;
}

.mb-xl-n2 {
	margin-bottom: -0.5rem !important;
}

.mb-xl-n3 {
	margin-bottom: -1rem !important;
}

.mb-xl-n4 {
	margin-bottom: -1.5rem !important;
}

.mb-xl-n5 {
	margin-bottom: -3rem !important;
}

.mb-xl-n10 {
	margin-bottom: -0.625rem !important;
}

.mb-xl-n15 {
	margin-bottom: -0.9375rem !important;
}

.mb-xl-n20 {
	margin-bottom: -1.25rem !important;
}

.mb-xl-n25 {
	margin-bottom: -1.5625rem !important;
}

.mb-xl-n30 {
	margin-bottom: -1.875rem !important;
}

.mb-xl-n35 {
	margin-bottom: -2.18753rem !important;
}

.mb-xl-n40 {
	margin-bottom: -2.5rem !important;
}

.mb-xl-n45 {
	margin-bottom: -2.8125rem !important;
}

.mb-xl-n50 {
	margin-bottom: -3.125rem !important;
}

.ms-xl-n1 {
	margin-left: -0.25rem !important;
}

.ms-xl-n2 {
	margin-left: -0.5rem !important;
}

.ms-xl-n3 {
	margin-left: -1rem !important;
}

.ms-xl-n4 {
	margin-left: -1.5rem !important;
}

.ms-xl-n5 {
	margin-left: -3rem !important;
}

.ms-xl-n10 {
	margin-left: -0.625rem !important;
}

.ms-xl-n15 {
	margin-left: -0.9375rem !important;
}

.ms-xl-n20 {
	margin-left: -1.25rem !important;
}

.ms-xl-n25 {
	margin-left: -1.5625rem !important;
}

.ms-xl-n30 {
	margin-left: -1.875rem !important;
}

.ms-xl-n35 {
	margin-left: -2.18753rem !important;
}

.ms-xl-n40 {
	margin-left: -2.5rem !important;
}

.ms-xl-n45 {
	margin-left: -2.8125rem !important;
}

.ms-xl-n50 {
	margin-left: -3.125rem !important;
}

.p-xl-0 {
	padding: 0 !important;
}

.p-xl-1 {
	padding: 0.25rem !important;
}

.p-xl-2 {
	padding: 0.5rem !important;
}

.p-xl-3 {
	padding: 1rem !important;
}

.p-xl-4 {
	padding: 1.5rem !important;
}

.p-xl-5 {
	padding: 3rem !important;
}

.p-xl-10 {
	padding: 0.625rem !important;
}

.p-xl-15 {
	padding: 0.9375rem !important;
}

.p-xl-20 {
	padding: 1.25rem !important;
}

.p-xl-25 {
	padding: 1.5625rem !important;
}

.p-xl-30 {
	padding: 1.875rem !important;
}

.p-xl-35 {
	padding: 2.18753rem !important;
}

.p-xl-40 {
	padding: 2.5rem !important;
}

.p-xl-45 {
	padding: 2.8125rem !important;
}

.p-xl-50 {
	padding: 3.125rem !important;
}

.px-xl-0 {
	padding-right: 0 !important;
	padding-left: 0 !important;
}

.px-xl-1 {
	padding-right: 0.25rem !important;
	padding-left: 0.25rem !important;
}

.px-xl-2 {
	padding-right: 0.5rem !important;
	padding-left: 0.5rem !important;
}

.px-xl-3 {
	padding-right: 1rem !important;
	padding-left: 1rem !important;
}

.px-xl-4 {
	padding-right: 1.5rem !important;
	padding-left: 1.5rem !important;
}

.px-xl-5 {
	padding-right: 3rem !important;
	padding-left: 3rem !important;
}

.px-xl-10 {
	padding-right: 0.625rem !important;
	padding-left: 0.625rem !important;
}

.px-xl-15 {
	padding-right: 0.9375rem !important;
	padding-left: 0.9375rem !important;
}

.px-xl-20 {
	padding-right: 1.25rem !important;
	padding-left: 1.25rem !important;
}

.px-xl-25 {
	padding-right: 1.5625rem !important;
	padding-left: 1.5625rem !important;
}

.px-xl-30 {
	padding-right: 1.875rem !important;
	padding-left: 1.875rem !important;
}

.px-xl-35 {
	padding-right: 2.18753rem !important;
	padding-left: 2.18753rem !important;
}

.px-xl-40 {
	padding-right: 2.5rem !important;
	padding-left: 2.5rem !important;
}

.px-xl-45 {
	padding-right: 2.8125rem !important;
	padding-left: 2.8125rem !important;
}

.px-xl-50 {
	padding-right: 3.125rem !important;
	padding-left: 3.125rem !important;
}

.py-xl-0 {
	padding-top: 0 !important;
	padding-bottom: 0 !important;
}

.py-xl-1 {
	padding-top: 0.25rem !important;
	padding-bottom: 0.25rem !important;
}

.py-xl-2 {
	padding-top: 0.5rem !important;
	padding-bottom: 0.5rem !important;
}

.py-xl-3 {
	padding-top: 1rem !important;
	padding-bottom: 1rem !important;
}

.py-xl-4 {
	padding-top: 1.5rem !important;
	padding-bottom: 1.5rem !important;
}

.py-xl-5 {
	padding-top: 3rem !important;
	padding-bottom: 3rem !important;
}

.py-xl-10 {
	padding-top: 0.625rem !important;
	padding-bottom: 0.625rem !important;
}

.py-xl-15 {
	padding-top: 0.9375rem !important;
	padding-bottom: 0.9375rem !important;
}

.py-xl-20 {
	padding-top: 1.25rem !important;
	padding-bottom: 1.25rem !important;
}

.py-xl-25 {
	padding-top: 1.5625rem !important;
	padding-bottom: 1.5625rem !important;
}

.py-xl-30 {
	padding-top: 1.875rem !important;
	padding-bottom: 1.875rem !important;
}

.py-xl-35 {
	padding-top: 2.18753rem !important;
	padding-bottom: 2.18753rem !important;
}

.py-xl-40 {
	padding-top: 2.5rem !important;
	padding-bottom: 2.5rem !important;
}

.py-xl-45 {
	padding-top: 2.8125rem !important;
	padding-bottom: 2.8125rem !important;
}

.py-xl-50 {
	padding-top: 3.125rem !important;
	padding-bottom: 3.125rem !important;
}

.pt-xl-0 {
	padding-top: 0 !important;
}

.pt-xl-1 {
	padding-top: 0.25rem !important;
}

.pt-xl-2 {
	padding-top: 0.5rem !important;
}

.pt-xl-3 {
	padding-top: 1rem !important;
}

.pt-xl-4 {
	padding-top: 1.5rem !important;
}

.pt-xl-5 {
	padding-top: 3rem !important;
}

.pt-xl-10 {
	padding-top: 0.625rem !important;
}

.pt-xl-15 {
	padding-top: 0.9375rem !important;
}

.pt-xl-20 {
	padding-top: 1.25rem !important;
}

.pt-xl-25 {
	padding-top: 1.5625rem !important;
}

.pt-xl-30 {
	padding-top: 1.875rem !important;
}

.pt-xl-35 {
	padding-top: 2.18753rem !important;
}

.pt-xl-40 {
	padding-top: 2.5rem !important;
}

.pt-xl-45 {
	padding-top: 2.8125rem !important;
}

.pt-xl-50 {
	padding-top: 3.125rem !important;
}

.pe-xl-0 {
	padding-right: 0 !important;
}

.pe-xl-1 {
	padding-right: 0.25rem !important;
}

.pe-xl-2 {
	padding-right: 0.5rem !important;
}

.pe-xl-3 {
	padding-right: 1rem !important;
}

.pe-xl-4 {
	padding-right: 1.5rem !important;
}

.pe-xl-5 {
	padding-right: 3rem !important;
}

.pe-xl-10 {
	padding-right: 0.625rem !important;
}

.pe-xl-15 {
	padding-right: 0.9375rem !important;
}

.pe-xl-20 {
	padding-right: 1.25rem !important;
}

.pe-xl-25 {
	padding-right: 1.5625rem !important;
}

.pe-xl-30 {
	padding-right: 1.875rem !important;
}

.pe-xl-35 {
	padding-right: 2.18753rem !important;
}

.pe-xl-40 {
	padding-right: 2.5rem !important;
}

.pe-xl-45 {
	padding-right: 2.8125rem !important;
}

.pe-xl-50 {
	padding-right: 3.125rem !important;
}

.pb-xl-0 {
	padding-bottom: 0 !important;
}

.pb-xl-1 {
	padding-bottom: 0.25rem !important;
}

.pb-xl-2 {
	padding-bottom: 0.5rem !important;
}

.pb-xl-3 {
	padding-bottom: 1rem !important;
}

.pb-xl-4 {
	padding-bottom: 1.5rem !important;
}

.pb-xl-5 {
	padding-bottom: 3rem !important;
}

.pb-xl-10 {
	padding-bottom: 0.625rem !important;
}

.pb-xl-15 {
	padding-bottom: 0.9375rem !important;
}

.pb-xl-20 {
	padding-bottom: 1.25rem !important;
}

.pb-xl-25 {
	padding-bottom: 1.5625rem !important;
}

.pb-xl-30 {
	padding-bottom: 1.875rem !important;
}

.pb-xl-35 {
	padding-bottom: 2.18753rem !important;
}

.pb-xl-40 {
	padding-bottom: 2.5rem !important;
}

.pb-xl-45 {
	padding-bottom: 2.8125rem !important;
}

.pb-xl-50 {
	padding-bottom: 3.125rem !important;
}

.ps-xl-0 {
	padding-left: 0 !important;
}

.ps-xl-1 {
	padding-left: 0.25rem !important;
}

.ps-xl-2 {
	padding-left: 0.5rem !important;
}

.ps-xl-3 {
	padding-left: 1rem !important;
}

.ps-xl-4 {
	padding-left: 1.5rem !important;
}

.ps-xl-5 {
	padding-left: 3rem !important;
}

.ps-xl-10 {
	padding-left: 0.625rem !important;
}

.ps-xl-15 {
	padding-left: 0.9375rem !important;
}

.ps-xl-20 {
	padding-left: 1.25rem !important;
}

.ps-xl-25 {
	padding-left: 1.5625rem !important;
}

.ps-xl-30 {
	padding-left: 1.875rem !important;
}

.ps-xl-35 {
	padding-left: 2.18753rem !important;
}

.ps-xl-40 {
	padding-left: 2.5rem !important;
}

.ps-xl-45 {
	padding-left: 2.8125rem !important;
}

.ps-xl-50 {
	padding-left: 3.125rem !important;
}

.text-xl-start {
	text-align: left !important;
}

.text-xl-end {
	text-align: right !important;
}

.text-xl-center {
	text-align: center !important;
}

.fs-1 {
	font-size: 40px !important;
}

.fs-2 {
	font-size: 32px !important;
}

.fs-3 {
	font-size: 28px !important;
}

.fs-4 {
	font-size: 24px !important;
}

.fs-5 {
	font-size: 20px !important;
}

}

@media (min-width: 1600px) {

.container-xxl,
.container-xl,
.container-lg,
.container-md,
.container-sm,
.container {
	max-width: 1320px;
}

.col-xxl {
	-webkit-box-flex: 1;
	-ms-flex: 1 0 0%;
	flex: 1 0 0%;
}

.row-cols-xxl-auto > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: auto;
}

.row-cols-xxl-1 > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 100%;
}

.row-cols-xxl-2 > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 50%;
}

.row-cols-xxl-3 > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 33.3333333333%;
}

.row-cols-xxl-4 > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 25%;
}

.row-cols-xxl-5 > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 20%;
}

.row-cols-xxl-6 > * {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 16.6666666667%;
}

.col-xxl-auto {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: auto;
}

.col-xxl-1 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 8.33333333%;
}

.col-xxl-2 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 16.66666667%;
}

.col-xxl-3 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 25%;
}

.col-xxl-4 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 33.33333333%;
}

.col-xxl-5 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 41.66666667%;
}

.col-xxl-6 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 50%;
}

.col-xxl-7 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 58.33333333%;
}

.col-xxl-8 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 66.66666667%;
}

.col-xxl-9 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 75%;
}

.col-xxl-10 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 83.33333333%;
}

.col-xxl-11 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 91.66666667%;
}

.col-xxl-12 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: 100%;
}

.offset-xxl-0 {
	margin-left: 0;
}

.offset-xxl-1 {
	margin-left: 8.33333333%;
}

.offset-xxl-2 {
	margin-left: 16.66666667%;
}

.offset-xxl-3 {
	margin-left: 25%;
}

.offset-xxl-4 {
	margin-left: 33.33333333%;
}

.offset-xxl-5 {
	margin-left: 41.66666667%;
}

.offset-xxl-6 {
	margin-left: 50%;
}

.offset-xxl-7 {
	margin-left: 58.33333333%;
}

.offset-xxl-8 {
	margin-left: 66.66666667%;
}

.offset-xxl-9 {
	margin-left: 75%;
}

.offset-xxl-10 {
	margin-left: 83.33333333%;
}

.offset-xxl-11 {
	margin-left: 91.66666667%;
}

.g-xxl-0,
.gx-xxl-0 {
	--bs-gutter-x: 0;
}

.g-xxl-0,
.gy-xxl-0 {
	--bs-gutter-y: 0;
}

.g-xxl-1,
.gx-xxl-1 {
	--bs-gutter-x: 0.25rem;
}

.g-xxl-1,
.gy-xxl-1 {
	--bs-gutter-y: 0.25rem;
}

.g-xxl-2,
.gx-xxl-2 {
	--bs-gutter-x: 0.5rem;
}

.g-xxl-2,
.gy-xxl-2 {
	--bs-gutter-y: 0.5rem;
}

.g-xxl-3,
.gx-xxl-3 {
	--bs-gutter-x: 1rem;
}

.g-xxl-3,
.gy-xxl-3 {
	--bs-gutter-y: 1rem;
}

.g-xxl-4,
.gx-xxl-4 {
	--bs-gutter-x: 1.5rem;
}

.g-xxl-4,
.gy-xxl-4 {
	--bs-gutter-y: 1.5rem;
}

.g-xxl-5,
.gx-xxl-5 {
	--bs-gutter-x: 3rem;
}

.g-xxl-5,
.gy-xxl-5 {
	--bs-gutter-y: 3rem;
}

.dropdown-menu-xxl-start {
	--bs-position: start;
}

.dropdown-menu-xxl-start[data-bs-popper] {
	right: auto;
	left: 0;
}

.dropdown-menu-xxl-end {
	--bs-position: end;
}

.dropdown-menu-xxl-end[data-bs-popper] {
	right: 0;
	left: auto;
}

.navbar-expand-xxl {
	-ms-flex-wrap: nowrap;
	flex-wrap: nowrap;
	-webkit-box-pack: start;
	-ms-flex-pack: start;
	justify-content: flex-start;
}

.navbar-expand-xxl .navbar-nav {
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-ms-flex-direction: row;
	flex-direction: row;
}

.navbar-expand-xxl .navbar-nav .dropdown-menu {
	position: absolute;
}

.navbar-expand-xxl .navbar-nav .nav-link {
	padding-right: 0.5rem;
	padding-left: 0.5rem;
}

.navbar-expand-xxl .navbar-nav-scroll {
	overflow: visible;
}

.navbar-expand-xxl .navbar-collapse {
	display: -webkit-box !important;
	display: -ms-flexbox !important;
	display: flex !important;
	-ms-flex-preferred-size: auto;
	flex-basis: auto;
}

.navbar-expand-xxl .navbar-toggler {
	display: none;
}

.list-group-horizontal-xxl {
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-ms-flex-direction: row;
	flex-direction: row;
}

.list-group-horizontal-xxl > .list-group-item:first-child {
	-webkit-border-bottom-left-radius: 0.25rem;
	border-bottom-left-radius: 0.25rem;
	-webkit-border-top-right-radius: 0;
	border-top-right-radius: 0;
}

.list-group-horizontal-xxl > .list-group-item:last-child {
	-webkit-border-top-right-radius: 0.25rem;
	border-top-right-radius: 0.25rem;
	-webkit-border-bottom-left-radius: 0;
	border-bottom-left-radius: 0;
}

.list-group-horizontal-xxl > .list-group-item.active {
	margin-top: 0;
}

.list-group-horizontal-xxl > .list-group-item + .list-group-item {
	border-top-width: 1px;
	border-left-width: 0;
}

.list-group-horizontal-xxl > .list-group-item + .list-group-item.active {
	margin-left: -1px;
	border-left-width: 1px;
}

.sticky-xxl-top {
	position: sticky;
	top: 0;
	z-index: 1020;
}

.float-xxl-start {
	float: left !important;
}

.float-xxl-end {
	float: right !important;
}

.float-xxl-none {
	float: none !important;
}

.d-xxl-inline {
	display: inline !important;
}

.d-xxl-inline-block {
	display: inline-block !important;
}

.d-xxl-block {
	display: block !important;
}

.d-xxl-grid {
	display: grid !important;
}

.d-xxl-table {
	display: table !important;
}

.d-xxl-table-row {
	display: table-row !important;
}

.d-xxl-table-cell {
	display: table-cell !important;
}

.d-xxl-flex {
	display: -webkit-box !important;
	display: -ms-flexbox !important;
	display: flex !important;
}

.d-xxl-inline-flex {
	display: -webkit-inline-box !important;
	display: -ms-inline-flexbox !important;
	display: inline-flex !important;
}

.d-xxl-none {
	display: none !important;
}

.flex-xxl-fill {
	-webkit-box-flex: 1 !important;
	-ms-flex: 1 1 auto !important;
	flex: 1 1 auto !important;
}

.flex-xxl-row {
	-webkit-box-orient: horizontal !important;
	-webkit-box-direction: normal !important;
	-ms-flex-direction: row !important;
	flex-direction: row !important;
}

.flex-xxl-column {
	-webkit-box-orient: vertical !important;
	-webkit-box-direction: normal !important;
	-ms-flex-direction: column !important;
	flex-direction: column !important;
}

.flex-xxl-row-reverse {
	-webkit-box-orient: horizontal !important;
	-webkit-box-direction: reverse !important;
	-ms-flex-direction: row-reverse !important;
	flex-direction: row-reverse !important;
}

.flex-xxl-column-reverse {
	-webkit-box-orient: vertical !important;
	-webkit-box-direction: reverse !important;
	-ms-flex-direction: column-reverse !important;
	flex-direction: column-reverse !important;
}

.flex-xxl-grow-0 {
	-webkit-box-flex: 0 !important;
	-ms-flex-positive: 0 !important;
	flex-grow: 0 !important;
}

.flex-xxl-grow-1 {
	-webkit-box-flex: 1 !important;
	-ms-flex-positive: 1 !important;
	flex-grow: 1 !important;
}

.flex-xxl-shrink-0 {
	-ms-flex-negative: 0 !important;
	flex-shrink: 0 !important;
}

.flex-xxl-shrink-1 {
	-ms-flex-negative: 1 !important;
	flex-shrink: 1 !important;
}

.flex-xxl-wrap {
	-ms-flex-wrap: wrap !important;
	flex-wrap: wrap !important;
}

.flex-xxl-nowrap {
	-ms-flex-wrap: nowrap !important;
	flex-wrap: nowrap !important;
}

.flex-xxl-wrap-reverse {
	-ms-flex-wrap: wrap-reverse !important;
	flex-wrap: wrap-reverse !important;
}

.gap-xxl-0 {
	gap: 0 !important;
}

.gap-xxl-1 {
	gap: 0.25rem !important;
}

.gap-xxl-2 {
	gap: 0.5rem !important;
}

.gap-xxl-3 {
	gap: 1rem !important;
}

.gap-xxl-4 {
	gap: 1.5rem !important;
}

.gap-xxl-5 {
	gap: 3rem !important;
}

.gap-xxl-10 {
	gap: 0.625rem !important;
}

.gap-xxl-15 {
	gap: 0.9375rem !important;
}

.gap-xxl-20 {
	gap: 1.25rem !important;
}

.gap-xxl-25 {
	gap: 1.5625rem !important;
}

.gap-xxl-30 {
	gap: 1.875rem !important;
}

.gap-xxl-35 {
	gap: 2.18753rem !important;
}

.gap-xxl-40 {
	gap: 2.5rem !important;
}

.gap-xxl-45 {
	gap: 2.8125rem !important;
}

.gap-xxl-50 {
	gap: 3.125rem !important;
}

.justify-content-xxl-start {
	-webkit-box-pack: start !important;
	-ms-flex-pack: start !important;
	justify-content: flex-start !important;
}

.justify-content-xxl-end {
	-webkit-box-pack: end !important;
	-ms-flex-pack: end !important;
	justify-content: flex-end !important;
}

.justify-content-xxl-center {
	-webkit-box-pack: center !important;
	-ms-flex-pack: center !important;
	justify-content: center !important;
}

.justify-content-xxl-between {
	-webkit-box-pack: justify !important;
	-ms-flex-pack: justify !important;
	justify-content: space-between !important;
}

.justify-content-xxl-around {
	-ms-flex-pack: distribute !important;
	justify-content: space-around !important;
}

.justify-content-xxl-evenly {
	-webkit-box-pack: space-evenly !important;
	-ms-flex-pack: space-evenly !important;
	justify-content: space-evenly !important;
}

.align-items-xxl-start {
	-webkit-box-align: start !important;
	-ms-flex-align: start !important;
	align-items: flex-start !important;
}

.align-items-xxl-end {
	-webkit-box-align: end !important;
	-ms-flex-align: end !important;
	align-items: flex-end !important;
}

.align-items-xxl-center {
	-webkit-box-align: center !important;
	-ms-flex-align: center !important;
	align-items: center !important;
}

.align-items-xxl-baseline {
	-webkit-box-align: baseline !important;
	-ms-flex-align: baseline !important;
	align-items: baseline !important;
}

.align-items-xxl-stretch {
	-webkit-box-align: stretch !important;
	-ms-flex-align: stretch !important;
	align-items: stretch !important;
}

.align-content-xxl-start {
	-ms-flex-line-pack: start !important;
	align-content: flex-start !important;
}

.align-content-xxl-end {
	-ms-flex-line-pack: end !important;
	align-content: flex-end !important;
}

.align-content-xxl-center {
	-ms-flex-line-pack: center !important;
	align-content: center !important;
}

.align-content-xxl-between {
	-ms-flex-line-pack: justify !important;
	align-content: space-between !important;
}

.align-content-xxl-around {
	-ms-flex-line-pack: distribute !important;
	align-content: space-around !important;
}

.align-content-xxl-stretch {
	-ms-flex-line-pack: stretch !important;
	align-content: stretch !important;
}

.align-self-xxl-auto {
	-ms-flex-item-align: auto !important;
	align-self: auto !important;
}

.align-self-xxl-start {
	-ms-flex-item-align: start !important;
	align-self: flex-start !important;
}

.align-self-xxl-end {
	-ms-flex-item-align: end !important;
	align-self: flex-end !important;
}

.align-self-xxl-center {
	-ms-flex-item-align: center !important;
	align-self: center !important;
}

.align-self-xxl-baseline {
	-ms-flex-item-align: baseline !important;
	align-self: baseline !important;
}

.align-self-xxl-stretch {
	-ms-flex-item-align: stretch !important;
	align-self: stretch !important;
}

.order-xxl-first {
	-webkit-box-ordinal-group: 0 !important;
	-ms-flex-order: -1 !important;
	order: -1 !important;
}

.order-xxl-0 {
	-webkit-box-ordinal-group: 1 !important;
	-ms-flex-order: 0 !important;
	order: 0 !important;
}

.order-xxl-1 {
	-webkit-box-ordinal-group: 2 !important;
	-ms-flex-order: 1 !important;
	order: 1 !important;
}

.order-xxl-2 {
	-webkit-box-ordinal-group: 3 !important;
	-ms-flex-order: 2 !important;
	order: 2 !important;
}

.order-xxl-3 {
	-webkit-box-ordinal-group: 4 !important;
	-ms-flex-order: 3 !important;
	order: 3 !important;
}

.order-xxl-4 {
	-webkit-box-ordinal-group: 5 !important;
	-ms-flex-order: 4 !important;
	order: 4 !important;
}

.order-xxl-5 {
	-webkit-box-ordinal-group: 6 !important;
	-ms-flex-order: 5 !important;
	order: 5 !important;
}

.order-xxl-last {
	-webkit-box-ordinal-group: 7 !important;
	-ms-flex-order: 6 !important;
	order: 6 !important;
}

.m-xxl-0 {
	margin: 0 !important;
}

.m-xxl-1 {
	margin: 0.25rem !important;
}

.m-xxl-2 {
	margin: 0.5rem !important;
}

.m-xxl-3 {
	margin: 1rem !important;
}

.m-xxl-4 {
	margin: 1.5rem !important;
}

.m-xxl-5 {
	margin: 3rem !important;
}

.m-xxl-10 {
	margin: 0.625rem !important;
}

.m-xxl-15 {
	margin: 0.9375rem !important;
}

.m-xxl-20 {
	margin: 1.25rem !important;
}

.m-xxl-25 {
	margin: 1.5625rem !important;
}

.m-xxl-30 {
	margin: 1.875rem !important;
}

.m-xxl-35 {
	margin: 2.18753rem !important;
}

.m-xxl-40 {
	margin: 2.5rem !important;
}

.m-xxl-45 {
	margin: 2.8125rem !important;
}

.m-xxl-50 {
	margin: 3.125rem !important;
}

.m-xxl-auto {
	margin: auto !important;
}

.mx-xxl-0 {
	margin-right: 0 !important;
	margin-left: 0 !important;
}

.mx-xxl-1 {
	margin-right: 0.25rem !important;
	margin-left: 0.25rem !important;
}

.mx-xxl-2 {
	margin-right: 0.5rem !important;
	margin-left: 0.5rem !important;
}

.mx-xxl-3 {
	margin-right: 1rem !important;
	margin-left: 1rem !important;
}

.mx-xxl-4 {
	margin-right: 1.5rem !important;
	margin-left: 1.5rem !important;
}

.mx-xxl-5 {
	margin-right: 3rem !important;
	margin-left: 3rem !important;
}

.mx-xxl-10 {
	margin-right: 0.625rem !important;
	margin-left: 0.625rem !important;
}

.mx-xxl-15 {
	margin-right: 0.9375rem !important;
	margin-left: 0.9375rem !important;
}

.mx-xxl-20 {
	margin-right: 1.25rem !important;
	margin-left: 1.25rem !important;
}

.mx-xxl-25 {
	margin-right: 1.5625rem !important;
	margin-left: 1.5625rem !important;
}

.mx-xxl-30 {
	margin-right: 1.875rem !important;
	margin-left: 1.875rem !important;
}

.mx-xxl-35 {
	margin-right: 2.18753rem !important;
	margin-left: 2.18753rem !important;
}

.mx-xxl-40 {
	margin-right: 2.5rem !important;
	margin-left: 2.5rem !important;
}

.mx-xxl-45 {
	margin-right: 2.8125rem !important;
	margin-left: 2.8125rem !important;
}

.mx-xxl-50 {
	margin-right: 3.125rem !important;
	margin-left: 3.125rem !important;
}

.mx-xxl-auto {
	margin-right: auto !important;
	margin-left: auto !important;
}

.my-xxl-0 {
	margin-top: 0 !important;
	margin-bottom: 0 !important;
}

.my-xxl-1 {
	margin-top: 0.25rem !important;
	margin-bottom: 0.25rem !important;
}

.my-xxl-2 {
	margin-top: 0.5rem !important;
	margin-bottom: 0.5rem !important;
}

.my-xxl-3 {
	margin-top: 1rem !important;
	margin-bottom: 1rem !important;
}

.my-xxl-4 {
	margin-top: 1.5rem !important;
	margin-bottom: 1.5rem !important;
}

.my-xxl-5 {
	margin-top: 3rem !important;
	margin-bottom: 3rem !important;
}

.my-xxl-10 {
	margin-top: 0.625rem !important;
	margin-bottom: 0.625rem !important;
}

.my-xxl-15 {
	margin-top: 0.9375rem !important;
	margin-bottom: 0.9375rem !important;
}

.my-xxl-20 {
	margin-top: 1.25rem !important;
	margin-bottom: 1.25rem !important;
}

.my-xxl-25 {
	margin-top: 1.5625rem !important;
	margin-bottom: 1.5625rem !important;
}

.my-xxl-30 {
	margin-top: 1.875rem !important;
	margin-bottom: 1.875rem !important;
}

.my-xxl-35 {
	margin-top: 2.18753rem !important;
	margin-bottom: 2.18753rem !important;
}

.my-xxl-40 {
	margin-top: 2.5rem !important;
	margin-bottom: 2.5rem !important;
}

.my-xxl-45 {
	margin-top: 2.8125rem !important;
	margin-bottom: 2.8125rem !important;
}

.my-xxl-50 {
	margin-top: 3.125rem !important;
	margin-bottom: 3.125rem !important;
}

.my-xxl-auto {
	margin-top: auto !important;
	margin-bottom: auto !important;
}

.mt-xxl-0 {
	margin-top: 0 !important;
}

.mt-xxl-1 {
	margin-top: 0.25rem !important;
}

.mt-xxl-2 {
	margin-top: 0.5rem !important;
}

.mt-xxl-3 {
	margin-top: 1rem !important;
}

.mt-xxl-4 {
	margin-top: 1.5rem !important;
}

.mt-xxl-5 {
	margin-top: 3rem !important;
}

.mt-xxl-10 {
	margin-top: 0.625rem !important;
}

.mt-xxl-15 {
	margin-top: 0.9375rem !important;
}

.mt-xxl-20 {
	margin-top: 1.25rem !important;
}

.mt-xxl-25 {
	margin-top: 1.5625rem !important;
}

.mt-xxl-30 {
	margin-top: 1.875rem !important;
}

.mt-xxl-35 {
	margin-top: 2.18753rem !important;
}

.mt-xxl-40 {
	margin-top: 2.5rem !important;
}

.mt-xxl-45 {
	margin-top: 2.8125rem !important;
}

.mt-xxl-50 {
	margin-top: 3.125rem !important;
}

.mt-xxl-auto {
	margin-top: auto !important;
}

.me-xxl-0 {
	margin-right: 0 !important;
}

.me-xxl-1 {
	margin-right: 0.25rem !important;
}

.me-xxl-2 {
	margin-right: 0.5rem !important;
}

.me-xxl-3 {
	margin-right: 1rem !important;
}

.me-xxl-4 {
	margin-right: 1.5rem !important;
}

.me-xxl-5 {
	margin-right: 3rem !important;
}

.me-xxl-10 {
	margin-right: 0.625rem !important;
}

.me-xxl-15 {
	margin-right: 0.9375rem !important;
}

.me-xxl-20 {
	margin-right: 1.25rem !important;
}

.me-xxl-25 {
	margin-right: 1.5625rem !important;
}

.me-xxl-30 {
	margin-right: 1.875rem !important;
}

.me-xxl-35 {
	margin-right: 2.18753rem !important;
}

.me-xxl-40 {
	margin-right: 2.5rem !important;
}

.me-xxl-45 {
	margin-right: 2.8125rem !important;
}

.me-xxl-50 {
	margin-right: 3.125rem !important;
}

.me-xxl-auto {
	margin-right: auto !important;
}

.mb-xxl-0 {
	margin-bottom: 0 !important;
}

.mb-xxl-1 {
	margin-bottom: 0.25rem !important;
}

.mb-xxl-2 {
	margin-bottom: 0.5rem !important;
}

.mb-xxl-3 {
	margin-bottom: 1rem !important;
}

.mb-xxl-4 {
	margin-bottom: 1.5rem !important;
}

.mb-xxl-5 {
	margin-bottom: 3rem !important;
}

.mb-xxl-10 {
	margin-bottom: 0.625rem !important;
}

.mb-xxl-15 {
	margin-bottom: 0.9375rem !important;
}

.mb-xxl-20 {
	margin-bottom: 1.25rem !important;
}

.mb-xxl-25 {
	margin-bottom: 1.5625rem !important;
}

.mb-xxl-30 {
	margin-bottom: 1.875rem !important;
}

.mb-xxl-35 {
	margin-bottom: 2.18753rem !important;
}

.mb-xxl-40 {
	margin-bottom: 2.5rem !important;
}

.mb-xxl-45 {
	margin-bottom: 2.8125rem !important;
}

.mb-xxl-50 {
	margin-bottom: 3.125rem !important;
}

.mb-xxl-auto {
	margin-bottom: auto !important;
}

.ms-xxl-0 {
	margin-left: 0 !important;
}

.ms-xxl-1 {
	margin-left: 0.25rem !important;
}

.ms-xxl-2 {
	margin-left: 0.5rem !important;
}

.ms-xxl-3 {
	margin-left: 1rem !important;
}

.ms-xxl-4 {
	margin-left: 1.5rem !important;
}

.ms-xxl-5 {
	margin-left: 3rem !important;
}

.ms-xxl-10 {
	margin-left: 0.625rem !important;
}

.ms-xxl-15 {
	margin-left: 0.9375rem !important;
}

.ms-xxl-20 {
	margin-left: 1.25rem !important;
}

.ms-xxl-25 {
	margin-left: 1.5625rem !important;
}

.ms-xxl-30 {
	margin-left: 1.875rem !important;
}

.ms-xxl-35 {
	margin-left: 2.18753rem !important;
}

.ms-xxl-40 {
	margin-left: 2.5rem !important;
}

.ms-xxl-45 {
	margin-left: 2.8125rem !important;
}

.ms-xxl-50 {
	margin-left: 3.125rem !important;
}

.ms-xxl-auto {
	margin-left: auto !important;
}

.m-xxl-n1 {
	margin: -0.25rem !important;
}

.m-xxl-n2 {
	margin: -0.5rem !important;
}

.m-xxl-n3 {
	margin: -1rem !important;
}

.m-xxl-n4 {
	margin: -1.5rem !important;
}

.m-xxl-n5 {
	margin: -3rem !important;
}

.m-xxl-n10 {
	margin: -0.625rem !important;
}

.m-xxl-n15 {
	margin: -0.9375rem !important;
}

.m-xxl-n20 {
	margin: -1.25rem !important;
}

.m-xxl-n25 {
	margin: -1.5625rem !important;
}

.m-xxl-n30 {
	margin: -1.875rem !important;
}

.m-xxl-n35 {
	margin: -2.18753rem !important;
}

.m-xxl-n40 {
	margin: -2.5rem !important;
}

.m-xxl-n45 {
	margin: -2.8125rem !important;
}

.m-xxl-n50 {
	margin: -3.125rem !important;
}

.mx-xxl-n1 {
	margin-right: -0.25rem !important;
	margin-left: -0.25rem !important;
}

.mx-xxl-n2 {
	margin-right: -0.5rem !important;
	margin-left: -0.5rem !important;
}

.mx-xxl-n3 {
	margin-right: -1rem !important;
	margin-left: -1rem !important;
}

.mx-xxl-n4 {
	margin-right: -1.5rem !important;
	margin-left: -1.5rem !important;
}

.mx-xxl-n5 {
	margin-right: -3rem !important;
	margin-left: -3rem !important;
}

.mx-xxl-n10 {
	margin-right: -0.625rem !important;
	margin-left: -0.625rem !important;
}

.mx-xxl-n15 {
	margin-right: -0.9375rem !important;
	margin-left: -0.9375rem !important;
}

.mx-xxl-n20 {
	margin-right: -1.25rem !important;
	margin-left: -1.25rem !important;
}

.mx-xxl-n25 {
	margin-right: -1.5625rem !important;
	margin-left: -1.5625rem !important;
}

.mx-xxl-n30 {
	margin-right: -1.875rem !important;
	margin-left: -1.875rem !important;
}

.mx-xxl-n35 {
	margin-right: -2.18753rem !important;
	margin-left: -2.18753rem !important;
}

.mx-xxl-n40 {
	margin-right: -2.5rem !important;
	margin-left: -2.5rem !important;
}

.mx-xxl-n45 {
	margin-right: -2.8125rem !important;
	margin-left: -2.8125rem !important;
}

.mx-xxl-n50 {
	margin-right: -3.125rem !important;
	margin-left: -3.125rem !important;
}

.my-xxl-n1 {
	margin-top: -0.25rem !important;
	margin-bottom: -0.25rem !important;
}

.my-xxl-n2 {
	margin-top: -0.5rem !important;
	margin-bottom: -0.5rem !important;
}

.my-xxl-n3 {
	margin-top: -1rem !important;
	margin-bottom: -1rem !important;
}

.my-xxl-n4 {
	margin-top: -1.5rem !important;
	margin-bottom: -1.5rem !important;
}

.my-xxl-n5 {
	margin-top: -3rem !important;
	margin-bottom: -3rem !important;
}

.my-xxl-n10 {
	margin-top: -0.625rem !important;
	margin-bottom: -0.625rem !important;
}

.my-xxl-n15 {
	margin-top: -0.9375rem !important;
	margin-bottom: -0.9375rem !important;
}

.my-xxl-n20 {
	margin-top: -1.25rem !important;
	margin-bottom: -1.25rem !important;
}

.my-xxl-n25 {
	margin-top: -1.5625rem !important;
	margin-bottom: -1.5625rem !important;
}

.my-xxl-n30 {
	margin-top: -1.875rem !important;
	margin-bottom: -1.875rem !important;
}

.my-xxl-n35 {
	margin-top: -2.18753rem !important;
	margin-bottom: -2.18753rem !important;
}

.my-xxl-n40 {
	margin-top: -2.5rem !important;
	margin-bottom: -2.5rem !important;
}

.my-xxl-n45 {
	margin-top: -2.8125rem !important;
	margin-bottom: -2.8125rem !important;
}

.my-xxl-n50 {
	margin-top: -3.125rem !important;
	margin-bottom: -3.125rem !important;
}

.mt-xxl-n1 {
	margin-top: -0.25rem !important;
}

.mt-xxl-n2 {
	margin-top: -0.5rem !important;
}

.mt-xxl-n3 {
	margin-top: -1rem !important;
}

.mt-xxl-n4 {
	margin-top: -1.5rem !important;
}

.mt-xxl-n5 {
	margin-top: -3rem !important;
}

.mt-xxl-n10 {
	margin-top: -0.625rem !important;
}

.mt-xxl-n15 {
	margin-top: -0.9375rem !important;
}

.mt-xxl-n20 {
	margin-top: -1.25rem !important;
}

.mt-xxl-n25 {
	margin-top: -1.5625rem !important;
}

.mt-xxl-n30 {
	margin-top: -1.875rem !important;
}

.mt-xxl-n35 {
	margin-top: -2.18753rem !important;
}

.mt-xxl-n40 {
	margin-top: -2.5rem !important;
}

.mt-xxl-n45 {
	margin-top: -2.8125rem !important;
}

.mt-xxl-n50 {
	margin-top: -3.125rem !important;
}

.me-xxl-n1 {
	margin-right: -0.25rem !important;
}

.me-xxl-n2 {
	margin-right: -0.5rem !important;
}

.me-xxl-n3 {
	margin-right: -1rem !important;
}

.me-xxl-n4 {
	margin-right: -1.5rem !important;
}

.me-xxl-n5 {
	margin-right: -3rem !important;
}

.me-xxl-n10 {
	margin-right: -0.625rem !important;
}

.me-xxl-n15 {
	margin-right: -0.9375rem !important;
}

.me-xxl-n20 {
	margin-right: -1.25rem !important;
}

.me-xxl-n25 {
	margin-right: -1.5625rem !important;
}

.me-xxl-n30 {
	margin-right: -1.875rem !important;
}

.me-xxl-n35 {
	margin-right: -2.18753rem !important;
}

.me-xxl-n40 {
	margin-right: -2.5rem !important;
}

.me-xxl-n45 {
	margin-right: -2.8125rem !important;
}

.me-xxl-n50 {
	margin-right: -3.125rem !important;
}

.mb-xxl-n1 {
	margin-bottom: -0.25rem !important;
}

.mb-xxl-n2 {
	margin-bottom: -0.5rem !important;
}

.mb-xxl-n3 {
	margin-bottom: -1rem !important;
}

.mb-xxl-n4 {
	margin-bottom: -1.5rem !important;
}

.mb-xxl-n5 {
	margin-bottom: -3rem !important;
}

.mb-xxl-n10 {
	margin-bottom: -0.625rem !important;
}

.mb-xxl-n15 {
	margin-bottom: -0.9375rem !important;
}

.mb-xxl-n20 {
	margin-bottom: -1.25rem !important;
}

.mb-xxl-n25 {
	margin-bottom: -1.5625rem !important;
}

.mb-xxl-n30 {
	margin-bottom: -1.875rem !important;
}

.mb-xxl-n35 {
	margin-bottom: -2.18753rem !important;
}

.mb-xxl-n40 {
	margin-bottom: -2.5rem !important;
}

.mb-xxl-n45 {
	margin-bottom: -2.8125rem !important;
}

.mb-xxl-n50 {
	margin-bottom: -3.125rem !important;
}

.ms-xxl-n1 {
	margin-left: -0.25rem !important;
}

.ms-xxl-n2 {
	margin-left: -0.5rem !important;
}

.ms-xxl-n3 {
	margin-left: -1rem !important;
}

.ms-xxl-n4 {
	margin-left: -1.5rem !important;
}

.ms-xxl-n5 {
	margin-left: -3rem !important;
}

.ms-xxl-n10 {
	margin-left: -0.625rem !important;
}

.ms-xxl-n15 {
	margin-left: -0.9375rem !important;
}

.ms-xxl-n20 {
	margin-left: -1.25rem !important;
}

.ms-xxl-n25 {
	margin-left: -1.5625rem !important;
}

.ms-xxl-n30 {
	margin-left: -1.875rem !important;
}

.ms-xxl-n35 {
	margin-left: -2.18753rem !important;
}

.ms-xxl-n40 {
	margin-left: -2.5rem !important;
}

.ms-xxl-n45 {
	margin-left: -2.8125rem !important;
}

.ms-xxl-n50 {
	margin-left: -3.125rem !important;
}

.p-xxl-0 {
	padding: 0 !important;
}

.p-xxl-1 {
	padding: 0.25rem !important;
}

.p-xxl-2 {
	padding: 0.5rem !important;
}

.p-xxl-3 {
	padding: 1rem !important;
}

.p-xxl-4 {
	padding: 1.5rem !important;
}

.p-xxl-5 {
	padding: 3rem !important;
}

.p-xxl-10 {
	padding: 0.625rem !important;
}

.p-xxl-15 {
	padding: 0.9375rem !important;
}

.p-xxl-20 {
	padding: 1.25rem !important;
}

.p-xxl-25 {
	padding: 1.5625rem !important;
}

.p-xxl-30 {
	padding: 1.875rem !important;
}

.p-xxl-35 {
	padding: 2.18753rem !important;
}

.p-xxl-40 {
	padding: 2.5rem !important;
}

.p-xxl-45 {
	padding: 2.8125rem !important;
}

.p-xxl-50 {
	padding: 3.125rem !important;
}

.px-xxl-0 {
	padding-right: 0 !important;
	padding-left: 0 !important;
}

.px-xxl-1 {
	padding-right: 0.25rem !important;
	padding-left: 0.25rem !important;
}

.px-xxl-2 {
	padding-right: 0.5rem !important;
	padding-left: 0.5rem !important;
}

.px-xxl-3 {
	padding-right: 1rem !important;
	padding-left: 1rem !important;
}

.px-xxl-4 {
	padding-right: 1.5rem !important;
	padding-left: 1.5rem !important;
}

.px-xxl-5 {
	padding-right: 3rem !important;
	padding-left: 3rem !important;
}

.px-xxl-10 {
	padding-right: 0.625rem !important;
	padding-left: 0.625rem !important;
}

.px-xxl-15 {
	padding-right: 0.9375rem !important;
	padding-left: 0.9375rem !important;
}

.px-xxl-20 {
	padding-right: 1.25rem !important;
	padding-left: 1.25rem !important;
}

.px-xxl-25 {
	padding-right: 1.5625rem !important;
	padding-left: 1.5625rem !important;
}

.px-xxl-30 {
	padding-right: 1.875rem !important;
	padding-left: 1.875rem !important;
}

.px-xxl-35 {
	padding-right: 2.18753rem !important;
	padding-left: 2.18753rem !important;
}

.px-xxl-40 {
	padding-right: 2.5rem !important;
	padding-left: 2.5rem !important;
}

.px-xxl-45 {
	padding-right: 2.8125rem !important;
	padding-left: 2.8125rem !important;
}

.px-xxl-50 {
	padding-right: 3.125rem !important;
	padding-left: 3.125rem !important;
}

.py-xxl-0 {
	padding-top: 0 !important;
	padding-bottom: 0 !important;
}

.py-xxl-1 {
	padding-top: 0.25rem !important;
	padding-bottom: 0.25rem !important;
}

.py-xxl-2 {
	padding-top: 0.5rem !important;
	padding-bottom: 0.5rem !important;
}

.py-xxl-3 {
	padding-top: 1rem !important;
	padding-bottom: 1rem !important;
}

.py-xxl-4 {
	padding-top: 1.5rem !important;
	padding-bottom: 1.5rem !important;
}

.py-xxl-5 {
	padding-top: 3rem !important;
	padding-bottom: 3rem !important;
}

.py-xxl-10 {
	padding-top: 0.625rem !important;
	padding-bottom: 0.625rem !important;
}

.py-xxl-15 {
	padding-top: 0.9375rem !important;
	padding-bottom: 0.9375rem !important;
}

.py-xxl-20 {
	padding-top: 1.25rem !important;
	padding-bottom: 1.25rem !important;
}

.py-xxl-25 {
	padding-top: 1.5625rem !important;
	padding-bottom: 1.5625rem !important;
}

.py-xxl-30 {
	padding-top: 1.875rem !important;
	padding-bottom: 1.875rem !important;
}

.py-xxl-35 {
	padding-top: 2.18753rem !important;
	padding-bottom: 2.18753rem !important;
}

.py-xxl-40 {
	padding-top: 2.5rem !important;
	padding-bottom: 2.5rem !important;
}

.py-xxl-45 {
	padding-top: 2.8125rem !important;
	padding-bottom: 2.8125rem !important;
}

.py-xxl-50 {
	padding-top: 3.125rem !important;
	padding-bottom: 3.125rem !important;
}

.pt-xxl-0 {
	padding-top: 0 !important;
}

.pt-xxl-1 {
	padding-top: 0.25rem !important;
}

.pt-xxl-2 {
	padding-top: 0.5rem !important;
}

.pt-xxl-3 {
	padding-top: 1rem !important;
}

.pt-xxl-4 {
	padding-top: 1.5rem !important;
}

.pt-xxl-5 {
	padding-top: 3rem !important;
}

.pt-xxl-10 {
	padding-top: 0.625rem !important;
}

.pt-xxl-15 {
	padding-top: 0.9375rem !important;
}

.pt-xxl-20 {
	padding-top: 1.25rem !important;
}

.pt-xxl-25 {
	padding-top: 1.5625rem !important;
}

.pt-xxl-30 {
	padding-top: 1.875rem !important;
}

.pt-xxl-35 {
	padding-top: 2.18753rem !important;
}

.pt-xxl-40 {
	padding-top: 2.5rem !important;
}

.pt-xxl-45 {
	padding-top: 2.8125rem !important;
}

.pt-xxl-50 {
	padding-top: 3.125rem !important;
}

.pe-xxl-0 {
	padding-right: 0 !important;
}

.pe-xxl-1 {
	padding-right: 0.25rem !important;
}

.pe-xxl-2 {
	padding-right: 0.5rem !important;
}

.pe-xxl-3 {
	padding-right: 1rem !important;
}

.pe-xxl-4 {
	padding-right: 1.5rem !important;
}

.pe-xxl-5 {
	padding-right: 3rem !important;
}

.pe-xxl-10 {
	padding-right: 0.625rem !important;
}

.pe-xxl-15 {
	padding-right: 0.9375rem !important;
}

.pe-xxl-20 {
	padding-right: 1.25rem !important;
}

.pe-xxl-25 {
	padding-right: 1.5625rem !important;
}

.pe-xxl-30 {
	padding-right: 1.875rem !important;
}

.pe-xxl-35 {
	padding-right: 2.18753rem !important;
}

.pe-xxl-40 {
	padding-right: 2.5rem !important;
}

.pe-xxl-45 {
	padding-right: 2.8125rem !important;
}

.pe-xxl-50 {
	padding-right: 3.125rem !important;
}

.pb-xxl-0 {
	padding-bottom: 0 !important;
}

.pb-xxl-1 {
	padding-bottom: 0.25rem !important;
}

.pb-xxl-2 {
	padding-bottom: 0.5rem !important;
}

.pb-xxl-3 {
	padding-bottom: 1rem !important;
}

.pb-xxl-4 {
	padding-bottom: 1.5rem !important;
}

.pb-xxl-5 {
	padding-bottom: 3rem !important;
}

.pb-xxl-10 {
	padding-bottom: 0.625rem !important;
}

.pb-xxl-15 {
	padding-bottom: 0.9375rem !important;
}

.pb-xxl-20 {
	padding-bottom: 1.25rem !important;
}

.pb-xxl-25 {
	padding-bottom: 1.5625rem !important;
}

.pb-xxl-30 {
	padding-bottom: 1.875rem !important;
}

.pb-xxl-35 {
	padding-bottom: 2.18753rem !important;
}

.pb-xxl-40 {
	padding-bottom: 2.5rem !important;
}

.pb-xxl-45 {
	padding-bottom: 2.8125rem !important;
}

.pb-xxl-50 {
	padding-bottom: 3.125rem !important;
}

.ps-xxl-0 {
	padding-left: 0 !important;
}

.ps-xxl-1 {
	padding-left: 0.25rem !important;
}

.ps-xxl-2 {
	padding-left: 0.5rem !important;
}

.ps-xxl-3 {
	padding-left: 1rem !important;
}

.ps-xxl-4 {
	padding-left: 1.5rem !important;
}

.ps-xxl-5 {
	padding-left: 3rem !important;
}

.ps-xxl-10 {
	padding-left: 0.625rem !important;
}

.ps-xxl-15 {
	padding-left: 0.9375rem !important;
}

.ps-xxl-20 {
	padding-left: 1.25rem !important;
}

.ps-xxl-25 {
	padding-left: 1.5625rem !important;
}

.ps-xxl-30 {
	padding-left: 1.875rem !important;
}

.ps-xxl-35 {
	padding-left: 2.18753rem !important;
}

.ps-xxl-40 {
	padding-left: 2.5rem !important;
}

.ps-xxl-45 {
	padding-left: 2.8125rem !important;
}

.ps-xxl-50 {
	padding-left: 3.125rem !important;
}

.text-xxl-start {
	text-align: left !important;
}

.text-xxl-end {
	text-align: right !important;
}

.text-xxl-center {
	text-align: center !important;
}

}

@media (max-width: 1599.98px) {

.table-responsive-xxl {
	overflow-x: auto;
	-webkit-overflow-scrolling: touch;
}

.modal-fullscreen-xxl-down {
	width: 100vw;
	max-width: none;
	height: 100%;
	margin: 0;
}

.modal-fullscreen-xxl-down .modal-content {
	height: 100%;
	border: 0;
	-webkit-border-radius: 0;
	border-radius: 0;
}

.modal-fullscreen-xxl-down .modal-header {
	-webkit-border-radius: 0;
	border-radius: 0;
}

.modal-fullscreen-xxl-down .modal-body {
	overflow-y: auto;
}

.modal-fullscreen-xxl-down .modal-footer {
	-webkit-border-radius: 0;
	border-radius: 0;
}

}

@media (max-width: 1199.98px) {

.table-responsive-xl {
	overflow-x: auto;
	-webkit-overflow-scrolling: touch;
}

.modal-fullscreen-xl-down {
	width: 100vw;
	max-width: none;
	height: 100%;
	margin: 0;
}

.modal-fullscreen-xl-down .modal-content {
	height: 100%;
	border: 0;
	-webkit-border-radius: 0;
	border-radius: 0;
}

.modal-fullscreen-xl-down .modal-header {
	-webkit-border-radius: 0;
	border-radius: 0;
}

.modal-fullscreen-xl-down .modal-body {
	overflow-y: auto;
}

.modal-fullscreen-xl-down .modal-footer {
	-webkit-border-radius: 0;
	border-radius: 0;
}

}

@media (max-width: 991.98px) {

.table-responsive-lg {
	overflow-x: auto;
	-webkit-overflow-scrolling: touch;
}

.modal-fullscreen-lg-down {
	width: 100vw;
	max-width: none;
	height: 100%;
	margin: 0;
}

.modal-fullscreen-lg-down .modal-content {
	height: 100%;
	border: 0;
	-webkit-border-radius: 0;
	border-radius: 0;
}

.modal-fullscreen-lg-down .modal-header {
	-webkit-border-radius: 0;
	border-radius: 0;
}

.modal-fullscreen-lg-down .modal-body {
	overflow-y: auto;
}

.modal-fullscreen-lg-down .modal-footer {
	-webkit-border-radius: 0;
	border-radius: 0;
}

}

@media (max-width: 767.98px) {

.table-responsive-md {
	overflow-x: auto;
	-webkit-overflow-scrolling: touch;
}

.modal-fullscreen-md-down {
	width: 100vw;
	max-width: none;
	height: 100%;
	margin: 0;
}

.modal-fullscreen-md-down .modal-content {
	height: 100%;
	border: 0;
	-webkit-border-radius: 0;
	border-radius: 0;
}

.modal-fullscreen-md-down .modal-header {
	-webkit-border-radius: 0;
	border-radius: 0;
}

.modal-fullscreen-md-down .modal-body {
	overflow-y: auto;
}

.modal-fullscreen-md-down .modal-footer {
	-webkit-border-radius: 0;
	border-radius: 0;
}

}

@media (max-width: 575.98px) {

.table-responsive-sm {
	overflow-x: auto;
	-webkit-overflow-scrolling: touch;
}

.modal-fullscreen-sm-down {
	width: 100vw;
	max-width: none;
	height: 100%;
	margin: 0;
}

.modal-fullscreen-sm-down .modal-content {
	height: 100%;
	border: 0;
	-webkit-border-radius: 0;
	border-radius: 0;
}

.modal-fullscreen-sm-down .modal-header {
	-webkit-border-radius: 0;
	border-radius: 0;
}

.modal-fullscreen-sm-down .modal-body {
	overflow-y: auto;
}

.modal-fullscreen-sm-down .modal-footer {
	-webkit-border-radius: 0;
	border-radius: 0;
}

}

@media print {

.d-print-inline {
	display: inline !important;
}

.d-print-inline-block {
	display: inline-block !important;
}

.d-print-block {
	display: block !important;
}

.d-print-grid {
	display: grid !important;
}

.d-print-table {
	display: table !important;
}

.d-print-table-row {
	display: table-row !important;
}

.d-print-table-cell {
	display: table-cell !important;
}

.d-print-flex {
	display: -webkit-box !important;
	display: -ms-flexbox !important;
	display: flex !important;
}

.d-print-inline-flex {
	display: -webkit-inline-box !important;
	display: -ms-inline-flexbox !important;
	display: inline-flex !important;
}

.d-print-none {
	display: none !important;
}

}

@-webkit-keyframes progress-bar-stripes {

0% {
	background-position-x: 8px;
}

}

@keyframes progress-bar-stripes {

0% {
	background-position-x: 8px;
}

}

@-webkit-keyframes spinner-border {

to {
	-webkit-transform: rotate(360deg) /* rtl:ignore */;
	transform: rotate(360deg) /* rtl:ignore */;
}

}

@keyframes spinner-border {

to {
	-webkit-transform: rotate(360deg) /* rtl:ignore */;
	transform: rotate(360deg) /* rtl:ignore */;
}

}

@-webkit-keyframes spinner-grow {

0% {
	-webkit-transform: scale(0);
	transform: scale(0);
}

50% {
	opacity: 1;
	-webkit-transform: none;
	transform: none;
}

}

@keyframes spinner-grow {

0% {
	-webkit-transform: scale(0);
	transform: scale(0);
}

50% {
	opacity: 1;
	-webkit-transform: none;
	transform: none;
}

}


/*# sourceMappingURL=maps/bootstrap.css.map */
