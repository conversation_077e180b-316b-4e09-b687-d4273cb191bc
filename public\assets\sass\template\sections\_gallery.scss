.gallery-style1 {
  .gallery-img {
    overflow: hidden;
    position: relative;
    border-radius: 30px;

    img {
      width: 100%;
      transform: scale(1.001);
      transition: all ease 0.4s;
    }

    &:after,
    &:before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      width: 50%;
      height: 40%;
      background-color: rgba(#E8063C, 0.90);
      opacity: 0;
      visibility: hidden;
      transition: all ease 0.6s;
      z-index: 1;
    }

    &:after {
      top: auto;
      bottom: 0;
      left: auto;
      right: 0;
    }
  }

  .gallery-btn {
    position: absolute;
    left: 50%;
    top: 50%;
    width: 60px;
    height: 60px;
    line-height: 60px;
    text-align: center;
    background-color: $theme-color2;
    color: $title-color;
    border-radius: 50%;
    font-size: 24px;
    transform: translate(-50%, -50%);
    z-index: 3;
    opacity: 0;
    visibility: hidden;

    &:before {
      content: '';
      position: absolute;
      left: 2px;
      top: 2px;
      right: 2px;
      bottom: 2px;
      background-color: $secondary-color;
      border-radius: inherit;
      z-index: -1;
      transition: all ease 0.4s;
      transform: scale(0.5);
      opacity: 0;
      visibility: hidden;
    }

    &:hover {
      color: $white-color;
      
      &:before {
        opacity: 1;
        visibility: visible;
        transform: scale(1);
      }
    }
  }

  &:hover {
    .gallery-img {
      &:after,
      &:before {
        opacity: 1;
        visibility: visible;
        height: 100%;
      }

      img {
        transform: scale(1.15);
      }
    }

    .gallery-btn {
      opacity: 1;
      visibility: visible;
    }
  }

  &.radius-2 {
    .gallery-img {
      border-radius: 150px 150px 30px 30px;
    }
  }

  &.radius-3 {
    .gallery-img {
      border-radius: 30px 30px 150px 150px;
    }
  }

  &.layout2 {
    .gallery-btn {
      background-color: transparent;
      color: $white-color;
      font-size: 30px;
    }
  }
}

.thumb-style1 {
  border-radius: 30px;
  position: relative;
  overflow: hidden;
  cursor: pointer;

  img {
    width: 100%;
  }

  &:before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: $theme-color;
    opacity: 0;
    visibility: hidden;
    transition: all ease 0.4s;
  }
}

.thumb-style1:hover,
.slick-current .thumb-style1 {
  &:before {
    opacity: 0.80;
    visibility: visible;
  }
}