/**
 * Utility functions for handling pagination
 */

/**
 * Calculate pagination metadata for client-side pagination
 * 
 * @param {Array} items - The full array of items
 * @param {Number} currentPage - The current page number (1-based)
 * @param {Number} pageSize - Number of items per page
 * @returns {Object} Pagination metadata
 */
export function getPaginationMetadata(items, currentPage, pageSize) {
  const totalItems = items.length;
  const totalPages = Math.max(1, Math.ceil(totalItems / pageSize));
  
  // Ensure current page is within valid range
  const validCurrentPage = Math.min(Math.max(1, currentPage), totalPages);
  
  const startIndex = (validCurrentPage - 1) * pageSize;
  const endIndex = Math.min(startIndex + pageSize, totalItems);
  
  return {
    totalItems,
    totalPages,
    currentPage: validCurrentPage,
    pageSize,
    startIndex,
    endIndex,
    hasPreviousPage: validCurrentPage > 1,
    hasNextPage: validCurrentPage < totalPages
  };
}

/**
 * Get paginated items from an array
 * 
 * @param {Array} items - The full array of items
 * @param {Number} currentPage - The current page number (1-based)
 * @param {Number} pageSize - Number of items per page
 * @returns {Array} The paginated items
 */
export function getPaginatedItems(items, currentPage, pageSize) {
  const { startIndex, endIndex } = getPaginationMetadata(items, currentPage, pageSize);
  return items.slice(startIndex, endIndex);
}

/**
 * Generate an array of page numbers to display in pagination controls
 * 
 * @param {Number} currentPage - The current page number
 * @param {Number} totalPages - The total number of pages
 * @param {Number} maxVisiblePages - Maximum number of page buttons to show
 * @returns {Array} Array of page numbers to display
 */
export function getVisiblePageNumbers(currentPage, totalPages, maxVisiblePages = 5) {
  if (totalPages <= maxVisiblePages) {
    // If we have fewer pages than the max visible, show all pages
    return Array.from({ length: totalPages }, (_, i) => i + 1);
  }
  
  // Calculate the range of visible pages
  let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
  let endPage = startPage + maxVisiblePages - 1;
  
  // Adjust if we're near the end
  if (endPage > totalPages) {
    endPage = totalPages;
    startPage = Math.max(1, endPage - maxVisiblePages + 1);
  }
  
  return Array.from(
    { length: endPage - startPage + 1 },
    (_, i) => startPage + i
  );
}
