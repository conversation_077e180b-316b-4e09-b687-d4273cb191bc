// utils/dynamicResourceLoader.js

const loadFrontendResources = () => {
    // Frontend CSS files
    const frontendResources = [
        'https://fonts.googleapis.com/css2?family=Fredoka:wght@400;500;600;700&family=Jost:wght@400;500&display=swap',
        '/frontend/assets1/css/bootstrap.min.css',
        '/frontend/assets1/css/fontawesome.min.css',
        '/frontend/assets1/css/layerslider.min.css',
        '/frontend/assets1/css/magnific-popup.min.css',
        '/frontend/assets1/css/slick.min.css',
        '/frontend/assets1/css/style.css',
    ];

    frontendResources.forEach((resource) => {   
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = resource;
        document.head.appendChild(link);
    });

    // Frontend JS files
    const frontendScripts = [
        '/frontend/assets1/js/vendor/jquery-3.6.0.min.js',
        '/frontend/assets1/js/slick.min.js',
        '/frontend/assets1/js/layerslider.utils.js',
        '/frontend/assets1/js/layerslider.transitions.js',
        '/frontend/assets1/js/layerslider.kreaturamedia.jquery.js',
        '/frontend/assets1/js/jquery-ui.min.js',
        '/frontend/assets1/js/bootstrap.min.js',
        '/frontend/assets1/js/jquery.magnific-popup.min.js',
        '/frontend/assets1/js/imagesloaded.pkgd.min.js',
        '/frontend/assets1/js/isotope.pkgd.min.js',
        '/frontend/assets1/js/main.js',
    ];

    frontendScripts.forEach((script) => {
        const scriptElement = document.createElement('script');
        scriptElement.src = script;
        document.body.appendChild(scriptElement);
    });
};

const unloadFrontendResources = () => {
    // Unload frontend CSS and JS resources
    const frontendStyles = document.querySelectorAll('link[href^="/frontend/assets1/"]');
    frontendStyles.forEach((link) => link.remove());

    const frontendScripts = document.querySelectorAll('script[src^="/frontend/assets1/"]');
    frontendScripts.forEach((script) => script.remove());
};

const loadAdminResources = () => {
    // Admin CSS files
    const adminResources = [
        // 'https://fonts.googleapis.com/css2?family=Jost:wght@400;500;600;700&display=swap',
        // 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css',
        // 'https://unpkg.com/swiper/swiper-bundle.min.css',
        // '/assets/vendor_assets/css/bootstrap/bootstrap.css',
        // '/assets/vendor_assets/css/daterangepicker.css',
        // '/assets/vendor_assets/css/fontawesome.css',
        // '/assets/vendor_assets/css/footable.standalone.min.css',
        // '/assets/vendor_assets/css/<EMAIL>',
        // '/assets/vendor_assets/css/jquery-jvectormap-2.0.5.css',
        // '/assets/vendor_assets/css/jquery.mCustomScrollbar.min.css',
        // // '/assets/vendor_assets/css/leaflet.css',
        // '/assets/vendor_assets/css/line-awesome.min.css',
        // '/assets/vendor_assets/css/magnific-popup.css',
        // '/assets/vendor_assets/css/MarkerCluster.css',
        // '/assets/vendor_assets/css/MarkerCluster.Default.css',
        // '/assets/vendor_assets/css/select2.min.css',
        // '/assets/vendor_assets/css/slick.css',
        // '/assets/vendor_assets/css/star-rating-svg.css',
        // '/assets/vendor_assets/css/trumbowyg.min.css',
        // '/style.css',
        // '/assets/vendor_assets/css/wickedpicker.min.css',
    ];

    adminResources.forEach((resource) => {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = resource;
        document.head.appendChild(link);
    });

    // Admin JS files
    const adminScripts = [
       
        
    ];

    adminScripts.forEach((script) => {
        const scriptElement = document.createElement('script');
        scriptElement.src = script;
        document.body.appendChild(scriptElement);
    });
};

const unloadAdminResources = () => {
    // Unload admin CSS and JS resources
    const adminStyles = document.querySelectorAll('link[href^="/assets/vendor_assets/"]');
    adminStyles.forEach((link) => link.remove());

    const adminScripts = document.querySelectorAll('script[src^="/assets/vendor_assets/"]');
    adminScripts.forEach((script) => script.remove());
};

export { loadAdminResources, loadFrontendResources, unloadAdminResources, unloadFrontendResources }
