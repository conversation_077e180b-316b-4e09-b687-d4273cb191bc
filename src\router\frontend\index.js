import AddNow from '@/components/frontend/AddNow.vue'
import AdmissionFront from '@/components/frontend/AdmissionFront.vue'
import LandingLayout from '@/layouts/LandingLayout.vue'
import LandingDashboard from '@/views/frontend/LandingDashboard.vue'

export default [
    {
        path: '/',
        component: LandingLayout,
        children: [
            {
                path: '',
                component: () => LandingDashboard,
            },
        ],
    },
    {
        path: '/admission',
        name: 'admission',
        component: LandingLayout,
        children: [
            {
                path: '',
                component: () => AdmissionFront,
            },
        ],
    },
    {
        path: '/addnow',
        name: 'addnow',
        component: LandingLayout,
        children: [
            {
                path: '',
                component: () => AddNow,
            },
        ],
    },
    {
        path: '/registration',
        name: 'registration',
        component: LandingLayout,
        children: [
            {
                path: '',
                component: () => import('@/views/frontend/Registration.vue'),
            },
        ],
    },
    {
        path: '/about',
        name: 'about',
        component: LandingLayout,
        children: [
            {
                path: '',
                component: () => import('@/views/frontend/About.vue'),
            },

        ],
    },
];
