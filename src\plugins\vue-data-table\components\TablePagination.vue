<template>
  <div class="table-pagination">
    <div class="pagination-info">
      Showing {{ startItem }} to {{ endItem }} of {{ total }} entries
    </div>

    <div class="pagination-controls">
      <div class="page-size-selector" v-if="pageSizes.length > 1">
        <select class="page-size-select" v-model="localPageSize" @change="handlePageSizeChange">
          <option v-for="size in pageSizes" :key="size" :value="size">
            {{ size }} per page
          </option>
        </select>
      </div>

      <nav aria-label="Table pagination">
        <ul class="pagination">
          <li class="page-item" :class="{ disabled: currentPage === 1 }">
            <a class="page-link" href="#" aria-label="First" @click.prevent="goToPage(1)">
              <span aria-hidden="true">&laquo;</span>
            </a>
          </li>
          <li class="page-item" :class="{ disabled: currentPage === 1 }">
            <a class="page-link" href="#" aria-label="Previous" @click.prevent="goToPage(currentPage - 1)">
              <span aria-hidden="true">&lsaquo;</span>
            </a>
          </li>

          <li v-for="pageNumber in visiblePageNumbers" :key="pageNumber" class="page-item"
            :class="{ active: currentPage === pageNumber }">
            <a class="page-link" href="#" @click.prevent="goToPage(pageNumber)">
              {{ pageNumber }}
            </a>
          </li>

          <li class="page-item" :class="{ disabled: currentPage === totalPages }">
            <a class="page-link" href="#" aria-label="Next" @click.prevent="goToPage(currentPage + 1)">
              <span aria-hidden="true">&rsaquo;</span>
            </a>
          </li>
          <li class="page-item" :class="{ disabled: currentPage === totalPages }">
            <a class="page-link" href="#" aria-label="Last" @click.prevent="goToPage(totalPages)">
              <span aria-hidden="true">&raquo;</span>
            </a>
          </li>
        </ul>
      </nav>
    </div>
  </div>
</template>

<script>
import {
  computed,
  ref,
  watch,
} from 'vue'

export default {
  name: 'TablePagination',
  props: {
    total: {
      type: Number,
      required: true
    },
    page: {
      type: Number,
      default: 1
    },
    pageSize: {
      type: Number,
      default: 10
    },
    pageSizes: {
      type: Array,
      default: () => [10, 20, 50, 100]
    },
    maxVisiblePages: {
      type: Number,
      default: 5
    },
    serverSide: {
      type: Boolean,
      default: false
    }
  },
  emits: ['page-change', 'page-size-change'],
  setup(props, { emit }) {
    const localPageSize = ref(props.pageSize);
    const currentPage = ref(props.page);

    // Computed properties
    const totalPages = computed(() => {
      return Math.max(1, Math.ceil(props.total / localPageSize.value));
    });

    const startItem = computed(() => {
      if (props.total === 0) return 0;
      return (currentPage.value - 1) * localPageSize.value + 1;
    });

    const endItem = computed(() => {
      return Math.min(startItem.value + localPageSize.value - 1, props.total);
    });

    const visiblePageNumbers = computed(() => {
      const maxPages = props.maxVisiblePages;
      const totalPagesValue = totalPages.value;
      const currentPageValue = currentPage.value;

      if (totalPagesValue <= maxPages) {
        // Show all pages if there are fewer than maxVisiblePages
        return Array.from({ length: totalPagesValue }, (_, i) => i + 1);
      }

      // Calculate the range of visible pages
      let startPage = Math.max(1, currentPageValue - Math.floor(maxPages / 2));
      let endPage = startPage + maxPages - 1;

      // Adjust if we're near the end
      if (endPage > totalPagesValue) {
        endPage = totalPagesValue;
        startPage = Math.max(1, endPage - maxPages + 1);
      }

      return Array.from(
        { length: endPage - startPage + 1 },
        (_, i) => startPage + i
      );
    });

    // Methods
    const goToPage = (page) => {
      if (page < 1 || page > totalPages.value || page === currentPage.value) {
        return;
      }

      currentPage.value = page;
      emit('page-change', page);
    };

    const handlePageSizeChange = () => {
      // Reset to first page when changing page size
      currentPage.value = 1;
      emit('page-size-change', localPageSize.value);
      emit('page-change', 1);
    };

    // Watch for prop changes
    watch(() => props.page, (newPage) => {
      currentPage.value = newPage;
    });

    watch(() => props.pageSize, (newSize) => {
      localPageSize.value = newSize;
    });

    return {
      localPageSize,
      currentPage,
      totalPages,
      startItem,
      endItem,
      visiblePageNumbers,
      goToPage,
      handlePageSizeChange
    };
  }
};
</script>

<style>
.table-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.pagination-info {
  color: var(--vdt-text-light, #64748b);
  font-size: 14px;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.page-size-selector {
  position: relative;
}

.page-size-select {
  appearance: none;
  padding: 6px 32px 6px 12px;
  border: 1px solid var(--vdt-border-color, #e2e8f0);
  border-radius: 4px;
  background-color: var(--vdt-bg-white, #fff);
  font-size: 14px;
  cursor: pointer;
  color: var(--vdt-text-color, #1e293b);
}

.page-size-selector::after {
  content: '';
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid var(--vdt-text-light, #64748b);
  pointer-events: none;
}

.pagination {
  display: flex;
  list-style: none;
  padding: 0;
  margin: 0;
  gap: 4px;
}

.page-item {
  margin: 0;
}

.page-link {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
  padding: 0 8px;
  border: 1px solid var(--vdt-border-color, #e2e8f0);
  border-radius: 4px;
  background-color: var(--vdt-bg-white, #fff);
  color: var(--vdt-text-light, #64748b);
  text-decoration: none;
  font-size: 14px;
  transition: all 0.2s;
}

.page-item.active .page-link {
  background-color: var(--vdt-primary-color, #3b82f6);
  border-color: var(--vdt-primary-color, #3b82f6);
  color: white;
}

.page-item.disabled .page-link {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-link:hover:not(.page-item.disabled .page-link) {
  background-color: var(--vdt-bg-hover, #f1f5f9);
  border-color: var(--vdt-primary-color, #3b82f6);
  color: var(--vdt-primary-color, #3b82f6);
}

@media (max-width: 768px) {
  .table-pagination {
    flex-direction: column;
    align-items: flex-start;
  }

  .pagination-controls {
    width: 100%;
    justify-content: space-between;
  }
}
</style>
