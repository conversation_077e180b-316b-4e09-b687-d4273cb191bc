<template>
  <div class="searchable-select" :class="{ 'active': isOpen, 'has-value': !!modelValue }">
    <!-- Selected value display / search input -->
    <div class="select-input-container" @click="toggleDropdown">
      <input ref="searchInput" type="text" class="select-input" v-model="searchQuery" :placeholder="placeholder"
        @focus="openDropdown" @input="onInput" @click.stop @keydown.esc="closeDropdown"
        @keydown.down="highlightNextOption" @keydown.up="highlightPrevOption"
        @keydown.enter.prevent="selectHighlighted" />
      <!-- Clear button (X) -->
      <button v-if="modelValue && modelValue !== ''" type="button" class="clear-button" @click.stop="clearSelection"
        title="Clear selection">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16">
          <path fill="none" d="M0 0h24v24H0z" />
          <path
            d="M12 10.586l4.95-4.95 1.414 1.414-4.95 4.95 4.95 4.95-1.414 1.414-4.95-4.95-4.95 4.95-1.414-1.414 4.95-4.95-4.95-4.95L7.05 5.636z"
            fill="currentColor" />
        </svg>
      </button>
      <div class="select-arrow" :class="{ 'open': isOpen }"></div>
    </div>

    <!-- Options dropdown -->
    <div v-if="isOpen" class="select-dropdown">
      <div v-if="filteredOptions.length === 0" class="no-results">
        No results found
      </div>
      <div v-for="(option, index) in filteredOptions" :key="option.value" class="select-option" :class="{
        'selected': option.value === modelValue,
        'highlighted': highlightedIndex === index
      }" @click.stop="selectOption(option.value)" @mouseover="highlightedIndex = index">
        {{ option.label }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SearchableSelect',
  props: {
    modelValue: {
      type: [String, Number],
      default: ''
    },
    options: {
      type: Array,
      default: () => []
    },
    placeholder: {
      type: String,
      default: 'Select an option'
    }
  },
  emits: ['update:modelValue', 'change'],
  data() {
    return {
      isOpen: false,
      searchQuery: '',
      highlightedIndex: -1,
      selectedLabel: '',
      clickOutsideHandler: null
    }
  },
  computed: {
    filteredOptions() {
      if (!this.searchQuery) {
        return this.options;
      }

      const query = this.searchQuery.toLowerCase();
      return this.options.filter(option =>
        option.label.toLowerCase().includes(query)
      );
    }
  },
  watch: {
    modelValue: {
      immediate: true,
      handler(newValue) {
        // Update the search input to show the selected option's label
        if (newValue) {
          const selectedOption = this.options.find(opt => opt.value === newValue);
          if (selectedOption) {
            this.selectedLabel = selectedOption.label;
            this.searchQuery = selectedOption.label;
          }
        } else {
          this.selectedLabel = '';
          this.searchQuery = '';
        }
      }
    },
    isOpen(newValue) {
      if (newValue) {
        // When opening, reset highlight to first item or selected item
        this.resetHighlight();

        // Add click outside listener
        this.$nextTick(() => {
          this.clickOutsideHandler = (event) => {
            if (!this.$el.contains(event.target)) {
              this.closeDropdown();
            }
          };
          document.addEventListener('click', this.clickOutsideHandler);
        });
      } else {
        // When closing, remove click outside listener
        if (this.clickOutsideHandler) {
          document.removeEventListener('click', this.clickOutsideHandler);
        }

        // Reset search to selected value when closing without selection
        if (this.modelValue) {
          const selectedOption = this.options.find(opt => opt.value === this.modelValue);
          if (selectedOption) {
            this.searchQuery = selectedOption.label;
          }
        } else {
          this.searchQuery = '';
        }
      }
    }
  },
  beforeUnmount() {
    // Clean up event listener
    if (this.clickOutsideHandler) {
      document.removeEventListener('click', this.clickOutsideHandler);
    }
  },
  methods: {
    toggleDropdown() {
      this.isOpen = !this.isOpen;
      if (this.isOpen) {
        this.$nextTick(() => {
          this.$refs.searchInput.focus();
        });
      }
    },
    openDropdown() {
      if (!this.isOpen) {
        this.isOpen = true;
      }
    },
    closeDropdown() {
      this.isOpen = false;
    },
    selectOption(value) {
      this.$emit('update:modelValue', value);
      this.$emit('change', value);
      this.closeDropdown();
    },
    clearSelection(event) {
      // Prevent dropdown from opening when clicking the clear button
      event.stopPropagation();

      // Clear the selection
      this.$emit('update:modelValue', '');
      this.$emit('change', '');
      this.searchQuery = '';

      // Focus the input after clearing
      this.$nextTick(() => {
        this.$refs.searchInput.focus();
      });
    },
    onInput() {
      // When user types, reset selection if the input doesn't match
      if (this.searchQuery !== this.selectedLabel) {
        // Only emit empty value if user has cleared the input
        if (!this.searchQuery) {
          this.$emit('update:modelValue', '');
          this.$emit('change', '');
        }
      }

      // Always open dropdown when typing
      this.openDropdown();

      // Reset highlight position
      this.resetHighlight();
    },
    resetHighlight() {
      if (this.filteredOptions.length > 0) {
        // If there's a selected value, highlight it
        if (this.modelValue) {
          const selectedIndex = this.filteredOptions.findIndex(opt => opt.value === this.modelValue);
          this.highlightedIndex = selectedIndex >= 0 ? selectedIndex : 0;
        } else {
          // Otherwise highlight the first option
          this.highlightedIndex = 0;
        }
      } else {
        this.highlightedIndex = -1;
      }
    },
    highlightNextOption() {
      if (this.filteredOptions.length > 0) {
        if (this.highlightedIndex < this.filteredOptions.length - 1) {
          this.highlightedIndex++;
        } else {
          // Cycle back to the first option
          this.highlightedIndex = 0;
        }
        this.scrollToHighlighted();
      }
    },
    highlightPrevOption() {
      if (this.filteredOptions.length > 0) {
        if (this.highlightedIndex > 0) {
          this.highlightedIndex--;
        } else {
          // Cycle to the last option
          this.highlightedIndex = this.filteredOptions.length - 1;
        }
        this.scrollToHighlighted();
      }
    },
    selectHighlighted() {
      if (this.highlightedIndex >= 0 && this.filteredOptions[this.highlightedIndex]) {
        this.selectOption(this.filteredOptions[this.highlightedIndex].value);
      }
    },
    scrollToHighlighted() {
      this.$nextTick(() => {
        const dropdown = this.$el.querySelector('.select-dropdown');
        const highlighted = dropdown.querySelector('.highlighted');
        if (dropdown && highlighted) {
          // Check if the highlighted option is outside the visible area
          const dropdownRect = dropdown.getBoundingClientRect();
          const highlightedRect = highlighted.getBoundingClientRect();

          if (highlightedRect.bottom > dropdownRect.bottom) {
            // Scroll down if the highlighted option is below the visible area
            dropdown.scrollTop += (highlightedRect.bottom - dropdownRect.bottom);
          } else if (highlightedRect.top < dropdownRect.top) {
            // Scroll up if the highlighted option is above the visible area
            dropdown.scrollTop -= (dropdownRect.top - highlightedRect.top);
          }
        }
      });
    }
  }
}
</script>

<style>
.searchable-select {
  position: relative;
  width: 100%;
}

.select-input-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  cursor: pointer;
}

.select-input {
  width: 100%;
  padding: 6px 28px 6px 8px;
  border: 1px solid var(--vdt-border-color, #e2e8f0);
  border-radius: 4px;
  font-size: 14px;
  background-color: var(--vdt-bg-white, #fff);
  color: var(--vdt-text-color, #1e293b);
  transition: all 0.2s ease;
  cursor: text;
}

.select-input:focus {
  outline: none;
  border-color: var(--vdt-primary-color, #3b82f6);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* Clear button */
.clear-button {
  position: absolute;
  right: 24px;
  /* Position to the left of the arrow */
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  border: none;
  background: transparent;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--vdt-text-light, #64748b);
  opacity: 0.7;
  transition: opacity 0.2s ease;
  z-index: 1;
}

.clear-button:hover {
  opacity: 1;
  color: var(--vdt-text-color, #1e293b);
}

.clear-button:focus {
  outline: none;
  color: var(--vdt-primary-color, #3b82f6);
}

.select-arrow {
  position: absolute;
  right: 8px;
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid var(--vdt-text-light, #64748b);
  transition: transform 0.2s ease;
  pointer-events: none;
}

.select-arrow.open {
  transform: rotate(180deg);
}

.select-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  max-height: 200px;
  overflow-y: auto;
  background-color: var(--vdt-bg-white, #fff);
  border: 1px solid var(--vdt-border-color, #e2e8f0);
  border-radius: 4px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 10;
  margin-top: 4px;
}

.select-option {
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.select-option:hover,
.select-option.highlighted {
  background-color: var(--vdt-bg-hover, #f1f5f9);
}

.select-option.selected {
  background-color: var(--vdt-bg-selected, rgba(59, 130, 246, 0.1));
  color: var(--vdt-primary-color, #3b82f6);
  font-weight: 500;
}

.no-results {
  padding: 8px 12px;
  color: var(--vdt-text-light, #64748b);
  font-style: italic;
  text-align: center;
}

.searchable-select.active .select-input {
  border-color: var(--vdt-primary-color, #3b82f6);
}

.searchable-select.has-value .select-input {
  color: var(--vdt-text-color, #1e293b);
  font-weight: 500;
}
</style>
