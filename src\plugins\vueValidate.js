/**
 * Vue Validate - A simple, intuitive validation system for Vue 3
 *
 * This plugin provides a unique approach to form validation with minimal setup.
 */

import {
  computed,
  reactive,
} from 'vue'

// Built-in validation rules
const rules = {
  required: (message = 'This field is required') => ({
    validate: value => !!value || value === 0 || value === false,
    message
  }),

  email: (message = 'Please enter a valid email address') => ({
    validate: value => {
      if (!value) return true
      const pattern = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
      return pattern.test(value)
    },
    message
  }),

  minLength: (min, message = `Must be at least ${min} characters`) => ({
    validate: value => {
      if (!value) return true
      return String(value).length >= min
    },
    message
  }),

  maxLength: (max, message = `Must be less than ${max} characters`) => ({
    validate: value => {
      if (!value) return true
      return String(value).length <= max
    },
    message
  }),

  min: (min, message = `Must be at least ${min}`) => ({
    validate: value => {
      if (!value && value !== 0) return true
      return Number(value) >= min
    },
    message
  }),

  max: (max, message = `Must be less than ${max}`) => ({
    validate: value => {
      if (!value && value !== 0) return true
      return Number(value) <= max
    },
    message
  }),

  pattern: (pattern, message = 'Invalid format') => ({
    validate: value => {
      if (!value) return true
      return pattern.test(value)
    },
    message
  }),

  match: (fieldName, message = `Must match ${fieldName}`) => ({
    validate: (value, formValues) => {
      if (!value) return true
      return value === formValues[fieldName]
    },
    message
  }),

  custom: (validator, message = 'Invalid value') => ({
    validate: validator,
    message
  })
}

// Create a form validation context
const createFormValidation = (initialValues = {}) => {
  const values = reactive({ ...initialValues })
  const errors = reactive({})
  const touched = reactive({})
  const validations = reactive({})

  // Track if form has been submitted
  const submitted = reactive({ value: false })

  // Computed properties
  const isValid = computed(() => {
    if (Object.keys(errors).length === 0) return true
    return Object.values(errors).every(fieldErrors => fieldErrors.length === 0)
  })

  const isDirty = computed(() => {
    return Object.keys(touched).some(key => touched[key])
  })

  // Register field validation
  const register = (field, fieldValidations = []) => {
    // Handle computed validation rules
    if (fieldValidations && typeof fieldValidations === 'object' && 'value' in fieldValidations && typeof fieldValidations.effect === 'function') {
      // This is likely a computed ref
      validations[field] = fieldValidations
    } else {
      validations[field] = fieldValidations
    }

    if (!(field in errors)) {
      errors[field] = []
    }

    if (!(field in touched)) {
      touched[field] = false
    }

    // Return field props and methods
    return {
      value: computed({
        get: () => values[field],
        set: (newValue) => {
          values[field] = newValue
          // Don't mark as touched or validate on every change
          // This will be handled by the component's blur/change handlers
        }
      }),
      errors: computed(() => errors[field]),
      touched: computed(() => touched[field]),
      setTouched: (validate = true) => {
        touched[field] = true
        if (validate) validateField(field)
      },
      hasError: computed(() => touched[field] && errors[field].length > 0),
      isValid: computed(() => errors[field].length === 0)
    }
  }

  // Validate a single field
  const validateField = async (field) => {
    if (!validations[field]) return true

    const fieldValue = values[field]
    const fieldErrors = []

    // Get the validation rules (handle computed rules)
    const rules = typeof validations[field].value === 'undefined'
      ? validations[field]
      : validations[field].value

    // Skip validation if no rules
    if (!rules || rules.length === 0) return true

    for (const rule of rules) {
      try {
        // Handle async validation
        const validationResult = rule.validate(fieldValue, values)
        const isValid = validationResult instanceof Promise
          ? await validationResult
          : validationResult

        if (isValid !== true) {
          fieldErrors.push(typeof isValid === 'string' ? isValid : rule.message)
          break // Stop on first error
        }
      } catch (error) {
        console.error('Validation error:', error)
        fieldErrors.push('Validation error occurred')
        break
      }
    }

    errors[field] = fieldErrors
    return fieldErrors.length === 0
  }

  // Validate all fields
  const validate = async () => {
    let isValid = true

    for (const field in validations) {
      const fieldIsValid = await validateField(field)
      isValid = isValid && fieldIsValid
      touched[field] = true
    }

    return isValid
  }

  // Reset form
  const reset = () => {
    Object.keys(values).forEach(key => {
      values[key] = initialValues[key] || ''
    })

    Object.keys(errors).forEach(key => {
      errors[key] = []
    })

    Object.keys(touched).forEach(key => {
      touched[key] = false
    })

    submitted.value = false
  }

  // Handle form submission
  const handleSubmit = (submitFn) => {
    return async (event) => {
      if (event) event.preventDefault()

      submitted.value = true

      // Touch all fields
      Object.keys(validations).forEach(field => {
        touched[field] = true
      })

      const valid = await validate()

      if (valid && submitFn) {
        try {
          await Promise.resolve(submitFn(values))
        } catch (error) {
          console.error('Form submission error:', error)
        }
      } else if (!valid) {
        // Scroll to the first error field
        setTimeout(() => {
          const errorField = document.querySelector('.has-error')
          if (errorField) {
            errorField.scrollIntoView({ behavior: 'smooth', block: 'center' })
          }
        }, 100)
      }

      return valid
    }
  }

  return {
    values,
    errors,
    touched,
    isValid,
    isDirty,
    submitted,
    register,
    validate,
    reset,
    handleSubmit
  }
}

// Vue plugin
const VueValidate = {
  install(app) {
    // Make rules globally available
    app.config.globalProperties.$rules = rules

    // Provide rules to components
    app.provide('validationRules', rules)

    // Add validation directive
    app.directive('validate', {
      mounted(el, binding, vnode) {
        const inputEl = el.tagName === 'INPUT' || el.tagName === 'SELECT' || el.tagName === 'TEXTAREA'
          ? el
          : el.querySelector('input, select, textarea')

        if (!inputEl) return

        // Add validation classes
        el.classList.add('validate-input')

        // Add error message element if it doesn't exist
        if (!el.querySelector('.validation-error')) {
          const errorEl = document.createElement('div')
          errorEl.className = 'validation-error'
          el.appendChild(errorEl)
        }

        // Get validation rules from binding value
        const validationRules = binding.value || []

        // Validate on blur
        inputEl.addEventListener('blur', () => {
          let isValid = true
          let errorMessage = ''

          for (const rule of validationRules) {
            if (!rule.validate(inputEl.value)) {
              isValid = false
              errorMessage = rule.message
              break
            }
          }

          if (!isValid) {
            el.classList.add('is-invalid')
            el.querySelector('.validation-error').textContent = errorMessage
          } else {
            el.classList.remove('is-invalid')
            el.querySelector('.validation-error').textContent = ''
          }
        })
      }
    })

    // Add global mixin for useValidation
    app.mixin({
      methods: {
        $useValidation: createFormValidation
      }
    })
  }
}

// Composable for use in setup()
export const useValidation = createFormValidation

// Export rules for direct use
export { rules }

// Export plugin
export default VueValidate
