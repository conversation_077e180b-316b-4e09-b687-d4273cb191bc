<script setup>
import {
  onMounted,
  ref,
} from 'vue'

import useSocialMedia from '@/stores/socialMedia'

const details = ref(null);

function submitForm() {
    useSocialMedia().sendSocialMediaDetails(userData.value)
    details.value = useSocialMedia().socialMediaDetails;
}
// Object to add the data
const userData = ref({
    contact_number: '',
    email: '',
    facebook_link: '',
    instagram_link: ''
});


onMounted(async () => {
    await useSocialMedia().getSocialMediaDetails();
    details.value = useSocialMedia().socialMediaDetails;

    userData.value.contact_number = details.value.contact_number;
    userData.value.email = details.value.email;
    userData.value.facebook_link = details.value.facebook_link;
    userData.value.instagram_link = details.value.instagram_link;
})
</script>
<template>
    <div class="d-flex justify-content-center align-items-center vh-100">
        <div class="col-lg-6 col-md-8 col-sm-10">
            <div class="card card-horizontal card-default card-md">
                <div class="card-header text-center">
                    <h6>Horizontal Form With Icons</h6>
                </div>
                <div class="card-body py-md-30">
                    <div class="horizontal-form">
                        <form @submit.prevent="submitForm" class="form-horizontal">
                            <div class="form-group row mb-25">
                                <div class="col-sm-3 d-flex align-items-center">
                                    <label for="inputNameIcon"
                                        class="col-form-label color-dark fs-14 fw-500 align-center mb-10">Contact
                                        Number</label>
                                </div>
                                <div class="col-sm-9">
                                    <div class="with-icon">
                                        <span class="las la-phone color-primary"></span>
                                        <input type="text" v-model="userData.contact_number"
                                            class="form-control ih-medium ip-gray radius-xs b-light" id="inputNameIcon"
                                            placeholder="Contact Number">
                                    </div>
                                </div>
                            </div>
                            <div class="form-group row mb-25">
                                <div class="col-sm-3 d-flex align-items-center">
                                    <label for="emailAddress"
                                        class="col-form-label color-dark fs-14 fw-500 align-center mb-10">Email
                                        Address</label>
                                </div>
                                <div class="col-sm-9">
                                    <div class="with-icon">
                                        <span class="lar la-envelope color-primary"></span>
                                        <input type="email" v-model="userData.email"
                                            class="form-control ih-medium ip-primary radius-lg b-dark" id="emailAddress"
                                            placeholder="<EMAIL>" autocomplete="email">
                                    </div>
                                </div>
                            </div>
                            <div class="form-group row mb-30">
                                <div class="col-sm-3 d-flex align-items-center">
                                    <label for="facebook_link"
                                        class="col-form-label color-dark fs-14 fw-500 align-center mb-10">Facebook
                                        link</label>
                                </div>
                                <div class="col-sm-9">
                                    <div class="with-icon">
                                        <span class="las la-facebook-f color-primary"></span>
                                        <input v-model="userData.facebook_link"
                                            class="form-control ih-medium ip-primary radius-lg b-dark"
                                            id="facebook_link" placeholder="https://facebook.com/yourprofile"
                                            pattern="https://.*">
                                    </div>
                                </div>
                            </div>

                            <div class="form-group row mb-30">
                                <div class="col-sm-3 d-flex align-items-center">
                                    <label for="facebook_link"
                                        class="col-form-label color-dark fs-14 fw-500 align-center mb-10">Instragram
                                        link</label>
                                </div>
                                <div class="col-sm-9">
                                    <div class="with-icon">
                                        <span class="las la-facebook-f color-primary"></span>
                                        <input v-model="userData.instagram_link"
                                            class="form-control ih-medium ip-primary radius-lg b-dark"
                                            id="facebook_link" placeholder="https://facebook.com/yourprofile"
                                            pattern="https://.*">
                                    </div>
                                </div>
                            </div>
                            <div class="text-center">
                                <button type="submit"
                                    class="btn btn-primary btn-default btn-squared px-30">Update</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
