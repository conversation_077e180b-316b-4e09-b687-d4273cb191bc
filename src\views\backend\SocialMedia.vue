<script setup>
import {
  onMounted,
  ref,
} from 'vue'

import useSocialMedia from '@/stores/socialMedia'

const details = ref(null);

function submitForm() {
    useSocialMedia().sendSocialMediaDetails(userData.value)
    details.value = useSocialMedia().socialMediaDetails;
}
// Object to add the data
const userData = ref({
    contact_number: '',
    email: '',
    facebook_link: '',
    instagram_link: ''
});


onMounted(async () => {
    await useSocialMedia().getSocialMediaDetails();
    details.value = useSocialMedia().socialMediaDetails;

    userData.value.contact_number = details.value.contact_number;
    userData.value.email = details.value.email;
    userData.value.facebook_link = details.value.facebook_link;
    userData.value.instagram_link = details.value.instagram_link;
})
</script>
<template>
    <div class="flex justify-center items-center min-h-screen bg-gray-50">
        <div class="w-full max-w-xl">
            <div class="bg-white rounded-lg shadow-lg">
                <div class="border-b px-6 py-4 text-center">
                    <h6 class="text-lg font-semibold">Horizontal Form With Icons</h6>
                </div>
                <div class="px-8 py-8">
                    <form @submit.prevent="submitForm" class="space-y-6">
                        <!-- Contact Number -->
                        <div class="flex flex-col sm:flex-row items-center gap-4">
                            <label for="inputNameIcon"
                                class="sm:w-1/3 text-gray-700 font-medium text-sm mb-2 sm:mb-0">Contact Number</label>
                            <div class="sm:w-2/3 w-full flex items-center bg-gray-100 rounded-md px-3 py-2">
                                <span class="las la-phone text-blue-600 mr-2"></span>
                                <input type="text" v-model="userData.contact_number"
                                    class="flex-1 bg-transparent outline-none text-gray-800" id="inputNameIcon"
                                    placeholder="Contact Number">
                            </div>
                        </div>
                        <!-- Email Address -->
                        <div class="flex flex-col sm:flex-row items-center gap-4">
                            <label for="emailAddress"
                                class="sm:w-1/3 text-gray-700 font-medium text-sm mb-2 sm:mb-0">Email Address</label>
                            <div class="sm:w-2/3 w-full flex items-center bg-gray-100 rounded-md px-3 py-2">
                                <span class="lar la-envelope text-blue-600 mr-2"></span>
                                <input type="email" v-model="userData.email"
                                    class="flex-1 bg-transparent outline-none text-gray-800" id="emailAddress"
                                    placeholder="<EMAIL>" autocomplete="email">
                            </div>
                        </div>
                        <!-- Facebook Link -->
                        <div class="flex flex-col sm:flex-row items-center gap-4">
                            <label for="facebook_link"
                                class="sm:w-1/3 text-gray-700 font-medium text-sm mb-2 sm:mb-0">Facebook link</label>
                            <div class="sm:w-2/3 w-full flex items-center bg-gray-100 rounded-md px-3 py-2">
                                <span class="lab la-facebook-f text-blue-600 mr-2"></span>
                                <input v-model="userData.facebook_link"
                                    class="flex-1 bg-transparent outline-none text-gray-800" id="facebook_link"
                                    placeholder="https://facebook.com/yourprofile" pattern="https://.*">
                            </div>
                        </div>
                        <!-- Instagram Link -->
                        <div class="flex flex-col sm:flex-row items-center gap-4">
                            <label for="instagram_link"
                                class="sm:w-1/3 text-gray-700 font-medium text-sm mb-2 sm:mb-0">Instagram link</label>
                            <div class="sm:w-2/3 w-full flex items-center bg-gray-100 rounded-md px-3 py-2">
                                <span class="lab la-instagram text-pink-500 mr-2"></span>
                                <input v-model="userData.instagram_link"
                                    class="flex-1 bg-transparent outline-none text-gray-800" id="instagram_link"
                                    placeholder="https://instagram.com/yourprofile" pattern="https://.*">
                            </div>
                        </div>
                        <div class="text-center">
                            <button type="submit"
                                class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-8 rounded-md shadow transition">Update</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</template>
