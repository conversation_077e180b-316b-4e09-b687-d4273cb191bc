<template>
  <div class="table-header">
    <div class="table-header-left">
      <h3 v-if="title" class="table-title">{{ title }}</h3>
      <table-search v-if="showSearch" :placeholder="searchPlaceholder" @search="$emit('search', $event)" />
    </div>
    <div class="table-header-right">
      <slot name="actions"></slot>

      <!-- Selection toggle button -->
      <button v-if="showSelectionToggle" class="btn btn-outline-primary selection-btn" @click="toggleSelection">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16">
          <path fill="none" d="M0 0h24v24H0z" />
          <path
            d="M4 3h16a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1zm1 2v14h14V5H5zm6.003 11L6.76 11.757l1.414-1.414 2.829 2.829 5.656-5.657 1.415 1.414L11.003 16z"
            fill="currentColor" />
        </svg>
        <span>{{ selectionCount > 0 ? `Selected (${selectionCount})` : 'Select All' }}</span>
      </button>

      <!-- Column selector dropdown - Temporarily disabled -->
      <!-- <div v-if="showColumnSelector" class="column-selector-dropdown">
        <button class="btn btn-outline-secondary column-selector-btn" @click="toggleColumnSelector">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16">
            <path fill="none" d="M0 0h24v24H0z" />
            <path
              d="M10 3h4a8 8 0 1 1 0 16v3.5c-5-2-12-5-12-11.5a8 8 0 0 1 8-8zm2 14h2a6 6 0 1 0 0-12h-4a6 6 0 0 0-6 6c0 3.61 2.462 5.966 8 8.48V17z"
              fill="currentColor" />
          </svg>
          <span>Columns</span>
        </button>
        <div v-if="isColumnSelectorOpen" class="column-selector-menu">
          <div class="column-selector-header">
            <h4>Show/Hide Columns</h4>
            <button class="column-selector-close" @click="toggleColumnSelector">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16">
                <path fill="none" d="M0 0h24v24H0z" />
                <path
                  d="M12 10.586l4.95-4.95 1.414 1.414-4.95 4.95 4.95 4.95-1.414 1.414-4.95-4.95-4.95 4.95-1.414-1.414 4.95-4.95-4.95-4.95L7.05 5.636z"
                  fill="currentColor" />
              </svg>
            </button>
          </div>
          <div class="column-selector-body">
            <div class="column-selector-select-all">
              <label>
                <input type="checkbox" :checked="allColumnsSelected" @change="toggleAllColumns" />
                <span>Select All</span>
              </label>
            </div>
            <div class="column-selector-list">
              <div v-for="column in flattenedColumns" :key="column.key" class="column-selector-item">
                <label>
                  <input type="checkbox" :value="column.key" :checked="isColumnVisible(column.key)"
                    @change="toggleColumn(column.key)" />
                  <span>{{ column.label }}</span>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div> -->

      <!-- Print button -->
      <button v-if="showPrint" class="btn btn-outline-secondary print-btn" @click="handlePrint">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16">
          <path fill="none" d="M0 0h24v24H0z" />
          <path
            d="M7 17h10v5H7v-5zm12 3v-5H5v5H3a1 1 0 0 1-1-1V9a1 1 0 0 1 1-1h18a1 1 0 0 1 1 1v10a1 1 0 0 1-1 1h-2zM5 10v2h3v-2H5zm2-8h10a1 1 0 0 1 1 1v3H6V3a1 1 0 0 1 1-1z"
            fill="currentColor" />
        </svg>
        <span>Print</span>
      </button>

      <!-- Export button -->
      <button v-if="showExport" class="btn btn-outline-secondary export-btn" @click="handleExport">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16">
          <path fill="none" d="M0 0h24v24H0z" />
          <path d="M13 10h5l-6 6-6-6h5V3h2v7zm-9 9h16v-7h2v8a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1v-8h2v7z"
            fill="currentColor" />
        </svg>
        <span>Export</span>
      </button>
    </div>
  </div>
</template>

<script>
import {
  computed,
  inject,
  ref,
} from 'vue'

import TableSearch from './TableSearch.vue'

export default {
  name: 'TableHeader',
  components: {
    TableSearch
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    showSearch: {
      type: Boolean,
      default: true
    },
    showExport: {
      type: Boolean,
      default: true
    },
    showPrint: {
      type: Boolean,
      default: true
    },
    showColumnSelector: {
      type: Boolean,
      default: true
    },
    showSelectionToggle: {
      type: Boolean,
      default: false
    },
    selectionCount: {
      type: Number,
      default: 0
    },
    columns: {
      type: Array,
      default: () => []
    },
    visibleColumns: {
      type: Array,
      default: () => []
    },
    searchPlaceholder: {
      type: String,
      default: 'Search...'
    }
  },
  emits: ['search', 'column-visibility-change', 'toggle-selection'],
  setup(props, { emit }) {
    const dataTable = inject('dataTable', null);
    const isColumnSelectorOpen = ref(false);

    // Computed properties
    const flattenedColumns = computed(() => {
      const result = [];

      props.columns.forEach(column => {
        if (column.children) {
          // Add child columns
          column.children.forEach(child => {
            if (child.key) {
              result.push(child);
            }
          });
        } else if (column.key) {
          // Add regular column
          result.push(column);
        }
      });

      return result;
    });

    const allColumnsSelected = computed(() => {
      const allColumnKeys = flattenedColumns.value.map(col => col.key);
      // Ensure visibleColumns is an array
      const visibleCols = Array.isArray(props.visibleColumns) ? props.visibleColumns : [];

      return allColumnKeys.length > 0 &&
        allColumnKeys.every(key => visibleCols.includes(key));
    });

    // Methods
    const handleExport = () => {
      if (dataTable) {
        dataTable.openExportModal();
      }
    };

    const handlePrint = () => {
      if (dataTable) {
        dataTable.openPrintModal();
      }
    };

    const toggleColumnSelector = () => {
      isColumnSelectorOpen.value = !isColumnSelectorOpen.value;
    };

    const isColumnVisible = (columnKey) => {
      // Ensure visibleColumns is an array
      return Array.isArray(props.visibleColumns) && props.visibleColumns.includes(columnKey);
    };

    const toggleColumn = (columnKey) => {
      // Ensure visibleColumns is an array
      const currentVisibleColumns = Array.isArray(props.visibleColumns) ? props.visibleColumns : [];
      let newVisibleColumns;

      if (isColumnVisible(columnKey)) {
        // Don't allow hiding all columns
        if (currentVisibleColumns.length <= 1) {
          return;
        }
        // Remove column from visible columns
        newVisibleColumns = currentVisibleColumns.filter(key => key !== columnKey);
      } else {
        // Add column to visible columns
        newVisibleColumns = [...currentVisibleColumns, columnKey];
      }

      // Update via parent component
      if (dataTable && dataTable.updateVisibleColumns) {
        dataTable.updateVisibleColumns(newVisibleColumns);
      }

      // Emit event for parent component
      emit('column-visibility-change', newVisibleColumns);
    };

    const toggleAllColumns = () => {
      let newVisibleColumns;

      if (allColumnsSelected.value) {
        // Hide all columns except the first one
        newVisibleColumns = flattenedColumns.value.length > 0 ? [flattenedColumns.value[0].key] : [];
      } else {
        // Show all columns
        newVisibleColumns = flattenedColumns.value.map(column => column.key);
      }

      // Ensure newVisibleColumns is an array
      if (!Array.isArray(newVisibleColumns)) {
        newVisibleColumns = [];
      }

      // Update via parent component
      if (dataTable && dataTable.updateVisibleColumns) {
        dataTable.updateVisibleColumns(newVisibleColumns);
      }

      // Emit event for parent component
      emit('column-visibility-change', newVisibleColumns);
    };

    // Close column selector when clicking outside
    if (typeof window !== 'undefined') {
      window.addEventListener('click', (event) => {
        if (isColumnSelectorOpen.value) {
          const dropdown = document.querySelector('.column-selector-dropdown');
          if (dropdown && !dropdown.contains(event.target)) {
            isColumnSelectorOpen.value = false;
          }
        }
      });
    }

    // Selection methods
    const toggleSelection = () => {
      emit('toggle-selection');
    };

    return {
      isColumnSelectorOpen,
      allColumnsSelected,
      flattenedColumns,
      handleExport,
      handlePrint,
      toggleColumnSelector,
      isColumnVisible,
      toggleColumn,
      toggleAllColumns,
      toggleSelection
    };
  }
};
</script>

<style>
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.table-header-left {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
  flex: 1;
}

.table-header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.table-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--vdt-text-color, #1e293b);
}

/* Button styles */
.btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  font-size: 14px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid var(--vdt-border-color, #e2e8f0);
  background-color: var(--vdt-bg-white, #fff);
  color: var(--vdt-text-color, #1e293b);
  position: relative;
  overflow: hidden;
}

.btn:hover {
  background-color: var(--vdt-bg-hover, #f1f5f9);
  color: var(--vdt-text-color, #1e293b);
  border-color: var(--vdt-border-color, #cbd5e1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.btn:active {
  transform: translateY(1px);
  box-shadow: 0 0 1px rgba(0, 0, 0, 0.1);
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

.btn-outline-primary {
  border-color: var(--vdt-primary-color, #3b82f6);
  color: var(--vdt-primary-color, #3b82f6);
}

.btn-outline-primary:hover {
  background-color: var(--vdt-primary-color, #3b82f6);
  color: white;
  border-color: var(--vdt-primary-hover, #2563eb);
  box-shadow: 0 1px 3px rgba(59, 130, 246, 0.4);
}

.btn-outline-secondary:hover {
  background-color: var(--vdt-text-color, #1e293b);
  color: white;
  border-color: var(--vdt-text-color, #1e293b);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* Specific styles for print button */
.print-btn:hover {
  background-color: #0ea5e9;
  /* Sky blue */
  border-color: #0284c7;
  color: white;
  box-shadow: 0 1px 3px rgba(14, 165, 233, 0.4);
}

/* Specific styles for export button */
.export-btn:hover {
  background-color: #10b981;
  /* Emerald green */
  border-color: #059669;
  color: white;
  box-shadow: 0 1px 3px rgba(16, 185, 129, 0.4);
}

.btn svg {
  flex-shrink: 0;
}

/* Column selector styles */
.column-selector-dropdown {
  position: relative;
}

.column-selector-btn {
  white-space: nowrap;
}

/* Specific styles for column selector button */
.column-selector-btn:hover {
  background-color: #8b5cf6;
  /* Violet */
  border-color: #7c3aed;
  color: white;
  box-shadow: 0 1px 3px rgba(139, 92, 246, 0.4);
}

.column-selector-menu {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  width: 250px;
  background-color: var(--vdt-bg-white, #fff);
  border: 1px solid var(--vdt-border-color, #e2e8f0);
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: var(--vdt-z-index-modal, 1050);
  overflow: hidden;
}

.column-selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--vdt-border-color, #e2e8f0);
}

.column-selector-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--vdt-text-color, #1e293b);
}

.column-selector-close {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  color: var(--vdt-text-light, #64748b);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.column-selector-close:hover {
  background-color: var(--vdt-bg-hover, #f1f5f9);
}

.column-selector-body {
  padding: 12px 16px;
}

.column-selector-select-all {
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--vdt-border-color, #e2e8f0);
}

.column-selector-list {
  max-height: 200px;
  overflow-y: auto;
}

.column-selector-item {
  margin-bottom: 6px;
}

.column-selector-item:last-child {
  margin-bottom: 0;
}

.column-selector-select-all label,
.column-selector-item label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 4px 0;
}

.column-selector-select-all input,
.column-selector-item input {
  margin: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .table-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .table-header-left,
  .table-header-right {
    width: 100%;
  }

  .table-header-right {
    justify-content: flex-end;
  }

  .column-selector-menu {
    right: auto;
    left: 0;
    width: 100%;
    max-width: 300px;
  }
}
</style>
