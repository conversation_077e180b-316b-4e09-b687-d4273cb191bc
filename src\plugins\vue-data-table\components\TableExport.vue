<template>
  <div v-if="visible" class="export-modal">
    <div class="export-modal-backdrop" @click="$emit('close')"></div>
    <div class="export-modal-container">
      <div class="export-modal-content">
        <div class="export-modal-header">
          <h4 class="export-modal-title">Export Data</h4>
          <button class="export-modal-close" @click="$emit('close')" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>

        <div class="export-modal-body">
          <div class="export-options">
            <div class="export-option">
              <input type="radio" id="export-all" name="export-range" value="all" v-model="exportRange" />
              <label for="export-all">
                Export all data
                <span v-if="isServerSide && isLoadingAllData" class="export-option-note">
                  (Loading all data...)
                </span>
              </label>
            </div>
            <div class="export-option">
              <input type="radio" id="export-filtered" name="export-range" value="filtered" v-model="exportRange" />
              <label for="export-filtered">Export filtered data</label>
            </div>
            <div class="export-option">
              <input type="radio" id="export-selected" name="export-range" value="selected" v-model="exportRange"
                :disabled="!hasSelectedItems" />
              <label for="export-selected">
                Export selected data
                <span v-if="!hasSelectedItems" class="export-option-disabled">
                  (No items selected)
                </span>
              </label>
            </div>
          </div>

          <div class="export-columns">
            <h5>Columns to Export</h5>
            <div class="export-columns-options">
              <div class="export-column-select-all">
                <input type="checkbox" id="select-all-columns" :checked="allColumnsSelected"
                  @change="toggleAllColumns" />
                <label for="select-all-columns">Select All</label>
              </div>
              <div class="export-columns-list">
                <div v-for="column in columns" :key="column.key" class="export-column-option">
                  <input type="checkbox" :id="`column-${column.key}`" :value="column.key" v-model="selectedColumns" />
                  <label :for="`column-${column.key}`">{{ column.label }}</label>
                </div>
              </div>
            </div>
          </div>

          <div class="export-format">
            <h5>Export Format</h5>
            <div class="export-format-options">
              <div class="export-format-option">
                <input type="radio" id="format-csv" name="export-format" value="csv" v-model="exportFormat" />
                <label for="format-csv">CSV</label>
              </div>
              <div class="export-format-option">
                <input type="radio" id="format-excel" name="export-format" value="excel" v-model="exportFormat" />
                <label for="format-excel">Excel</label>
              </div>
              <div class="export-format-option">
                <input type="radio" id="format-json" name="export-format" value="json" v-model="exportFormat" />
                <label for="format-json">JSON</label>
              </div>
            </div>
          </div>
        </div>

        <div class="export-modal-footer">
          <button class="btn btn-outline-secondary" @click="$emit('close')">
            Cancel
          </button>
          <button class="btn btn-primary" @click="exportData" :disabled="selectedColumns.length === 0">
            Export
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  computed,
  ref,
} from 'vue'

import {
  exportToCsv,
  exportToExcel,
  exportToJson,
} from '../utils/exportUtils'

export default {
  name: 'TableExport',
  props: {
    columns: {
      type: Array,
      required: true
    },
    data: {
      type: Array,
      required: true
    },
    selectedItems: {
      type: Array,
      default: () => []
    },
    fileName: {
      type: String,
      default: 'table-export'
    },
    visible: {
      type: Boolean,
      default: false
    },
    isServerSide: {
      type: Boolean,
      default: false
    },
    isLoadingAllData: {
      type: Boolean,
      default: false
    },
    fetchAllData: {
      type: Function,
      default: null
    }
  },
  emits: ['close'],
  setup(props) {
    // State
    const exportRange = ref('all');
    const exportFormat = ref('csv');
    const selectedColumns = ref(props.columns.map(col => col.key));

    // Computed
    const hasSelectedItems = computed(() => {
      return props.selectedItems && props.selectedItems.length > 0;
    });

    const allColumnsSelected = computed(() => {
      return selectedColumns.value.length === props.columns.length;
    });

    const dataToExport = computed(() => {
      let data = props.data;

      if (exportRange.value === 'selected' && hasSelectedItems.value) {
        data = props.selectedItems;
      }

      // Filter data to only include selected columns
      return data.map(item => {
        const filteredItem = {};
        selectedColumns.value.forEach(key => {
          filteredItem[key] = item[key];
        });
        return filteredItem;
      });
    });

    const columnLabels = computed(() => {
      const labels = {};
      props.columns.forEach(col => {
        if (selectedColumns.value.includes(col.key)) {
          labels[col.key] = col.label;
        }
      });
      return labels;
    });

    // Methods
    const toggleAllColumns = () => {
      if (allColumnsSelected.value) {
        selectedColumns.value = [];
      } else {
        selectedColumns.value = props.columns.map(col => col.key);
      }
    };

    const exportData = async () => {
      if (selectedColumns.value.length === 0) return;

      let dataToProcess = dataToExport.value;

      // For server-side tables with 'all' export range, fetch all data
      if (props.isServerSide && exportRange.value === 'all' && props.fetchAllData) {
        try {
          const allData = await props.fetchAllData();

          // Filter data to only include selected columns
          dataToProcess = allData.map(item => {
            const filteredItem = {};
            selectedColumns.value.forEach(key => {
              filteredItem[key] = item[key];
            });
            return filteredItem;
          });
        } catch (error) {
          console.error('Error fetching all data for export:', error);
          // Fall back to current data if fetch fails
          dataToProcess = dataToExport.value;
        }
      }

      const filename = `${props.fileName}-${new Date().toISOString().slice(0, 10)}`;

      switch (exportFormat.value) {
        case 'csv':
          exportToCsv(dataToProcess, columnLabels.value, filename);
          break;
        case 'excel':
          exportToExcel(dataToProcess, columnLabels.value, filename);
          break;
        case 'json':
          exportToJson(dataToProcess, filename);
          break;
      }

      // Close the modal after export
      setTimeout(() => {
        this.$emit('close');
      }, 500);
    };

    return {
      exportRange,
      exportFormat,
      selectedColumns,
      hasSelectedItems,
      allColumnsSelected,
      toggleAllColumns,
      exportData
    };
  }
};
</script>

<style>
.export-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: var(--vdt-z-index-modal, 9999);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.export-modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.export-modal-container {
  position: relative;
  z-index: 10000;
  width: 100%;
  max-width: 600px;
}

.export-modal-content {
  position: relative;
  width: 100%;
  max-width: 600px;
  max-height: calc(100vh - 100px);
  /* Adjust max height to avoid overlapping */
  overflow-y: auto;
  background-color: var(--vdt-bg-white, #fff);
  border-radius: var(--vdt-radius, 8px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 8px 24px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  z-index: 10000;
  /* Ensure content is above other elements */
}

.export-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--vdt-border-color, #e2e8f0);
  background-color: var(--vdt-bg-white, #fff);
  position: sticky;
  top: 0;
  z-index: 10001;
  /* Higher than the modal content */
  border-top-left-radius: var(--vdt-radius, 8px);
  border-top-right-radius: var(--vdt-radius, 8px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 1px;
  /* Ensure there's no gap */
}

.export-modal-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--vdt-text-color, #1e293b);
}

.export-modal-close {
  background: none;
  border: none;
  font-size: 24px;
  line-height: 1;
  cursor: pointer;
  color: var(--vdt-text-light, #64748b);
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s, color 0.2s;
}

.export-modal-close:hover {
  background-color: var(--vdt-bg-hover, #f1f5f9);
  color: var(--vdt-text-color, #1e293b);
}

.export-modal-body {
  padding: 16px;
  flex: 1;
  overflow-y: auto;
  background-color: var(--vdt-bg-white, #fff);
}

.export-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding: 16px;
  border-top: 1px solid var(--vdt-border-color, #e2e8f0);
  background-color: var(--vdt-bg-white, #fff);
  position: sticky;
  bottom: 0;
  z-index: 10001;
  /* Same as header */
  border-bottom-left-radius: var(--vdt-radius, 8px);
  border-bottom-right-radius: var(--vdt-radius, 8px);
}

.export-options,
.export-columns,
.export-format {
  margin-bottom: 24px;
}

.export-option,
.export-column-option,
.export-format-option {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.export-option input,
.export-column-option input,
.export-format-option input,
.export-column-select-all input {
  margin-right: 8px;
}

.export-option-disabled,
.export-option-note {
  color: var(--vdt-text-light, #64748b);
  font-size: 12px;
  margin-left: 4px;
  font-style: italic;
}

.export-columns h5,
.export-format h5 {
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 600;
}

.export-column-select-all {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-weight: 500;
}

.export-columns-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid var(--vdt-border-color, #e2e8f0);
  border-radius: 4px;
  padding: 8px;
}

.btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-outline-secondary {
  background-color: transparent;
  border: 1px solid var(--vdt-border-color, #e2e8f0);
  color: var(--vdt-text-light, #64748b);
}

.btn-outline-secondary:hover {
  background-color: var(--vdt-bg-light, #f8fafc);
  color: var(--vdt-text-color, #1e293b);
  border-color: var(--vdt-border-color, #cbd5e1);
}

.btn-primary {
  background-color: var(--vdt-primary-color, #3b82f6);
  border: 1px solid var(--vdt-primary-color, #3b82f6);
  color: white;
}

.btn-primary:hover {
  background-color: var(--vdt-primary-hover, #2563eb);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>
