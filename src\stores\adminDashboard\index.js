import { defineStore } from 'pinia'

import api from '@/api/api'

const useAdminDashboard = defineStore('adminDashboard', {
    state: () => ({
        activeTab: 'overview',
        activeUsers: [],
    }),
    actions: {
        async userlist() {
            const response = await api.get('/user-list');
            return response.data.data.data.length;
        },
        async admissionList() {
            const response = await api.get('/admissions');
            return response.data.data.data.length;
        },
        async newsletterList() {
            const response = await api.get('/getAllnewsLetter');
            if (response.data?.data?.length < 1) return 0;
            return response.data.data.length;
        }
    }

});

export default useAdminDashboard;