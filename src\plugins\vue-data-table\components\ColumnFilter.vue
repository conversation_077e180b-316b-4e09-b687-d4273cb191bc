<template>
  <div class="column-filter" :class="{ 'active': hasFilter }">
    <!-- Text filter -->
    <input v-if="type === 'text'" type="text" class="filter-input" v-model="filterValue"
      :placeholder="`Filter ${column.label}...`" @input="applyFilter" />

    <!-- Select filter with search -->
    <searchable-select v-else-if="type === 'select'" v-model="filterValue" :options="selectOptions"
      :placeholder="`All ${column.label}`" @change="applyFilter" />

    <!-- Date filter -->
    <input v-else-if="type === 'date'" type="date" class="filter-date" v-model="filterValue" @input="applyFilter" />

    <!-- Date range filter -->
    <div v-else-if="type === 'daterange'" class="date-range-filter">
      <input type="date" class="filter-date start-date" v-model="startDate" @input="applyDateRangeFilter"
        placeholder="From" />
      <span class="date-separator">to</span>
      <input type="date" class="filter-date end-date" v-model="endDate" @input="applyDateRangeFilter"
        placeholder="To" />
    </div>
  </div>
</template>

<script>
import SearchableSelect from './SearchableSelect.vue'

// Debounce function to limit how often a function can be called
function debounce(func, wait) {
  let timeout;
  return function (...args) {
    const context = this;
    clearTimeout(timeout);
    timeout = setTimeout(() => func.apply(context, args), wait);
  };
}

export default {
  name: 'ColumnFilter',
  components: {
    SearchableSelect
  },
  data() {
    return {
      filterValue: '',
      startDate: '',
      endDate: '',
      // Create debounced versions of the filter methods
      debouncedApplyFilter: null,
      debouncedApplyDateRangeFilter: null
    }
  },

  props: {
    column: {
      type: Object,
      required: true
    },
    type: {
      type: String,
      default: 'text',
      validator: (value) => ['text', 'select', 'date', 'daterange'].includes(value)
    },
    options: {
      type: Array,
      default: () => []
    },
    // Add debounce prop with default value
    debounce: {
      type: Number,
      default: 300 // 300ms is a good default for most use cases
    }
  },

  created() {
    // Initialize debounced methods with configurable delay
    this.debouncedApplyFilter = debounce(this.emitFilter, this.debounce);
    this.debouncedApplyDateRangeFilter = debounce(this.emitDateRangeFilter, this.debounce);
  },
  computed: {
    hasFilter() {
      if (this.type === 'daterange') {
        return this.startDate || this.endDate;
      }
      return !!this.filterValue;
    },
    // Format options for the searchable select component
    selectOptions() {
      // Ensure options are in the correct format for SearchableSelect
      return this.options.map(option => {
        // If option is already in {value, label} format, return as is
        if (typeof option === 'object' && option.value !== undefined && option.label !== undefined) {
          return option;
        }
        // If option is a string or number, use it for both value and label
        return {
          value: option,
          label: String(option)
        };
      });
    }
  },
  methods: {
    // Call the debounced version when input changes
    applyFilter() {
      this.debouncedApplyFilter();
    },

    // Actual filter emission function
    emitFilter() {
      this.$emit('filter', {
        column: this.column.key,
        value: this.filterValue
      });
    },

    // Call the debounced version when date input changes
    applyDateRangeFilter() {
      this.debouncedApplyDateRangeFilter();
    },

    // Actual date range filter emission function
    emitDateRangeFilter() {
      this.$emit('filter', {
        column: this.column.key,
        value: {
          start: this.startDate,
          end: this.endDate
        }
      });
    },

    reset() {
      this.filterValue = '';
      this.startDate = '';
      this.endDate = '';
      // Emit immediately on reset without debouncing
      this.emitFilter();
    }
  }
}
</script>

<style>
.column-filter {
  width: 100%;
  padding: 4px 0;
}

.filter-input,
.filter-date {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid var(--vdt-border-color, #e2e8f0);
  border-radius: 4px;
  font-size: 14px;
  background-color: var(--vdt-bg-white, #fff);
  color: var(--vdt-text-color, #1e293b);
  transition: all 0.2s ease;
}

.filter-input:focus,
.filter-date:focus {
  outline: none;
  border-color: var(--vdt-primary-color, #3b82f6);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.date-range-filter {
  display: flex;
  align-items: center;
  gap: 4px;
}

.date-range-filter .filter-date {
  flex: 1;
  min-width: 0;
}

.date-separator {
  color: var(--vdt-text-light, #64748b);
  font-size: 12px;
  flex-shrink: 0;
}

.column-filter.active .filter-input,
.column-filter.active .filter-date {
  border-color: var(--vdt-primary-color, #3b82f6);
  background-color: rgba(59, 130, 246, 0.05);
}

/* Ensure the searchable select fits well in the filter */
.column-filter .searchable-select {
  width: 100%;
}
</style>
