# Vue Data Table

A powerful, flexible, and easy-to-use data table component for Vue.js applications. This component supports both client-side and server-side pagination, sorting, filtering, and data export.

## Features

- 📊 Responsive table layout
- 🔄 Client-side and server-side pagination
- 🔍 Search/filtering capabilities
- ↕️ Column sorting
- ✅ Row selection
- 📁 Export to CSV, Excel, and JSON
- 🎨 Customizable styling
- 🧩 Slot-based customization

## Installation

Register the plugin in your main.js file:

```js
import { createApp } from 'vue'
import App from './App.vue'
import VueDataTable from './plugins/vue-data-table'

const app = createApp(App)
app.use(VueDataTable)
app.mount('#app')
```

## Basic Usage

```vue
<template>
  <data-table
    :items="items"
    :columns="columns"
    title="Users"
  />
</template>

<script setup>
import { ref } from 'vue';

const columns = [
  { key: 'id', label: 'ID' },
  { key: 'name', label: 'Name', sortable: true },
  { key: 'email', label: 'Email', sortable: true },
  { key: 'role', label: 'Role' }
];

const items = ref([
  { id: 1, name: '<PERSON>', email: '<EMAIL>', role: 'Admin' },
  { id: 2, name: '<PERSON> <PERSON>', email: '<EMAIL>', role: 'User' },
  { id: 3, name: 'Bob Johnson', email: '<EMAIL>', role: 'Editor' }
]);
</script>
```

## Client-Side Pagination

By default, the component uses client-side pagination:

```vue
<template>
  <data-table
    :items="items"
    :columns="columns"
    :page-size="10"
    :page-sizes="[5, 10, 20, 50]"
  />
</template>
```

## Server-Side Pagination

For server-side pagination, set the `server-side` prop to `true` and handle the pagination events:

```vue
<template>
  <data-table
    :items="items"
    :columns="columns"
    :total-items="totalItems"
    :current-page-server="currentPage"
    :page-size="pageSize"
    :server-side="true"
    @page-change="handlePageChange"
    @page-size-change="handlePageSizeChange"
    @sort="handleSort"
    @search="handleSearch"
  />
</template>

<script setup>
import { ref } from 'vue';

const columns = [
  { key: 'id', label: 'ID' },
  { key: 'name', label: 'Name', sortable: true },
  { key: 'email', label: 'Email', sortable: true },
  { key: 'role', label: 'Role' }
];

const items = ref([]);
const totalItems = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const sortKey = ref('');
const sortOrder = ref('asc');
const searchQuery = ref('');

// Load data from server
async function loadData() {
  const response = await fetch(`/api/users?page=${currentPage.value}&pageSize=${pageSize.value}&sortKey=${sortKey.value}&sortOrder=${sortOrder.value}&search=${searchQuery.value}`);
  const data = await response.json();
  
  items.value = data.items;
  totalItems.value = data.total;
}

// Event handlers
function handlePageChange(page) {
  currentPage.value = page;
  loadData();
}

function handlePageSizeChange(size) {
  pageSize.value = size;
  currentPage.value = 1;
  loadData();
}

function handleSort({ key, order }) {
  sortKey.value = key;
  sortOrder.value = order;
  loadData();
}

function handleSearch(query) {
  searchQuery.value = query;
  currentPage.value = 1;
  loadData();
}

// Initial data load
loadData();
</script>
```

## Row Selection

Enable row selection with the `selectable` prop:

```vue
<template>
  <data-table
    :items="items"
    :columns="columns"
    :selectable="true"
    v-model:selected="selectedItems"
    @update:selected="handleSelectionChange"
  />
  
  <div v-if="selectedItems.length > 0">
    Selected {{ selectedItems.length }} items
  </div>
</template>

<script setup>
import { ref } from 'vue';

const selectedItems = ref([]);

function handleSelectionChange(items) {
  console.log('Selected items:', items);
}
</script>
```

## Custom Cell Rendering

Use slots to customize cell rendering:

```vue
<template>
  <data-table
    :items="items"
    :columns="columns"
  >
    <!-- Custom cell rendering for the 'status' column -->
    <template #cell-status="{ value }">
      <span :class="getStatusClass(value)">{{ value }}</span>
    </template>
    
    <!-- Custom actions column -->
    <template #actions="{ item }">
      <button @click="editItem(item)" class="btn-edit">Edit</button>
      <button @click="deleteItem(item)" class="btn-delete">Delete</button>
    </template>
  </data-table>
</template>

<script setup>
function getStatusClass(status) {
  return {
    'status-active': status === 'Active',
    'status-inactive': status === 'Inactive',
    'status-pending': status === 'Pending'
  };
}

function editItem(item) {
  // Edit logic
}

function deleteItem(item) {
  // Delete logic
}
</script>
```

## Custom Header Actions

Add custom buttons or controls to the table header:

```vue
<template>
  <data-table
    :items="items"
    :columns="columns"
  >
    <template #header-actions>
      <button @click="addNewItem" class="btn-add">
        Add New
      </button>
      <button @click="refreshData" class="btn-refresh">
        Refresh
      </button>
    </template>
  </data-table>
</template>
```

## Styling

The component uses CSS variables for styling, which you can override in your CSS:

```css
:root {
  --vdt-primary-color: #4f46e5;
  --vdt-primary-color-dark: #4338ca;
  --vdt-text-color: #1e293b;
  --vdt-text-color-light: #64748b;
  --vdt-border-color: #e2e8f0;
  --vdt-bg-color: #ffffff;
  --vdt-bg-color-light: #f8fafc;
  --vdt-bg-color-hover: #f1f5f9;
  --vdt-bg-color-selected: rgba(79, 70, 229, 0.1);
  --vdt-border-radius: 8px;
  --vdt-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
```

## API Reference

### DataTable Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `items` | Array | `[]` | The data items to display in the table |
| `columns` | Array | Required | Array of column definitions |
| `itemKey` | String | `'id'` | The property to use as the unique key for items |
| `title` | String | `''` | Table title |
| `tableClass` | String | `''` | Additional CSS classes for the table |
| `selectable` | Boolean | `false` | Enable row selection |
| `showSearch` | Boolean | `true` | Show the search input |
| `showPagination` | Boolean | `true` | Show pagination controls |
| `showExport` | Boolean | `true` | Show export button |
| `pageSize` | Number | `10` | Number of items per page |
| `pageSizes` | Array | `[10, 20, 50, 100]` | Available page size options |
| `serverSide` | Boolean | `false` | Use server-side pagination |
| `totalItems` | Number | `0` | Total number of items (for server-side pagination) |
| `currentPageServer` | Number | `1` | Current page (for server-side pagination) |
| `loading` | Boolean | `false` | Show loading indicator |
| `exportFileName` | String | `'table-export'` | Base filename for exports |

### DataTable Events

| Event | Parameters | Description |
|-------|------------|-------------|
| `update:selected` | `Array` | Emitted when selection changes |
| `row-click` | `Object` | Emitted when a row is clicked |
| `sort` | `{ key, order }` | Emitted when sorting changes |
| `search` | `String` | Emitted when search query changes |
| `page-change` | `Number` | Emitted when page changes |
| `page-size-change` | `Number` | Emitted when page size changes |
| `export` | `{ format, data }` | Emitted when data is exported |

### DataTable Slots

| Slot | Props | Description |
|------|-------|-------------|
| `header` | - | Custom table header |
| `header-actions` | - | Custom actions in the table header |
| `footer` | - | Custom table footer |
| `no-data` | - | Content to show when there's no data |
| `cell-{columnKey}` | `{ item, value }` | Custom cell rendering for a specific column |
| `actions` | `{ item, index }` | Custom actions for each row |

## Utility Functions

The plugin also exports utility functions for pagination, sorting, and exporting:

```js
import { exportUtils, paginationUtils, sortUtils } from './plugins/vue-data-table';

// Export data to CSV
exportUtils.exportToCsv(data, columnLabels, 'export-filename');

// Get paginated items
const paginatedItems = paginationUtils.getPaginatedItems(allItems, currentPage, pageSize);

// Sort items
const sortedItems = sortUtils.sortItems(items, 'name', 'asc');
```
