blockquote {
  display: block;
  position: relative;
  overflow: hidden;
  font-size: 20px;
  line-height: 30px;
  font-weight: 400;
  color: $title-color;
  background-color: $smoke-color;
  border-left: 4px solid $theme-color;

  padding: 43px 50px 42px 35px;
  margin: 35px 0;

  p {
    font-family: inherit;
    margin-bottom: 0 !important;
    line-height: 1.5;
    color: inherit;
    width: 100%;
    position: relative;
    z-index: 3;
    font-style: italic;
  }

  &:before {
    content: '\f10e';
    font-family: $icon-font;
    position: absolute;
    right: 70px;
    bottom: 27px;
    font-size: 3.5rem;
    font-weight: 900;
    line-height: 1;
    color: $theme-color;
  }



  p {
    margin-bottom: 0;

    a {
      color: inherit;
    }
  }

  cite {
    display: inline-block;
    font-size: 16px;
    position: relative;
    padding-left: 45px;
    line-height: 1;
    font-weight: 400;
    margin-top: 22px;
    font-style: normal;
    color: $title-color;

    &:before {
      content: '';
      position: absolute;
      left: 0;
      bottom: 8px;
      width: 30px;
      height: 2px;
      border-top: 2px solid $theme-color;
    }
  }
}


blockquote.vs-quote {
  border: 5px solid $theme-color;
  background-color: transparent;
  padding: 36px 60px 36px 160px;

  &:before {
    content: '\f10d';
    font-size: 6rem;
    font-weight: 300;
    right: auto;
    bottom: auto;
    left: 35px;
    top: 50%;
    transform: translateY(-50%);
    color: $theme-color;
    opacity: 0.2;
  }

  p {
    font-size: 26px;
    line-height: 40px;
    font-weight: 400;
    font-style: normal;
  }
}


.blog-meta {

  span,
  a {
    display: inline-block;
    margin-right: 20px;
    font-size: 16px;
    color: $theme-color;
    font-weight: 500;
    transition: all ease 0.4s;

    &:last-child {
      margin-right: 0;
    }

    i {
      margin-right: 7px;
    }
  }

  a:hover {
    color: $secondary-color;
  }
}

.blog-category {
  margin-bottom: -10px;

  a {
    display: inline-block;
    color: $white-color;
    padding: 4.5px 24.5px;
    margin-right: 5px;
    margin-bottom: 10px;
    border: 1px solid transparent;
    background-color: $theme-color;

    &:hover {
      background-color: $white-color;
      color: $body-color;
      border-color: $theme-color;
    }
  }
}

.blog-title {
  a {
    color: inherit;

    &:hover {
      color: $theme-color;
    }
  }
}

.vs-blog {
  margin-bottom: 30px;
}

.share-links-title {
  font-size: 18px;
  color: $title-color;
  font-family: $title-font;
  font-weight: 600;
  margin: 0 15px 0 0;
  display: inline-block;
}

.share-links {
  margin: 50px 0 50px 0;
  padding: 17px 30px;
  background-color: $theme-color2;
  border-radius: 9999px;

  .row {
    align-items: center;
    --bs-gutter-y: 15px;
  }

  .tagcloud {
    display: inline-block;

    a:not(:hover) {
      background-color: $white-color;
    }
  }

  .social-links {
    display: inline-block;
    list-style-type: none;
    margin: 0;
    padding: 0;

    li {
      display: inline-block;
      margin-right: 4px;

      &:last-child {
        margin-right: 0;
      }
    }

    a {
      width: 40px;
      height: 40px;
      line-height: 40px;
      font-size: 14px;
      text-align: center;
      display: block;
      color: $title-color;
      background-color: $white-color;
      border-radius: 50%;

      &:hover {
        color: $white-color;
        background-color: $theme-color;
        border-color: $theme-color;
      }
    }
  }
}

.blog-author {
  display: flex;
  align-items: center;
  padding: 35px 50px 35px 35px;
  margin: 50px 0;
  background-color: $secondary-color;
  border-radius: 30px;

  .media-img {
    width: 180px;
    margin-right: 30px;
    border-radius: 30px;
    overflow: hidden;

    img {
      transform: scale(1.001);
      transition: all ease 0.4s;
    }
  }

  .author-degi {
    color: $white-color;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 0.05em;
    font-family: $title-font;
    display: block;
    margin: 0 0 5px 0;
  }

  .author-name {
    color: $white-color;
    line-height: 1;
    font-size: 30px;
    letter-spacing: 0.01em;
    margin-bottom: 15px;
  }

  .author-text {
    color: #E0E0E0;
    margin: 0;
    font-size: 18px;
    line-height: 1.5;
  }

  &:hover {
    .media-img {
      img {
        transform: scale(1.15);
      }
    }
  }
}

.blog-inner-title {
  font-size: 40px;
  position: relative;
  margin: -0.25em 0 40px 0;
  padding: 0 0 7px 0;

  &:before {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 30px;
    height: 3px;
    background-color: $theme-color2;
  }
}


.blog-single {
  position: relative;
  margin-bottom: 75.5px;
  border: 3px solid $theme-color2;
  border-radius: 30px;

  .vs-btn {
    margin-bottom: -25.5px;
  }

  .blog-meta {
    margin-bottom: 5px;
  }

  .blog-title {
    line-height: 1.15;
    margin-bottom: 15px;
    font-size: 46px;
    text-transform: capitalize;
  }

  .blog-text {
    margin-bottom: 40px;
  }

  .blog-audio,
  .blog-img {
    position: relative;
    background-color: $smoke-color;
    margin: -3px -3px 0 -3px;
    border-radius: 30px 30px 0 0;
    overflow: hidden;
  }

  .blog-audio {
    line-height: 1;
  }

  .blog-img {

    img {
      transform: scale(1.002);
      transition: all ease 0.4s;
    }

    .slick-arrow {
      --pos-x: 30px;
    }

    .play-btn {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
  }

  .blog-content {
    padding: 35px 60px 1px 60px;
  }

  &:hover {
    .blog-img {
      img {
        transform: scale(1.15);
      }

      .slick-arrow {
        opacity: 1;
        visibility: visible;
      }
    }
  }

  .mega-hover:not(.blog-img) {
    border-radius: 30px;
  }
}


.blog-details {
  .blog-single {
    border: none;
  }

  
  .blog-single {
    .blog-img {
      border-radius: 30px;
      margin-bottom: 35px;
    }

    .blog-content {
      padding: 0;
      border: none;
    }
  }
}

.blog-style1 {
  border: 3px solid $theme-color2;
  border-radius: 30px;

  .blog-date {
    font-weight: 600;
    font-size: 16px;
    font-family: $title-font;
    margin-bottom: 3px;
    display: inline-block;
    
    i {
      margin-right: 10px;
    }
  }

  .blog-content {
    padding: 21px 30px 7px 30px;
  }

  .blog-btn {
    width: 100%;
    display: block;
    font-size: 18px;
    font-weight: 600;
    font-family: $title-font;
    border-radius: 9999px;
    line-height: 1;
    padding: 16px 30px;
    color: $title-color;
    background-color: $theme-color2;

    i {
      margin-left: 8px;
      position: relative;
      top: 1px;
    }
  }

  .blog-img {
    border-radius: 30px;
    border: inherit;
    margin: -3px;
    overflow: hidden;

    img {
      transform: scale(1.001);
      transition: all ease 0.4s;
    }
  }

  &:hover {
    .blog-img {
      img {
        transform: scale(1.15);
      }
    }
  }
}

.blog-style2 {  
  &:not(:last-child) {
    margin-bottom: 40px;
  }
  
  .blog-body {
    display: flex;
    align-items: center;
    padding: 15px 50px 15px 15px;
    border: 3px solid $theme-color2;
    border-radius: 30px;
    transition: all ease 0.4s;
  }

  &:nth-child(2) {
    @media (min-width: $ml) {
      .blog-body {
        margin-left: -110px;
        width: 100%;
      }
    }
  }

  &:hover {
    .blog-body {
      border-color: $theme-color;
    }
  }


  .blog-img {
    min-width: 10px;
    overflow: hidden;
    margin-right: 40px;
    border-radius: 30px;

    img {
      width: 100%;
      transform: scale(1.001);
      transition: all ease 0.4s;
    }
  }

  &:hover {
    .blog-img {
      img {
        transform: scale(1.15);
      }
    }
  }

  .blog-content {
    flex: 1;
  }

  .blog-title,
  .blog-meta {
    margin-bottom: 5px;
  }

  .blog-text {
    margin-bottom: 0;
  }

}

@include ml {
  blockquote {
    font-size: 18px;
    padding: 23px 30px 26px 30px;
  }

  blockquote.vs-quote {
    padding: 26px 60px 26px 130px;

    p {
      font-size: 20px;
      line-height: 30px;
    }

    &:before {
      font-size: 5rem;
      left: 25px;
    }
  }

  .blog-single {
    .blog-content {
      padding: 35px 40px 1px 40px;
    }

    .blog-title {
      font-size: 40px;
    }

    .blog-text {
      margin-bottom: 25px;
    }
  }

  .blog-style2 {
    .blog-body {
      padding: 15px 15px 25px 15px;
    }
  }
}

@include lg {
  .share-links {
    border-radius: 20px;
  }

  .blog-author {
    .author-text {
      font-size: 16px;
    }

    .media-img {
      width: 150px;
      margin-right: 25px;
    }
  }

  .blog-single {
    .blog-content {
      padding: 25px 30px 1px 30px;
    }

    .blog-title {
      font-size: 36px;
    }
  }

  .blog-details {
    .blog-single {
      .blog-img {
        margin-bottom: 25px;
        border-radius: 20px;
      }
    }
  }

  .blog-style2 {
    text-align: center;
    
    .blog-body {
      display: block;
    }
    .blog-img {
      margin-right: 0;
      margin-bottom: 20px;
    }
  }

}


@include sm {
  blockquote {
    font-size: 16px;
    padding: 23px 15px 26px 15px;

    &:before {
      right: 30px;
      bottom: 30px;
      font-size: 2rem;
    }

    cite {
      margin-top: 17px;
      font-size: 14px;

      &::before {
        bottom: 6px;
      }
    }
  }

  blockquote.vs-quote {
    padding: 26px 20px 26px 20px;

    &:before {
      content: '\f10e';
      left: auto;
      top: auto;
      transform: none;
      right: 20px;
      bottom: 20px;
    }

    p {
      font-size: 18px;
    }
  }

  .share-links {
    padding: 17px 20px;
  }

  .blog-inner-title {
    font-size: 26px;
  }

  .share-links-title {
    font-size: 18px;
    display: block;
    margin: 0 0 10px 0;
  }

  .blog-meta {

    >span,
    a {
      font-size: 14px;
      margin-right: 15px;
    }
  }

  .blog-author {
    display: block;
    padding: 35px 25px 35px 25px;

    .media-img {
      width: 145px;
      margin-right: 0;
      margin-bottom: 20px;
    }
  }

  .blog-single {
    border-radius: 15px;

    .blog-audio,
    .blog-img {
      border-radius: 15px 15px 0 0;
    }

    .blog-content {
      padding: 25px 20px 1px 20px;
    }

    .blog-title {
      font-size: 24px;
      line-height: 1.4;
    }
  }

  
}