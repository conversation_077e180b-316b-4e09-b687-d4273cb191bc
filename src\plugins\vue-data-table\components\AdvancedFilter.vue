<template>
  <div class="advanced-filter">
    <!-- Filter Toggle Button -->
    <button
      @click="toggleFilters"
      class="inline-flex items-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
    >
      <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
      </svg>
      Filters
      <span v-if="activeFiltersCount > 0" class="ml-2 inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
        {{ activeFiltersCount }}
      </span>
    </button>
    
    <!-- Filter Panel -->
    <div v-if="showFilters" class="mt-4 rounded-lg border border-gray-200 bg-white p-4 shadow-sm">
      <!-- Column Filters -->
      <div v-if="filterableColumns.length > 0" class="mb-6">
        <h4 class="mb-3 text-sm font-medium text-gray-900">Column Filters</h4>
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
          <div v-for="column in filterableColumns" :key="column.key" class="space-y-1">
            <label class="block text-xs font-medium text-gray-700">
              {{ column.label }}
            </label>
            
            <!-- Text Filter -->
            <input
              v-if="column.filterType === 'text'"
              v-model="columnFilters[column.key]"
              type="text"
              :placeholder="`Filter by ${column.label.toLowerCase()}`"
              class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
            
            <!-- Number Filter -->
            <input
              v-else-if="column.filterType === 'number'"
              v-model.number="columnFilters[column.key]"
              type="number"
              :placeholder="`Filter by ${column.label.toLowerCase()}`"
              class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
            
            <!-- Date Filter -->
            <input
              v-else-if="column.filterType === 'date'"
              v-model="columnFilters[column.key]"
              type="date"
              class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
            
            <!-- Select Filter -->
            <select
              v-else-if="column.filterType === 'select'"
              v-model="columnFilters[column.key]"
              class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
            >
              <option value="">All {{ column.label }}</option>
              <option
                v-for="option in column.filterOptions"
                :key="option.value"
                :value="option.value"
              >
                {{ option.label }}
              </option>
            </select>
            
            <!-- Boolean Filter -->
            <select
              v-else-if="column.filterType === 'boolean'"
              v-model="columnFilters[column.key]"
              class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
            >
              <option value="">All</option>
              <option value="true">Yes</option>
              <option value="false">No</option>
            </select>
          </div>
        </div>
      </div>
      
      <!-- Advanced Filters -->
      <div class="mb-6">
        <div class="mb-3 flex items-center justify-between">
          <h4 class="text-sm font-medium text-gray-900">Advanced Filters</h4>
          <button
            @click="addFilter"
            class="inline-flex items-center rounded-md bg-blue-600 px-3 py-1.5 text-xs font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            <svg class="mr-1 h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
            Add Filter
          </button>
        </div>
        
        <div v-if="advancedFilters.length === 0" class="text-center py-4 text-sm text-gray-500">
          No advanced filters added
        </div>
        
        <div v-else class="space-y-3">
          <div
            v-for="(filter, index) in advancedFilters"
            :key="index"
            class="flex items-center space-x-3 rounded-md border border-gray-200 p-3"
          >
            <!-- Column Selection -->
            <select
              v-model="filter.key"
              class="rounded-md border border-gray-300 px-3 py-1.5 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
            >
              <option value="">Select Column</option>
              <option
                v-for="column in filterableColumns"
                :key="column.key"
                :value="column.key"
              >
                {{ column.label }}
              </option>
            </select>
            
            <!-- Operator Selection -->
            <select
              v-model="filter.operator"
              class="rounded-md border border-gray-300 px-3 py-1.5 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
            >
              <option v-for="op in getOperators(filter.key)" :key="op.value" :value="op.value">
                {{ op.label }}
              </option>
            </select>
            
            <!-- Value Input -->
            <input
              v-if="!isRangeOperator(filter.operator)"
              v-model="filter.value"
              :type="getInputType(filter.key)"
              placeholder="Value"
              class="flex-1 rounded-md border border-gray-300 px-3 py-1.5 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
            
            <!-- Range Inputs -->
            <div v-else class="flex flex-1 space-x-2">
              <input
                v-model="filter.value[0]"
                :type="getInputType(filter.key)"
                placeholder="From"
                class="flex-1 rounded-md border border-gray-300 px-3 py-1.5 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
              <input
                v-model="filter.value[1]"
                :type="getInputType(filter.key)"
                placeholder="To"
                class="flex-1 rounded-md border border-gray-300 px-3 py-1.5 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
            </div>
            
            <!-- Remove Button -->
            <button
              @click="removeFilter(index)"
              class="rounded-md p-1.5 text-gray-400 hover:bg-gray-100 hover:text-gray-600"
            >
              <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      </div>
      
      <!-- Filter Actions -->
      <div class="flex justify-between">
        <button
          @click="clearAllFilters"
          class="inline-flex items-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          Clear All
        </button>
        
        <div class="space-x-2">
          <button
            @click="applyFilters"
            class="inline-flex items-center rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Apply Filters
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'

export default {
  name: 'AdvancedFilter',
  props: {
    columns: {
      type: Array,
      default: () => []
    },
    modelValue: {
      type: Object,
      default: () => ({
        columnFilters: {},
        advancedFilters: []
      })
    }
  },
  emits: ['update:modelValue', 'apply'],
  setup(props, { emit }) {
    const showFilters = ref(false)
    const columnFilters = ref({ ...props.modelValue.columnFilters })
    const advancedFilters = ref([...props.modelValue.advancedFilters])
    
    const filterableColumns = computed(() => {
      return props.columns.filter(column => column.filterable !== false)
    })
    
    const activeFiltersCount = computed(() => {
      const columnCount = Object.keys(columnFilters.value).filter(key => 
        columnFilters.value[key] !== null && 
        columnFilters.value[key] !== undefined && 
        columnFilters.value[key] !== ''
      ).length
      
      const advancedCount = advancedFilters.value.filter(filter => 
        filter.key && filter.value !== null && filter.value !== undefined && filter.value !== ''
      ).length
      
      return columnCount + advancedCount
    })
    
    const operators = {
      text: [
        { value: 'like', label: 'Contains' },
        { value: 'not_like', label: 'Does not contain' },
        { value: 'eq', label: 'Equals' },
        { value: 'ne', label: 'Not equals' },
        { value: 'starts_with', label: 'Starts with' },
        { value: 'ends_with', label: 'Ends with' },
        { value: 'empty', label: 'Is empty' },
        { value: 'not_empty', label: 'Is not empty' }
      ],
      number: [
        { value: 'eq', label: 'Equals' },
        { value: 'ne', label: 'Not equals' },
        { value: 'gt', label: 'Greater than' },
        { value: 'gte', label: 'Greater than or equal' },
        { value: 'lt', label: 'Less than' },
        { value: 'lte', label: 'Less than or equal' },
        { value: 'between', label: 'Between' }
      ],
      date: [
        { value: 'date_eq', label: 'On date' },
        { value: 'date_gt', label: 'After date' },
        { value: 'date_gte', label: 'On or after date' },
        { value: 'date_lt', label: 'Before date' },
        { value: 'date_lte', label: 'On or before date' },
        { value: 'date_between', label: 'Between dates' }
      ],
      select: [
        { value: 'eq', label: 'Equals' },
        { value: 'ne', label: 'Not equals' },
        { value: 'in', label: 'In list' },
        { value: 'not_in', label: 'Not in list' }
      ],
      boolean: [
        { value: 'eq', label: 'Equals' }
      ]
    }
    
    const toggleFilters = () => {
      showFilters.value = !showFilters.value
    }
    
    const getOperators = (columnKey) => {
      const column = filterableColumns.value.find(col => col.key === columnKey)
      if (!column) return operators.text
      
      return operators[column.filterType] || operators.text
    }
    
    const getInputType = (columnKey) => {
      const column = filterableColumns.value.find(col => col.key === columnKey)
      if (!column) return 'text'
      
      switch (column.filterType) {
        case 'number':
          return 'number'
        case 'date':
          return 'date'
        default:
          return 'text'
      }
    }
    
    const isRangeOperator = (operator) => {
      return ['between', 'date_between'].includes(operator)
    }
    
    const addFilter = () => {
      advancedFilters.value.push({
        key: '',
        operator: 'eq',
        value: ''
      })
    }
    
    const removeFilter = (index) => {
      advancedFilters.value.splice(index, 1)
    }
    
    const clearAllFilters = () => {
      columnFilters.value = {}
      advancedFilters.value = []
      applyFilters()
    }
    
    const applyFilters = () => {
      const filterData = {
        columnFilters: { ...columnFilters.value },
        advancedFilters: [...advancedFilters.value]
      }
      
      emit('update:modelValue', filterData)
      emit('apply', filterData)
    }
    
    // Watch for changes and auto-apply column filters
    watch(columnFilters, () => {
      applyFilters()
    }, { deep: true })
    
    // Initialize range values for range operators
    watch(advancedFilters, (newFilters) => {
      newFilters.forEach(filter => {
        if (isRangeOperator(filter.operator) && !Array.isArray(filter.value)) {
          filter.value = ['', '']
        } else if (!isRangeOperator(filter.operator) && Array.isArray(filter.value)) {
          filter.value = ''
        }
      })
    }, { deep: true })
    
    return {
      showFilters,
      columnFilters,
      advancedFilters,
      filterableColumns,
      activeFiltersCount,
      toggleFilters,
      getOperators,
      getInputType,
      isRangeOperator,
      addFilter,
      removeFilter,
      clearAllFilters,
      applyFilters
    }
  }
}
</script>
