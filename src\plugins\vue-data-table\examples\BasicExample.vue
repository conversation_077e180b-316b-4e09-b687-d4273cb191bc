<template>
  <div class="p-6">
    <h1 class="text-2xl font-bold mb-6">Vue Data Table - Basic Example</h1>
    
    <!-- Basic Data Table -->
    <div class="mb-8">
      <h2 class="text-xl font-semibold mb-4">Basic Table</h2>
      <DataTable
        :items="users"
        :columns="basicColumns"
        title="Users List"
        :selectable="true"
        :searchable="true"
        :export-config="{ csv: true, excel: true, json: true }"
        @row-click="handleRowClick"
      />
    </div>
    
    <!-- Enhanced Data Table with CRUD -->
    <div class="mb-8">
      <h2 class="text-xl font-semibold mb-4">Enhanced Table with CRUD</h2>
      <EnhancedDataTable
        :items="products"
        :columns="productColumns"
        title="Products Management"
        :selectable="true"
        :searchable="true"
        :crud="crudConfig"
        :export-config="{ csv: true, excel: true, json: true, pdf: true }"
        :print-config="{ enabled: true }"
        @crud-create="handleCreate"
        @crud-update="handleUpdate"
        @crud-delete="handleDelete"
        @refresh="loadData"
      >
        <!-- Custom cell for status -->
        <template #cell-status="{ value }">
          <span 
            :class="[
              'px-2 py-1 rounded-full text-xs font-medium',
              value === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            ]"
          >
            {{ value }}
          </span>
        </template>
        
        <!-- Custom cell for price -->
        <template #cell-price="{ value }">
          <span class="font-medium text-green-600">${{ value.toFixed(2) }}</span>
        </template>
      </EnhancedDataTable>
    </div>
    
    <!-- Server-side Table -->
    <div class="mb-8">
      <h2 class="text-xl font-semibold mb-4">Server-side Table</h2>
      <ApiDataTable
        :api-url="'/api/users'"
        :columns="serverColumns"
        title="Server-side Users"
        :selectable="true"
        :auto-load="true"
        @data-loaded="handleDataLoaded"
        @data-error="handleDataError"
      />
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'

export default {
  name: 'BasicExample',
  setup() {
    // Sample data
    const users = ref([
      { id: 1, name: 'John Doe', email: '<EMAIL>', role: 'Admin', status: 'active', created_at: '2024-01-15' },
      { id: 2, name: 'Jane Smith', email: '<EMAIL>', role: 'User', status: 'active', created_at: '2024-01-16' },
      { id: 3, name: 'Bob Johnson', email: '<EMAIL>', role: 'User', status: 'inactive', created_at: '2024-01-17' },
      { id: 4, name: 'Alice Brown', email: '<EMAIL>', role: 'Moderator', status: 'active', created_at: '2024-01-18' },
      { id: 5, name: 'Charlie Wilson', email: '<EMAIL>', role: 'User', status: 'active', created_at: '2024-01-19' }
    ])

    const products = ref([
      { id: 1, name: 'Laptop Pro', category: 'Electronics', price: 1299.99, stock: 15, status: 'active' },
      { id: 2, name: 'Wireless Mouse', category: 'Electronics', price: 29.99, stock: 50, status: 'active' },
      { id: 3, name: 'Office Chair', category: 'Furniture', price: 199.99, stock: 8, status: 'active' },
      { id: 4, name: 'Desk Lamp', category: 'Furniture', price: 49.99, stock: 0, status: 'inactive' },
      { id: 5, name: 'Smartphone', category: 'Electronics', price: 699.99, stock: 25, status: 'active' }
    ])

    // Column definitions
    const basicColumns = ref([
      { key: 'id', label: 'ID', sortable: true, width: '80px' },
      { key: 'name', label: 'Name', sortable: true, filterable: true },
      { key: 'email', label: 'Email', sortable: true, filterable: true },
      { key: 'role', label: 'Role', sortable: true, filterable: true, filterType: 'select', filterOptions: [
        { label: 'Admin', value: 'Admin' },
        { label: 'User', value: 'User' },
        { label: 'Moderator', value: 'Moderator' }
      ]},
      { key: 'status', label: 'Status', sortable: true, filterable: true, filterType: 'select', filterOptions: [
        { label: 'Active', value: 'active' },
        { label: 'Inactive', value: 'inactive' }
      ]},
      { key: 'created_at', label: 'Created', sortable: true, filterType: 'date' }
    ])

    const productColumns = ref([
      { key: 'id', label: 'ID', sortable: true, width: '80px' },
      { key: 'name', label: 'Product Name', sortable: true, filterable: true },
      { key: 'category', label: 'Category', sortable: true, filterable: true, filterType: 'select', filterOptions: [
        { label: 'Electronics', value: 'Electronics' },
        { label: 'Furniture', value: 'Furniture' }
      ]},
      { key: 'price', label: 'Price', sortable: true, filterable: true, filterType: 'number', align: 'right' },
      { key: 'stock', label: 'Stock', sortable: true, filterable: true, filterType: 'number', align: 'center' },
      { key: 'status', label: 'Status', sortable: true, filterable: true, filterType: 'select', filterOptions: [
        { label: 'Active', value: 'active' },
        { label: 'Inactive', value: 'inactive' }
      ]}
    ])

    const serverColumns = ref([
      { key: 'id', label: 'ID', sortable: true },
      { key: 'name', label: 'Name', sortable: true, filterable: true },
      { key: 'email', label: 'Email', sortable: true, filterable: true },
      { key: 'phone', label: 'Phone', filterable: true },
      { key: 'company.name', label: 'Company', sortable: true, filterable: true }
    ])

    // CRUD configuration
    const crudConfig = ref({
      create: {
        enabled: true,
        fields: [
          { key: 'name', label: 'Product Name', type: 'text', required: true },
          { key: 'category', label: 'Category', type: 'select', required: true, options: [
            { label: 'Electronics', value: 'Electronics' },
            { label: 'Furniture', value: 'Furniture' }
          ]},
          { key: 'price', label: 'Price', type: 'number', required: true },
          { key: 'stock', label: 'Stock', type: 'number', required: true },
          { key: 'status', label: 'Status', type: 'select', required: true, options: [
            { label: 'Active', value: 'active' },
            { label: 'Inactive', value: 'inactive' }
          ]}
        ],
        validation: {
          name: ['required', 'min:3'],
          price: ['required', 'numeric', 'min_value:0'],
          stock: ['required', 'integer', 'min_value:0']
        }
      },
      update: {
        enabled: true,
        fields: [
          { key: 'name', label: 'Product Name', type: 'text', required: true },
          { key: 'category', label: 'Category', type: 'select', required: true, options: [
            { label: 'Electronics', value: 'Electronics' },
            { label: 'Furniture', value: 'Furniture' }
          ]},
          { key: 'price', label: 'Price', type: 'number', required: true },
          { key: 'stock', label: 'Stock', type: 'number', required: true },
          { key: 'status', label: 'Status', type: 'select', required: true, options: [
            { label: 'Active', value: 'active' },
            { label: 'Inactive', value: 'inactive' }
          ]}
        ]
      },
      delete: {
        enabled: true,
        confirmation: true
      }
    })

    // Event handlers
    const handleRowClick = (row, index) => {
      console.log('Row clicked:', row, index)
    }

    const handleCreate = (data) => {
      console.log('Create:', data)
      const newId = Math.max(...products.value.map(p => p.id)) + 1
      products.value.push({ ...data, id: newId })
    }

    const handleUpdate = (data) => {
      console.log('Update:', data)
      const index = products.value.findIndex(p => p.id === data.id)
      if (index !== -1) {
        products.value[index] = { ...products.value[index], ...data }
      }
    }

    const handleDelete = (data) => {
      console.log('Delete:', data)
      if (Array.isArray(data)) {
        // Bulk delete
        const ids = data.map(item => item.id)
        products.value = products.value.filter(p => !ids.includes(p.id))
      } else {
        // Single delete
        products.value = products.value.filter(p => p.id !== data.id)
      }
    }

    const loadData = () => {
      console.log('Refreshing data...')
      // Simulate data refresh
    }

    const handleDataLoaded = (data) => {
      console.log('Server data loaded:', data)
    }

    const handleDataError = (error) => {
      console.error('Server data error:', error)
    }

    return {
      users,
      products,
      basicColumns,
      productColumns,
      serverColumns,
      crudConfig,
      handleRowClick,
      handleCreate,
      handleUpdate,
      handleDelete,
      loadData,
      handleDataLoaded,
      handleDataError
    }
  }
}
</script>
