<template>
  <div v-if="visible" class="fixed inset-0 z-50 overflow-y-auto" @click="handleBackdropClick">
    <div class="flex min-h-screen items-center justify-center p-4">
      <!-- Backdrop -->
      <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity"></div>
      
      <!-- Modal -->
      <div class="relative w-full max-w-2xl transform rounded-lg bg-white shadow-xl transition-all">
        <!-- Header -->
        <div class="flex items-center justify-between border-b border-gray-200 px-6 py-4">
          <h3 class="text-lg font-semibold text-gray-900">
            {{ modalTitle }}
          </h3>
          <button
            @click="$emit('close')"
            class="rounded-md p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-600"
          >
            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <!-- Body -->
        <div class="px-6 py-4">
          <form @submit.prevent="handleSubmit" class="space-y-4">
            <div v-for="field in formFields" :key="field.key" class="space-y-1">
              <label :for="field.key" class="block text-sm font-medium text-gray-700">
                {{ field.label }}
                <span v-if="field.required" class="text-red-500">*</span>
              </label>
              
              <!-- Text Input -->
              <input
                v-if="field.type === 'text' || field.type === 'email' || field.type === 'password'"
                :id="field.key"
                v-model="formData[field.key]"
                :type="field.type"
                :placeholder="field.placeholder"
                :readonly="field.readonly"
                :disabled="field.disabled"
                class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 disabled:bg-gray-100"
                :class="{ 'border-red-500': hasError(field.key) }"
              />
              
              <!-- Number Input -->
              <input
                v-else-if="field.type === 'number'"
                :id="field.key"
                v-model.number="formData[field.key]"
                type="number"
                :placeholder="field.placeholder"
                :readonly="field.readonly"
                :disabled="field.disabled"
                class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 disabled:bg-gray-100"
                :class="{ 'border-red-500': hasError(field.key) }"
              />
              
              <!-- Date Input -->
              <input
                v-else-if="field.type === 'date'"
                :id="field.key"
                v-model="formData[field.key]"
                type="date"
                :readonly="field.readonly"
                :disabled="field.disabled"
                class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 disabled:bg-gray-100"
                :class="{ 'border-red-500': hasError(field.key) }"
              />
              
              <!-- Select Input -->
              <select
                v-else-if="field.type === 'select'"
                :id="field.key"
                v-model="formData[field.key]"
                :disabled="field.disabled"
                class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 disabled:bg-gray-100"
                :class="{ 'border-red-500': hasError(field.key) }"
              >
                <option value="">Select {{ field.label }}</option>
                <option
                  v-for="option in field.options"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </option>
              </select>
              
              <!-- Textarea -->
              <textarea
                v-else-if="field.type === 'textarea'"
                :id="field.key"
                v-model="formData[field.key]"
                :placeholder="field.placeholder"
                :readonly="field.readonly"
                :disabled="field.disabled"
                rows="3"
                class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 disabled:bg-gray-100"
                :class="{ 'border-red-500': hasError(field.key) }"
              ></textarea>
              
              <!-- Checkbox -->
              <div v-else-if="field.type === 'checkbox'" class="flex items-center">
                <input
                  :id="field.key"
                  v-model="formData[field.key]"
                  type="checkbox"
                  :disabled="field.disabled"
                  class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label :for="field.key" class="ml-2 text-sm text-gray-700">
                  {{ field.label }}
                </label>
              </div>
              
              <!-- File Input -->
              <input
                v-else-if="field.type === 'file'"
                :id="field.key"
                type="file"
                :disabled="field.disabled"
                @change="handleFileChange(field.key, $event)"
                class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 disabled:bg-gray-100"
                :class="{ 'border-red-500': hasError(field.key) }"
              />
              
              <!-- Error Messages -->
              <div v-if="hasError(field.key)" class="text-sm text-red-600">
                <div v-for="error in getErrors(field.key)" :key="error">
                  {{ error }}
                </div>
              </div>
            </div>
          </form>
        </div>
        
        <!-- Footer -->
        <div class="flex justify-end space-x-3 border-t border-gray-200 px-6 py-4">
          <button
            type="button"
            @click="$emit('close')"
            class="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Cancel
          </button>
          <button
            type="button"
            @click="handleSubmit"
            :disabled="loading"
            class="rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
          >
            <span v-if="loading" class="flex items-center">
              <svg class="mr-2 h-4 w-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              {{ mode === 'create' ? 'Creating...' : 'Updating...' }}
            </span>
            <span v-else>
              {{ mode === 'create' ? 'Create' : 'Update' }}
            </span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'
import { validateFormData } from '../utils/crudUtils'

export default {
  name: 'CrudModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    mode: {
      type: String,
      default: 'create', // 'create' or 'edit'
      validator: value => ['create', 'edit'].includes(value)
    },
    title: {
      type: String,
      default: ''
    },
    fields: {
      type: Array,
      default: () => []
    },
    data: {
      type: Object,
      default: () => ({})
    },
    validation: {
      type: Object,
      default: () => ({})
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  emits: ['close', 'submit'],
  setup(props, { emit }) {
    const formData = ref({})
    const errors = ref({})
    
    const modalTitle = computed(() => {
      if (props.title) return props.title
      return props.mode === 'create' ? 'Create New Record' : 'Edit Record'
    })
    
    const formFields = computed(() => {
      return props.fields.filter(field => field.type !== 'hidden')
    })
    
    // Initialize form data when modal opens or data changes
    watch([() => props.visible, () => props.data], () => {
      if (props.visible) {
        initializeFormData()
        errors.value = {}
      }
    }, { immediate: true })
    
    const initializeFormData = () => {
      const initialData = {}
      
      props.fields.forEach(field => {
        if (props.mode === 'edit' && props.data[field.key] !== undefined) {
          initialData[field.key] = props.data[field.key]
        } else {
          initialData[field.key] = field.default || getDefaultValue(field.type)
        }
      })
      
      formData.value = initialData
    }
    
    const getDefaultValue = (type) => {
      switch (type) {
        case 'checkbox':
          return false
        case 'number':
          return 0
        case 'select':
          return ''
        default:
          return ''
      }
    }
    
    const hasError = (fieldKey) => {
      return errors.value[fieldKey] && errors.value[fieldKey].length > 0
    }
    
    const getErrors = (fieldKey) => {
      return errors.value[fieldKey] || []
    }
    
    const handleFileChange = (fieldKey, event) => {
      const file = event.target.files[0]
      formData.value[fieldKey] = file
    }
    
    const handleSubmit = () => {
      // Validate form data
      const validation = validateFormData(formData.value, props.validation, props.fields)
      
      if (!validation.isValid) {
        errors.value = validation.errors
        return
      }
      
      errors.value = {}
      emit('submit', { ...formData.value })
    }
    
    const handleBackdropClick = (event) => {
      if (event.target === event.currentTarget) {
        emit('close')
      }
    }
    
    return {
      formData,
      errors,
      modalTitle,
      formFields,
      hasError,
      getErrors,
      handleFileChange,
      handleSubmit,
      handleBackdropClick
    }
  }
}
</script>
