.sec-title2,
.sec-title {
  font-size: 60px;
  margin-top: -0.25em;
  margin-bottom: 10px;
  text-transform: capitalize;
}

.sec-title2 {
  font-size: 55px;
}

.sec-subtitle {
  display: block;
  text-transform: uppercase;
  font-size: 16px;
  letter-spacing: 0.3em;
  color: $theme-color;
  font-weight: 700;
  font-family: $title-font;
  line-height: 1;
  margin-top: -0.1em;
  margin-bottom: 25px;
}

.sec-subtitle2 {
  font-family: $title-font;
  font-size: 20px;
  font-weight: 600;
  text-transform: uppercase;
  color: $title-color;
  position: relative;
  margin-top: -0.25em;
  margin-bottom: 0;
  padding-bottom: 10px;
  display: block;

}

.title-divider2,
.title-divider1 {
  height: 3px;
  width: 24px;
  background-color: $theme-color2;
  border-radius: 9999px;
  display: inline-block;
  margin-bottom: 17px;
}

.title-divider2 {
  height: 4px;
  width: 42px;
}

.sec-text {
  font-size: 18px;
}

.sec-bubble {
  width: 36px;
  height: 36px;
  position: relative;
  display: inline-block;
  margin-bottom: 20px;
  
  .bubble {
    position: absolute;
    display: inline-block;
    width: 20px;
    height: 20px;
    background-color: $theme-color2;
    border-radius: 50%;
    
    &:nth-child(1) {
      top: 0;
      left: 50%;
      z-index: 3;
      margin-left: -10px;
    }
    
    &:nth-child(2) {
      right: 0;
      bottom: 0;
      z-index: 1;
      background-color: $theme-color;
    }
    
    &:nth-child(3) {
      left: 0;
      bottom: 0;
      z-index: 2;
      background-color: $secondary-color;
    }
  }
}

.title-area {
  margin-bottom: calc(var(--section-title-space) - 22px);
}

.sec-btns {
  margin-bottom: calc(var(--section-title-space) - 9px);

  .icon-btn {
    margin-right: 5px;

    &:last-child {
      margin-right: 0;
    }
  }
}

.sec-bottom-btns {
  margin-top: 40px;
  margin-bottom: 30px;
  text-align: center;
}

.vs-carousel {
  +.sec-bottom-btns {
    margin-top: 10px;
  }
}

@include ml {
  .sec-title2,
  .sec-title {
    font-size: 48px;
  }
}

@include lg {
  .sec-title2,
  .sec-title {
    font-size: 42px;
  }


  .sec-subtitle {
    letter-spacing: 0.1em;
    margin-bottom: 20px;
  }

  .sec-text {
    font-size: 16px;
  }
}

@include md {
  .sec-title2,
  .sec-title {
    font-size: 36px;
  }
}

@include sm {

  .sec-btns {
    margin-bottom: 30px;
  }
}