{"name": "@your-org/vue-data-table", "version": "1.0.0", "description": "A comprehensive Vue 3 data table component with advanced features including filtering, pagination, CRUD operations, export, and print functionality", "main": "index.js", "module": "index.js", "types": "types/index.d.ts", "files": ["components", "composables", "utils", "styles", "types", "index.js", "README.md"], "keywords": ["vue", "vue3", "datatable", "table", "pagination", "filter", "export", "crud", "typescript", "tailwindcss"], "author": "Your Name", "license": "MIT", "peerDependencies": {"vue": "^3.0.0", "axios": "^1.0.0"}, "dependencies": {"html2canvas": "^1.4.1", "jspdf": "^3.0.1"}, "optionalDependencies": {"tailwindcss": "^3.0.0"}, "repository": {"type": "git", "url": "your-repo-url"}, "bugs": {"url": "your-repo-url/issues"}, "homepage": "your-repo-url#readme", "scripts": {"build": "echo 'Build script for packaging'", "test": "echo 'Test script'"}}