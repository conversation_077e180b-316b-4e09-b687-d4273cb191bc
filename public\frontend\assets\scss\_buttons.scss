//>>>>> Component Button Here <<<<<//
.theme-btn {
	display: inline-block;
	vertical-align: middle;
	border: none;
	outline: none !important;
	color: $white;
	font-size: 16px;
	font-weight: 400;
	padding: 18px 30px;
	@include transition;
	letter-spacing: 0;
	border-radius: 5px;
	position: relative;
	overflow: hidden;
	text-align: center;
	line-height: 1;
	z-index: 9;
	text-transform: capitalize;

	i {
		margin-left: 5px;
	}

	&::before {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		content: "";
		width: 20px;
		height: 20px;
		background-color: $p4-clr;
		z-index: -1;
		transition: all 0.6s;
		opacity: 0;
		visibility: hidden;
	}

	&:hover {
		.ani-arrow {
			transform: translateX(4px);
			display: inline-block;
		}
		&::before {
			width: 100%;
			height: 100%;
			opacity: 1;
			visibility: visible;
		}
	}

	&.bg-white {
		background-color: $white;
		color: $header-color;

		&:hover {
			color: $white;
		}
	}

	&.transparent {
		background-color: transparent;
		border: 1px solid $theme-color-2;
		color: $theme-color-2;
		padding: 20px 40px;

		&:hover {
			color: $white;
		}

		@include breakpoint(max-md) {
			padding: 18px 32px;
		}

		@include breakpoint(max-sm) {
			padding: 16px 30px;
			font-size: 14px;
		}
	}

	&.transparent-2 {
		background-color: transparent;
		border: 1px solid $p2-clr;
		color: $p2-clr;
		padding: 20px 40px;

		&:hover {
			color: $white;
			border: 1px solid transparent;
		}

		@include breakpoint(max-md) {
			padding: 18px 32px;
		}

		@include breakpoint(max-sm) {
			padding: 16px 30px;
			font-size: 14px;
		}
	}

	@include breakpoint(max-xl) {
		padding: 14px 24px;
	}

	@include breakpoint(max-sm) {
		padding: 14px 24px;
		font-size: 14px;
	}
}
.p5-btn{
	&:hover{
		color: $white !important;
		i{
			color: $white;
		}
		span{
			color: $white !important;
		}
	}
}

.theme-btn-2 {
	font-size: 16px;
	display: inline-block;
	font-weight: 600;
	color: $text-color;
	text-transform: capitalize;

	i {
		margin-left: 10px;
	}

	&:hover {
		color: $p2-clr;
	}
}
//Red more Button
.readmore {
	color: $black;
	transition: all 0.4s;
	.arrows {
		color: $black;
		transition: all 0.4s;
	}
	&:hover{
		.arrows{
			margin-left: 5px;
		}
	}
}
//>>>>> Component Button End <<<<<//
