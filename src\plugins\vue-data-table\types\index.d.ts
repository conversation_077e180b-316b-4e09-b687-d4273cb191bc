import { Component, Plugin } from 'vue'

export interface TableColumn {
  key: string
  label: string
  sortable?: boolean
  filterable?: boolean
  filterType?: 'text' | 'select' | 'date' | 'number' | 'boolean'
  filterOptions?: Array<{ label: string; value: any }>
  width?: string | number
  minWidth?: string | number
  align?: 'left' | 'center' | 'right'
  fixed?: 'left' | 'right'
  render?: (value: any, row: any, index: number) => string
  class?: string
  headerClass?: string
  visible?: boolean
}

export interface TablePagination {
  page: number
  pageSize: number
  total: number
  pageSizes?: number[]
}

export interface TableSort {
  key: string
  order: 'asc' | 'desc'
}

export interface TableFilter {
  key: string
  value: any
  operator?: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'like' | 'in' | 'between'
}

export interface CrudConfig {
  create?: boolean | CrudOperation
  read?: boolean | CrudOperation
  update?: boolean | CrudOperation
  delete?: boolean | CrudOperation
}

export interface CrudOperation {
  enabled: boolean
  endpoint?: string
  method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE'
  fields?: FormField[]
  validation?: ValidationRules
  permissions?: string[]
}

export interface FormField {
  key: string
  label: string
  type: 'text' | 'email' | 'password' | 'number' | 'select' | 'textarea' | 'checkbox' | 'radio' | 'date' | 'file'
  required?: boolean
  options?: Array<{ label: string; value: any }>
  validation?: string[]
  placeholder?: string
  disabled?: boolean
  readonly?: boolean
}

export interface ValidationRules {
  [key: string]: string[]
}

export interface ExportConfig {
  csv?: boolean
  excel?: boolean
  json?: boolean
  pdf?: boolean
  filename?: string
}

export interface PrintConfig {
  enabled?: boolean
  title?: string
  orientation?: 'portrait' | 'landscape'
  pageSize?: 'A4' | 'A3' | 'Letter'
}

export interface DataTableProps {
  items: any[]
  columns: TableColumn[]
  title?: string
  loading?: boolean
  serverSide?: boolean
  pagination?: TablePagination
  sort?: TableSort
  filters?: TableFilter[]
  selectable?: boolean
  selected?: any[]
  crud?: CrudConfig
  export?: ExportConfig
  print?: PrintConfig
  searchable?: boolean
  searchPlaceholder?: string
  noDataText?: string
  loadingText?: string
  theme?: 'default' | 'dark' | 'minimal'
  size?: 'sm' | 'md' | 'lg'
  striped?: boolean
  bordered?: boolean
  hover?: boolean
  stickyHeader?: boolean
  virtualScroll?: boolean
  rowHeight?: number
  maxHeight?: string | number
}

export interface DataTableEmits {
  'update:selected': (selected: any[]) => void
  'update:pagination': (pagination: TablePagination) => void
  'update:sort': (sort: TableSort) => void
  'update:filters': (filters: TableFilter[]) => void
  'row-click': (row: any, index: number) => void
  'row-dblclick': (row: any, index: number) => void
  'cell-click': (value: any, row: any, column: TableColumn, index: number) => void
  'search': (query: string) => void
  'export': (type: string, data: any[]) => void
  'crud-create': (data: any) => void
  'crud-update': (data: any, original: any) => void
  'crud-delete': (data: any) => void
  'refresh': () => void
}

declare const DataTable: Component<DataTableProps, {}, {}, {}, {}, {}, {}, DataTableEmits>
declare const ApiDataTable: Component
declare const TableHeader: Component
declare const TableSearch: Component
declare const TablePagination: Component
declare const TableExport: Component
declare const CrudModal: Component
declare const AdvancedFilter: Component

declare const VueDataTable: Plugin

export {
  DataTable,
  ApiDataTable,
  TableHeader,
  TableSearch,
  TablePagination,
  TableExport,
  CrudModal,
  AdvancedFilter,
  VueDataTable as default
}

export * from './composables'
export * from './utils'
