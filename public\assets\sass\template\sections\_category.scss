.category-style1 {
    position: relative;
    z-index: 1;
    padding: 40px 35px 40px 35px;
    border-radius: 30px;
    margin: 10px 0 40px 0;

    .category-bg3,
    .category-bg2,
    .category-bg1 {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        border-radius: 30px;
        opacity: 1;
        visibility: visible;
        transform-origin: top center;
        transition: all ease 0.4s;
        box-shadow: 0 10px 4px rgba(#78338A, 0.30);
    }

    .category-bg1 {
        background-color: $white-color;
        z-index: -1;
    }

    .category-bg2 {
        background-color: $secondary-color;
        transform: scale(0.9) translateY(-10px);
        z-index: -2;
    }

    .category-bg3 {
        background-color: $theme-color;
        transform: scale(0.7) translateY(-10px);
        z-index: -3;
        box-shadow: none;
    }

    &:hover {
        .category-bg1 {
            opacity: 0;
            visibility: hidden;
            transform: scale(1.08);
        }

        .category-bg2 {
            transform: scale(1.001) translateY(0);
        }

        .category-bg3 {
            transform: scale(0.9) translateY(-10px);
        }
    }

    .category-grade {
        width: 90px;
        height: 90px;
        display: inline-flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background-color: $theme-color;
        border-radius: 50%;
        line-height: 1;
        font-weight: 600;
        text-transform: uppercase;
        color: $white-color;
        font-family: $title-font;
        font-size: 14px;
        margin-bottom: 22px;
        box-shadow: inset 5px 0 0 1px rgba(#000, 0.18);
    }


    .grade-name {
        display: block;
        margin-top: 0px;
        font-size: 40px;
    }

    .category-name {
        margin: 0 0 10px 0;
    }

    .category-label {
        margin: 0;
        line-height: 1;
        font-weight: 500;
        font-family: $title-font;
        transition: all ease 0.4s;
    }

    &:hover {

        .category-label,
        .category-name a {
            color: $white-color;
        }
    }
}

.category-style2 {
    position: relative;
    margin-bottom: 30px;

    .category-img {
        width: 270px;
        height: 270px;
        border-radius: 50%;
        overflow: hidden;

        img {
            width: 100%;
            transition: all ease 0.4s;
            transform: scale(1.001);
        }
    }

    .category-title {
        margin-bottom: 5px;
        margin-top: -0.2em;
    }

    .category-content {
        background-color: $white-color;
        border: 2px solid $secondary-color;
        position: absolute;
        right: 0;
        bottom: 30px;
        width: 270px;
        max-width: 100%;
        border-radius: 9999px;
        padding: 23px 48px 25px 48px;
        transition: all ease 0.4s;
    }

    .category-text {
        margin: 0;
        font-weight: 500;
        color: $title-color;
        font-size: 18px;
        line-height: 1;
        text-transform: uppercase;
    }

    &:hover {
        .category-img {
            img {
                transform: scale(1.15);
            }
        }

        .category-content {
            border-color: $theme-color;
        }
    }
}

@include ml {
    .category-style2 {
        .category-title {
            font-size: 28px;
        }

        .category-text {
            font-size: 16px;
        }

        .category-content {
            bottom: 20px;
            width: 220px;
            padding: 23px 30px 25px 30px;
            transition: all ease 0.4s;
        }

        .category-text {
            font-size: 16px;
        }
    }
}


@include md {
    .category-style1 {
        padding: 30px 25px 30px 25px;
    }
}


@include sm {
    .category-style1 {
        padding: 30px 15px 30px 15px;
        text-align: center;
    }

    .category-style2 {
        text-align: center;

        .category-img {
            width: 100%;
            height: auto;
        }
        
        .category-content {
            position: relative;
            right: 0;
            bottom: 0;
            width: 100%;
            margin: -30px auto 0 auto;
            padding: 15px 10px 15px 10px;
        }

        .category-title {
            font-size: 18px;
        }

        .category-text {
            font-size: 12px;
        }
    }
}