/*
Theme Name: Ascent
Author: <PERSON>gli
Author URI: https://themeforest.net/user/mugli
Description: Ascent -Kids & Kindergarden School HTML Template<
Version: 1.0.0
*/
/*CSS Table Of Content Ends Here*/
@import url("https://fonts.googleapis.com/css2?family=Bubblegum+Sans&family=Jost:ital,wght@0,100..900;1,100..900&family=Nunito:ital,wght@0,200..1000;1,200..1000&display=swap");
:root {
  --body: #fff;
  --black: #000;
  --white: #fff;
  --p1-clr: #0A6375;
  --p2-clr: #F7941E;
  --p3-clr: #ED145B;
  --p4-clr: #73BE48;
  --p5-clr: #1CBBB4;
  --cmnbg: #FFF0E5;
  --theme: #F39F5F;
  --theme2: #70A6B1;
  --header: #385469;
  --text: #5C707E;
  --pra: #686868;
  --text-2: #ffffffcc;
  --border: #E5E5E5;
  --border2: #242449;
  --border3: #5262FF;
  --bg: #F4EEE5;
  --bg2: #EFF5F6;
  --bg3: #70A6B1;
  --box-shadow: 0px 4px 25px rgba(0, 0, 0, 0.06);
}

.theme-btn {
  display: inline-block;
  vertical-align: middle;
  border: none;
  outline: none !important;
  color: var(--white);
  font-size: 16px;
  font-weight: 400;
  padding: 18px 30px;
  transition: all 0.4s ease-in-out;
  letter-spacing: 0;
  border-radius: 5px;
  position: relative;
  overflow: hidden;
  text-align: center;
  line-height: 1;
  z-index: 9;
  text-transform: capitalize;
}
.theme-btn i {
  margin-left: 5px;
}
.theme-btn::before {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  content: "";
  width: 20px;
  height: 20px;
  background-color: var(--p4-clr);
  z-index: -1;
  transition: all 0.6s;
  opacity: 0;
  visibility: hidden;
}
.theme-btn:hover .ani-arrow {
  transform: translateX(4px);
  display: inline-block;
}
.theme-btn:hover::before {
  width: 100%;
  height: 100%;
  opacity: 1;
  visibility: visible;
}
.theme-btn.bg-white {
  background-color: var(--white);
  color: var(--header);
}
.theme-btn.bg-white:hover {
  color: var(--white);
}
.theme-btn.transparent {
  background-color: transparent;
  border: 1px solid var(--theme2);
  color: var(--theme2);
  padding: 20px 40px;
}
.theme-btn.transparent:hover {
  color: var(--white);
}
@media (max-width: 767px) {
  .theme-btn.transparent {
    padding: 18px 32px;
  }
}
@media (max-width: 575px) {
  .theme-btn.transparent {
    padding: 16px 30px;
    font-size: 14px;
  }
}
.theme-btn.transparent-2 {
  background-color: transparent;
  border: 1px solid var(--p2-clr);
  color: var(--p2-clr);
  padding: 20px 40px;
}
.theme-btn.transparent-2:hover {
  color: var(--white);
  border: 1px solid transparent;
}
@media (max-width: 767px) {
  .theme-btn.transparent-2 {
    padding: 18px 32px;
  }
}
@media (max-width: 575px) {
  .theme-btn.transparent-2 {
    padding: 16px 30px;
    font-size: 14px;
  }
}
@media (max-width: 1199px) {
  .theme-btn {
    padding: 14px 24px;
  }
}
@media (max-width: 575px) {
  .theme-btn {
    padding: 14px 24px;
    font-size: 14px;
  }
}

.p5-btn:hover {
  color: var(--white) !important;
}
.p5-btn:hover i {
  color: var(--white);
}
.p5-btn:hover span {
  color: var(--white) !important;
}

.theme-btn-2 {
  font-size: 16px;
  display: inline-block;
  font-weight: 600;
  color: var(--text);
  text-transform: capitalize;
}
.theme-btn-2 i {
  margin-left: 10px;
}
.theme-btn-2:hover {
  color: var(--p2-clr);
}

.readmore {
  color: var(--black);
  transition: all 0.4s;
}
.readmore .arrows {
  color: var(--black);
  transition: all 0.4s;
}
.readmore:hover .arrows {
  margin-left: 5px;
}

/* --------------------------------------------
    Template Default Fonts & Fonts Styles
 ---------------------------------------------- */
.heading-font {
  font-family: "Jost", sans-serif;
}

.sub-font {
  font-family: "Bubblegum Sans", sans-serif;
}

.body-font {
  font-family: "Nunito", sans-serif;
}

body {
  font-family: "Nunito", sans-serif;
  font-size: 16px;
  font-weight: normal;
  line-height: 28px;
  color: var(--text);
  background-color: var(--white);
  padding: 0;
  margin: 0;
  overflow-x: hidden;
}

ul {
  padding: 0;
  margin: 0;
  list-style: none;
}

button {
  border: none;
  background-color: transparent;
  padding: 0;
}

input:focus {
  color: var(--white);
  outline: none;
}

input {
  color: var(--white);
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Jost", sans-serif;
  margin: 0px;
  padding: 0;
  color: var(--header);
  text-transform: capitalize;
  transition: all 0.4s ease-in-out;
}

h1 {
  font-size: 70px;
  font-weight: 700;
  line-height: 112%;
}

h2 {
  font-size: 50px;
  line-height: 116%;
  font-weight: 700;
}
@media (max-width: 1199px) {
  h2 {
    font-size: 42px;
  }
}
@media (max-width: 767px) {
  h2 {
    font-size: 36px;
  }
}
@media (max-width: 575px) {
  h2 {
    font-size: 28px;
  }
}

h3 {
  font-size: 32px;
  font-weight: 700;
  line-height: 130%;
}
@media (max-width: 1399px) {
  h3 {
    font-size: 28px;
  }
}
@media (max-width: 1199px) {
  h3 {
    font-size: 26px;
  }
}
@media (max-width: 575px) {
  h3 {
    font-size: 22px;
  }
}

h4 {
  font-size: 24px;
  font-weight: 700;
  line-height: 130%;
}
@media (max-width: 1399px) {
  h4 {
    font-size: 22px;
  }
}
@media (max-width: 1199px) {
  h4 {
    font-size: 20px;
  }
}

h5 {
  font-size: 18px;
  font-weight: 700;
}

h6 {
  font-size: 16px;
  font-weight: 600;
  line-height: 145%;
}

a {
  text-decoration: none;
  outline: none !important;
  cursor: pointer;
  color: var(--header);
  transition: all 0.4s ease-in-out;
}

p {
  margin: 0px;
  transition: all 0.4s ease-in-out;
  font-family: "Nunito", sans-serif;
  font-size: 16px;
  line-height: 25.6px;
}

span {
  font-size: 16px;
  line-height: 25.6px;
  margin: 0px;
  transition: all 0.4s ease-in-out;
}

@media (max-width: 1199px) {
  .container {
    max-width: 1340px;
    margin: 0 auto;
  }
}

.gra-border {
  border: 1px solid #f2f2f2;
}

.gra-border2 {
  border: 2px solid #f2f2f2;
}

.p1-clr {
  color: var(--p1-clr);
}

.p1-bg {
  background: var(--p1-clr);
}

.p2-clr {
  color: var(--p2-clr);
}

.p2-bg {
  background: var(--p2-clr);
}

.p3-clr {
  color: var(--p3-clr);
}

.p3-bg {
  background: var(--p3-clr);
}

.p4-clr {
  color: var(--p4-clr);
}

.p4-bg {
  background: var(--p4-clr);
}

.p5-clr {
  color: var(--p5-clr);
}

.p5-border {
  border: 1px solid var(--p5-clr);
}

.p5-bg {
  background: var(--p5-clr);
}

.cmn-bg {
  background: var(--cmnbg);
}

.d-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.trns {
  transition: all 0.4s;
}

.black {
  color: var(--black);
}

.white {
  color: var(--white);
}

.white-bg {
  background: var(--white);
}

.theme {
  color: var(--theme);
}

.theme2 {
  color: var(--theme2);
}

.header {
  color: var(--header);
}

.pra {
  color: var(--text);
}

.text {
  color: var(--text);
}

.round10 {
  border-radius: 10px;
}

.round100 {
  border-radius: 100px;
}

.mb-60 {
  margin-bottom: 60px;
}

.mb-50 {
  margin-bottom: 50px;
}

.mb-40 {
  margin-bottom: 40px;
}

.mb-30 {
  margin-bottom: 30px;
}

.mb-24 {
  margin-bottom: 24px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-60 {
  margin-top: 60px;
}

.mt-50 {
  margin-top: 50px;
}

.mt-40 {
  margin-top: 40px;
}

.mt-30 {
  margin-top: 30px;
}

.mt-24 {
  margin-top: 24px;
}

.mt-20 {
  margin-top: 20px;
}

.pb-60 {
  padding-bottom: 60px;
}

.pb-50 {
  padding-bottom: 50px;
}

.pb-40 {
  padding-bottom: 40px;
}

.pb-30 {
  padding-bottom: 30px;
}

.pb-24 {
  padding-bottom: 24px;
}

.pb-20 {
  padding-bottom: 20px;
}

.pt-60 {
  padding-top: 60px;
}

.pt-50 {
  padding-top: 50px;
}

.pt-40 {
  padding-top: 40px;
}

.pt-30 {
  padding-top: 30px;
}

.pt-24 {
  padding-top: 24px;
}

.pt-20 {
  padding-top: 20px;
}

@media (max-width: 1399px) {
  .mb-60 {
    margin-bottom: 50px;
  }
  .mb-50 {
    margin-bottom: 40px;
  }
  .mb-40 {
    margin-bottom: 30px;
  }
  .mb-30 {
    margin-bottom: 24px;
  }
  .mb-24 {
    margin-bottom: 20px;
  }
  .mb-20 {
    margin-bottom: 17px;
  }
  .mt-60 {
    margin-top: 50px;
  }
  .mt-50 {
    margin-top: 40px;
  }
  .mt-40 {
    margin-top: 30px;
  }
  .mt-30 {
    margin-top: 24px;
  }
  .mt-24 {
    margin-top: 20px;
  }
  .mt-20 {
    margin-top: 17px;
  }
  .pb-60 {
    padding-bottom: 50px;
  }
  .pb-50 {
    padding-bottom: 40px;
  }
  .pb-40 {
    padding-bottom: 30px;
  }
  .pb-30 {
    padding-bottom: 24px;
  }
  .pb-24 {
    padding-bottom: 20px;
  }
  .pb-20 {
    padding-bottom: 17px;
  }
  .pt-60 {
    padding-top: 50px;
  }
  .pt-50 {
    padding-top: 40px;
  }
  .pt-40 {
    padding-top: 30px;
  }
  .pt-30 {
    padding-top: 24px;
  }
  .pt-24 {
    padding-top: 20px;
  }
  .pt-20 {
    padding-top: 17px;
  }
}
@media (max-width: 991px) {
  .mb-60 {
    margin-bottom: 40px;
  }
  .mb-50 {
    margin-bottom: 30px;
  }
  .mb-40 {
    margin-bottom: 24px;
  }
  .mb-30 {
    margin-bottom: 20px;
  }
  .mb-24 {
    margin-bottom: 16px;
  }
  .mb-20 {
    margin-bottom: 15px;
  }
  .mt-60 {
    margin-top: 40px;
  }
  .mt-50 {
    margin-top: 30px;
  }
  .mt-40 {
    margin-top: 24px;
  }
  .mt-30 {
    margin-top: 20px;
  }
  .mt-24 {
    margin-top: 16px;
  }
  .mt-20 {
    margin-top: 15px;
  }
  .pb-60 {
    padding-bottom: 40px;
  }
  .pb-50 {
    padding-bottom: 30px;
  }
  .pb-40 {
    padding-bottom: 24px;
  }
  .pb-30 {
    padding-bottom: 20px;
  }
  .pb-24 {
    padding-top: 16px;
  }
  .pb-20 {
    padding-bottom: 15px;
  }
  .pt-60 {
    padding-top: 40px;
  }
  .pt-50 {
    padding-top: 30px;
  }
  .pt-40 {
    padding-top: 24px;
  }
  .pt-30 {
    padding-top: 20px;
  }
  .pt-24 {
    padding-top: 16px;
  }
  .pt-20 {
    padding-top: 15px;
  }
}
.iconbg-v2 {
  background: var(--p2-clr);
}

.iconbg-v3 {
  background: var(--p3-clr);
}

.iconbg-v4 {
  background: var(--p4-clr);
}

.iconbg-v5 {
  background: var(--p5-clr);
}

.about-one-thumbs {
  display: flex;
  align-items: end;
  gap: 24px;
}
.about-one-thumbs .thumbs .customer-satisfaction {
  background: var(--p2-clr);
  border-radius: 12px;
  padding: 16px 22px;
  display: inline-flex;
  align-items: center;
  position: absolute;
  left: -45px;
  bottom: -40px;
  gap: 11px;
}
.about-one-thumbs .thumbs .customer-satisfaction .icon {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: var(--white);
}
.about-one-thumbs .thumbs .about-light1 {
  position: absolute;
  top: -40px;
  left: -50px;
  animation: zin1 2s linear infinite;
}
.about-one-thumbs .thumbs .about-arrows {
  position: absolute;
  right: -70px;
  top: -20px;
}
.about-one-thumbs .about-one-grow .academy-box {
  background: var(--cmnbg);
  border-radius: 10px;
  padding: 21px 21px;
}
.about-one-thumbs .about-one-grow .academy-box img {
  margin-bottom: 10px;
}
.about-one-thumbs .about-one-grow .academy-box h4 {
  margin-bottom: 6px;
  display: block;
}
.about-one-thumbs .about-one-grow .academy-box2 {
  background: var(--white);
  border-radius: 10px;
  padding: 21px 21px;
  filter: drop-shadow(0px 4.8px 24.4px rgba(19, 16, 34, 0.1));
}
.about-one-thumbs .about-one-grow .academy-box2 h3 {
  color: var(--p5-clr);
  margin-bottom: 3px;
}
.about-one-thumbs .about-one-grow .academy-box2 h3 span {
  font-size: 32px;
  color: var(--p5-clr);
}
@media (max-width: 1199px) {
  .about-one-thumbs {
    gap: 19px;
    display: grid;
  }
  .about-one-thumbs .thumbs {
    max-width: 320px;
  }
  .about-one-thumbs .thumbs .main-img {
    width: 100%;
  }
  .about-one-thumbs .thumbs .customer-satisfaction {
    left: initial;
    right: -140px;
    bottom: 40%;
  }
}
@media (max-width: 991px) {
  .about-one-thumbs .thumbs .customer-satisfaction {
    left: initial;
    right: -40px;
    bottom: 40%;
  }
}
@media (max-width: 767px) {
  .about-one-thumbs .thumbs .customer-satisfaction {
    left: initial;
    right: 10px;
    bottom: 1%;
  }
}
@media (max-width: 575px) {
  .about-one-thumbs .thumbs {
    width: 100% !important;
    max-width: 100%;
  }
  .about-one-thumbs .thumbs .main-img {
    width: 100%;
  }
}

.program-item {
  border-radius: 10px;
  padding: 40px 40px 40px 0;
  display: flex;
  align-items: center;
  background: var(--white);
  margin-left: 40px;
  gap: 30px;
}
.program-item .icons {
  min-width: 88px;
  min-height: 88px;
  margin-left: -40px;
  background: var(--white);
  transition: all 0.6s;
}
.program-item .icons img {
  transition: all 0.6s;
}
.program-item .content h4 {
  margin-bottom: 18px;
}
.program-item .content p {
  margin-bottom: 28px;
}
.program-item .content .readmore:hover {
  color: var(--p4-clr);
}
.program-item .content .readmore:hover i {
  color: var(--p4-clr);
}
.program-item:hover {
  box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
}
.program-item:hover .content h4 a {
  color: var(--p4-clr);
}
.program-item:hover .icons {
  background: var(--p4-clr);
}
.program-item:hover .icons img {
  filter: brightness(25);
}
@media (max-width: 991px) {
  .program-item {
    padding: 20px 20px 20px 0;
    gap: 14px;
  }
  .program-item .content h4 {
    margin-bottom: 10px;
  }
  .program-item .content p {
    margin-bottom: 12px;
  }
}
@media (max-width: 991px) {
  .program-item {
    padding: 20px 14px 20px 14px;
    gap: 14px;
    display: grid;
    margin-left: 0;
  }
  .program-item .icons {
    width: 88px;
    margin-left: 0;
  }
  .program-item .content h4 {
    margin-bottom: 10px;
  }
  .program-item .content p {
    margin-bottom: 12px;
  }
}

.program-sectionv1 .program-child {
  position: absolute;
  top: 0;
  right: 40px;
  z-index: -1;
  animation: lf 2s linear infinite;
}
@media (max-width: 991px) {
  .program-sectionv1 .program-child {
    display: none;
  }
}

.protfolio-sectionv1 .protfolio-itemv1 {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.protfolio-sectionv1 .protfolio-itemv1 img {
  width: 100%;
  height: 100%;
}
.protfolio-sectionv1 .protfolio-itemv1 .protfolio-content-box {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  z-index: 1;
  left: 50%;
  top: 50%;
  width: 92%;
  height: 88%;
  background: rgba(247, 148, 30, 0.8);
  border-radius: 10px;
  transition: all 0.4s;
  visibility: hidden;
  opacity: 0;
}
.protfolio-sectionv1 .protfolio-itemv1 .protfolio-content-box .box {
  max-width: 250px;
}
.protfolio-sectionv1 .protfolio-itemv1 .protfolio-content-box h4 {
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 5px;
}
.protfolio-sectionv1 .protfolio-itemv1 .protfolio-content-box p {
  font-size: 14px;
}
.protfolio-sectionv1 .protfolio-itemv1 .protfolio-content-box .prot-arrow {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--white);
  position: absolute;
  bottom: -10px;
  transform: translateX(-50%);
  left: 50%;
}
.protfolio-sectionv1 .protfolio-itemv1 .protfolio-content-box .prot-arrow i {
  font-size: 16px;
  color: var(--p3-clr);
}
.protfolio-sectionv1 .protfolio-itemv1:hover .protfolio-content-box {
  transform: translate(-50%, -50%);
  visibility: visible;
  opacity: 1;
}

.protfolio-tabs .tablinks {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 30px;
}
.protfolio-tabs .tablinks .nav-links .tablink {
  padding: 10px 20px;
  border-radius: 10px;
  border: 1px solid #f2f2f2;
  color: var(--text);
  transition: all 0.4s;
}
.protfolio-tabs .tablinks .nav-links.active .tablink {
  background: var(--p2-clr);
  color: var(--white);
}
@media (max-width: 1399px) {
  .protfolio-tabs .tablinks {
    gap: 10px 20px;
    flex-wrap: wrap;
  }
  .protfolio-tabs .tablinks .nav-links .tablink {
    padding: 8px 20px;
  }
}

.knowledge-section {
  z-index: 1;
}
.knowledge-section .knowledge-shapeleft {
  position: absolute;
  left: 70px;
  bottom: 0;
}
.knowledge-section .knowledge-animal {
  position: absolute;
  top: 100px;
  right: 60px;
  animation: lf 2s linear infinite;
}
.knowledge-section .knowledge-box-wrap {
  display: flex;
  gap: 24px;
  align-items: center;
}
.knowledge-section .knowledge-box-wrap .knowledge-inner .knowledge-big-item {
  width: 300px;
  height: 300px;
  text-align: center;
  font-weight: 600;
  border-radius: 10px;
}
.knowledge-section .knowledge-box-wrap .knowledge-inner .knowledge-big-item span,
.knowledge-section .knowledge-box-wrap .knowledge-inner .knowledge-big-item h2 {
  font-size: 50px;
  line-height: 58px;
}
.knowledge-section .knowledge-box-wrap .knowledge-inner .knowledge-item {
  border-radius: 10px;
  width: 190px;
  height: 190px;
  text-align: center;
  font-weight: 600;
}
.knowledge-section .knowledge-box-wrap .knowledge-inner .knowledge-item span,
.knowledge-section .knowledge-box-wrap .knowledge-inner .knowledge-item h3 {
  font-size: 32px;
  line-height: 36px;
}
.knowledge-section .knowledge-box-wrap .knowledge-inner .knowledge-left {
  margin-left: auto;
}
@media (max-width: 1600px) {
  .knowledge-section .knowledge-shapeleft {
    left: 30px;
    bottom: 0;
    height: 400px;
    z-index: -1;
  }
  .knowledge-section .knowledge-animal {
    top: 100px;
    right: 30px;
    width: 180px;
  }
}
@media (max-width: 1399px) {
  .knowledge-section .knowledge-box-wrap {
    display: flex;
    gap: 16px;
    align-items: center;
  }
  .knowledge-section .knowledge-box-wrap .knowledge-inner .knowledge-big-item {
    width: 240px;
    height: 240px;
    font-weight: 500;
  }
  .knowledge-section .knowledge-box-wrap .knowledge-inner .knowledge-big-item span,
  .knowledge-section .knowledge-box-wrap .knowledge-inner .knowledge-big-item h2 {
    font-size: 36px;
    line-height: 40px;
  }
  .knowledge-section .knowledge-box-wrap .knowledge-inner .knowledge-item {
    width: 140px;
    height: 140px;
    font-weight: 500;
  }
  .knowledge-section .knowledge-box-wrap .knowledge-inner .knowledge-item span,
  .knowledge-section .knowledge-box-wrap .knowledge-inner .knowledge-item h3 {
    font-size: 19px;
    line-height: 22px;
  }
  .knowledge-section .knowledge-box-wrap .knowledge-inner .knowledge-left {
    margin-left: auto;
  }
}
@media (max-width: 1199px) {
  .knowledge-section .knowledge-box-wrap {
    display: flex;
    gap: 10px;
    align-items: center;
  }
  .knowledge-section .knowledge-box-wrap .knowledge-inner .knowledge-big-item {
    width: 200px;
    height: 200px;
    font-weight: 500;
  }
  .knowledge-section .knowledge-box-wrap .knowledge-inner .knowledge-big-item span,
  .knowledge-section .knowledge-box-wrap .knowledge-inner .knowledge-big-item h2 {
    font-size: 36px;
    line-height: 40px;
  }
  .knowledge-section .knowledge-box-wrap .knowledge-inner .knowledge-item {
    width: 110px;
    height: 110px;
    font-weight: 500;
  }
  .knowledge-section .knowledge-box-wrap .knowledge-inner .knowledge-item span,
  .knowledge-section .knowledge-box-wrap .knowledge-inner .knowledge-item h3 {
    font-size: 19px;
    line-height: 22px;
  }
  .knowledge-section .knowledge-box-wrap .knowledge-inner .knowledge-left {
    margin-left: auto;
  }
  .knowledge-section .knowledge-animal {
    top: 30px;
    right: 30px;
    width: 120px;
  }
}
@media (max-width: 767px) {
  .knowledge-section .knowledge-box-wrap {
    width: 100%;
    display: flex;
    justify-content: center;
    gap: 10px;
    align-items: center;
  }
  .knowledge-section .knowledge-box-wrap .knowledge-inner .knowledge-big-item {
    width: 200px;
    height: 200px;
    font-weight: 500;
  }
  .knowledge-section .knowledge-box-wrap .knowledge-inner .knowledge-big-item span,
  .knowledge-section .knowledge-box-wrap .knowledge-inner .knowledge-big-item h2 {
    font-size: 36px;
    line-height: 40px;
  }
  .knowledge-section .knowledge-box-wrap .knowledge-inner .knowledge-item {
    width: 110px;
    height: 110px;
    font-weight: 500;
  }
  .knowledge-section .knowledge-box-wrap .knowledge-inner .knowledge-item span,
  .knowledge-section .knowledge-box-wrap .knowledge-inner .knowledge-item h3 {
    font-size: 19px;
    line-height: 22px;
  }
  .knowledge-section .knowledge-box-wrap .knowledge-inner .knowledge-left {
    margin-left: auto;
  }
}
@media (max-width: 500px) {
  .knowledge-section .knowledge-box-wrap .knowledge-inner .knowledge-big-item {
    width: 100px;
    height: 100px;
    font-weight: 500;
  }
  .knowledge-section .knowledge-box-wrap .knowledge-inner .knowledge-big-item span,
  .knowledge-section .knowledge-box-wrap .knowledge-inner .knowledge-big-item h2 {
    font-size: 18px;
    line-height: 22px;
  }
  .knowledge-section .knowledge-box-wrap .knowledge-inner .knowledge-item {
    width: 80px;
    height: 80px;
    font-weight: 500;
  }
  .knowledge-section .knowledge-box-wrap .knowledge-inner .knowledge-item span,
  .knowledge-section .knowledge-box-wrap .knowledge-inner .knowledge-item h3 {
    font-size: 16px;
    line-height: 28px;
  }
  .knowledge-section .knowledge-box-wrap .knowledge-inner .knowledge-left {
    margin-left: auto;
  }
}

.knowledge-content {
  max-width: 470px;
}
.knowledge-content .theme-btn {
  border-radius: 100px;
}

.stay-section {
  z-index: 1;
}
.stay-section .stay-element {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: -1;
}
@media (max-width: 575px) {
  .stay-section .stay-thumb {
    max-width: 380px;
    margin: 0 auto;
  }
  .stay-section .stay-thumb img {
    width: 100%;
  }
}

.about-contentv02 .theme-btn {
  padding: 17px 30px;
}
.about-contentv02 .theme-btn::before {
  background: var(--white);
}
.about-contentv02 .theme-btn:hover span {
  color: var(--black);
}
.about-contentv02 .cart-btn {
  border: 2px solid var(--p2-clr);
  padding: 14px 30px;
}
.about-contentv02 .cart-btn::before {
  background: var(--p2-clr);
}
.about-contentv02 .cart-btn:hover span {
  color: var(--white);
}

.about-thumb-innerv2 {
  display: flex;
  gap: 24px;
}
.about-thumb-innerv2 .thumb-left-cont {
  display: grid;
  gap: 24px;
}
.about-thumb-innerv2 .thumb-left-cont .thumb {
  width: 100%;
}
.about-thumb-innerv2 .thumb-left-cont .thumb img {
  width: 100%;
  border-radius: 0 0 0 150px;
}
.about-thumb-innerv2 .thumb-left-cont .thumb-cont {
  background: var(--cmnbg);
  padding: 50px 20px;
  border-radius: 64px 64px 0 0;
  display: flex;
  justify-content: center;
  gap: 40px;
  border-bottom: 5px solid var(--p4-clr);
}
.about-thumb-innerv2 .thumb-left-cont .thumb-cont .aicon {
  width: 60px;
  height: 60px;
  object-fit: contain;
}
.about-thumb-innerv2 .thumb-left-cont .thumb-cont .contents h4 {
  font-size: 32px;
  color: var(--black);
}
.about-thumb-innerv2 .thumb-left-cont .thumb-cont .contents h4 span {
  font-size: 32px;
  color: var(--black);
}
.about-thumb-innerv2 .right-thumb {
  width: 300px;
  height: 496px;
}
.about-thumb-innerv2 .right-thumb img {
  width: 100%;
  height: 100%;
  border-radius: 150px 0px;
}
@media (max-width: 1399px) {
  .about-thumb-innerv2 .thumb-left-cont {
    width: 280px;
  }
  .about-thumb-innerv2 .right-thumb {
    width: 300px;
    height: 496px;
  }
}
@media (max-width: 1199px) {
  .about-thumb-innerv2 .thumb-left-cont {
    width: initial;
  }
  .about-thumb-innerv2 .right-thumb {
    width: initial;
    height: initial;
  }
}
@media (max-width: 575px) {
  .about-thumb-innerv2 .thumb-left-cont {
    width: 100%;
  }
}

.singletab .tabcontents {
  position: relative;
}

.singletab .tabcontents .tabitem {
  transform: translateY(100px);
  position: absolute;
  z-index: -1;
  top: 0;
  width: 100%;
  opacity: 0;
  transition: 0.8s all;
}

.singletab .tabcontents .tabitem.active {
  position: initial;
  z-index: 1;
  opacity: 1;
  transform: translateY(0);
}

.social-wrapper a {
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 5px;
}

.inspair-section {
  background: url(../../assets/img/ainspair/inspair-bg.png) no-repeat center center;
  background-size: cover;
}
.inspair-section .inspainr-thumb-box {
  position: relative;
}
.inspair-section .inspair-thumb {
  animation: lf 2s linear infinite;
  width: 100%;
}
.inspair-section .inspair-thumb img {
  width: 100%;
}
.inspair-section .inspair-content {
  padding: 190px 0;
}
.inspair-section .inspair-content form {
  display: flex;
  align-items: center;
  border-radius: 10px;
  padding: 10px;
  border: 1px solid var(--black);
  background: var(--white);
}
.inspair-section .inspair-content form input {
  padding: 16px 2px 16px 22px;
  width: 100%;
  border: unset;
  color: var(--black);
  background: transparent;
  font-weight: 500;
  font-size: 16px;
}
.inspair-section .inspair-content form ::placeholder {
  color: var(--text);
}
.inspair-section .inspair-content form button {
  border-radius: 10px;
  height: 55px;
  min-width: 130px;
  background: var(--p2-clr);
  display: flex;
  align-items: center;
  justify-content: center;
  border: unset;
}
.inspair-section .inspair-content form button i {
  color: var(--white);
  font-size: 16px;
}
@media (max-width: 1399px) {
  .inspair-section .inspair-content {
    padding: 110px 0;
  }
}
@media (max-width: 991px) {
  .inspair-section .inspair-content form {
    padding: 2px 4px;
  }
  .inspair-section .inspair-content form input {
    padding: 10px 2px 10px 16px;
  }
  .inspair-section .inspair-content form button {
    height: 40px;
    min-width: 90px;
    font-size: 14px;
  }
  .inspair-section .inspair-content form button i {
    color: var(--white);
    font-size: 12px;
  }
}
@media (max-width: 575px) {
  .inspair-section .inspair-content {
    padding: 0px 0;
  }
}

.blog-sectionv1 {
  background: linear-gradient(180deg, #fff0e5 0%, rgba(255, 240, 229, 0) 100%);
}
.blog-sectionv1 .news-small-items {
  padding: 10px;
  box-shadow: 0px 4.4px 20px -1px rgba(19, 16, 34, 0.05);
  background: var(--white);
  border-radius: 10px;
  display: flex;
  align-items: center;
  gap: 20px;
}
.blog-sectionv1 .news-small-items .news-thumb img {
  border-radius: 10px;
}
.blog-sectionv1 .news-small-items .news-content ul {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 14px;
}
.blog-sectionv1 .news-small-items .news-content ul li {
  font-size: 14px;
  font-family: "Nunito", sans-serif;
  color: var(--text) !important;
}
.blog-sectionv1 .news-small-items .news-content ul li i {
  color: var(--p5-clr);
  margin-right: 5px;
}
.blog-sectionv1 .news-small-items .news-content h4 {
  margin-bottom: 20px;
}
.blog-sectionv1 .news-small-items .news-content h4 a:hover {
  color: var(--p5-clr);
}
.blog-sectionv1 .news-small-items .news-content .readmore:hover {
  color: var(--p5-clr);
}
.blog-sectionv1 .news-small-items .news-content .readmore:hover .arrows {
  color: var(--p5-clr);
}
@media (max-width: 1199px) {
  .blog-sectionv1 .news-small-items {
    gap: 14px;
  }
  .blog-sectionv1 .news-small-items .news-thumb {
    width: 160px;
  }
  .blog-sectionv1 .news-small-items .news-thumb img {
    width: 100%;
  }
  .blog-sectionv1 .news-small-items .news-content ul {
    display: flex;
    align-items: center;
    gap: 14px;
    margin-bottom: 7px;
  }
  .blog-sectionv1 .news-small-items .news-content h4 {
    margin-bottom: 7px;
    font-weight: 500;
  }
  .blog-sectionv1 .news-small-items .news-content .readmore {
    font-size: 14px;
  }
  .blog-sectionv1 .news-small-items .news-content .readmore i {
    font-size: 13px;
  }
}
@media (max-width: 575px) {
  .blog-sectionv1 .news-small-items {
    gap: 14px;
    display: grid;
    justify-content: center;
    text-align: center;
  }
  .blog-sectionv1 .news-small-items .news-thumb {
    width: 100%;
    margin: 0 auto;
  }
  .blog-sectionv1 .news-small-items .news-thumb img {
    width: 100%;
  }
  .blog-sectionv1 .news-small-items .news-content ul {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .blog-sectionv1 .news-small-items .news-content .readmore {
    justify-content: center;
  }
}
.blog-sectionv1 .news-single-items .news-thumbig {
  width: 100%;
}
.blog-sectionv1 .news-single-items .news-thumbig img {
  width: 100%;
}
.blog-sectionv1 .news-single-items .news-content {
  padding: 36px 40px;
}
@media (max-width: 1399px) {
  .blog-sectionv1 .news-single-items .news-content {
    padding: 20px 24px;
  }
}

.talk-content .section-title h3 {
  margin-bottom: 20px;
}
.talk-content .section-title p {
  margin-bottom: 40px;
  color: var(--text);
}
@media (max-width: 991px) {
  .talk-content .section-title h3 {
    margin-bottom: 16px;
  }
  .talk-content .section-title p {
    margin-bottom: 24px;
    color: var(--text);
  }
}

.counter-inner .counter-talk-items {
  padding: 30px 18px;
  display: flex;
  gap: 20px;
  align-items: center;
  transition: all 0.4s;
}
.counter-inner .counter-talk-items .icon {
  min-width: 80px;
  min-height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.4s;
}
.counter-inner .counter-talk-items .content h3 {
  color: var(--black);
}
.counter-inner .counter-talk-items .content h3 span {
  font-size: 32px;
  color: var(--black);
}
.counter-inner .counter-talk-items .content p {
  font-size: 16px;
  color: var(--text);
}
.counter-inner .counter-talk-items:hover {
  box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
}
.counter-inner .counter-talk-items:hover .icon {
  transform: scale(1.04);
}
@media (max-width: 1399px) {
  .counter-inner .counter-talk-items {
    padding: 20px 16px;
    gap: 14px;
  }
  .counter-inner .counter-talk-items .icon {
    min-width: 65px;
    min-height: 65px;
  }
  .counter-inner .counter-talk-items .icon img {
    width: 30px;
    object-fit: contain;
  }
  .counter-inner .counter-talk-items .content h3 {
    color: var(--black);
  }
  .counter-inner .counter-talk-items .content h3 span {
    font-size: 32px;
    color: var(--black);
  }
  .counter-inner .counter-talk-items .content p {
    font-size: 16px;
    color: var(--text);
  }
}
@media (max-width: 1399px) {
  .counter-inner .counter-talk-items {
    padding: 18px 14px;
    gap: 14px;
  }
  .counter-inner .counter-talk-items .icon {
    min-width: 50px;
    min-height: 50px;
  }
  .counter-inner .counter-talk-items .icon img {
    width: 24px;
    object-fit: contain;
  }
  .counter-inner .counter-talk-items .content h3 {
    color: var(--black);
  }
  .counter-inner .counter-talk-items .content h3 span {
    font-size: 32px;
    color: var(--black);
  }
  .counter-inner .counter-talk-items .content p {
    font-size: 16px;
    color: var(--text);
  }
}

@-webkit-keyframes rippleOne {
  70% {
    -webkit-box-shadow: 0 0 0 40px rgba(244, 68, 56, 0);
    box-shadow: 0 0 0 40px rgba(244, 68, 56, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(244, 68, 56, 0);
    box-shadow: 0 0 0 0 rgba(244, 68, 56, 0);
  }
}
@keyframes rippleOne {
  70% {
    -webkit-box-shadow: 0 0 0 40px rgba(244, 68, 56, 0);
    box-shadow: 0 0 0 40px rgba(244, 68, 56, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(244, 68, 56, 0);
    box-shadow: 0 0 0 0 rgba(244, 68, 56, 0);
  }
}
@keyframes cir36 {
  100% {
    transform: rotate(360deg);
  }
}
@keyframes rounded {
  50% {
    transform: rotate(15deg);
  }
}
@keyframes updown {
  50% {
    transform: translateY(-10px);
  }
}
@keyframes lf {
  50% {
    transform: translateX(14px);
  }
}
@keyframes zin1 {
  50% {
    transform: scale(1.08);
  }
}
@keyframes rots1 {
  50% {
    transform: rotate(10deg);
  }
}
@-webkit-keyframes spinner {
  to {
    -webkit-transform: rotateZ(360deg);
    transform: rotateZ(360deg);
  }
}
@keyframes spinner {
  to {
    -webkit-transform: rotateZ(360deg);
    transform: rotateZ(360deg);
  }
}
@-webkit-keyframes letters-loading {
  0%, 75%, 100% {
    opacity: 0;
    transform: rotateY(-90deg);
  }
  25%, 50% {
    opacity: 1;
    transform: rotateY(0deg);
  }
}
@keyframes letters-loading {
  0%, 75%, 100% {
    opacity: 0;
    transform: rotateY(-90deg);
  }
  25%, 50% {
    opacity: 1;
    transform: rotateY(0deg);
  }
}
@keyframes loaderspin {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
@keyframes tpswing {
  0% {
    -webkit-transform: rotate(20deg);
    -ms-transform: rotate(20deg);
    transform: rotate(20deg);
  }
  100% {
    -webkit-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    transform: rotate(0deg);
  }
}
@keyframes width {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-webkit-keyframes width {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-webkit-keyframes loaderspin {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
@keyframes loaderpulse {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.2);
  }
}
@keyframes rounded {
  50% {
    transform: rotate(20deg);
  }
}
@keyframes cir36 {
  100% {
    transform: rotate(360deg);
  }
}
.float-bob-y {
  -webkit-animation-name: float-bob-y;
  animation-name: float-bob-y;
  -webkit-animation-duration: 3s;
  animation-duration: 3s;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
}

@-webkit-keyframes float-bob-y {
  0% {
    -webkit-transform: translateY(-30px);
    transform: translateY(-30px);
  }
  50% {
    -webkit-transform: translateY(-10px);
    transform: translateY(-10px);
  }
  100% {
    -webkit-transform: translateY(-30px);
    transform: translateY(-30px);
  }
}
@keyframes float-bob-y {
  0% {
    -webkit-transform: translateY(-30px);
    transform: translateY(-30px);
  }
  50% {
    -webkit-transform: translateY(-10px);
    transform: translateY(-10px);
  }
  100% {
    -webkit-transform: translateY(-30px);
    transform: translateY(-30px);
  }
}
.float-bob-x {
  -webkit-animation-name: float-bob-x;
  animation-name: float-bob-x;
  -webkit-animation-duration: 3s;
  animation-duration: 3s;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
}

@-webkit-keyframes float-bob-x {
  0% {
    -webkit-transform: translateX(0px);
    transform: translateX(30px);
  }
  50% {
    -webkit-transform: translateX(10px);
    transform: translateX(10px);
  }
  100% {
    -webkit-transform: translateX(30px);
    transform: translateX(30px);
  }
}
@keyframes float-bob-x {
  0% {
    -webkit-transform: translateX(30px);
    transform: translateX(30px);
  }
  50% {
    -webkit-transform: translateX(10px);
    transform: translateX(10px);
  }
  100% {
    -webkit-transform: translateX(30px);
    transform: translateX(30px);
  }
}
@keyframes bounce-x {
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
  50% {
    -webkit-transform: translateX(30px);
    transform: translateX(30px);
  }
  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}
.bounce-x {
  -webkit-animation: bounce-x 7s infinite linear;
  animation: bounce-x 7s infinite linear;
}

@keyframes criss-cross-left {
  0% {
    left: -20px;
  }
  50% {
    left: 50%;
    width: 20px;
    height: 20px;
  }
  100% {
    left: 50%;
    width: 375px;
    height: 375px;
  }
}
@keyframes criss-cross-right {
  0% {
    right: -20px;
  }
  50% {
    right: 50%;
    width: 20px;
    height: 20px;
  }
  100% {
    right: 50%;
    width: 375px;
    height: 375px;
  }
}
@keyframes rotated2 {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(-360deg);
  }
}
@keyframes wave {
  0% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(-25%);
  }
  100% {
    transform: translateX(-50%);
  }
}
@keyframes zints1 {
  100% {
    width: 65%;
    height: 65%;
  }
}
@keyframes zints2 {
  100% {
    width: 65%;
    height: 65%;
  }
}
.brand-slider .brand-image {
  text-align: center;
  filter: grayscale(100%);
  transition: all 0.4s ease-in-out;
  opacity: 0.4;
}
.brand-slider .brand-image:hover {
  filter: initial;
  opacity: 1;
}
.brand-slider .swiper-slide.swiper-slide-active .brand-image {
  filter: initial;
  opacity: 1;
}

iframe {
  width: 100%;
  height: 500px;
}
@media (max-width: 767px) {
  iframe {
    height: 400px;
  }
}
@media (max-width: 575px) {
  iframe {
    height: 300px;
  }
}

.contact-infosectionv1 .contact-call-info {
  border-radius: 5px;
  background: var(--White, #fff);
  box-shadow: 0px 4.8px 24.4px -6px rgba(19, 16, 34, 0.1);
  padding: 30px 20px;
  text-align: center;
}
.contact-infosectionv1 .contact-call-info h5 {
  margin-bottom: 10px;
  font-size: 20px;
  font-weight: 700;
}
.contact-infosectionv1 .contact-call-info .icon {
  width: 65px;
  height: 65px;
  border-radius: 50%;
  background: var(--p4-clr);
  margin: 0 auto 20px;
}
.contact-infosectionv1 .contact-call-info .icon i {
  color: var(--white);
  font-size: 24px;
}
@media (max-width: 1199px) {
  .contact-infosectionv1 .contact-call-info {
    padding: 20px 20px;
  }
  .contact-infosectionv1 .contact-call-info .icon {
    width: 45px;
    height: 45px;
    margin: 0 auto 18px;
  }
  .contact-infosectionv1 .contact-call-info .icon i {
    font-size: 18px;
  }
}

.contact-contentv2 i {
  color: var(--black) !important;
}

.contact-thumbv02 {
  padding-right: 34px;
}
.contact-thumbv02 .thumb-smal {
  position: absolute;
  top: 60px;
  left: 0;
  animation: updown 2s linear infinite;
}
.contact-thumbv02 .thumb-smal img {
  border-radius: 10px;
}
.contact-thumbv02 .thumbb {
  max-width: 464px;
  margin-left: auto;
}
.contact-thumbv02 .thumbb img {
  border-radius: 10px;
}
.contact-thumbv02 .badg-count {
  background: var(--p2-clr);
  border-radius: 10px;
  padding: 22px 20px;
  display: flex;
  align-items: center;
  gap: 20px;
  position: absolute;
  left: 0;
  bottom: 30px;
  animation: lf 2s linear infinite;
}
.contact-thumbv02 .badg-count .cont h4 {
  font-size: 28px;
  font-weight: 700;
  color: var(--white);
  margin-bottom: 4px;
}
.contact-thumbv02 .badg-count .cont h4 span {
  font-size: 28px;
  font-weight: 700;
  color: var(--white);
}
.contact-thumbv02 .badg-count .cont .subti {
  font-size: 20px;
  font-weight: 500;
  font-family: "Jost", sans-serif;
  color: var(--white);
}
@media (max-width: 991px) {
  .contact-thumbv02 {
    padding-right: 0;
  }
  .contact-thumbv02 .thumb-smal {
    top: 20px;
    left: 10px;
    width: 120px;
  }
  .contact-thumbv02 .thumb-smal img {
    width: 100%;
  }
  .contact-thumbv02 .thumbb img {
    width: 100%;
  }
  .contact-thumbv02 .badg-count {
    padding: 18px 18px;
    gap: 10px;
    left: 0;
    bottom: 10px;
  }
  .contact-thumbv02 .badg-count .cont h4 {
    font-size: 24px;
    margin-bottom: 2px;
  }
  .contact-thumbv02 .badg-count .cont h4 span {
    font-size: 24px;
  }
  .contact-thumbv02 .badg-count .cont .subti {
    font-size: 16px;
  }
}
@media (max-width: 991px) {
  .contact-thumbv02 {
    padding-right: 0;
  }
  .contact-thumbv02 .thumb-smal {
    top: 20px;
    left: 10px;
    width: 120px;
  }
  .contact-thumbv02 .thumb-smal img {
    width: 100%;
  }
  .contact-thumbv02 .thumbb {
    width: 100%;
    max-width: 100%;
  }
  .contact-thumbv02 .thumbb img {
    width: 100%;
  }
}

.faq-thumbs {
  width: 100%;
  animation: smzom 10s linear infinite;
}
.faq-thumbs img {
  width: 100%;
}

@keyframes smzom {
  50% {
    transform: scale(1.04);
  }
}
.faq-content .faq .accordion-single {
  transition: all 0.4s;
  border-radius: 5px;
  border: 2px solid var(--Gray-200, #f2f2f2);
  padding: 20px 20px;
}
.faq-content .faq .header-area {
  cursor: pointer;
}
.faq-content .faq .header-area button {
  font-size: 20px;
  font-weight: 700;
  color: var(--black);
  padding-right: 40px;
  text-align: start;
}
.faq-content .faq .header-area button::after {
  position: absolute;
  z-index: 1;
  right: 0;
  border-radius: 5px;
  font-size: 24px;
  transform: rotate(0deg);
  transition: all 0.5s;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: "Font Awesome 5 Free";
  content: "+";
  font-weight: 900;
  width: 40px;
  min-width: 40px;
  height: 40px;
  background: var(--p2-clr);
  color: var(--white);
}
.faq-content .faq .active .header-area button::after {
  position: absolute;
  z-index: 1;
  right: 0;
  border-radius: 5px;
  font-size: 24px;
  transform: rotate(0deg);
  transition: all 0.5s;
  background: var(--p5-clr) !important;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: "Font Awesome 5 Free";
  content: "-" !important;
  font-weight: 900;
  width: 40px;
  min-width: 40px;
  height: 40px;
  background: var(--p2-clr);
  color: var(--white);
}
.faq-content .faq .content-area {
  display: none;
  padding-top: 18px;
}
@media (max-width: 1199px) {
  .faq-content .faq .accordion-single {
    padding: 15px 15px;
  }
  .faq-content .faq .header-area {
    cursor: pointer;
  }
  .faq-content .faq .header-area button {
    font-size: 17px;
    padding-right: 50px;
    text-align: start;
  }
  .faq-content .faq .header-area button::after {
    width: 34px;
    min-width: 34px;
    height: 34px;
  }
}

.footer-widgets-wrapper {
  padding: 80px 0 80px;
  position: relative;
  z-index: 9;
}
.footer-widgets-wrapper .single-footer-widget .widget-head {
  margin-bottom: 30px;
}
.footer-widgets-wrapper .single-footer-widget .footer-logo {
  margin-bottom: 30px;
  display: block;
}
@media (max-width: 1199px) {
  .footer-widgets-wrapper {
    padding: 60px 0 70px;
  }
  .footer-widgets-wrapper .single-footer-widget .widget-head {
    margin-bottom: 20px;
  }
  .footer-widgets-wrapper .single-footer-widget .footer-logo {
    margin-bottom: 20px;
  }
}
@media (max-width: 991px) {
  .footer-widgets-wrapper {
    padding: 50px 0 60px;
  }
}

.footer-bottom {
  position: relative;
  z-index: 9;
}
.footer-bottom .footer-wrapper {
  position: relative;
  z-index: 9;
  padding: 26px 0;
}
.footer-bottom .footer-wrapper p a {
  color: var(--p2-clr);
}
.footer-bottom .footer-wrapper .footer-menu {
  display: flex;
  align-items: center;
  gap: 30px;
}
.footer-bottom .footer-wrapper .footer-menu li a {
  background-image: linear-gradient(var(--theme), var(--theme));
  background-position: 0 95%;
  background-repeat: no-repeat;
  background-size: 0% 2px;
  display: inline-block;
  transition: all 0.4s ease-in-out;
  font-size: 16px;
  font-weight: 400;
  font-family: "Nunito", sans-serif;
}
.footer-bottom .footer-wrapper .footer-menu li a:hover {
  background-size: 100% 1px;
  color: var(--p2-clr);
}
@media (max-width: 575px) {
  .footer-bottom .footer-wrapper .footer-menu {
    display: flex;
    align-items: center;
    gap: 1px 12px;
    flex-wrap: wrap;
    justify-content: center;
  }
  .footer-bottom .footer-wrapper .footer-menu li a {
    color: var(--header);
    background-image: linear-gradient(var(--theme), var(--theme));
    background-position: 0 95%;
    background-repeat: no-repeat;
    background-size: 0% 2px;
    display: inline-block;
    transition: all 0.4s ease-in-out;
    font-size: 16px;
    font-weight: 400;
    font-family: "Nunito", sans-serif;
    color: rgba(255, 255, 255, 0.7);
  }
  .footer-bottom .footer-wrapper .footer-menu li a:hover {
    background-size: 100% 1px;
    color: var(--p2-clr);
  }
}
.footer-bottom .scroll-icon {
  position: absolute;
  left: 90.2%;
  top: -40px;
  transform: translateX(-50%);
  width: 50px;
  height: 50px;
  line-height: 45px;
  background-color: var(--p2-clr);
  border-radius: 50%;
  text-align: center;
  color: var(--white);
  border: 3px solid var(--white);
  display: inline-block;
  z-index: 99;
}
@media (max-width: 1399px) {
  .footer-bottom .scroll-icon {
    left: 50%;
  }
}

.footer-style1 .footer-content .pre-pragraph {
  color: var(--text);
  margin-bottom: 24px;
}
.footer-style1 .footer-content .social-wrapper {
  gap: 14px;
}
.footer-style1 .footer-content .social-wrapper a {
  background: var(--cmnbg);
}
.footer-style1 .footer-content .social-wrapper a i {
  color: var(--black);
  transition: all 0.5s;
}
.footer-style1 .footer-content .social-wrapper a svg {
  stroke: var(--black);
  transition: all 0.5s;
}
.footer-style1 .footer-content .social-wrapper a:hover {
  background: var(--p3-clr);
}
.footer-style1 .footer-content .social-wrapper a:hover i {
  color: var(--white);
}
.footer-style1 .footer-content .social-wrapper a:hover svg {
  stroke: var(--white);
}
.footer-style1 .single-footer-widget .list-area {
  display: grid;
  gap: 10px;
}
.footer-style1 .single-footer-widget .list-area li a {
  transition: all 0.4s;
  font-size: 16px;
  color: var(--text);
  font-family: "Nunito", sans-serif;
}
.footer-style1 .single-footer-widget .list-area li a:hover {
  color: var(--p2-clr);
  margin-left: 5px;
}
.footer-style1 .single-footer-widget .list-contact {
  gap: 16px;
}
.footer-style1 .single-footer-widget .list-contact li {
  display: flex;
  gap: 13px;
}
.footer-style1 .single-footer-widget .list-contact li i {
  width: 42px;
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  border: 1px solid #f2f2f2;
  color: var(--p4-clr);
  font-size: 16px;
  line-height: 2;
}
.footer-style1 .single-footer-widget .list-contact li .lited {
  max-width: 168px;
  color: var(--text);
}
.footer-style1 .single-footer-widget .widget-head h4 {
  font-weight: 500;
  color: var(--black);
  position: relative;
  display: inline-block;
  padding-bottom: 10px;
}
.footer-style1 .single-footer-widget .widget-head h4::before {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 140%;
  content: "";
  height: 2px;
  background: var(--text);
}
.footer-style1 .single-footer-widget .widget-head h4::after {
  position: absolute;
  left: 140%;
  bottom: -3px;
  width: 8px;
  height: 8px;
  content: "";
  background: var(--text);
  border-radius: 50%;
}
.footer-style1 .footer-bottom .footer-wrapper {
  border-top: 1px solid rgba(13, 13, 13, 0.1);
}
.footer-style1 .footer-bottom .footer-wrapper p {
  color: var(--text);
}
.footer-style1 .footer-bottom .footer-wrapper p a {
  color: var(--p2-clr);
}
.footer-style1 .footer-bottom .footer-wrapper .footer-menu li a {
  color: var(--text);
}

.footer-style2 .single-footer-widget .footer-content .pre-pragraph {
  color: rgba(255, 255, 255, 0.8);
  line-height: 24px;
  margin-bottom: 30px;
}
.footer-style2 .single-footer-widget .footer-content .social-wrapper {
  gap: 16px;
}
.footer-style2 .single-footer-widget .footer-content .social-wrapper a {
  border-radius: 5px;
  background: rgba(255, 255, 255, 0.1);
}
.footer-style2 .single-footer-widget .footer-content .social-wrapper a:hover {
  background: var(--p4-clr);
}
.footer-style2 .single-footer-widget .list-area {
  display: grid;
  gap: 10px;
}
.footer-style2 .single-footer-widget .list-area li a {
  transition: all 0.4s;
  font-size: 16px;
  color: var(--white);
  font-family: "Nunito", sans-serif;
}
.footer-style2 .single-footer-widget .list-area li a:hover {
  color: var(--p2-clr);
  margin-left: 5px;
}
.footer-style2 .single-footer-widget .list-contact {
  gap: 18px;
}
.footer-style2 .single-footer-widget .list-contact li {
  display: flex;
  gap: 13px;
}
.footer-style2 .single-footer-widget .list-contact li i {
  color: var(--white);
  font-size: 16px;
  line-height: 2;
}
.footer-style2 .single-footer-widget .list-contact li .lited {
  max-width: 168px;
  color: var(--white);
}
.footer-style2 .single-footer-widget .widget-head h4 {
  font-weight: 500;
  color: var(--white);
  position: relative;
  display: inline-block;
  padding-bottom: 10px;
}
.footer-style2 .single-footer-widget .widget-head h4::before {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 140%;
  content: "";
  height: 2px;
  background: var(--white);
}
.footer-style2 .single-footer-widget .widget-head h4::after {
  position: absolute;
  left: 140%;
  bottom: -3px;
  width: 8px;
  height: 8px;
  content: "";
  background: var(--white);
  border-radius: 50%;
}
.footer-style2 .single-footer-form p {
  margin-bottom: 30px;
  max-width: 254px;
}
.footer-style2 .single-footer-form form {
  max-width: 270px;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
}
.footer-style2 .single-footer-form form input {
  padding: 16px 2px 16px 18px;
  width: 100%;
  height: 60px;
  border: unset;
  color: var(--white);
  background: transparent;
  font-weight: 300;
  font-size: 14px;
}
.footer-style2 .single-footer-form form ::placeholder {
  color: var(--white);
}
.footer-style2 .single-footer-form form button {
  height: 60px;
  min-width: 70px;
  background: var(--p2-clr);
  display: flex;
  align-items: center;
  justify-content: center;
  border: unset;
}
.footer-style2 .single-footer-form form button i {
  color: var(--white);
  font-size: 16px;
}
.footer-style2 .footer-bottom .footer-wrapper {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}
.footer-style2 .footer-bottom .footer-wrapper p {
  color: rgba(255, 255, 255, 0.7);
}
.footer-style2 .footer-bottom .footer-wrapper p a {
  color: var(--p2-clr);
}
.footer-style2 .footer-bottom .footer-wrapper .footer-menu li a {
  color: rgba(255, 255, 255, 0.7);
}
.footer-style2 .footer-bottom .footer-wrapper .footer-menu li a:hover {
  background-size: 100% 1px;
  color: var(--p2-clr);
}
.footer-style2 .footer-apple {
  right: 60px;
  top: 180px;
  animation: updown 2s linear infinite;
}
.footer-style2 .footer-cut {
  bottom: 170px;
  left: 40%;
}
@media (max-width: 1399px) {
  .footer-style2 .single-footer-widget .footer-content .pre-pragraph {
    margin-bottom: 22px;
  }
  .footer-style2 .single-footer-widget .footer-content .social-wrapper {
    gap: 10px;
  }
  .footer-style2 .single-footer-widget .list-area {
    gap: 8px;
  }
  .footer-style2 .single-footer-widget .list-contact {
    gap: 12px;
  }
  .footer-style2 .single-footer-form p {
    margin-bottom: 24px;
  }
}

.header-top-section {
  position: relative;
  z-index: 9;
  background-color: var(--p3-clr);
}
.header-top-section .header-top-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
}
.header-top-section .header-top-wrapper .contact-list {
  display: flex;
  align-items: center;
  gap: 40px;
}
.header-top-section .header-top-wrapper .contact-list li {
  color: var(--white);
  font-weight: 400;
}
.header-top-section .header-top-wrapper .contact-list li i {
  margin-right: 5px;
}
.header-top-section .header-top-wrapper .contact-list li a {
  color: var(--white);
  font-weight: 400;
}
.header-top-section .header-top-wrapper .social-wrapper {
  gap: 14px;
}
.header-top-section .header-top-wrapper .social-wrapper a {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(255, 255, 255, 0.3);
}
.header-top-section .header-top-wrapper .social-wrapper a svg {
  stroke: var(--white);
  width: 8px;
}
.header-top-section .header-top-wrapper .social-wrapper a i {
  font-size: 12px;
}
.header-top-section .header-top-wrapper .social-wrapper a .ani-arrow {
  transition: all 0.5s;
}
.header-top-section .header-top-wrapper .social-wrapper a:hover {
  background: var(--p2-clr);
  border-color: var(--p2-clr);
}
.header-top-section .header-top-wrapper .social-wrapper a:hover i {
  color: var(--white);
}
.header-top-section .header-top-wrapper .social-wrapper a:hover svg {
  stroke: var(--white);
}
.header-top-section .header-top-wrapper.style-2 {
  padding: 15px 0;
}
.header-top-section .header-top-wrapper.style-2 .contact-list li {
  color: var(--white);
}
.header-top-section .header-top-wrapper.style-2 .contact-list li a {
  color: var(--white);
}
.header-top-section .header-top-wrapper.style-2 .social-icon span {
  color: var(--white);
}
.header-top-section .header-top-wrapper.style-2 .social-icon a {
  color: var(--white);
}
.header-top-section .header-top-wrapper.style-2 .social-icon a:hover {
  color: var(--p2-clr);
}

.header-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.header-main .main-menu ul {
  margin-bottom: 0;
}
.header-main .main-menu ul li {
  position: relative;
  list-style: none;
  display: inline-block;
  margin-inline-end: 26px;
}
.header-main .main-menu ul li:last-child {
  margin-inline-end: 0;
}
.header-main .main-menu ul li a {
  display: inline-block;
  font-size: 18px;
  font-weight: 600;
  color: var(--black);
  padding: 20px 0;
  text-align: left;
  font-family: "Jost", sans-serif;
  position: relative;
  text-transform: capitalize;
  transition: all 0.4s ease-in-out;
}
.header-main .main-menu ul li a i {
  margin-left: 2px;
  font-size: 16px;
}
.header-main .main-menu ul li a:hover {
  color: var(--theme) !important;
}
.header-main .main-menu ul li .submenu {
  position: absolute;
  top: 100%;
  inset-inline-start: 0;
  min-width: 240px;
  background: var(--white);
  z-index: 99999;
  visibility: hidden;
  opacity: 0;
  transform-origin: top center;
  color: var(--header);
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
  transform: translateY(10px);
  transition: all 0.4s ease-in-out;
}
.header-main .main-menu ul li .submenu li {
  display: block;
  width: 100%;
  margin: 0;
  padding: 0;
}
.header-main .main-menu ul li .submenu li a {
  position: relative;
  z-index: 11;
  font-size: 16px;
  font-weight: 600;
  color: var(--header);
  padding: 0 25px;
  padding-bottom: 11px;
  padding-top: 11px;
  width: 100%;
  border-bottom: 1px solid #eeeeee;
}
.header-main .main-menu ul li .submenu li:last-child a {
  border: none;
}
.header-main .main-menu ul li .submenu li .submenu {
  inset-inline-start: 100%;
  top: 0;
  visibility: hidden;
  opacity: 0;
}
.header-main .main-menu ul li .submenu li:hover > a {
  background: var(--p2-clr);
  color: var(--white) !important;
}
.header-main .main-menu ul li .submenu li:hover > a::after {
  color: var(--p2-clr);
}
.header-main .main-menu ul li .submenu li:hover > .submenu {
  -webkit-transform: translateY(1);
  -moz-transform: translateY(1);
  -ms-transform: translateY(1);
  -o-transform: translateY(1);
  transform: translateY(1);
  visibility: visible;
  opacity: 1;
}
.header-main .main-menu ul li .submenu li.has-dropdown > a::after {
  position: absolute;
  top: 50%;
  inset-inline-end: 25px;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  color: var(--p2-clr);
}
.header-main .main-menu ul li .has-homemenu {
  width: 500px;
  padding: 30px 30px 10px 30px;
  opacity: 0;
  left: -250px;
  visibility: hidden;
  padding: 20px 20px 20px 20px;
}
.header-main .main-menu ul li .has-homemenu .homemenu-items {
  display: flex;
  align-items: center;
  gap: 18px;
  justify-content: space-between;
}
@media (max-width: 991px) {
  .header-main .main-menu ul li .has-homemenu .homemenu-items {
    flex-wrap: wrap;
  }
}
.header-main .main-menu ul li .has-homemenu .homemenu-items .homemenu {
  position: relative;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
}
.header-main .main-menu ul li .has-homemenu .homemenu-items .homemenu .homemenu-thumb {
  position: relative;
  width: 100%;
}
.header-main .main-menu ul li .has-homemenu .homemenu-items .homemenu .homemenu-thumb .demo-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60%;
  gap: 10px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s ease-in-out;
  margin-top: 20px;
}
.header-main .main-menu ul li .has-homemenu .homemenu-items .homemenu .homemenu-thumb .demo-button .theme-btn {
  padding: 14px 20px;
  color: var(--white) !important;
  width: initial;
  font-size: 14px;
  text-align: center;
  border-radius: 0;
}
.header-main .main-menu ul li .has-homemenu .homemenu-items .homemenu .homemenu-thumb .demo-button .theme-btn:hover {
  color: var(--white) !important;
}
.header-main .main-menu ul li .has-homemenu .homemenu-items .homemenu .homemenu-thumb::before {
  background: -webkit-gradient(linear, left top, left bottom, from(rgba(20, 19, 19, 0)), to(#5e5ef6));
  background: linear-gradient(to bottom, rgba(99, 92, 92, 0) 0%, #252527 100%);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  overflow: hidden;
  opacity: 0;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  content: "";
}
.header-main .main-menu ul li .has-homemenu .homemenu-items .homemenu .homemenu-thumb:hover::before {
  visibility: visible;
  opacity: 1;
}
.header-main .main-menu ul li .has-homemenu .homemenu-items .homemenu .homemenu-thumb:hover .demo-button {
  opacity: 1;
  visibility: visible;
  margin-top: 0;
}
.header-main .main-menu ul li .has-homemenu .homemenu-items .homemenu .homemenu-thumb:hover .homemenu-btn {
  opacity: 1;
  visibility: visible;
  bottom: 50%;
  transform: translateY(50%);
}
.header-main .main-menu ul li .has-homemenu .homemenu-items .homemenu .homemenu-thumb img {
  width: 100%;
}
.header-main .main-menu ul li .has-homemenu .homemenu-items .homemenu .homemenu-title {
  text-align: center;
  margin: 15px auto;
  display: inline-block;
  font-size: 16px;
}
.header-main .main-menu ul li:hover > a {
  color: var(--p2-clr);
}
.header-main .main-menu ul li:hover > a::after {
  color: var(--p2-clr);
}
.header-main .main-menu ul li:hover > .submenu {
  visibility: visible;
  opacity: 1;
  transform: translateY(0px);
}
.header-main .sidebar__toggle {
  cursor: pointer;
  font-size: 39px;
}
@media (max-width: 575px) {
  .header-main .sidebar__toggle {
    font-size: 24px;
  }
}

.header-1 {
  padding: 18px 0;
}
.header-1 .header-main .header-left {
  display: flex;
  align-items: center;
  gap: 60px;
}
@media (max-width: 1899px) {
  .header-1 .header-main .header-left {
    gap: 40px;
  }
}
@media (max-width: 1399px) {
  .header-1 .header-main .header-left {
    gap: 25px;
  }
}
@media (max-width: 1199px) {
  .header-1 .header-main .header-left {
    gap: 20px;
  }
}
.header-1 .header-main .header-right {
  gap: 30px;
}
@media (max-width: 1199px) {
  .header-1 .header-main .header-right {
    gap: 20px;
  }
}
@media (max-width: 767px) {
  .header-1 .header-main .header-right {
    gap: 10px;
  }
}
.header-1 .header-main .header-right .search-icon {
  text-align: center;
  color: var(--black);
  margin-left: 30px;
}
@media (max-width: 1199px) {
  .header-1 .header-main .header-right .search-icon {
    margin-left: 0;
  }
}
@media (max-width: 1399px) {
  .header-1 .header-main .header-right .header-button .theme-btn {
    padding: 10px 22px;
  }
}

.header-2 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  padding: 0 0;
  z-index: 99;
}
.header-2 .mega-menu-wrapper {
  padding: 20px 36px;
  border-bottom: 10px solid var(--p2-clr);
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}
@media (max-width: 1399px) {
  .header-2 {
    padding: 0 0;
  }
  .header-2 .mega-menu-wrapper {
    padding: 14px 14px;
    border-bottom: 3px solid var(--p2-clr);
  }
}

.sidebar__toggle {
  cursor: pointer;
}

.sticky {
  position: fixed !important;
  top: 0 !important;
  left: 0;
  width: 100%;
  z-index: 100;
  transition: all 0.9s;
  background-color: var(--white);
  box-shadow: var(--box-shadow);
  -webkit-animation: 500ms ease-in-out 0s normal none 1 running fadeInDown;
  animation: 500ms ease-in-out 0s normal none 1 running fadeInDown;
}
.sticky.header-1 {
  padding: 10px 0;
}
.sticky.header-2 {
  padding: 0px 0;
  background: transparent !important;
  box-shadow: none;
}
.sticky.header-2 .mega-menu-wrapper {
  box-shadow: rgba(50, 50, 93, 0.25) 0px 6px 12px -2px, rgba(0, 0, 0, 0.3) 0px 3px 7px -3px;
}

.offcanvas__info {
  background: var(--cmnbg) none repeat scroll 0 0;
  border-left: 2px solid var(--p2-clr);
  position: fixed;
  right: 0;
  top: 0;
  width: 400px;
  height: 100%;
  -webkit-transform: translateX(calc(100% + 80px));
  -moz-transform: translateX(calc(100% + 80px));
  -ms-transform: translateX(calc(100% + 80px));
  -o-transform: translateX(calc(100% + 80px));
  transform: translateX(calc(100% + 80px));
  -webkit-transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
  -moz-transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
  transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
  z-index: 99999;
  overflow-y: scroll;
  overscroll-behavior-y: contain;
  scrollbar-width: none;
}
.offcanvas__info::-webkit-scrollbar {
  display: none;
}

.offcanvas__info.info-open {
  opacity: 1;
  -webkit-transform: translateX(0);
  -moz-transform: translateX(0);
  -ms-transform: translateX(0);
  -o-transform: translateX(0);
  transform: translateX(0);
}

.offcanvas__wrapper {
  position: relative;
  height: 100%;
  padding: 30px 30px;
}
.offcanvas__wrapper .offcanvas__content .text {
  color: var(--text);
}
.offcanvas__wrapper .offcanvas__content .offcanvas__close {
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  border-radius: 4px;
  background-color: var(--p2-clr);
  position: relative;
  z-index: 9;
  cursor: pointer;
}
.offcanvas__wrapper .offcanvas__content .offcanvas__close i {
  color: var(--white);
}
.offcanvas__wrapper .offcanvas__content .offcanvas__contact {
  margin-top: 20px;
}
.offcanvas__wrapper .offcanvas__content .offcanvas__contact ul {
  margin-top: 20px;
}
.offcanvas__wrapper .offcanvas__content .offcanvas__contact ul li {
  font-size: 16px;
  font-weight: 600;
  text-transform: capitalize;
}
.offcanvas__wrapper .offcanvas__content .offcanvas__contact ul li a {
  color: var(--text);
}
.offcanvas__wrapper .offcanvas__content .offcanvas__contact ul li:not(:last-child) {
  margin-bottom: 15px;
}
.offcanvas__wrapper .offcanvas__content .offcanvas__contact ul li .offcanvas__contact-icon {
  margin-right: 20px;
}
.offcanvas__wrapper .offcanvas__content .offcanvas__contact ul li .offcanvas__contact-icon i {
  color: var(--p2-clr);
}
.offcanvas__wrapper .offcanvas__content .offcanvas__contact span {
  text-transform: initial;
}
.offcanvas__wrapper .offcanvas__content .offcanvas__contact .header-button .theme-btn {
  width: 100%;
  padding: 16px 40px;
  text-transform: capitalize !important;
}
.offcanvas__wrapper .offcanvas__content .offcanvas__contact .social-icon {
  margin-top: 30px;
  gap: 10px;
}
.offcanvas__wrapper .offcanvas__content .offcanvas__contact .social-icon a {
  width: 45px;
  height: 45px;
  line-height: 45px;
  text-align: center;
  font-size: 16px;
  display: block;
  background: var(--white);
  color: var(--text);
  border-radius: 50%;
  -webkit-transition: all 0.4s ease-in-out;
  transition: all 0.4s ease-in-out;
  text-align: center;
  border: 1px solid var(--border);
}
.offcanvas__wrapper .offcanvas__content .offcanvas__contact .social-icon a:hover {
  border-color: var(--p2-clr);
  background-color: var(--p2-clr);
  color: var(--white);
}

.offcanvas__overlay {
  position: fixed;
  height: 100%;
  width: 100%;
  background: #151515;
  z-index: 900;
  top: 0;
  opacity: 0;
  visibility: hidden;
  right: 0;
}

.offcanvas__overlay.overlay-open {
  opacity: 0.8;
  visibility: visible;
}

@media (max-width: 450px) {
  .offcanvas__info {
    width: 300px;
  }
}
@media (max-width: 575px) {
  .offcanvas__wrapper {
    padding: 20px;
  }
}
::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

/* Track */
::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px var(--p2-clr);
  border-radius: 5px;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: var(--p2-clr);
  border-radius: 10px;
}

.fix {
  overflow: hidden;
}

.ralt {
  position: relative;
}

.ml-100 {
  margin-left: 100px;
}

.ripple {
  position: relative;
}
.ripple::before, .ripple::after {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 60px;
  height: 60px;
  -webkit-transform: translateX(-50%) translateY(-50%);
  transform: translateX(-50%) translateY(-50%);
  border-radius: 50%;
  box-shadow: 0 0 0 0 rgba(243, 159, 95, 0.5);
  -webkit-animation: rippleOne 3s infinite;
  animation: rippleOne 3s infinite;
}
.ripple::before {
  -webkit-animation-delay: 0.9s;
  animation-delay: 0.9s;
  content: "";
  position: absolute;
  right: 0;
  bottom: 0;
}
.ripple::after {
  -webkit-animation-delay: 0.6s;
  animation-delay: 0.6s;
  content: "";
  position: absolute;
  right: 0;
  bottom: 0;
}

.swiper-dot {
  margin-bottom: 2px;
  position: relative;
}
.swiper-dot::before {
  position: absolute;
  bottom: 13px;
  left: 37%;
  transform: translate(-50%, -50%);
  width: 105px;
  height: 2px;
  background: linear-gradient(90deg, #f39f5f 4.85%, rgba(255, 255, 255, 0) 96.39%);
  content: "";
  transform: rotate(-180deg);
}
@media (max-width: 1399px) {
  .swiper-dot::before {
    display: none;
  }
}
.swiper-dot::after {
  position: absolute;
  bottom: 13px;
  right: 37%;
  width: 105px;
  height: 2px;
  background: linear-gradient(90deg, #f39f5f 4.85%, rgba(255, 255, 255, 0) 96.39%);
  content: "";
}
@media (max-width: 1399px) {
  .swiper-dot::after {
    display: none;
  }
}
.swiper-dot .swiper-pagination-bullet {
  width: 10px;
  height: 10px;
  transition: 0.6s;
  background-color: var(--p2-clr);
  opacity: 1;
  border-radius: 10px;
}
.swiper-dot .swiper-pagination-bullet:not(:last-child) {
  margin-right: 15px;
}
.swiper-dot .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background-color: var(--p2-clr);
  transition: 0.6s;
  position: relative;
}
.swiper-dot .swiper-pagination-bullet.swiper-pagination-bullet-active::before {
  position: absolute;
  width: 30px;
  height: 30px;
  line-height: 30px;
  top: -10px;
  left: -10px;
  border-radius: 50%;
  background-color: transparent;
  border: 2px solid var(--p2-clr);
  content: "";
}

.array-button {
  display: flex;
  align-items: center;
  gap: 15px;
}
.array-button .array-prev {
  width: 61px;
  height: 56px;
  line-height: 56px;
  text-align: center;
  background-color: var(--white);
  color: var(--header);
  border-radius: 22px;
  transition: all 0.4s ease-in-out;
}
.array-button .array-prev:hover {
  background-color: var(--p2-clr);
  color: var(--white);
}
.array-button .array-next {
  width: 61px;
  height: 56px;
  line-height: 56px;
  text-align: center;
  background-color: var(--p2-clr);
  color: var(--white);
  border-radius: 22px;
  transition: all 0.4s ease-in-out;
}
.array-button .array-next:hover {
  background-color: var(--white);
  color: var(--p2-clr);
}

.mt-10 {
  margin-top: 10px;
}

@media (max-width: 767px) {
  br {
    display: none;
  }
}

/* background */
.bg-cover {
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
  background-position: center;
}

.bg-cover-2 {
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
  background-position: center;
  width: 100%;
  height: 100%;
}

.nice-select {
  background-color: transparent;
  border: transparent;
  float: initial;
  overflow: initial;
  height: initial;
  padding: 0;
  display: inline-flex;
  align-items: center;
  line-height: 150%;
  width: 100%;
  border: none;
}
.nice-select:focus, .nice-select:hover {
  border-color: transparent;
}
.nice-select::after {
  height: 8px;
  width: 8px;
  right: -25px;
  top: 15px;
  border-color: var(--header);
  border-bottom: 2px solid var(--header);
  border-right: 2px solid var(--header);
}
.nice-select .list {
  width: initial;
  background-color: var(--p2-clr);
  box-shadow: none;
  overflow: initial;
  box-shadow: rgba(0, 0, 0, 0.15) 0px 3px 3px 0px;
  width: 100%;
  top: 100%;
  padding: 0;
  max-height: 50vh;
  overflow-x: auto;
  right: -50px;
}
.nice-select .list::-webkit-scrollbar {
  width: 2px;
  opacity: 1;
  display: block;
}
.nice-select .list::-webkit-scrollbar-button, .nice-select .list::-webkit-scrollbar-thumb {
  background: var(--header);
}
.nice-select .option {
  background-color: transparent;
  font-size: 16px;
  line-height: 150%;
  padding: 4px 5px;
  min-height: initial;
  font-weight: 500;
}
.nice-select .option:hover, .nice-select .option:focus, .nice-select .option.selected.focus {
  background-color: transparent;
}
.nice-select .current {
  font-weight: 500;
  color: var(--header);
}

.scroll-up {
  cursor: pointer;
  display: block;
  border-radius: 50px;
  box-shadow: inset 0 0 0 2px var(--border);
  z-index: 99;
  opacity: 0;
  visibility: hidden;
  transform: translateY(15px);
  position: fixed;
  right: 25px;
  bottom: 35px;
  height: 50px;
  width: 50px;
  transition: all 0.4s ease-in-out;
}

.scroll-up::after {
  position: absolute;
  font-family: "Font Awesome 6 free";
  content: "\f062";
  text-align: center;
  line-height: 50px;
  font-weight: 700;
  font-size: 18px;
  color: var(--p2-clr);
  left: 0;
  top: 0;
  height: 50px;
  width: 50px;
  cursor: pointer;
  display: block;
  z-index: 1;
  transition: all 0.4s ease-in-out;
}

.scroll-up svg path {
  fill: none;
}

.scroll-up svg.scroll-circle path {
  stroke: var(--theme2);
  stroke-width: 4px;
  box-sizing: border-box;
  transition: all 0.4s ease-in-out;
}

.scroll-up.active-scroll {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.page-nav-wrap ul li {
  display: inline-block;
}
.page-nav-wrap ul li .page-numbers {
  display: inline-block;
  width: 50px;
  height: 50px;
  line-height: 50px;
  background: transparent;
  font-weight: 600;
  transition: all 0.3s ease-in-out;
  margin: 0 2px;
  border: 1px solid var(--border);
  color: var(--text);
  border-radius: 50%;
}
.page-nav-wrap ul li .page-numbers.current {
  background-color: var(--p2-clr);
  color: var(--white);
}
@media (max-width: 767px) {
  .page-nav-wrap ul li .page-numbers {
    margin-top: 10px;
    width: 50px;
    height: 50px;
    line-height: 50px;
    font-size: 14px;
  }
}
.page-nav-wrap ul li .page-numbers i {
  margin-top: 2px;
}
.page-nav-wrap ul li .page-numbers:hover {
  background-color: var(--p2-clr);
  color: var(--white);
  border: 1px solid transparent;
}

.box-color-1 {
  background-color: rgba(248, 184, 31, 0.15);
  color: #f8b81f;
}

.box-color-2 {
  background-color: rgba(88, 102, 235, 0.15);
  color: #5866eb;
}

.box-color-3 {
  background-color: rgba(57, 192, 250, 0.15);
  color: #39c0fa;
}

.box-color-4 {
  background-color: rgba(249, 37, 150, 0.15);
  color: #f92596;
}

.border-none {
  border: none !important;
}

.box-shadow {
  box-shadow: var(--box-shadow);
}

.bor-1 {
  border: 1px solid var(--p2-clr);
}

.mb-55 {
  margin-bottom: 55px !important;
}

.border-array-style {
  border: 1px solid var(--p2-clr);
}

.pt-80 {
  padding-top: 80px;
}

.fz-40 {
  font-size: 40px;
}

.banner-1 {
  background: var(--cmnbg);
  padding-top: 80px;
  height: 790px;
  z-index: 1;
  position: relative;
}
.banner-1 .banner-v1-content h1 {
  font-size: 70px;
  line-height: 82px;
  color: var(--black);
  margin-bottom: 20px;
}
.banner-1 .banner-v1-content h1 .text-layer {
  position: absolute;
  top: 0;
  left: -30px;
}
.banner-1 .banner-v1-content h1 span {
  font-size: 70px;
  line-height: 82px;
}
.banner-1 .banner-v1-content h1 .title-explore {
  font-weight: 400;
}
.banner-1 .banner-v1-content h1 .text-sount {
  color: var(--p3-clr);
}
.banner-1 .banner-v1-content p {
  font-size: 16px;
  max-width: 430px;
  margin: 0 auto 24px;
}
.banner-1 .banner-v1-content .theme-btn:hover::before {
  background: var(--p5-clr);
}
.banner-1 .banner-shape-thumb2 {
  margin-top: 240px;
  animation: updown1 2s linear infinite;
}
.banner-1 .banner-shape-thumb1 {
  animation: updown1 3s linear infinite;
}
.banner-1 .rainbow-shape {
  position: absolute;
  bottom: -81px;
  left: 0;
  z-index: -1;
}
.banner-1 .left-ring {
  position: absolute;
  top: 60px;
  left: 0;
  animation: updownq1 4s linear infinite;
}
.banner-1 .right-ring {
  position: absolute;
  right: 0;
  bottom: 160px;
  animation: updown1 4s linear infinite;
}
.banner-1 .global-upen {
  position: absolute;
  top: 100px;
  right: 20%;
  animation: skw1 2s linear infinite;
}
@media (max-width: 1399px) {
  .banner-1 .banner-shape-thumb2,
  .banner-1 .banner-shape-thumb1 {
    width: 100%;
  }
  .banner-1 .banner-shape-thumb2 img,
  .banner-1 .banner-shape-thumb1 img {
    width: 100%;
  }
  .banner-1 .rainbow-shape {
    position: absolute;
    bottom: -121px;
    left: 0;
    z-index: -1;
  }
}
@media (max-width: 1199px) {
  .banner-1 .banner-v1-content h1 {
    font-size: 60px;
    line-height: 72px;
    color: var(--black);
    margin-bottom: 20px;
  }
  .banner-1 .banner-v1-content h1 span {
    font-size: 60px;
    line-height: 72px;
  }
  .banner-1 .banner-v1-content .theme-btn {
    padding: 10px 29px;
  }
  .banner-1 .rainbow-shape {
    bottom: -221px;
    left: 0;
  }
  .banner-1 .global-upen {
    display: none;
  }
}
@media (max-width: 991px) {
  .banner-1 {
    padding-top: 50px;
    height: initial;
  }
  .banner-1 .rainbow-shape {
    display: none;
  }
  .banner-1 .banner-shape-thumb2 {
    margin-top: 100px;
    margin-bottom: 40px;
  }
  .banner-1 .banner-shape-thumb1 {
    margin-top: 30px;
  }
  .banner-1 .left-ring {
    display: none;
  }
}
@media (max-width: 767px) {
  .banner-1 .banner-v1-content h1 {
    font-size: 48px;
    line-height: 62px;
    margin-bottom: 16px;
  }
  .banner-1 .banner-v1-content h1 span {
    font-size: 48px;
    line-height: 62px;
  }
}
@media (max-width: 575px) {
  .banner-1 .banner-shape-thumb2 {
    margin-top: 50px;
    margin-bottom: 40px;
    width: 140px;
  }
  .banner-1 .banner-shape-thumb1 {
    margin-top: 0px;
    width: 150px;
    position: absolute;
    right: 0;
    bottom: 30px;
  }
  .banner-1 .right-ring {
    right: -60px;
    bottom: 160px;
  }
  .banner-1 .global-upen {
    display: block;
    bottom: 20px;
    top: initial;
    right: initial;
    left: 40%;
  }
}
@media (max-width: 500px) {
  .banner-1 .banner-v1-content {
    padding: 0px 5px;
  }
  .banner-1 .banner-v1-content h1 {
    font-size: 40px;
    line-height: 52px;
    margin-bottom: 16px;
  }
  .banner-1 .banner-v1-content h1 span {
    font-size: 40px;
    line-height: 52px;
  }
}

@keyframes updown1 {
  50% {
    transform: translateY(-10px);
  }
}
@keyframes updownq1 {
  50% {
    transform: translatex(-40px);
  }
}
@keyframes skw1 {
  50% {
    transform: skewX(5deg);
  }
}
.bannerv2-section {
  padding: 265px 0 180px;
  background: url(../img/abanner/banner-2bg.png) no-repeat center center;
  background-size: cover;
  z-index: 1;
}
.bannerv2-section .banner-kyte {
  position: absolute;
  bottom: 100px;
  left: 40px;
  animation: lf 2s linear infinite;
}
.bannerv2-section .banner-shape {
  position: absolute;
  right: -8px;
  bottom: -8px;
}
.bannerv2-section .banner-shadow {
  position: absolute;
  bottom: 0;
  left: 0;
}
.bannerv2-section .herov2-content h1 {
  color: var(--black);
}
.bannerv2-section .herov2-content .small-aregtengle {
  position: absolute;
  bottom: 18px;
  left: 170px;
  animation: rots1 2s linear infinite;
}
@media (max-width: 1899px) {
  .bannerv2-section {
    padding: 220px 0 170px;
  }
  .bannerv2-section .banner-shape {
    position: absolute;
    right: -8px;
    bottom: -8px;
    width: 900px;
  }
  .bannerv2-section .banner-kyte {
    bottom: 40px;
    left: -40px;
  }
}
@media (max-width: 1600px) {
  .bannerv2-section {
    padding: 220px 0 150px;
  }
  .bannerv2-section .banner-shape {
    position: absolute;
    right: -8px;
    bottom: -8px;
    width: 750px;
  }
}
@media (max-width: 1399px) {
  .bannerv2-section {
    padding: 190px 0 150px;
  }
  .bannerv2-section .herov2-content h1 {
    font-size: 48px;
    line-height: 63px;
  }
  .bannerv2-section .banner-shape {
    position: absolute;
    right: -8px;
    bottom: -8px;
    width: 680px;
  }
  .bannerv2-section .banner-kyte {
    bottom: -80px;
    left: -40px;
  }
}
@media (max-width: 1199px) {
  .bannerv2-section {
    padding: 150px 0 150px;
  }
  .bannerv2-section .herov2-content h1 {
    font-size: 42px;
    line-height: 55px;
  }
  .bannerv2-section .banner-shape {
    position: absolute;
    right: -8px;
    bottom: -8px;
    width: 580px;
    z-index: -1;
  }
  .bannerv2-section .banner-kyte {
    bottom: -80px;
    left: -40px;
  }
}
@media (max-width: 991px) {
  .bannerv2-section {
    padding: 150px 0 150px;
  }
  .bannerv2-section .herov2-content h1 {
    font-size: 42px;
    line-height: 55px;
  }
  .bannerv2-section .herov2-content .small-aregtengle {
    bottom: 18px;
    left: 150px;
    width: 60px;
  }
  .bannerv2-section .banner-shape {
    right: -8px;
    bottom: -8px;
    width: 580px;
    z-index: -1;
    opacity: 0.4;
  }
  .bannerv2-section .banner-kyte {
    bottom: 0px;
    left: 0px;
    width: 190px;
  }
}
@media (max-width: 767px) {
  .bannerv2-section {
    padding: 140px 0 150px;
  }
  .bannerv2-section .herov2-content h1 {
    font-size: 42px;
    line-height: 55px;
  }
  .bannerv2-section .herov2-content .small-aregtengle {
    bottom: 18px;
    left: 150px;
    width: 60px;
  }
  .bannerv2-section .banner-shape {
    right: -8px;
    bottom: -8px;
    width: 580px;
    z-index: -1;
    opacity: 0.2;
  }
  .bannerv2-section .banner-kyte {
    bottom: 0px;
    left: 0px;
    width: 190px;
  }
}
@media (max-width: 500px) {
  .bannerv2-section {
    padding: 140px 0 150px;
  }
  .bannerv2-section .herov2-content h1 {
    font-size: 36px;
    line-height: 48px;
  }
}

.breadcrumnd-banner {
  padding: 60px 0 60px;
}
.breadcrumnd-banner .breadcrumnd-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.breadcrumnd-banner .breadcrumnd-wrapper .breadcrumnd-content .bread-list li {
  color: var(--black);
  font-size: 28px;
  font-weight: 700;
  font-family: "Nunito", sans-serif;
}
.breadcrumnd-banner .breadcrumnd-wrapper .breadcrumnd-content .bread-list li a {
  color: var(--black);
  font-weight: 700;
  font-size: 28px;
  font-family: "Nunito", sans-serif;
}
.breadcrumnd-banner .breadcrumnd-wrapper .breadcrumnd-content .bread-list li i {
  font-size: 20px;
  line-height: 0;
}
.breadcrumnd-banner .breadcrumnd-wrapper .breadcrumnd-thumb {
  max-width: 315px;
}
.breadcrumnd-banner .breadcrumnd-wrapper .breadcrumnd-thumb .mimg {
  width: 100%;
}
.breadcrumnd-banner .breadcrumnd-wrapper .breadcrumnd-thumb .bread-child {
  position: absolute;
  right: 0;
  bottom: 0;
  animation: lf 2s linear infinite;
}
.breadcrumnd-banner .breadcrumnd-wrapper .breadcrumnd-thumb .bread-cat {
  position: absolute;
  left: -30px;
  bottom: 20px;
  animation: updown 2s linear infinite;
}
@media (max-width: 991px) {
  .breadcrumnd-banner .breadcrumnd-wrapper .breadcrumnd-content h1 {
    font-size: 55px;
  }
}
@media (max-width: 767px) {
  .breadcrumnd-banner .breadcrumnd-wrapper {
    display: grid;
    gap: 40px;
    justify-content: center;
  }
  .breadcrumnd-banner .breadcrumnd-wrapper .breadcrumnd-content h1 {
    text-align: center;
    font-size: 36px;
  }
  .breadcrumnd-banner .breadcrumnd-wrapper .breadcrumnd-content .bread-list {
    justify-content: center;
  }
  .breadcrumnd-banner .breadcrumnd-wrapper .breadcrumnd-content .bread-list li {
    font-size: 18px;
    font-weight: 500;
  }
  .breadcrumnd-banner .breadcrumnd-wrapper .breadcrumnd-content .bread-list li a {
    font-weight: 700;
    font-size: 18px;
  }
  .breadcrumnd-banner .breadcrumnd-wrapper .breadcrumnd-content .bread-list li i {
    font-size: 14px;
    line-height: 0;
  }
  .breadcrumnd-banner .breadcrumnd-wrapper .breadcrumnd-thumb {
    max-width: 315px;
  }
  .breadcrumnd-banner .breadcrumnd-wrapper .breadcrumnd-thumb .mimg {
    width: 100%;
  }
}
@media (max-width: 767px) {
  .breadcrumnd-banner .breadcrumnd-wrapper .breadcrumnd-thumb {
    max-width: 240px;
  }
  .breadcrumnd-banner .breadcrumnd-wrapper .breadcrumnd-thumb .mimg {
    width: 100%;
  }
}

.marquee-wrapper {
  position: relative;
  overflow: hidden;
  white-space: nowrap;
  z-index: 9;
  margin-top: -15px;
  margin-bottom: -30px;
}

.text-slider {
  font-size: 60px;
  height: 100px;
  line-height: 90px;
  font-weight: 700;
  text-transform: capitalize;
  color: var(--p2-clr);
}
.text-slider:not(:last-child) {
  margin-right: 30px;
}
@media (max-width: 767px) {
  .text-slider {
    font-size: 36px;
  }
}
.text-slider img {
  margin-bottom: 10px;
}
.text-slider.text-color {
  color: var(--header);
}
.text-slider.text-color-2 {
  color: var(--header);
  font-size: 50px;
}

.marquee-inner {
  position: absolute;
  display: inline-flex;
  width: 200%;
}

.marquee-list {
  float: left;
  width: 50%;
}

.marquee-item {
  float: left;
  transition: animation 0.2s ease-out;
}

.marquee-inner.to-left {
  animation: marqueeLeft 25s linear infinite;
}

@keyframes marqueeLeft {
  0% {
    left: 0;
  }
  100% {
    left: -100%;
  }
}
.marquee-inner.to-right {
  animation: marqueeRight 25s linear infinite;
}

@keyframes marqueeRight {
  0% {
    right: 0;
  }
  100% {
    right: -100%;
  }
}
.marque-section-2 {
  position: relative;
}
@media (max-width: 1199px) {
  .marque-section-2 {
    margin-bottom: -20px;
  }
}
@media (max-width: 991px) {
  .marque-section-2 {
    margin-bottom: -40px;
  }
}

.mean-container a.meanmenu-reveal {
  display: none;
}

.mean-container .mean-nav {
  background: none;
  margin-top: 0;
}

.mean-container .mean-bar {
  padding: 0;
  min-height: auto;
  background: none;
}

.mean-container .mean-nav > ul {
  padding: 0;
  margin: 0;
  width: 100%;
  list-style-type: none;
  display: block !important;
}
.mean-container .mean-nav > ul .homemenu-items {
  display: flex;
  align-items: center;
  gap: 30px;
  justify-content: space-between;
}
@media (max-width: 1199px) {
  .mean-container .mean-nav > ul .homemenu-items {
    flex-wrap: wrap;
  }
}
.mean-container .mean-nav > ul .homemenu-items .homemenu {
  position: relative;
}
@media (max-width: 1199px) {
  .mean-container .mean-nav > ul .homemenu-items .homemenu {
    max-width: 300px;
    text-align: center;
    margin: 0 auto;
    border: 1px solid var(--border);
    padding: 10px;
  }
}
.mean-container .mean-nav > ul .homemenu-items .homemenu .homemenu-thumb {
  position: relative;
}
.mean-container .mean-nav > ul .homemenu-items .homemenu .homemenu-thumb .demo-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60%;
  gap: 10px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s ease-in-out;
  margin-top: 20px;
}
.mean-container .mean-nav > ul .homemenu-items .homemenu .homemenu-thumb .demo-button .theme-btn {
  padding: 12px 20px;
  color: var(--white) !important;
  width: initial;
  font-size: 16px;
  text-align: center;
  border-radius: 0;
}
.mean-container .mean-nav > ul .homemenu-items .homemenu .homemenu-thumb .demo-button .theme-btn:hover {
  color: var(--white) !important;
}
.mean-container .mean-nav > ul .homemenu-items .homemenu .homemenu-thumb::before {
  background: -webkit-gradient(linear, left top, left bottom, from(rgba(20, 19, 19, 0)), to(#5e5ef6));
  background: linear-gradient(to bottom, rgba(99, 92, 92, 0) 0%, #252527 100%);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  overflow: hidden;
  opacity: 0;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  content: "";
}
.mean-container .mean-nav > ul .homemenu-items .homemenu .homemenu-thumb:hover::before {
  visibility: visible;
  opacity: 1;
}
.mean-container .mean-nav > ul .homemenu-items .homemenu .homemenu-thumb:hover .demo-button {
  opacity: 1;
  visibility: visible;
  margin-top: 0;
}
.mean-container .mean-nav > ul .homemenu-items .homemenu .homemenu-thumb:hover .homemenu-btn {
  opacity: 1;
  visibility: visible;
  bottom: 50%;
  transform: translateY(50%);
}
.mean-container .mean-nav > ul .homemenu-items .homemenu .homemenu-thumb img {
  width: 100%;
}
.mean-container .mean-nav > ul .homemenu-items .homemenu .homemenu-title {
  text-align: center;
  margin: 15px auto;
  display: inline-block;
  font-size: 16px;
}

.mean-container a.meanmenu-reveal {
  display: none !important;
}

.mean-container .mean-nav ul li a {
  width: 100%;
  padding: 10px 0;
  color: var(--header);
  font-size: 16px;
  line-height: 1.5;
  font-weight: 500;
  text-transform: capitalize;
  border-bottom: 1px solid var(--border) !important;
  border: none;
}
.mean-container .mean-nav ul li a:hover {
  color: var(--p2-clr);
}

.mean-container .mean-nav ul li a:last-child {
  border-bottom: 0;
}

.mean-container .mean-nav ul li a:hover {
  color: var(--theme2);
}

.mean-container .mean-nav ul li a.mean-expand {
  margin-top: 5px;
  padding: 0 !important;
}

.mean-container .mean-nav ul li > a > i {
  display: none;
}

.mean-container .mean-nav ul li > a.mean-expand i {
  display: inline-block;
  font-size: 18px;
}

.mean-container .mean-nav > ul > li:first-child > a {
  border-top: 0;
}

.mean-container .mean-nav ul li a.mean-expand.mean-clicked i {
  transform: rotate(45deg);
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transition: all 0.4s ease-in-out;
}

.mean-container .mean-nav ul li .mega-menu li a {
  height: 200px;
  width: 100%;
  padding: 0;
  border-top: 0;
  margin-bottom: 20px;
}

.news-card-items {
  background-color: var(--white);
  border-radius: 10px;
}
.news-card-items .news-image {
  position: relative;
  z-index: 2;
  overflow: hidden;
  width: 100%;
  border-radius: 10px;
}
.news-card-items .news-image .post-date {
  position: absolute;
  bottom: 24px;
  right: 24px;
  padding: 4px 5px;
  border-radius: 8px;
  width: 70px;
  height: 72px;
  text-align: center;
}
.news-card-items .news-image .post-date span {
  color: var(--white);
  font-size: 20px;
  font-weight: 700;
  font-family: "Jost", sans-serif;
}
.news-card-items .news-image img {
  width: 100%;
  height: 100%;
  display: block;
}
.news-card-items .news-image .news-layer-wrapper {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  transition: 0.5s;
}
.news-card-items .news-image .news-layer-wrapper .news-layer-image {
  width: 25%;
  height: 100%;
  transition: 0.5s;
  background-size: cover;
}
.news-card-items .news-image .news-layer-wrapper .news-layer-image:nth-child(1) {
  background-position: 0;
  transition-delay: 0;
}
.news-card-items .news-image .news-layer-wrapper .news-layer-image:nth-child(2) {
  background-position: 33.33%;
  transition-delay: 0.1s;
}
.news-card-items .news-image .news-layer-wrapper .news-layer-image:nth-child(3) {
  background-position: 66.66%;
  transition-delay: 0.2s;
}
.news-card-items .news-image .news-layer-wrapper .news-layer-image:nth-child(4) {
  background-position: 100%;
  transition-delay: 0.3s;
}
.news-card-items:hover h4 a {
  color: var(--p5-clr);
}
.news-card-items:hover .news-image .news-layer-wrapper .news-layer-image {
  transform: translateY(-100%);
}
@media (max-width: 1399px) {
  .news-card-items .news-image .post-date {
    bottom: 18px;
    right: 18px;
    width: 58px;
    height: 58px;
  }
  .news-card-items .news-image .post-date span {
    font-size: 16px;
    font-weight: 700;
    line-height: 18px;
  }
  .news-card-items .news-image img {
    width: 100%;
    height: 100%;
    display: block;
  }
}

.car-element {
  position: absolute;
  left: 90px;
  bottom: 150px;
  animation: lf 2s linear infinite;
}
@media (max-width: 1600px) {
  .car-element {
    left: 10px;
    bottom: 150px;
  }
}
@media (max-width: 1399px) {
  .car-element {
    left: 10px;
    bottom: 20px;
  }
}

.carft-section .carft-man {
  position: absolute;
  right: 50px;
  bottom: 50px;
  animation: updown 2s linear infinite;
}
@media (max-width: 1399px) {
  .carft-section .carft-man {
    right: 20px;
    bottom: 20px;
  }
}
@media (max-width: 1199px) {
  .carft-section .carft-man {
    right: 20px;
    bottom: 20px;
    width: 110px;
  }
}
@media (max-width: 1199px) {
  .carft-section .carft-man {
    right: initial;
    left: 14px;
    bottom: 10px;
    width: 80px;
  }
}

.carft-content .protfolio-tabs .tablinks {
  gap: 18px;
  flex-wrap: wrap;
  justify-content: flex-start;
}
.carft-content .protfolio-tabs .tablinks .nav-links .tablink {
  border-radius: 100px;
  border: 1px solid var(--p3-clr);
  color: var(--p3-clr);
}
.carft-content .protfolio-tabs .tablinks .nav-links.active .tablink {
  background: var(--p3-clr);
  color: var(--white);
}

.extra-class-item {
  padding: 30px;
  border: 1px solid #f2f2f2;
}
.extra-class-item:hover a {
  color: var(--p3-clr);
}
.extra-class-item:hover h4 a {
  color: var(--p3-clr);
}

.blog-single-items {
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0px 4.4px 20px -1px rgba(19, 16, 34, 0.05);
}
.blog-single-items .blog-content {
  padding: 30px 40px 40px;
}
.blog-single-items .blog-content h4 {
  font-size: 28px;
}
.blog-single-items .blog-content h4 a {
  color: var(--black);
}
.blog-single-items .blog-content ul {
  display: flex;
  gap: 30px;
  align-items: center;
}
.blog-single-items .blog-content .theme-btn {
  border: 2px solid var(--Gray-200, #f2f2f2);
  margin-bottom: 20px;
}
.blog-single-items .blog-content .theme-btn:hover {
  color: var(--white) !important;
}
.blog-single-items .blog-content .theme-btn:hover span {
  color: var(--white) !important;
}
.blog-single-items:hover h4 a {
  color: var(--p5-clr);
}
@media (max-width: 1199px) {
  .blog-single-items .blog-content {
    padding: 24px 20px 24px;
  }
  .blog-single-items .blog-content h4 {
    font-size: 26px;
  }
  .blog-single-items .blog-content ul {
    display: flex;
    gap: 7px 20px;
    flex-wrap: wrap;
  }
}
@media (max-width: 500px) {
  .blog-single-items .blog-content {
    padding: 24px 20px 24px;
  }
  .blog-single-items .blog-content h4 {
    font-size: 20px;
  }
  .blog-single-items .blog-content ul {
    display: flex;
    gap: 7px 20px;
    flex-wrap: wrap;
  }
}

.cus-pagination {
  display: flex;
  gap: 15px;
  justify-content: center;
  align-items: center;
}
.cus-pagination li a {
  width: 60px;
  height: 60px;
  justify-content: center;
  align-items: center;
  display: flex;
  border-radius: 10px;
  border: 1px solid var(--200, #ccc);
  transition: all 0.4s;
  font-weight: 500;
  font-size: 24px;
  color: var(--black);
  font-family: "Jost", sans-serif;
}
.cus-pagination li a:hover {
  background: var(--p5-clr);
  color: var(--white) !important;
}
.cus-pagination li a:hover h4,
.cus-pagination li a:hover i {
  color: var(--white);
}
@media (max-width: 1199px) {
  .cus-pagination li a {
    width: 42px;
    height: 42px;
  }
}

.blog-right-wrap {
  display: grid;
  gap: 40px;
}
.blog-right-wrap .blog-right-common {
  box-shadow: 0px 4.8px 24.4px -6px rgba(19, 16, 34, 0.1);
  border-radius: 5px;
  padding: 40px;
}
.blog-right-wrap .blog-right-common .cmn-title {
  display: inline-block;
  position: relative;
  color: var(--black);
}
.blog-right-wrap .blog-right-common .cmn-title::before {
  position: absolute;
  right: -24px;
  bottom: 5px;
  border-radius: 5px;
  background: var(--p4-clr);
  content: "";
  width: 22px;
  height: 2px;
}
.blog-right-wrap .blog-right-common form {
  border-radius: 10px;
  border: 2px solid var(--Gray-200, #f2f2f2);
  display: flex;
  align-items: center;
}
.blog-right-wrap .blog-right-common form input {
  width: 100%;
  padding: 16px 20px;
  border: unset;
  color: var(--text);
}
.blog-right-wrap .blog-right-common form button {
  padding-right: 20px;
}
.blog-right-wrap .blog-right-common form button i {
  color: var(--p5-clr);
}
.blog-right-wrap .blog-right-common .blog-category {
  display: grid;
  gap: 20px;
}
.blog-right-wrap .blog-right-common .blog-category li a {
  border-radius: 10px;
  border: 2px solid var(--Gray-200, #f2f2f2);
  padding: 10px 20px;
  color: var(--text);
  transition: all 0.4s;
}
.blog-right-wrap .blog-right-common .blog-category li a:hover {
  background: var(--p4-clr);
  border-color: var(--p4-clr);
  color: var(--white);
}
.blog-right-wrap .blog-right-common .blog-category li a:hover span {
  color: var(--white);
}
.blog-right-wrap .blog-right-common .recent-post-blog {
  display: grid;
  gap: 20px;
}
.blog-right-wrap .blog-right-common .recent-post-blog li {
  border-radius: 10px;
  border: 2px solid var(--Gray-200, #f2f2f2);
  padding: 20px 24px;
}
.blog-right-wrap .blog-right-common .recent-post-blog li span {
  font-size: 14px;
  color: var(--text);
}
.blog-right-wrap .blog-right-common .recent-post-blog li .dots {
  width: 5px;
  height: 5px;
  display: block;
  border-radius: 50%;
  background: var(--p5-clr);
}
.blog-right-wrap .blog-right-common .recent-post-blog li a {
  margin-top: 5px;
  display: block;
  font-size: 20px;
  font-weight: 700;
  color: var(--black);
  font-family: "Jost", sans-serif;
  line-height: 24px;
  transition: all 0.4s;
}
.blog-right-wrap .blog-right-common .recent-post-blog li:hover a {
  color: var(--p5-clr);
}
.blog-right-wrap .blog-right-common .tag-blog {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}
.blog-right-wrap .blog-right-common .tag-blog li a {
  color: var(--text);
  padding: 5px 10px;
  border-radius: 5px;
  background: #fff0e5;
  display: inline-block;
  transition: all 0.4s;
}
.blog-right-wrap .blog-right-common .tag-blog li a:hover {
  background: var(--p5-clr);
  color: var(--white);
}
.blog-right-wrap .blog-profile img {
  border-radius: 5px;
  margin-bottom: 20px;
}
.blog-right-wrap .blog-profile h4 {
  margin-bottom: 18px;
}
.blog-right-wrap .blog-profile p {
  margin-bottom: 28px;
}
.blog-right-wrap .blog-profile .social-wrapper {
  gap: 14px;
}
.blog-right-wrap .blog-profile .social-wrapper a {
  background: var(--cmnbg);
  transition: all 0.4s;
}
.blog-right-wrap .blog-profile .social-wrapper a i {
  transition: all 0.4s;
  color: var(--black);
}
.blog-right-wrap .blog-profile .social-wrapper a svg {
  transition: all 0.4s;
  stroke: var(--black);
}
.blog-right-wrap .blog-profile .social-wrapper a:hover {
  background: var(--p4-clr);
}
.blog-right-wrap .blog-profile .social-wrapper a:hover i {
  transition: all 0.4s;
  color: var(--white);
}
.blog-right-wrap .blog-profile .social-wrapper a:hover svg {
  transition: all 0.4s;
  stroke: var(--white);
}
@media (max-width: 1199px) {
  .blog-right-wrap {
    gap: 30px;
  }
  .blog-right-wrap .blog-right-common {
    padding: 20px;
  }
  .blog-right-wrap .blog-right-common .blog-category {
    display: grid;
    gap: 10px;
  }
  .blog-right-wrap .blog-right-common .recent-post-blog {
    gap: 15px;
  }
  .blog-right-wrap .blog-right-common .recent-post-blog li {
    padding: 14px 18px;
  }
  .blog-right-wrap .blog-right-common .recent-post-blog li a {
    margin-top: 5px;
    font-size: 18px;
    font-weight: 700;
    line-height: 20px;
  }
  .blog-right-wrap .blog-profile img {
    border-radius: 5px;
    margin-bottom: 16px;
  }
  .blog-right-wrap .blog-profile h4 {
    margin-bottom: 14px;
  }
  .blog-right-wrap .blog-profile p {
    margin-bottom: 20px;
  }
}
@media (max-width: 991px) {
  .blog-right-wrap {
    gap: 20px;
  }
}
@media (max-width: 575px) {
  .blog-right-wrap .blog-right-common {
    padding: 16px;
  }
}

.blog-content-custom ul {
  display: flex;
  align-items: center;
  gap: 30px;
}

.blog-single-items-details {
  box-shadow: none;
}

.quote-text-box {
  border-radius: 10px;
  background: var(--white);
  box-shadow: 0px 4.8px 24.4px -6px rgba(19, 16, 34, 0.1);
  padding: 39px 60px;
}
.quote-text-box .cmn-title {
  display: inline-block;
  position: relative;
  color: var(--black);
}
.quote-text-box .cmn-title::before {
  position: absolute;
  right: -20px;
  bottom: 5px;
  border-radius: 5px;
  background: var(--p4-clr);
  content: "";
  width: 15px;
  height: 2px;
}
@media (max-width: 1199px) {
  .quote-text-box {
    padding: 28px 24px;
  }
}

@media (max-width: 1399px) {
  .details-middle-thumbblog .thumb1 {
    width: 520px;
  }
  .details-middle-thumbblog .thumb1 img {
    width: 100%;
  }
  .details-middle-thumbblog .thumb2 {
    width: 300px;
  }
  .details-middle-thumbblog .thumb2 img {
    width: 100%;
  }
}

.listing-custom-text {
  gap: 70px;
}
@media (max-width: 767px) {
  .listing-custom-text {
    gap: 10px;
    flex-wrap: wrap;
  }
}

.details-tag .tag-blog {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}
.details-tag .tag-blog li a {
  color: var(--text);
  padding: 5px 10px;
  border-radius: 5px;
  background: #fff0e5;
  display: inline-block;
  transition: all 0.4s;
}
.details-tag .tag-blog li a:hover {
  background: var(--p5-clr);
  color: var(--white);
}
@media (max-width: 1199px) {
  .details-tag .tag-blog {
    gap: 7px 14px;
  }
}

.praview-social-adjust {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 7px;
  border-top: 2px solid #f2f2f2;
  border-bottom: 2px solid #f2f2f2;
  padding: 20px 0;
}
.praview-social-adjust .next-pre {
  font-size: 20px;
  color: var(--black);
  font-family: "Jost", sans-serif;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.4s;
}
.praview-social-adjust .next-pre i {
  font-size: 18px;
  transition: all 0.4s;
}
.praview-social-adjust .next-pre:hover {
  color: var(--p3-clr);
}
.praview-social-adjust .next-pre:hover i {
  color: var(--p3-clr);
}
.praview-social-adjust .social-wrapper {
  gap: 14px;
}
.praview-social-adjust .social-wrapper a {
  border: 1px solid rgba(237, 20, 91, 0.2);
  transition: all 0.4s;
  border-radius: 50%;
  width: 40px;
  height: 40px;
}
.praview-social-adjust .social-wrapper a i {
  transition: all 0.4s;
  color: var(--black);
}
.praview-social-adjust .social-wrapper a svg {
  transition: all 0.4s;
  stroke: var(--black);
}
.praview-social-adjust .social-wrapper a:hover {
  background: var(--p3-clr);
}
.praview-social-adjust .social-wrapper a:hover i {
  transition: all 0.4s;
  color: var(--white);
}
.praview-social-adjust .social-wrapper a:hover svg {
  transition: all 0.4s;
  stroke: var(--white);
}
@media (max-width: 575px) {
  .praview-social-adjust {
    display: grid;
    justify-content: center;
    gap: 20px;
  }
  .praview-social-adjust .next-pre {
    justify-content: center;
  }
}

.write-comment h4 {
  font-size: 28px;
}
.write-comment form .comment-grp {
  display: flex;
  align-items: center;
  border-radius: 10px;
  border: 2px solid var(--Gray-200, #f2f2f2);
}
.write-comment form .comment-grp textarea,
.write-comment form .comment-grp input {
  width: 100%;
  border: unset;
  padding: 10px 20px;
  color: var(--text);
}
.write-comment form .comment-grp i {
  padding-right: 20px;
  color: var(--p2-clr);
}
.write-comment form .text-aras textarea {
  width: 100%;
}
.write-comment form .text-aras .enves {
  position: absolute;
  top: 18px;
  right: 0px;
}

.preloader {
  align-items: center;
  cursor: default;
  display: flex;
  height: 100%;
  justify-content: center;
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  z-index: 9999999;
  background: rgba(243, 243, 243, 0.87);
}
.preloader span {
  position: absolute;
}
.preloader span:nth-child(1) {
  top: 0;
  right: -100px;
  width: 100px;
  height: 100px;
  background: rgba(247, 149, 30, 0.631372549);
  animation: zints1 2s linear infinite;
}
.preloader span:nth-child(2) {
  bottom: 0;
  left: -100px;
  width: 100px;
  height: 100px;
  background: rgba(247, 149, 30, 0.631372549);
  animation: zints2 2s linear infinite;
}
.preloader span:nth-child(3) {
  animation: updown 2s linear infinite;
}

.search-wrap {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999999;
  background-color: rgba(255, 255, 255, 0.9);
}
.search-wrap .search-inner {
  position: relative;
  width: 100%;
  height: 100%;
}
.search-wrap .search-cell {
  position: absolute;
  top: 50%;
  width: 100%;
  transform: translateY(-50%);
}
.search-wrap .search-field-holder {
  width: 50%;
  margin: auto;
  position: relative;
  animation: slideInUp 0.3s;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .search-wrap .search-field-holder {
    width: 70%;
  }
}
@media (max-width: 575px) {
  .search-wrap .search-field-holder {
    width: 80%;
  }
}
.search-wrap .main-search-input {
  width: 100%;
  height: 40px;
  border: 0;
  padding: 0 50px;
  text-transform: capitalize;
  background: transparent;
  font-size: 16px;
  color: var(--p2-clr);
  border-bottom: 0.5px solid var(--p2-clr);
  text-align: center;
  letter-spacing: 2px;
}

@media (max-width: 575px) {
  .search-wrap .main-search-input {
    height: 50px;
    padding: 0 0;
    line-height: 50px;
    font-size: 18px;
  }
}
.search-wrap input.form-control,
.search-wrap input.form-control:focus {
  background-color: var(--p2-clr);
}

input.main-search-input::placeholder {
  color: var(--p2-clr);
  opacity: 1;
  font-size: 18px;
}

@media (max-width: 575px) {
  input.main-search-input::placeholder {
    font-size: 18px;
  }
}
.search-close {
  position: absolute;
  top: 50px;
  right: 50px;
  font-size: 30px;
  color: var(--p2-clr);
  cursor: pointer;
}

.pricing-items {
  box-shadow: -20px 4.8px 24.4px -6px rgba(19, 16, 34, 0.1);
  transition: all 0.4s;
  border-radius: 10px;
}
.pricing-items .pricing-head {
  color: var(--black);
  font-size: 28px;
  font-weight: 600;
  padding: 14px 8px;
  text-align: center;
  background: #fff0e5;
  font-family: "Jost", sans-serif;
}
.pricing-items .pricing-body {
  padding: 30px 40px 40px;
}
.pricing-items .pricing-body .theme-btn {
  border: 1px solid var(--p2-clr);
  color: var(--black);
  border-radius: 10px;
}
.pricing-items .pricing-body .theme-btn:hover {
  background: var(--p2-clr);
  color: var(--white);
}
.pricing-items .pricing-body .theme-btn:hover::before {
  background: var(--p2-clr);
}
.pricing-items .pricing-body .pricing-listing li i {
  font-size: 14px;
}
.pricing-items .pricing-body .pricing-listing li .cros {
  border-radius: 50%;
  font-size: 16px;
  color: #fff0e5;
  background: #0a6375;
}
.pricing-items .price-title {
  color: var(--p4-clr);
}
.pricing-items .price-title .mos {
  color: var(--black);
  font-size: 24px;
}
.pricing-items.cart-active .pricing-body .theme-btn {
  background: var(--p2-clr);
  color: var(--white);
}
.pricing-items.cart-active .pricing-head {
  background: var(--p2-clr);
  color: var(--white);
}
@media (max-width: 1600px) {
  .pricing-items .pricing-body {
    padding: 20px 18px 20px;
  }
}

.student-count-item {
  padding: 44px 20px 44px 70px;
  box-shadow: 0px 4.8px 24.4px -6px rgba(19, 16, 34, 0.1);
  border-radius: 10px;
  display: flex;
  align-items: center;
  gap: 40px;
}
.student-count-item .content h1 {
  font-size: 70px;
  margin-bottom: 8px;
  -webkit-text-stroke-width: 2px;
  -webkit-text-stroke-color: var(--pra);
  color: transparent;
}
.student-count-item .content h1 span {
  font-size: 70px;
  -webkit-text-stroke-width: 2px;
  -webkit-text-stroke-color: var(--pra);
  color: transparent;
}
.student-count-item .content p {
  font-size: 20px;
  font-weight: 700;
  font-family: "Jost", sans-serif;
  color: var(--black);
}
@media (max-width: 1199px) {
  .student-count-item {
    padding: 30px 20px 30px 30px;
    gap: 20px;
  }
  .student-count-item .icon {
    width: 70px;
  }
  .student-count-item .icon img {
    width: 100%;
  }
  .student-count-item .content h1 {
    font-size: 48px;
  }
  .student-count-item .content h1 span {
    font-size: 48px;
  }
  .student-count-item .content p {
    font-size: 18px;
  }
}
@media (max-width: 767px) {
  .student-count-item {
    padding: 24px 16px 24px 18px;
    gap: 18px;
  }
  .student-count-item .icon {
    width: 50px;
  }
  .student-count-item .icon img {
    width: 100%;
  }
  .student-count-item .content h1 {
    font-size: 38px;
  }
  .student-count-item .content h1 span {
    font-size: 34px;
  }
  .student-count-item .content p {
    font-size: 18px;
  }
}
@media (max-width: 575px) {
  .student-count-item {
    padding: 24px 16px 24px 18px;
    gap: 18px;
    justify-content: center;
  }
  .student-count-item .icon {
    width: 50px;
  }
  .student-count-item .icon img {
    width: 100%;
  }
  .student-count-item .content h1 {
    font-size: 38px;
  }
  .student-count-item .content h1 span {
    font-size: 34px;
  }
  .student-count-item .content p {
    font-size: 18px;
  }
}

.section-title {
  z-index: 1;
}
.section-title .sub-title {
  margin-bottom: 14px;
  font-size: 19px;
  line-height: 24px;
  font-family: "Bubblegum Sans", sans-serif;
  display: block;
}

.center {
  text-align: center;
  margin: 0 auto;
}

.section-bg {
  background-color: var(--bg);
}

.section-bg-2 {
  background-color: var(--bg2);
}

.section-bg-3 {
  background-color: var(--theme2);
}

.section-padding {
  padding: 120px 0;
}
@media (max-width: 1199px) {
  .section-padding {
    padding: 100px 0;
  }
}
@media (max-width: 991px) {
  .section-padding {
    padding: 80px 0;
  }
}

.space-top {
  padding-top: 120px;
}
@media (max-width: 1199px) {
  .space-top {
    padding-top: 100px;
  }
}
@media (max-width: 991px) {
  .space-top {
    padding-top: 80px;
  }
}

.space-bottom {
  padding-bottom: 120px;
}
@media (max-width: 1199px) {
  .space-bottom {
    padding-bottom: 100px;
  }
}
@media (max-width: 991px) {
  .space-bottom {
    padding-bottom: 80px;
  }
}

.service-sectionv1 {
  position: relative;
}
.service-sectionv1::before {
  position: absolute;
  right: 300px;
  top: 0;
  content: "";
  width: 50%;
  height: 392px;
  background: var(--cmnbg);
  border-radius: 10px;
  z-index: -1;
}
.service-sectionv1 .swiper-slide.swiper-slide-active .program-item {
  background: var(--white);
  box-shadow: 0px 4.8px 24.4px -6px rgba(19, 16, 34, 0.1);
}
.service-sectionv1 .swiper-slide.swiper-slide-active .program-item p,
.service-sectionv1 .swiper-slide.swiper-slide-active .program-item .readmore {
  opacity: 1;
}
.service-sectionv1 .dot-cmn .swiper-pagination-bullet {
  width: 15px;
  height: 5px;
  border-radius: 10px;
  background: #f2f2f2;
  opacity: 1;
}
.service-sectionv1 .dot-cmn .swiper-pagination-bullet-active {
  width: 33px;
  background: var(--p5-clr) !important;
}
.service-sectionv1 .aservice-shape1 {
  position: absolute;
  left: 65px;
  bottom: 10px;
  animation: lf 2s linear infinite;
}
@media (max-width: 1899px) {
  .service-sectionv1 {
    padding: 80px 0 60px;
  }
  .service-sectionv1::before {
    position: absolute;
    right: 0px;
    top: 0;
    content: "";
    width: 1011px;
    height: 392px;
    border-radius: 10px;
    z-index: -1;
  }
}
@media (max-width: 1199px) {
  .service-sectionv1::before {
    width: 100%;
    height: 100%;
  }
}
@media (max-width: 767px) {
  .service-sectionv1 .aservice-shape1 {
    display: none;
  }
}

.service-wrapslide .program-item {
  padding: 32px 30px;
  margin-left: 0;
  background: transparent;
  transition: all 0.5s;
  margin-bottom: 25px;
}
.service-wrapslide .program-item h4 a {
  color: var(--black);
}
.service-wrapslide .program-item p,
.service-wrapslide .program-item .readmore {
  opacity: 0;
}
.service-wrapslide .program-item.active, .service-wrapslide .program-item:hover {
  background: var(--white);
}
.service-wrapslide .program-item.active p,
.service-wrapslide .program-item.active .readmore, .service-wrapslide .program-item:hover p,
.service-wrapslide .program-item:hover .readmore {
  opacity: 1;
}

.clone-component-section .bard-element {
  position: absolute;
  top: 300px;
  right: 100px;
  animation: lf 2s linear infinite;
}
.clone-component-section .product-clode-content .theme-btn {
  padding: 17px 30px;
}
.clone-component-section .product-clode-content .theme-btn::before {
  background: var(--white);
}
.clone-component-section .product-clode-content .theme-btn:hover span {
  color: var(--black);
}
.clone-component-section .product-clode-content .cart-btn {
  border: 2px solid var(--p3-clr);
  padding: 14px 30px;
}
.clone-component-section .product-clode-content .cart-btn::before {
  background: var(--p3-clr);
}
.clone-component-section .product-clode-content .cart-btn:hover span {
  color: var(--white);
}
.clone-component-section .product-title {
  max-width: 450px;
  margin: 0 auto 40px;
}
.clone-component-section .sun-uncle-section {
  display: flex;
  justify-content: center;
}
.clone-component-section .sun-uncle-section .sun-bg {
  z-index: 1;
  position: relative;
}
.clone-component-section .sun-uncle-section .sun-unlce {
  width: 650px;
  height: 700px;
  position: absolute;
  bottom: -150px;
  animation: updown 2s linear infinite;
}
.clone-component-section .sun-uncle-section .sun-unlce img {
  object-fit: contain;
  width: 100%;
  height: 100%;
}
@media (max-width: 1600px) {
  .clone-component-section .sun-uncle-section .sun-unlce {
    width: 650px;
    height: 700px;
    bottom: -220px;
  }
}
@media (max-width: 1399px) {
  .clone-component-section .sun-uncle-section .sun-unlce {
    width: 450px;
    height: 600px;
    bottom: -180px;
  }
  .clone-component-section .product-clode-content .theme-btn {
    padding: 15px 20px;
  }
  .clone-component-section .product-clode-content .cart-btn {
    padding: 12px 20px;
  }
}
@media (max-width: 991px) {
  .clone-component-section .sun-uncle-section .sun-unlce {
    width: 450px;
    height: 600px;
    bottom: -250px;
  }
}
@media (max-width: 575px) {
  .clone-component-section .sun-uncle-section .sun-unlce {
    width: 250px;
    height: 500px;
    bottom: -220px;
  }
}

.extra-sectionv .jerap-element {
  position: absolute;
  left: 0;
  top: 80px;
  z-index: -1;
}
@media (max-width: 1600px) {
  .extra-sectionv .jerap-element {
    left: -50px;
    top: 80px;
    width: 400px;
    object-fit: contain;
  }
}

.common-content-box .custom-title {
  font-size: 28px;
}
@media (max-width: 575px) {
  .common-content-box .custom-title {
    font-size: 19px;
  }
}

.details-class-info {
  border-radius: 10px;
  padding: 30px;
  background: var(--cmnbg);
}
.details-class-info .icon {
  width: 88px;
  height: 88px;
  background: var(--white);
  border-radius: 10px;
  margin-bottom: 20px;
  transition: all 0.5s;
}
.details-class-info .icon img {
  width: 55px;
  object-fit: contain;
}
.details-class-info:hover .icon {
  transform: scale(1.05);
}
@media (max-width: 1199px) {
  .details-class-info {
    padding: 20px;
  }
  .details-class-info .icon {
    width: 68px;
    height: 68px;
    margin-bottom: 16px;
  }
  .details-class-info .icon img {
    width: 45px;
    object-fit: contain;
  }
}

.professional-item {
  position: relative;
  padding: 40px;
  border-radius: 50px 10px;
  background: var(--white);
  box-shadow: 0px 4.8px 24.4px -6px rgba(19, 16, 34, 0.1);
  transition: all 0.4s;
  border: 2px solid var(--white);
}
.professional-item .thumb {
  width: 100%;
  overflow: hidden;
}
.professional-item .thumb img {
  width: 100%;
  border-radius: 50px 10px;
  overflow: hidden;
  transition: all 0.52s;
}
.professional-item .social-wrapper {
  gap: 14px;
}
.professional-item .social-wrapper a {
  background: var(--cmnbg);
  transition: all 0.4s;
}
.professional-item .social-wrapper a i {
  transition: all 0.4s;
  color: var(--black);
}
.professional-item .social-wrapper a svg {
  transition: all 0.4s;
  stroke: var(--black);
}
.professional-item .social-wrapper a:hover {
  background: var(--p3-clr);
}
.professional-item .social-wrapper a:hover i {
  transition: all 0.4s;
  color: var(--white);
}
.professional-item .social-wrapper a:hover svg {
  transition: all 0.4s;
  stroke: var(--white);
}
.professional-item:hover {
  border-color: var(--p4-clr);
}
.professional-item:hover .thumb img {
  transform: scale(1.04);
  border-radius: 10px 70px;
}
@media (max-width: 1600px) {
  .professional-item {
    padding: 20px 20px;
  }
}
@media (max-width: 1199px) {
  .professional-item {
    padding: 18px 16px;
  }
}

.testimonial-innerbox {
  padding: 80px 0;
  position: relative;
}
.testimonial-innerbox::before {
  background: url(../img/atestimonial/testimonial-big.png) no-repeat center center;
  background-size: cover;
  width: 65%;
  height: 100%;
  position: absolute;
  content: "";
  top: 0;
  border-radius: 10px;
}

.testimonial-slidewrap01 .testimonial-item01 {
  padding: 60px;
  border-radius: 10px;
  border: 1px solid #f2f2f2;
  background: var(--white);
  box-shadow: 0px 0px 60px 0px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  margin-bottom: 1px;
}
.testimonial-slidewrap01 .testimonial-item01 .man-info {
  gap: 20px;
}
@media (max-width: 1600px) {
  .testimonial-slidewrap01 .testimonial-item01 {
    padding: 30px;
  }
  .testimonial-slidewrap01 .testimonial-item01 .man-info {
    gap: 10px;
  }
  .testimonial-slidewrap01 .testimonial-item01 .quote-testi {
    width: 26px;
  }
}
@media (max-width: 991px) {
  .testimonial-slidewrap01 .testimonial-item01 {
    padding: 20px;
  }
}
@media (max-width: 991px) {
  .testimonial-slidewrap01 .testimonial-item01 .cont h4 {
    font-size: 16px;
    margin-bottom: 0 !important;
  }
}

.ratting-area i {
  color: var(--p2-clr);
  font-size: 20px;
}/*# sourceMappingURL=main.css.map */