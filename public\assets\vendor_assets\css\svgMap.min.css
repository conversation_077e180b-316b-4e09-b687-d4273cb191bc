/*! svgMap | https://github.com/StephanWagner/svgMap | MIT License | Copyright Stephan <PERSON> | https://stephanwagner.me */
.svgMap-container,.svgMap-wrapper{position:relative}.svgMap-block-zoom-notice{position:absolute;z-index:2;top:100%;left:0;right:0;bottom:0;background:rgba(0,0,0,.8);pointer-events:none;opacity:0;color:#fff;transition:opacity 250ms}.svgMap-block-zoom-notice-active .svgMap-block-zoom-notice{pointer-events:all;top:0;opacity:1}.svgMap-block-zoom-notice>div{position:absolute;top:50%;left:0;right:0;text-align:center;padding:0 32px;transform:translateY(-50%);font-size:28px}@media (max-width:900px){.svgMap-block-zoom-notice>div{font-size:22px}}.svgMap-map-wrapper{position:relative;width:100%;padding-top:50%;overflow:hidden;background:#d9ecff;color:#111}.svgMap-map-wrapper *{box-sizing:border-box}.svgMap-map-wrapper :focus:not(:focus-visible){outline:0}.svgMap-map-wrapper .svgMap-map-image{display:block;position:absolute;top:0;left:0;width:100%;height:100%;margin:0}.svgMap-map-wrapper .svgMap-map-controls-wrapper{position:absolute;bottom:10px;left:10px;z-index:1;display:flex;overflow:hidden;border-radius:2px;box-shadow:0 0 0 2px rgba(0,0,0,.1)}.svgMap-map-wrapper .svgMap-map-controls-move,.svgMap-map-wrapper .svgMap-map-controls-zoom{display:flex;margin-right:5px;overflow:hidden;background:#fff}.svgMap-map-wrapper .svgMap-map-controls-move:last-child,.svgMap-map-wrapper .svgMap-map-controls-zoom:last-child{margin-right:0}.svgMap-map-wrapper .svgMap-control-button{background-color:transparent;border:none;border-radius:0;color:inherit;font:inherit;line-height:inherit;margin:0;padding:0;overflow:visible;text-transform:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;cursor:pointer;width:30px;height:30px;position:relative}.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-button:after,.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-button:before{content:"";position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);background:#666;transition:background-color 250ms}.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-button:before{width:11px;height:3px}@media (hover:hover){.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-button:hover:after,.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-button:hover:before{background:#111}}.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-button:active:after,.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-button:active:before{background:#111}.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-button.svgMap-disabled:after,.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-button.svgMap-disabled:before{background:#ccc}.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-in-button{margin:1px 0 1px 1px}.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-in-button:after{width:3px;height:11px}.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-out-button{margin:1px 1px 1px 0}.svgMap-map-wrapper .svgMap-country{stroke:#fff;stroke-width:1;stroke-linejoin:round;vector-effect:non-scaling-stroke;transition:fill 250ms,stroke 250ms}.svgMap-map-wrapper .svgMap-country[data-link]{cursor:pointer}@media (hover:hover){.svgMap-map-wrapper .svgMap-country:hover{stroke:#333;stroke-width:1.5}}.svgMap-map-wrapper .svgMap-country.svgMap-active{stroke:#333;stroke-width:1.5}.svgMap-tooltip{box-shadow:0 0 3px rgba(0,0,0,.2);position:absolute;z-index:2;border-radius:2px;background:#fff;transform:translate(-50%,-100%);border-bottom:1px solid #000;display:none;pointer-events:none;min-width:60px}.svgMap-tooltip.svgMap-tooltip-flipped{transform:translate(-50%,0);border-bottom:0;border-top:1px solid #000}.svgMap-tooltip.svgMap-active{display:block}.svgMap-tooltip .svgMap-tooltip-content-container{position:relative;padding:10px 20px}.svgMap-tooltip .svgMap-tooltip-content-container .svgMap-tooltip-flag-container{text-align:center;margin:2px 0 5px}.svgMap-tooltip .svgMap-tooltip-content-container .svgMap-tooltip-flag-container.svgMap-tooltip-flag-container-emoji{font-size:50px;line-height:0;padding:25px 0 15px}.svgMap-tooltip .svgMap-tooltip-content-container .svgMap-tooltip-flag-container .svgMap-tooltip-flag{display:block;margin:auto;width:auto;height:32px;padding:2px;background:rgba(0,0,0,.15);border-radius:2px}.svgMap-tooltip .svgMap-tooltip-title{white-space:nowrap;font-size:18px;line-height:28px;padding:0 0 8px;text-align:center}.svgMap-tooltip .svgMap-tooltip-content{white-space:nowrap;text-align:center;font-size:14px;color:#777;margin:-5px 0 0}.svgMap-tooltip .svgMap-tooltip-content table{padding:0;border-spacing:0;margin:auto}.svgMap-tooltip .svgMap-tooltip-content table td{padding:2px 0;text-align:left}.svgMap-tooltip .svgMap-tooltip-content table td span{color:#111}.svgMap-tooltip .svgMap-tooltip-content table td:first-child{padding-right:10px;text-align:right}.svgMap-tooltip .svgMap-tooltip-content table td sup{vertical-align:baseline;position:relative;top:-5px}.svgMap-tooltip .svgMap-tooltip-content .svgMap-tooltip-no-data{padding:2px 0;color:#777;font-style:italic}.svgMap-tooltip .svgMap-tooltip-pointer{position:absolute;top:100%;left:50%;transform:translateX(-50%);overflow:hidden;height:10px;width:30px}.svgMap-tooltip .svgMap-tooltip-pointer:after{content:"";width:20px;height:20px;background:#fff;border:1px solid #000;position:absolute;bottom:6px;left:50%;transform:translateX(-50%) rotate(45deg)}.svgMap-tooltip.svgMap-tooltip-flipped .svgMap-tooltip-pointer{bottom:auto;top:-10px;transform:translateX(-50%) scaleY(-1)}