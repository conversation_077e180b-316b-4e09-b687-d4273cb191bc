//>>>>> Header Top Start <<<<<//
.header-top-section {
	position: relative;
	z-index: 9;
	background-color: $p3-clr;
	.header-top-wrapper {
		@include flex;
		justify-content: space-between;
		padding: 12px 0;
		.contact-list {
			@include flex;
			gap: 40px;

			li {
				color: $white;
				font-weight: 400;

				i {
					margin-right: 5px;
				}

				a {
					color: $white;
					font-weight: 400;
				}
			}
		}

		.social-wrapper {
			gap: 14px;
			a {
				width: 24px;
				height: 24px;
				display: flex;
				align-items: center;
				justify-content: center;
				svg {
					stroke: $white;
					width: 8px;
				}
				i {
					font-size: 12px;
				}
				.ani-arrow {
					transition: all 0.5s;
				}
				border: 1px solid rgba(255, 255, 255, 0.3);
				&:hover {
					background: $p2-clr;
					border-color: $p2-clr;
					i {
						color: $white;
					}
					svg {
						stroke: $white;
					}
				}
			}
		}

		&.style-2 {
			padding: 15px 0;

			.contact-list {
				li {
					color: $white;
					a {
						color: $white;
					}
				}
			}

			.social-icon {
				span {
					color: $white;
				}

				a {
					color: $white;

					&:hover {
						color: $p2-clr;
					}
				}
			}
		}
	}
}

//>>>>> Header Main Area Start <<<<<//
.header-main {
	display: flex;
	align-items: center;
	justify-content: space-between;
	.main-menu {
		ul {
			margin-bottom: 0;
			li {
				position: relative;
				list-style: none;
				display: inline-block;
				margin-inline-end: 26px;

				&:last-child {
					margin-inline-end: 0;
				}

				a {
					display: inline-block;
					font-size: 18px;
					font-weight: 600;
					color: $black;
					padding: 20px 0;
					text-align: left;
					font-family: $heading-font;
					position: relative;
					text-transform: capitalize;
					@include transition;

					i {
						margin-left: 2px;
						font-size: 16px;
					}

					&:hover {
						color: $theme-color !important;
					}
				}
				.submenu {
					position: absolute;
					top: 100%;
					inset-inline-start: 0;
					min-width: 240px;
					background: $white;
					z-index: 99999;
					visibility: hidden;
					opacity: 0;
					transform-origin: top center;
					color: $header-color;
					box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
					-webkit-box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
					-moz-box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
					transform: translateY(10px);
					@include transition;

					li {
						display: block;
						width: 100%;
						margin: 0;
						padding: 0;

						a {
							position: relative;
							z-index: 11;
							font-size: 16px;
							font-weight: 600;
							color: $header-color;
							padding: 0 25px;
							padding-bottom: 11px;
							padding-top: 11px;
							width: 100%;
							border-bottom: 1px solid #eeeeee;
						}
						&:last-child {
							a {
								border: none;
							}
						}
						.submenu {
							inset-inline-start: 100%;
							top: 0;
							visibility: hidden;
							opacity: 0;
						}
						&:hover {
							> a {
								background: $p2-clr;
								color: $white !important;

								&::after {
									color: $p2-clr;
								}
							}
							> .submenu {
								-webkit-transform: translateY(1);
								-moz-transform: translateY(1);
								-ms-transform: translateY(1);
								-o-transform: translateY(1);
								transform: translateY(1);
								visibility: visible;
								opacity: 1;
							}
						}
					}
					li.has-dropdown {
						> a {
							&::after {
								position: absolute;
								top: 50%;
								inset-inline-end: 25px;
								-webkit-transform: translateY(-50%);
								-moz-transform: translateY(-50%);
								-ms-transform: translateY(-50%);
								-o-transform: translateY(-50%);
								transform: translateY(-50%);
								color: $p2-clr;
							}
						}
					}
				}

				.has-homemenu {
					width: 500px;
					padding: 30px 30px 10px 30px;
					opacity: 0;
					left: -250px;
					visibility: hidden;
					padding: 20px 20px 20px 20px;

					.homemenu-items {
						@include flex;
						gap: 18px;
						justify-content: space-between;
						@include breakpoint(max-lg) {
							flex-wrap: wrap;
						}
						.homemenu {
							position: relative;
							box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
							.homemenu-thumb {
								position: relative;
								width: 100%;
								.demo-button {
									position: absolute;
									top: 50%;
									left: 50%;
									transform: translate(-50%, -50%);
									width: 60%;
									gap: 10px;
									display: flex;
									justify-content: center;
									flex-direction: column;
									opacity: 0;
									visibility: hidden;
									@include transition;
									margin-top: 20px;
									.theme-btn {
										padding: 14px 20px;
										color: $white !important;
										width: initial;
										font-size: 14px;
										text-align: center;
										border-radius: 0;

										&:hover {
											color: $white !important;
										}
									}
								}
								&::before {
									background: -webkit-gradient(
										linear,
										left top,
										left bottom,
										from(rgba(20, 19, 19, 0)),
										to(#5e5ef6)
									);
									background: linear-gradient(
										to bottom,
										rgba(99, 92, 92, 0) 0%,
										#252527 100%
									);
									background-repeat: no-repeat;
									background-size: cover;
									background-position: center;
									width: 100%;
									height: 100%;
									position: absolute;
									left: 0;
									top: 0;
									overflow: hidden;
									opacity: 0;
									-webkit-transition: all 0.3s ease-in-out;
									transition: all 0.3s ease-in-out;
									content: "";
								}
								&:hover {
									&::before {
										visibility: visible;
										opacity: 1;
									}

									.demo-button {
										opacity: 1;
										visibility: visible;
										margin-top: 0;
									}
									& .homemenu-btn {
										opacity: 1;
										visibility: visible;
										bottom: 50%;
										transform: translateY(50%);
									}
								}
								img {
									width: 100%;
								}
							}

							.homemenu-title {
								text-align: center;
								margin: 15px auto;
								display: inline-block;
								font-size: 16px;
							}
						}
					}
				}

				&:hover {
					> a {
						color: $p2-clr;
						&::after {
							color: $p2-clr;
						}
					}
					> .submenu {
						visibility: visible;
						opacity: 1;
						transform: translateY(0px);
					}
				}
			}
		}
	}
	.sidebar__toggle {
		cursor: pointer;
		font-size: 39px;
		@include breakpoint(max-sm) {
			font-size: 24px;
		}
	}
}

.header-1 {
	padding: 18px 0;
	.header-main {
		.header-left {
			@include flex;
			gap: 60px;

			@include breakpoint(max-xl4) {
				gap: 40px;
			}

			@include breakpoint(max-xxl) {
				gap: 25px;
			}

			@include breakpoint(max-xl) {
				gap: 20px;
			}
		}
		.header-right {
			gap: 30px;

			@include breakpoint(max-xl) {
				gap: 20px;
			}

			@include breakpoint(max-md) {
				gap: 10px;
			}

			.search-icon {
				text-align: center;
				color: $black;
				margin-left: 30px;

				@include breakpoint(max-xl) {
					margin-left: 0;
				}
			}

			.header-button {
				@include breakpoint(max-xxl) {
					.theme-btn {
						padding: 10px 22px;
					}
				}
			}
		}
	}
}
//Header Two
.header-2 {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	padding: 0 0;
	z-index: 99;
	.mega-menu-wrapper {
		padding: 20px 36px;
		border-bottom: 10px solid $p2-clr;
		border-bottom-left-radius: 10px;
		border-bottom-right-radius: 10px;
	}
	@include breakpoint(max-xxl) {
		padding: 0 0;
		.mega-menu-wrapper {
			padding: 14px 14px;
			border-bottom: 3px solid $p2-clr;
		}
	}
}

.sidebar__toggle {
	cursor: pointer;
}
//>>>>> Sticky Start <<<<</
.sticky {
	position: fixed !important;
	top: 0 !important;
	left: 0;
	width: 100%;
	z-index: 100;
	transition: all 0.9s;
	background-color: $white;
	box-shadow: $shadow;
	-webkit-animation: 500ms ease-in-out 0s normal none 1 running fadeInDown;
	animation: 500ms ease-in-out 0s normal none 1 running fadeInDown;
	&.header-1 {
		padding: 10px 0;
	}
	&.header-2 {
		padding: 0px 0;
		background: transparent !important;
		box-shadow: none;
		.mega-menu-wrapper{
			box-shadow: rgba(50, 50, 93, 0.25) 0px 6px 12px -2px, rgba(0, 0, 0, 0.3) 0px 3px 7px -3px;
		}
	}
}

//>>>>> Offcanvas Start <<<<<//
.offcanvas__info {
	background: $cmnbg none repeat scroll 0 0;
	border-left: 2px solid $p2-clr;
	position: fixed;
	right: 0;
	top: 0;
	width: 400px;
	height: 100%;
	-webkit-transform: translateX(calc(100% + 80px));
	-moz-transform: translateX(calc(100% + 80px));
	-ms-transform: translateX(calc(100% + 80px));
	-o-transform: translateX(calc(100% + 80px));
	transform: translateX(calc(100% + 80px));
	-webkit-transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
	-moz-transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
	transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
	z-index: 99999;
	overflow-y: scroll;
	overscroll-behavior-y: contain;
	scrollbar-width: none;
	&::-webkit-scrollbar {
		display: none;
	}
}
.offcanvas__info.info-open {
	opacity: 1;
	-webkit-transform: translateX(0);
	-moz-transform: translateX(0);
	-ms-transform: translateX(0);
	-o-transform: translateX(0);
	transform: translateX(0);
}
.offcanvas__wrapper {
	position: relative;
	height: 100%;
	padding: 30px 30px;
	.offcanvas__content {
		.text {
			color: $text-color;
		}
		.offcanvas__close {
			width: 40px;
			height: 40px;
			line-height: 40px;
			text-align: center;
			border-radius: 4px;
			background-color: $p2-clr;
			position: relative;
			z-index: 9;
			cursor: pointer;

			i {
				color: $white;
			}
		}
		.offcanvas__contact {
			margin-top: 20px;
			ul {
				margin-top: 20px;

				li {
					font-size: 16px;
					font-weight: 600;
					text-transform: capitalize;

					a {
						color: $text-color;
					}

					&:not(:last-child) {
						margin-bottom: 15px;
					}

					.offcanvas__contact-icon {
						margin-right: 20px;

						i {
							color: $p2-clr;
						}
					}
				}
			}

			span {
				text-transform: initial;
			}

			.header-button {
				.theme-btn {
					width: 100%;
					padding: 16px 40px;
					text-transform: capitalize !important;
				}
			}

			.social-icon {
				margin-top: 30px;
				gap: 10px;

				a {
					width: 45px;
					height: 45px;
					line-height: 45px;
					text-align: center;
					font-size: 16px;
					display: block;
					background: $white;
					color: $pra;
					border-radius: 50%;
					-webkit-transition: all 0.4s ease-in-out;
					transition: all 0.4s ease-in-out;
					text-align: center;
					border: 1px solid $border-color;

					&:hover {
						border-color: $p2-clr;
						background-color: $p2-clr;
						color: $white;
					}
				}
			}
		}
	}
}

.offcanvas__overlay {
	position: fixed;
	height: 100%;
	width: 100%;
	background: #151515;
	z-index: 900;
	top: 0;
	opacity: 0;
	visibility: hidden;
	right: 0;
}

.offcanvas__overlay.overlay-open {
	opacity: 0.8;
	visibility: visible;
}

@media (max-width: 450px) {
	.offcanvas__info {
		width: 300px;
	}
}

@media (max-width: 575px) {
	.offcanvas__wrapper {
		padding: 20px;
	}
}
