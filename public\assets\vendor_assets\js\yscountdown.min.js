/*! yscountdown v1.0.0 | <PERSON> <<EMAIL>> | MIT License | https://github.com/yusufsefasezer/ysCountDown.js */
!function(t,o){"function"==typeof define&&define.amd?define([],function(){return o(t)}):"object"==typeof exports?module.exports=o(t):t.ysCountDown=o(t)}("undefined"!=typeof global?global:"undefined"!=typeof window?window:this,function(u){"use strict";return function(t,o){var e={},n=null,r=null,a=null,l=null,i=!1,s=(e.init=function(t,o){if(!("addEventListener"in u))throw"ysCountDown: This browser does not support the required JavaScript methods.";if(e.destroy(),n="string"==typeof t?new Date(t):t,!((t=n)instanceof Date)||isNaN(t))throw new TypeError("ysCountDown: Please enter a valid date.");if("function"!=typeof o)throw new TypeError("ysCountDown: Please enter a callback function.");r=o,s()},e.destroy=function(){r=n=null,f(),l=null,i=!1},function(){a=a||setInterval(function(){var t,o;t=new Date,(o=Math.ceil((n.getTime()-t.getTime())/1e3))<=0&&(i=!0,f()),l={seconds:o%60,minutes:Math.floor(o/60)%60,hours:Math.floor(o/60/60)%24,days:Math.floor(o/60/60/24)%7,daysToWeek:Math.floor(o/60/60/24)%7,daysToMonth:Math.floor(o/60/60/24%30.4368),weeks:Math.floor(o/60/60/24/7),weeksToMonth:Math.floor(o/60/60/24/7)%4,months:Math.floor(o/60/60/24/30.4368),monthsToYear:Math.floor(o/60/60/24/30.4368)%12,years:Math.abs(n.getFullYear()-t.getFullYear()),totalDays:Math.floor(o/60/60/24),totalHours:Math.floor(o/60/60),totalMinutes:Math.floor(o/60),totalSeconds:o},r(l,i)},100)}),f=function(){a&&(clearInterval(a),a=null)};return e.init(t,o),e}});