<template>
    <div class="contents error-page-wrapper">
        <div class="container-fluid">
            <div class="row justify-content-center">
                <div class="col-12">
                    <!-- Start: error page -->
                    <div class="min-vh-100 d-flex align-items-center justify-content-center">
                        <div class="error-page text-center w-100">
                            <img src="/img/svg/404.svg" alt="404" class="svg error-page__image">
                            <div class="error-page__title">404</div>
                            <h5 class="fw-500">Oops! The page you're looking for can't be found.</h5>
                            <p class="error-page__description">
                                It seems you've hit a broken link or the page has been moved. Don't worry, let's get you
                                back on track!
                            </p>
                            <div class="content-center mt-30">
                                <a href="/admin/home" class="btn btn-primary btn-default btn-squared px-30">Return
                                    Home</a>
                                <a href="/contact" class="btn btn-secondary btn-default btn-squared px-30 ml-10">Contact
                                    Support</a>
                            </div>
                        </div>
                    </div>
                    <!-- End: error page -->
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
.error-page-wrapper {
    background: linear-gradient(135deg, #f5f7fa, #c3cfe2);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.error-page {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.error-page__image {
    max-width: 300px;
    margin-bottom: 20px;
}

.error-page__title {
    font-size: 6rem;
    font-weight: bold;
    color: #2c3e50;
}

.error-page__description {
    font-size: 1.2rem;
    color: #7f8c8d;
    margin-top: 10px;
    margin-bottom: 30px;
}

.btn-secondary {
    background-color: #95a5a6;
    border-color: #95a5a6;
    color: #fff;
}

.btn-secondary:hover {
    background-color: #7f8c8d;
    border-color: #7f8c8d;
}
</style>