<script setup>
import {
    onMounted,
    ref,
} from 'vue'

import api from '@/api/api'

api.get('/user-list').then(response => {
    console.log(response.data.data.data);
})

// Data state
const users = ref([])
const loading = ref(false)
const totalRecords = ref(0)

// Column definitions for the enhanced data table
const columns = ref([
    {
        key: 'id',
        label: 'ID',
        sortable: true,
        width: '80px',
        align: 'center'
    },
    {
        key: 'image_url',
        label: 'Avatar',
        sortable: false,
        filterable: false,
        width: '80px',
        align: 'center'
    },
    {
        key: 'name',
        label: 'Name',
        sortable: true,
        filterable: true,
        filterType: 'text'
    },
    {
        key: 'email',
        label: 'Email',
        sortable: true,
        filterable: true,
        filterType: 'text'
    },
    {
        key: 'user_position',
        label: 'Position',
        sortable: true,
        filterable: true,
        filterType: 'select',
        filterOptions: [
            { label: 'Administrator', value: 'Administrator' },
            { label: 'Developer', value: 'Developer' },
            { label: 'Staff', value: 'Staff' },
            { label: 'Container', value: 'Container' }
        ]
    },
    {
        key: 'status',
        label: 'Status',
        sortable: true,
        filterable: true,
        filterType: 'select',
        filterOptions: [
            { label: 'Active', value: 1 },
            { label: 'Inactive', value: 0 }
        ]
    },
    {
        key: 'created_at',
        label: 'Created Date',
        sortable: true,
        filterable: true,
        filterType: 'date'
    }
])

// CRUD configuration
const crudConfig = ref({
    create: {
        enabled: true,
        fields: [
            { key: 'name', label: 'Name', type: 'text', required: true },
            { key: 'email', label: 'Email', type: 'email', required: true },
            { key: 'password', label: 'Password', type: 'password', required: true },
            {
                key: 'user_position', label: 'Position', type: 'select', required: false, options: [
                    { label: 'Administrator', value: 'Administrator' },
                    { label: 'Developer', value: 'Developer' },
                    { label: 'Staff', value: 'Staff' },
                    { label: 'Container', value: 'Container' }
                ]
            },
            {
                key: 'status', label: 'Status', type: 'select', required: true, options: [
                    { label: 'Active', value: 1 },
                    { label: 'Inactive', value: 0 }
                ]
            },
            { key: 'image_url', label: 'Avatar', type: 'file', required: false }
        ],
        validation: {
            name: ['required', 'min:2'],
            email: ['required', 'email'],
            password: ['required', 'min:6']
        }
    },
    update: {
        enabled: true,
        fields: [
            { key: 'name', label: 'Name', type: 'text', required: true },
            { key: 'email', label: 'Email', type: 'email', required: true },
            {
                key: 'user_position', label: 'Position', type: 'select', required: false, options: [
                    { label: 'Administrator', value: 'Administrator' },
                    { label: 'Developer', value: 'Developer' },
                    { label: 'Staff', value: 'Staff' },
                    { label: 'Container', value: 'Container' }
                ]
            },
            {
                key: 'status', label: 'Status', type: 'select', required: true, options: [
                    { label: 'Active', value: 1 },
                    { label: 'Inactive', value: 0 }
                ]
            },
            { key: 'image_url', label: 'Avatar', type: 'file', required: false }
        ],
        validation: {
            name: ['required', 'min:2'],
            email: ['required', 'email']
        }
    },
    delete: {
        enabled: true,
        confirmation: true
    }
})

// Export configuration
const exportConfig = ref({
    csv: true,
    excel: true,
    json: true,
    filename: 'users-list'
})

// Load users data
const loadUsers = async () => {
    loading.value = true
    try {
        const response = await api.get('/user-list')
        console.log('API Response:', response.data)

        // Extract data from the nested structure
        const apiData = response.data.data
        users.value = apiData.data || []
        totalRecords.value = apiData.total || users.value.length

        console.log('Users loaded:', users.value)
        console.log('Total records:', totalRecords.value)
    } catch (error) {
        console.error('Error loading users:', error)
        users.value = []
        totalRecords.value = 0
    } finally {
        loading.value = false
    }
}

// Event handlers
const handleCreate = async (formData) => {
    try {
        // Prepare form data for file upload if image is present
        const submitData = new FormData()
        Object.keys(formData).forEach(key => {
            if (formData[key] !== null && formData[key] !== undefined) {
                submitData.append(key, formData[key])
            }
        })

        const response = await api.post('/users', submitData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        })
        console.log('User created:', response.data)
        await loadUsers() // Refresh the list
    } catch (error) {
        console.error('Error creating user:', error)
        // You can add toast notification here
    }
}

const handleUpdate = async (formData) => {
    try {
        // Prepare form data for file upload if image is present
        const submitData = new FormData()
        Object.keys(formData).forEach(key => {
            if (formData[key] !== null && formData[key] !== undefined) {
                submitData.append(key, formData[key])
            }
        })

        // Add method spoofing for PUT request with FormData
        submitData.append('_method', 'PUT')

        const response = await api.post(`/users/${formData.id}`, submitData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        })
        console.log('User updated:', response.data)
        await loadUsers() // Refresh the list
    } catch (error) {
        console.error('Error updating user:', error)
        // You can add toast notification here
    }
}

const handleDelete = async (userData) => {
    try {
        if (Array.isArray(userData)) {
            // Bulk delete
            const ids = userData.map(user => user.id)
            await api.post('/users/bulk-delete', { ids })
            console.log('Users deleted:', ids)
        } else {
            // Single delete
            await api.delete(`/users/${userData.id}`)
            console.log('User deleted:', userData.id)
        }
        await loadUsers() // Refresh the list
    } catch (error) {
        console.error('Error deleting user(s):', error)
        // You can add toast notification here
    }
}

const handleRowClick = (row, index) => {
    console.log('Row clicked:', row, index)
}

const handleRefresh = () => {
    loadUsers()
}

// Initialize data on component mount
onMounted(() => {
    loadUsers()
})
</script>

<template>
    <div class="p-6">
        <!-- Enhanced Data Table -->
        <EnhancedDataTable :items="users" :columns="columns" title="User Management" :loading="loading"
            :selectable="true" :searchable="true" :crud="crudConfig" :export-config="exportConfig"
            :show-advanced-filters="true" :show-bulk-actions="true" :show-pagination="true" :show-info="true"
            @crud-create="handleCreate" @crud-update="handleUpdate" @crud-delete="handleDelete"
            @row-click="handleRowClick" @refresh="handleRefresh">
            <!-- Custom cell for avatar/image -->
            <template #cell-image_url="{ value, item }">
                <div class="flex items-center justify-center">
                    <div class="h-10 w-10 rounded-full overflow-hidden bg-gray-200">
                        <img v-if="value" :src="`http://127.0.0.1:8000/storage/${value}`" :alt="item.name"
                            class="h-full w-full object-cover" @error="$event.target.style.display = 'none'" />
                        <div v-else
                            class="h-full w-full flex items-center justify-center bg-blue-500 text-white text-sm font-medium">
                            {{ item.name ? item.name.charAt(0).toUpperCase() : 'U' }}
                        </div>
                    </div>
                </div>
            </template>

            <!-- Custom cell for status -->
            <template #cell-status="{ value }">
                <span :class="[
                    'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                    value === 1 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                ]">
                    {{ value === 1 ? 'Active' : 'Inactive' }}
                </span>
            </template>

            <!-- Custom cell for user position -->
            <template #cell-user_position="{ value }">
                <span :class="[
                    'inline-flex items-center px-2 py-1 rounded text-xs font-medium',
                    value === 'Administrator' ? 'bg-purple-100 text-purple-800' :
                        value === 'Developer' ? 'bg-blue-100 text-blue-800' :
                            value === 'Staff' ? 'bg-gray-100 text-gray-800' :
                                value === 'Container' ? 'bg-orange-100 text-orange-800' :
                                    'bg-gray-100 text-gray-500'
                ]">
                    {{ value || 'Not Set' }}
                </span>
            </template>

            <!-- Custom cell for created_at -->
            <template #cell-created_at="{ value }">
                <span class="text-gray-600 text-sm">
                    {{ value ? new Date(value).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                    }) : '-' }}
                </span>
            </template>

            <!-- Custom cell for email -->
            <template #cell-email="{ value }">
                <a :href="`mailto:${value}`" class="text-blue-600 hover:text-blue-800 hover:underline"
                    :title="`Send email to ${value}`">
                    {{ value }}
                </a>
            </template>

            <!-- Custom cell for name with avatar -->
            <template #cell-name="{ value, item }">
                <div class="flex items-center space-x-3">
                    <div class="flex-shrink-0">
                        <div class="h-8 w-8 rounded-full overflow-hidden bg-gray-200">
                            <img v-if="item.image_url" :src="`http://127.0.0.1:8000/storage/${item.image_url}`"
                                :alt="value" class="h-full w-full object-cover"
                                @error="$event.target.style.display = 'none'" />
                            <div v-else
                                class="h-full w-full flex items-center justify-center bg-blue-500 text-white text-xs font-medium">
                                {{ value ? value.charAt(0).toUpperCase() : 'U' }}
                            </div>
                        </div>
                    </div>
                    <div class="font-medium text-gray-900">{{ value }}</div>
                </div>
            </template>
        </EnhancedDataTable>
    </div>
</template>

<style scoped>
/* Custom styles for the enhanced data table */
.p-6 {
    padding: 1.5rem;
}

/* Additional custom styling can be added here if needed */
</style>