<script setup lang="ts">
import {
  onMounted,
  ref,
} from 'vue'

import {
  Column,
  DataTable,
} from 'primevue'

import api from '@/api/api'

const customers = ref([]);
const totalRecords = ref(0);
const loading = ref(false);

const loadUsers = async () => {
    loading.value = true;
    const response = await api.get('/admissions');
    customers.value = response.data.data.data;
    console.log(customers.value);
    totalRecords.value = response.data.data.total || customers.value.length;
    loading.value = false;
};
onMounted(() => {
    loadUsers();
})
</script>

<template>
    <div class="vue-data-table">
        <!-- Header -->
        <div class="vue-data-table-header">
            <div class="table-header">
                <div class="table-header-left">
                    <h4 class="userDatatable-title">Admission List</h4>
                </div>
                <div class="table-header-right">
                    <div class="order-search">
                        <div class="order-search__form">
                            <input type="text" class="form-control" placeholder="Search...">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <circle cx="11" cy="11" r="8"></circle>
                                <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- DataTable -->
        <div class="vue-data-table-body">
            <div class="table-responsive">
                <DataTable :value="customers" :loading="loading" :totalRecords="totalRecords" paginator :rows="10"
                    responsiveLayout="scroll" class="table table-rounded" stripedRows>

                    <!-- ID Column -->
                    <Column field="id" header="ID" />

                    <!-- Child Name Column -->
                    <Column field="child_name" header="Child Name" />

                    <!-- Email Column -->
                    <Column field="email" header="Email" />

                    <!-- Actions Column -->
                    <Column header="Actions" bodyClass="text-center">
                        <template #body>
                            <div class="table-actions">
                                <a href="#" class="view"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                        stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                                        <circle cx="12" cy="12" r="3"></circle>
                                    </svg></a>
                                <a href="#" class="edit"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                        stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                        <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                                    </svg></a>
                                <a href="#" class="delete"><svg xmlns="http://www.w3.org/2000/svg" width="16"
                                        height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <polyline points="3 6 5 6 21 6"></polyline>
                                        <path
                                            d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2">
                                        </path>
                                    </svg></a>
                            </div>
                        </template>
                    </Column>
                </DataTable>
            </div>
        </div>
    </div>
</template>

<style scoped>
.vue-data-table {
    width: 100%;
    border: 1px solid var(--vdt-border-color, #e2e8f0);
    border-radius: var(--vdt-border-radius, 8px);
    overflow: hidden;
    background-color: var(--vdt-bg-color, #ffffff);
    box-shadow: var(--vdt-box-shadow, 0 1px 3px rgba(0, 0, 0, 0.1));
}

.vue-data-table-header {
    padding: 16px;
    border-bottom: 1px solid var(--vdt-border-color, #e2e8f0);
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.userDatatable-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--vdt-text-color, #1e293b);
    margin: 0;
}

.order-search__form {
    padding: 0 15px;
    background: var(--bg-lighter, #f8fafc);
    height: 40px;
    display: flex;
    align-items: center;
    border-radius: 20px;
    width: 300px;
}

.order-search__form .form-control {
    border: none;
    padding: 8px 13px;
    background: transparent;
    width: 100%;
}

.order-search__form svg {
    color: var(--color-light, #64748b);
}

.table-actions {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
}

.table-actions a {
    color: var(--color-light, #64748b);
}

.table-actions a:hover {
    color: var(--vdt-primary-color, #3b82f6);
}

.table-actions a.view:hover {
    color: var(--vdt-primary-color, #3b82f6);
}

.table-actions a.edit:hover {
    color: #10b981;
}

.table-actions a.delete:hover {
    color: #ef4444;
}

/* Override PrimeVue styles */
:deep(.p-datatable) {
    font-family: inherit;
}

:deep(.p-datatable .p-datatable-header) {
    background: transparent;
    border: none;
    padding: 0;
}

:deep(.p-datatable .p-datatable-thead > tr > th) {
    background-color: var(--bg-normal, #f8fafc);
    color: var(--color-gray, #64748b);
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    padding: 15px 20px;
    border-color: var(--border-color, #e2e8f0);
}

:deep(.p-datatable .p-datatable-tbody > tr) {
    transition: background-color 0.15s ease;
}

:deep(.p-datatable .p-datatable-tbody > tr:hover) {
    background-color: var(--bg-hover, #f1f5f9);
}

:deep(.p-datatable .p-datatable-tbody > tr > td) {
    padding: 12px 20px;
    border-color: var(--border-color, #e2e8f0);
}

:deep(.p-paginator) {
    padding: 16px;
    border-top: 1px solid var(--border-color, #e2e8f0);
    background-color: transparent;
}

:deep(.p-paginator .p-paginator-element) {
    color: var(--color-dark, #1e293b);
}

:deep(.p-paginator .p-paginator-element.p-highlight) {
    background-color: var(--vdt-primary-color, #3b82f6);
    color: white;
}
</style>