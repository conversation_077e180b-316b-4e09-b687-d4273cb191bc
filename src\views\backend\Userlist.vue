<script setup>
import {
    onMounted,
    ref,
} from 'vue'

import api from '@/api/api'

// Data state
const users = ref([])
const loading = ref(false)
const totalRecords = ref(0)

// Column definitions for the enhanced data table
const columns = ref([
    {
        key: 'id',
        label: 'ID',
        sortable: true,
        width: '80px',
        align: 'center'
    },
    {
        key: 'child_name',
        label: 'Child Name',
        sortable: true,
        filterable: true,
        filterType: 'text'
    },
    {
        key: 'email',
        label: 'Email',
        sortable: true,
        filterable: true,
        filterType: 'text'
    },
    {
        key: 'phone',
        label: 'Phone',
        sortable: true,
        filterable: true,
        filterType: 'text'
    },
    {
        key: 'status',
        label: 'Status',
        sortable: true,
        filterable: true,
        filterType: 'select',
        filterOptions: [
            { label: 'Active', value: 'active' },
            { label: 'Pending', value: 'pending' },
            { label: 'Inactive', value: 'inactive' }
        ]
    },
    {
        key: 'created_at',
        label: 'Created Date',
        sortable: true,
        filterable: true,
        filterType: 'date'
    }
])

// CRUD configuration
const crudConfig = ref({
    create: {
        enabled: true,
        fields: [
            { key: 'child_name', label: 'Child Name', type: 'text', required: true },
            { key: 'email', label: 'Email', type: 'email', required: true },
            { key: 'phone', label: 'Phone', type: 'text', required: true },
            { key: 'parent_name', label: 'Parent Name', type: 'text', required: true },
            { key: 'address', label: 'Address', type: 'textarea', required: false },
            {
                key: 'status', label: 'Status', type: 'select', required: true, options: [
                    { label: 'Active', value: 'active' },
                    { label: 'Pending', value: 'pending' },
                    { label: 'Inactive', value: 'inactive' }
                ]
            }
        ],
        validation: {
            child_name: ['required', 'min:2'],
            email: ['required', 'email'],
            phone: ['required', 'min:10'],
            parent_name: ['required', 'min:2']
        }
    },
    update: {
        enabled: true,
        fields: [
            { key: 'child_name', label: 'Child Name', type: 'text', required: true },
            { key: 'email', label: 'Email', type: 'email', required: true },
            { key: 'phone', label: 'Phone', type: 'text', required: true },
            { key: 'parent_name', label: 'Parent Name', type: 'text', required: true },
            { key: 'address', label: 'Address', type: 'textarea', required: false },
            {
                key: 'status', label: 'Status', type: 'select', required: true, options: [
                    { label: 'Active', value: 'active' },
                    { label: 'Pending', value: 'pending' },
                    { label: 'Inactive', value: 'inactive' }
                ]
            }
        ],
        validation: {
            child_name: ['required', 'min:2'],
            email: ['required', 'email'],
            phone: ['required', 'min:10'],
            parent_name: ['required', 'min:2']
        }
    },
    delete: {
        enabled: true,
        confirmation: true
    }
})

// Export configuration
const exportConfig = ref({
    csv: true,
    excel: true,
    json: true,
    filename: 'admissions-list'
})

// Load users data
const loadUsers = async () => {
    loading.value = true
    try {
        const response = await api.get('/admissions')
        users.value = response.data.data.data || []
        totalRecords.value = response.data.data.total || users.value.length
        console.log('Users loaded:', users.value)
    } catch (error) {
        console.error('Error loading users:', error)
        users.value = []
    } finally {
        loading.value = false
    }
}

// Event handlers
const handleCreate = async (formData) => {
    try {
        const response = await api.post('/admissions', formData)
        console.log('User created:', response.data)
        await loadUsers() // Refresh the list
    } catch (error) {
        console.error('Error creating user:', error)
    }
}

const handleUpdate = async (formData) => {
    try {
        const response = await api.put(`/admissions/${formData.id}`, formData)
        console.log('User updated:', response.data)
        await loadUsers() // Refresh the list
    } catch (error) {
        console.error('Error updating user:', error)
    }
}

const handleDelete = async (userData) => {
    try {
        if (Array.isArray(userData)) {
            // Bulk delete
            const ids = userData.map(user => user.id)
            await api.post('/admissions/bulk-delete', { ids })
            console.log('Users deleted:', ids)
        } else {
            // Single delete
            await api.delete(`/admissions/${userData.id}`)
            console.log('User deleted:', userData.id)
        }
        await loadUsers() // Refresh the list
    } catch (error) {
        console.error('Error deleting user(s):', error)
    }
}

const handleRowClick = (row, index) => {
    console.log('Row clicked:', row, index)
}

const handleRefresh = () => {
    loadUsers()
}

// Initialize data on component mount
onMounted(() => {
    loadUsers()
})
</script>

<template>
    <div class="p-6">
        <!-- Enhanced Data Table -->
        <EnhancedDataTable :items="users" :columns="columns" title="Admission Management" :loading="loading"
            :selectable="true" :searchable="true" :crud="crudConfig" :export-config="exportConfig"
            :show-advanced-filters="true" :show-bulk-actions="true" :show-pagination="true" :show-info="true"
            @crud-create="handleCreate" @crud-update="handleUpdate" @crud-delete="handleDelete"
            @row-click="handleRowClick" @refresh="handleRefresh">
            <!-- Custom cell for status -->
            <template #cell-status="{ value }">
                <span :class="[
                    'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                    value === 'active' ? 'bg-green-100 text-green-800' :
                        value === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                ]">
                    {{ value }}
                </span>
            </template>

            <!-- Custom cell for created_at -->
            <template #cell-created_at="{ value }">
                <span class="text-gray-600">
                    {{ value ? new Date(value).toLocaleDateString() : '-' }}
                </span>
            </template>

            <!-- Custom cell for email -->
            <template #cell-email="{ value }">
                <a :href="`mailto:${value}`" class="text-blue-600 hover:text-blue-800 hover:underline">
                    {{ value }}
                </a>
            </template>

            <!-- Custom cell for phone -->
            <template #cell-phone="{ value }">
                <a :href="`tel:${value}`" class="text-gray-900 hover:text-blue-600">
                    {{ value }}
                </a>
            </template>
        </EnhancedDataTable>
    </div>
</template>

<style scoped>
/* Custom styles for the enhanced data table */
.p-6 {
    padding: 1.5rem;
}

/* Additional custom styling can be added here if needed */
</style>