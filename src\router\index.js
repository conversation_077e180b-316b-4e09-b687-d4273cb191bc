// router/index.js

import {
  createRouter,
  createWebHistory,
} from 'vue-router'

import useAuthStore from '@/stores/auth'
import {
  loadAdminResources,
  loadFrontendResources,
  unloadFrontendResources,
} from '@/utils/dynamicResourceLoader'
import Login from '@/views/auth/Login.vue'

import backendRoutes from './backend'
import frontendRoutes from './frontend'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      component: Login,
      meta: { isAdmin: false },
    },
    ...backendRoutes,
    ...frontendRoutes,
    {
      path: '/404',
      name: 'not-found',
      component: () => import('@/views/errors/NotFound.vue'),
    },
    {
      path: '/:pathMatch(.*)*',
      redirect: { name: 'not-found' },
    },
  ],
});

// Navigation guard to handle authentication and resource loading
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore();
  const isAdminRoute = to.meta.isAdmin;

  // Unload resources for the previous route
  if (from.meta.isAdmin) {
    // unloadAdminResources();
  } else {
    unloadFrontendResources();
  }

  // Load resources for the current route
  if (isAdminRoute) {
    loadAdminResources();
  } else {
    loadFrontendResources();
  }

  if (isAdminRoute && !authStore.isAuthenticated) {
    next('/login');
  } else if (authStore.isAuthenticated && to.path === '/login') {
    next('/');
  } else {
    next();
  }
});

export default router;
