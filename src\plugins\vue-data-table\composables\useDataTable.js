import {
  computed,
  nextTick,
  ref,
  watch,
} from 'vue'

import * as filterUtils from '../utils/filterUtils'
import * as sortUtils from '../utils/sortUtils'

/**
 * Main composable for data table functionality
 * @param {Object} options - Configuration options
 * @returns {Object} - Reactive state and methods
 */
export function useDataTable(options = {}) {
  const {
    items: initialItems = [],
    columns: initialColumns = [],
    serverSide = false,
    initialPageSize = 10,
    initialSort = null,
    initialFilters = [],
    searchable = true,
    selectable = false
  } = options

  // Reactive state
  const items = ref([...initialItems])
  const columns = ref([...initialColumns])
  const loading = ref(false)
  const searchQuery = ref('')
  const selectedItems = ref([])

  // Pagination state
  const currentPage = ref(1)
  const pageSize = ref(initialPageSize)
  const totalItems = ref(0)

  // Sorting state
  const sortKey = ref(initialSort?.key || '')
  const sortOrder = ref(initialSort?.order || 'asc')

  // Filtering state
  const filters = ref([...initialFilters])
  const columnFilters = ref({})

  // Computed properties
  const filteredItems = computed(() => {
    if (serverSide.value) return items.value

    let result = [...items.value]

    // Apply search filter
    if (searchQuery.value) {
      result = filterUtils.applySearch(result, searchQuery.value, columns.value)
    }

    // Apply column filters
    if (Object.keys(columnFilters.value).length > 0) {
      result = filterUtils.applyColumnFilters(result, columnFilters.value)
    }

    // Apply additional filters
    if (filters.value.length > 0) {
      result = filterUtils.applyFilters(result, filters.value)
    }

    return result
  })

  const sortedItems = computed(() => {
    if (serverSide.value) return filteredItems.value

    if (!sortKey.value) return filteredItems.value

    return sortUtils.sortData(filteredItems.value, sortKey.value, sortOrder.value)
  })

  const paginatedItems = computed(() => {
    if (serverSide.value) return sortedItems.value

    const start = (currentPage.value - 1) * pageSize.value
    const end = start + pageSize.value

    return sortedItems.value.slice(start, end)
  })

  const totalPages = computed(() => {
    if (serverSide.value) {
      return Math.ceil(totalItems.value / pageSize.value)
    }
    return Math.ceil(filteredItems.value.length / pageSize.value)
  })

  const paginationInfo = computed(() => {
    const total = serverSide.value ? totalItems.value : filteredItems.value.length
    const start = total === 0 ? 0 : (currentPage.value - 1) * pageSize.value + 1
    const end = Math.min(currentPage.value * pageSize.value, total)

    return {
      start,
      end,
      total,
      page: currentPage.value,
      pageSize: pageSize.value,
      totalPages: totalPages.value
    }
  })

  const hasSelection = computed(() => selectedItems.value.length > 0)
  const isAllSelected = computed(() => {
    if (paginatedItems.value.length === 0) return false
    return paginatedItems.value.every(item => isItemSelected(item))
  })

  // Methods
  const setItems = (newItems) => {
    items.value = [...newItems]
    if (!serverSide) {
      totalItems.value = newItems.length
    }
  }

  const setColumns = (newColumns) => {
    columns.value = [...newColumns]
  }

  const setLoading = (state) => {
    loading.value = state
  }

  const setTotalItems = (total) => {
    totalItems.value = total
  }

  const search = (query) => {
    searchQuery.value = query
    if (!serverSide) {
      currentPage.value = 1
    }
  }

  const sort = (key, order = null) => {
    if (sortKey.value === key && !order) {
      sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
    } else {
      sortKey.value = key
      sortOrder.value = order || 'asc'
    }

    if (!serverSide) {
      currentPage.value = 1
    }
  }

  const filter = (key, value, operator = 'eq') => {
    const existingIndex = filters.value.findIndex(f => f.key === key)

    if (value === null || value === undefined || value === '') {
      if (existingIndex !== -1) {
        filters.value.splice(existingIndex, 1)
      }
    } else {
      const filterObj = { key, value, operator }
      if (existingIndex !== -1) {
        filters.value[existingIndex] = filterObj
      } else {
        filters.value.push(filterObj)
      }
    }

    if (!serverSide) {
      currentPage.value = 1
    }
  }

  const setColumnFilter = (key, value) => {
    if (value === null || value === undefined || value === '') {
      delete columnFilters.value[key]
    } else {
      columnFilters.value[key] = value
    }

    if (!serverSide) {
      currentPage.value = 1
    }
  }

  const clearFilters = () => {
    filters.value = []
    columnFilters.value = {}
    searchQuery.value = ''
    if (!serverSide) {
      currentPage.value = 1
    }
  }

  const goToPage = (page) => {
    if (page >= 1 && page <= totalPages.value) {
      currentPage.value = page
    }
  }

  const changePageSize = (size) => {
    pageSize.value = size
    currentPage.value = 1
  }

  const getItemKey = (item, index) => {
    return item.id || item._id || index
  }

  const isItemSelected = (item) => {
    const key = getItemKey(item)
    return selectedItems.value.some(selected => getItemKey(selected) === key)
  }

  const selectItem = (item) => {
    if (!isItemSelected(item)) {
      selectedItems.value.push(item)
    }
  }

  const unselectItem = (item) => {
    const key = getItemKey(item)
    selectedItems.value = selectedItems.value.filter(selected => getItemKey(selected) !== key)
  }

  const toggleItemSelection = (item) => {
    if (isItemSelected(item)) {
      unselectItem(item)
    } else {
      selectItem(item)
    }
  }

  const selectAll = () => {
    paginatedItems.value.forEach(item => {
      if (!isItemSelected(item)) {
        selectedItems.value.push(item)
      }
    })
  }

  const unselectAll = () => {
    const currentPageKeys = paginatedItems.value.map(item => getItemKey(item))
    selectedItems.value = selectedItems.value.filter(
      selected => !currentPageKeys.includes(getItemKey(selected))
    )
  }

  const toggleSelectAll = () => {
    if (isAllSelected.value) {
      unselectAll()
    } else {
      selectAll()
    }
  }

  const clearSelection = () => {
    selectedItems.value = []
  }

  const refresh = () => {
    // This method should be overridden by the parent component for server-side data
    if (!serverSide) {
      // For client-side, we can just reset to first page
      currentPage.value = 1
    }
  }

  // Watchers for server-side operations
  if (serverSide) {
    watch([currentPage, pageSize, sortKey, sortOrder, filters, searchQuery], () => {
      // Emit events for server-side handling
      nextTick(() => {
        refresh()
      })
    }, { deep: true })
  }

  return {
    // State
    items,
    columns,
    loading,
    searchQuery,
    selectedItems,
    currentPage,
    pageSize,
    totalItems,
    sortKey,
    sortOrder,
    filters,
    columnFilters,

    // Computed
    filteredItems,
    sortedItems,
    paginatedItems,
    totalPages,
    paginationInfo,
    hasSelection,
    isAllSelected,

    // Methods
    setItems,
    setColumns,
    setLoading,
    setTotalItems,
    search,
    sort,
    filter,
    setColumnFilter,
    clearFilters,
    goToPage,
    changePageSize,
    getItemKey,
    isItemSelected,
    selectItem,
    unselectItem,
    toggleItemSelection,
    selectAll,
    unselectAll,
    toggleSelectAll,
    clearSelection,
    refresh
  }
}
