.schedule-style1 {
  display: flex;
  align-items: center;
  margin-bottom: 30px;

  .schedule-img {
    overflow: hidden;
    margin-right: 20px;
    border-radius: 50%;

    img {
      width: 100%;
      transform: scale(1.001);
      transition: all ease 0.4s;
    }
  }

  &:hover {
    .schedule-img {
      img {
        transform: scale(1.15);
      }
    }
  }

  .schedule-content {
    flex: 1;
  }

  .schedule-title {
    position: relative;
    padding: 0 0 8px 0;
    margin: -0.2em 0 25px 0;

    &:before {
      content: '';
      position: absolute;
      left: 0;
      bottom: 0;
      width: 30px;
      height: 5px;
      background-color: $theme-color2;
      border-radius: 5px;
    }
  }

  .schedule-table {
    .tr {
      background-color: $white-color;
      color: $title-color;
      display: flex;
      font-size: 16px;
      font-weight: 500;
      width: 100%;
      margin-bottom: 5px;
      border-radius: 9999px;
      padding: 14px 30px;
      gap: 0 25px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .th {
      color: $theme-color;
    }
  }
}


@include lg {
  .schedule-style1 {
    display: block;
    text-align: center;

    .schedule-table {
      .tr {
        justify-content: center;
      }
    }

    .schedule-img {
      margin-right: 0;
      margin-bottom: 25px;
    }

    .schedule-title {
      &:before {
        left: 50%;
        margin-left: -15px;
      }
    }
  }
}