<template>
  <div class="table-search">
    <div class="search-input-wrapper">
      <input
        type="text"
        class="search-input"
        :placeholder="placeholder"
        v-model="searchQuery"
        @input="handleInput"
      />
      <span class="search-icon">
        <i class="fa fa-search"></i>
      </span>
      <button 
        v-if="searchQuery" 
        class="clear-button" 
        @click="clearSearch"
        aria-label="Clear search"
      >
        <i class="fa fa-times"></i>
      </button>
    </div>
  </div>
</template>

<script>
import { ref, watch } from 'vue';

export default {
  name: 'TableSearch',
  props: {
    placeholder: {
      type: String,
      default: 'Search...'
    },
    debounce: {
      type: Number,
      default: 300
    }
  },
  emits: ['search'],
  setup(props, { emit }) {
    const searchQuery = ref('');
    let debounceTimeout = null;
    
    const handleInput = () => {
      if (debounceTimeout) {
        clearTimeout(debounceTimeout);
      }
      
      debounceTimeout = setTimeout(() => {
        emit('search', searchQuery.value);
      }, props.debounce);
    };
    
    const clearSearch = () => {
      searchQuery.value = '';
      emit('search', '');
    };
    
    // Clean up the timeout when component is unmounted
    watch(() => searchQuery.value, () => {
      handleInput();
    });
    
    return {
      searchQuery,
      handleInput,
      clearSearch
    };
  }
};
</script>

<style>
.table-search {
  min-width: 200px;
  max-width: 300px;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  width: 100%;
  padding: 8px 36px 8px 12px;
  border: 1px solid var(--border-color, #e2e8f0);
  border-radius: 4px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
}

.search-input:focus {
  border-color: var(--color-primary, #3b82f6);
}

.search-icon {
  position: absolute;
  right: 12px;
  color: var(--color-light, #94a3b8);
  pointer-events: none;
}

.clear-button {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  color: var(--color-light, #94a3b8);
  cursor: pointer;
  padding: 0;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.clear-button:hover {
  color: var(--color-dark, #1e293b);
}

@media (max-width: 768px) {
  .table-search {
    width: 100%;
    max-width: none;
  }
}
</style>
