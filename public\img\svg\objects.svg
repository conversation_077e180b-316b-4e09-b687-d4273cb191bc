<svg xmlns="http://www.w3.org/2000/svg" width="275.029" height="218.63" viewBox="0 0 275.029 218.63"><g transform="translate(-139.404 -125.813)"><g transform="translate(153.255 125.813)"><path d="M345.547,370.056H255.74v-97.63A22.926,22.926,0,0,1,278.666,249.5h41.98a24.906,24.906,0,0,1,24.9,24.9Z" transform="translate(-170.52 -151.463)" fill="#272b41"/><g transform="translate(33.139)"><g transform="translate(0 85.479)"><path d="M199.164,340.547c9.8,2.657,23.2-29.74,27.756-41.635,1.018-2.648,1.6-4.278,1.6-4.278L214.439,285s-.871,1.544-2.251,4.114C205.607,301.449,187.554,337.4,199.164,340.547Z" transform="translate(-195.359 -241.821)" fill="#ffa19f"/><path d="M214.87,289.114l14.732,9.8c1.018-2.648,1.6-4.278,1.6-4.278L217.121,285S216.241,286.544,214.87,289.114Z" transform="translate(-198.041 -241.821)" fill="#0d0a3e"/><g transform="translate(15.785 8.047)"><path d="M241.39,278.521l-.1.7-.207,1.38-.75,5.029-.4,2.657-.19,1.268-.293,1.975-.88,5.857-9.712-6.529-.034-.017-.052-.035-1.173-.785-.086-.06-2.786-1.872-1.354-.906-9.712-6.521c2.13-4.063,4.313-8.03,6.486-11.756h0c.552-.94,1.1-1.872,1.656-2.777.474-.785.949-1.561,1.423-2.32h0c.129-.207.25-.414.38-.612,6.046-9.583,11.877-16.75,16.328-18.588a6.012,6.012,0,0,1,1.061-.336l.009.794.052,4.692.017,1.82.043,3.8.017,1.5.052,4.77.017,1.673.043,4.054.009,1.285.052,4.9.017,1.492Z" transform="translate(-213.66 -244.27)" fill="#9a5bfd"/></g><g transform="translate(29.637 33.573)"><path d="M229.72,294.009l.095.06,1.173.785.673-3.424,3.286-16.724a.711.711,0,0,0-.561-.828.7.7,0,0,0-.828.552l-2.7,13.749-.612,3.14Z" transform="translate(-229.72 -273.864)" fill="#fff"/></g><g transform="translate(17.847)"><path d="M333.7,308.575l-1.018.078-3.079.242-2.036.155-9.151.716-2.234.172-1.018.078-2,.155-6.245.483a17.1,17.1,0,0,1-.518-2.579l-.009.578-.155,9.013-.259,14.9s.052,17.113-.155,35.416H216.05c1.087-6.633,2.363-14.491,3.752-22.961.078-.483.155-.966.242-1.458q.414-2.562.854-5.175c.138-.854.285-1.716.423-2.579l.181-1.061.311-1.863c.587-3.536,1.182-7.125,1.777-10.721.034-.233.078-.466.121-.7.388-2.32.776-4.649,1.164-6.961.112-.656.224-1.3.328-1.949.147-.88.293-1.751.44-2.622.121-.725.25-1.44.371-2.165,1.07-6.288,2.122-12.429,3.14-18.217.5-2.864.992-5.632,1.475-8.289.034-.164.06-.328.086-.483.035-.207.078-.414.112-.621.017-.1.034-.207.052-.3l.181-.983c.035-.207.078-.414.112-.621,1.233-6.719,2.967-15.439,4.753-22.5.259-1.018.518-2,.776-2.941.1-.362.2-.716.3-1.061.164-.569.328-1.113.492-1.639,1.035-3.372,2.044-5.822,2.941-6.641a1.316,1.316,0,0,1,.336-.233c.2-.086.388-.172.578-.259s.354-.155.526-.233h0a96.406,96.406,0,0,1,9.669-3.605c.586-.181,1.173-.362,1.742-.526.526-.155,1.044-.3,1.553-.449.932-.259,1.837-.5,2.708-.716a91.936,91.936,0,0,1,14.094-2.5l19.407.561s3.649,2.113,9.565,6.34h0c.819.587,1.682,1.216,2.579,1.88,3.554,2.622,7.711,5.874,12.274,9.747,0,0,.552.388,1.458,1.182l.017.017c.3.267.647.578,1.018.94h0a43.115,43.115,0,0,1,9.936,14.939c.19.466.4,1.1.63,1.863.25.819.518,1.8.794,2.924.095.371.181.75.285,1.147.121.509.242,1.044.371,1.6.647,2.907,1.32,6.383,1.915,10.135q.142.906.285,1.837h0c.31,2.1.586,4.27.819,6.46q.116,1.035.207,2.07c.069.759.129,1.518.19,2.268s.112,1.509.147,2.251c.095,1.6.147,3.165.155,4.7h0C333.7,308.048,333.7,308.307,333.7,308.575Z" transform="translate(-216.05 -234.94)" fill="#9a5bfd"/></g><g transform="translate(21.057 1.639)"><g transform="translate(21.018)"><g transform="translate(0 6.15)"><path d="M245.537,244.8a.764.764,0,0,1-.328.2.813.813,0,0,1-.811-.2l-.259-.259a6.006,6.006,0,0,1,1.061-.336c.181-.078.354-.155.526-.233h0A.789.789,0,0,1,245.537,244.8Z" transform="translate(-244.14 -243.97)" fill="#832ddb"/></g><g transform="translate(9.852 0.854)"><path d="M255.8,244.7a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,1,0-1.139-1.139l-1.509,1.509A.8.8,0,0,0,255.8,244.7Z" transform="translate(-255.563 -238.377)" fill="#832ddb"/><path d="M263.664,237.83a.757.757,0,0,1-.233.518l-1.509,1.509a.8.8,0,0,1-1.139,0,.812.812,0,0,1,0-1.139l.173-.172C261.887,238.287,262.784,238.046,263.664,237.83Z" transform="translate(-256.248 -237.83)" fill="#832ddb"/><path d="M260.543,242.961l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.805.805,0,1,0-1.139,1.139Z" transform="translate(-256.215 -238.347)" fill="#832ddb"/><path d="M258.873,240.241a.8.8,0,0,1-1.139,0l-.535-.535c.587-.181,1.173-.362,1.742-.526A.8.8,0,0,1,258.873,240.241Z" transform="translate(-255.788 -238.016)" fill="#832ddb"/></g><g transform="translate(25.29)"><path d="M273.693,244.7a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.812.812,0,0,0,273.693,244.7Z" transform="translate(-273.46 -237.523)" fill="#832ddb"/><path d="M278.673,239.721a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.812.812,0,0,0,278.673,239.721Z" transform="translate(-274.145 -236.84)" fill="#832ddb"/><path d="M278.442,242.961l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,278.442,242.961Z" transform="translate(-274.112 -237.493)" fill="#832ddb"/><path d="M273.932,238.45l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.805.805,0,1,0-1.139,1.139Z" transform="translate(-273.492 -236.872)" fill="#832ddb"/></g><g transform="translate(40.722)"><path d="M291.592,244.7a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.8.8,0,0,0,291.592,244.7Z" transform="translate(-291.353 -237.523)" fill="#832ddb"/><path d="M296.572,239.721a.8.8,0,0,0,1.139,0l1.51-1.509a.8.8,0,0,0,0-1.139.812.812,0,0,0-1.139,0l-1.51,1.509A.8.8,0,0,0,296.572,239.721Z" transform="translate(-292.037 -236.84)" fill="#832ddb"/><path d="M296.333,242.961l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.805.805,0,1,0-1.139,1.139Z" transform="translate(-292.005 -237.493)" fill="#832ddb"/><path d="M291.823,238.45l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.8.8,0,0,0-1.139,0A.812.812,0,0,0,291.823,238.45Z" transform="translate(-291.385 -236.872)" fill="#832ddb"/></g><g transform="translate(56.16 4.289)"><path d="M309.483,244.7a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.812.812,0,0,0,309.483,244.7Z" transform="translate(-309.25 -241.812)" fill="#832ddb"/><path d="M316.678,244.81a.805.805,0,0,1-.923-.155l-1.509-1.509a.91.91,0,0,1-.155-.216C314.918,243.516,315.772,244.146,316.678,244.81Z" transform="translate(-309.915 -241.966)" fill="#832ddb"/></g></g><g transform="translate(15.437 11.862)"><path d="M237.9,258.45a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,1,0-1.139-1.139l-1.509,1.509A.812.812,0,0,0,237.9,258.45Z" transform="translate(-237.67 -251.276)" fill="#832ddb"/><path d="M242.883,253.48a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,1,0-1.139-1.139l-1.509,1.509A.812.812,0,0,0,242.883,253.48Z" transform="translate(-238.355 -250.592)" fill="#832ddb"/><path d="M242.652,256.711l1.509,1.509a.805.805,0,1,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,242.652,256.711Z" transform="translate(-238.322 -251.245)" fill="#832ddb"/><path d="M238.133,252.2l1.509,1.509a.805.805,0,1,0,1.139-1.139l-1.509-1.509a.805.805,0,1,0-1.139,1.139Z" transform="translate(-237.702 -250.625)" fill="#832ddb"/><g transform="translate(15.433)"><path d="M255.8,258.45a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,1,0-1.139-1.139l-1.509,1.509A.8.8,0,0,0,255.8,258.45Z" transform="translate(-255.563 -251.276)" fill="#832ddb"/><path d="M260.782,253.48a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,1,0-1.139-1.139l-1.509,1.509A.8.8,0,0,0,260.782,253.48Z" transform="translate(-256.247 -250.592)" fill="#832ddb"/><path d="M260.543,256.711l1.509,1.509a.812.812,0,0,0,1.139,0,.8.8,0,0,0,0-1.139l-1.509-1.509a.805.805,0,1,0-1.139,1.139Z" transform="translate(-256.215 -251.245)" fill="#832ddb"/><path d="M256.032,252.2l1.509,1.509a.805.805,0,1,0,1.139-1.139l-1.509-1.509a.805.805,0,1,0-1.139,1.139Z" transform="translate(-255.594 -250.625)" fill="#832ddb"/></g><g transform="translate(30.87)"><path d="M273.693,258.45a.8.8,0,0,0,1.139,0l1.509-1.509A.805.805,0,1,0,275.2,255.8l-1.509,1.509A.812.812,0,0,0,273.693,258.45Z" transform="translate(-273.46 -251.276)" fill="#832ddb"/><path d="M278.673,253.48a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.812.812,0,0,0,278.673,253.48Z" transform="translate(-274.145 -250.592)" fill="#832ddb"/><path d="M278.442,256.711l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,278.442,256.711Z" transform="translate(-274.112 -251.245)" fill="#832ddb"/><path d="M273.932,252.2l1.509,1.509a.805.805,0,1,0,1.139-1.139l-1.509-1.509a.805.805,0,1,0-1.139,1.139Z" transform="translate(-273.492 -250.625)" fill="#832ddb"/></g><g transform="translate(46.303)"><path d="M291.592,258.45a.8.8,0,0,0,1.139,0l1.509-1.509A.805.805,0,1,0,293.1,255.8l-1.509,1.509A.8.8,0,0,0,291.592,258.45Z" transform="translate(-291.353 -251.276)" fill="#832ddb"/><path d="M296.572,253.48a.8.8,0,0,0,1.139,0l1.51-1.509a.805.805,0,1,0-1.139-1.139l-1.51,1.509A.8.8,0,0,0,296.572,253.48Z" transform="translate(-292.037 -250.592)" fill="#832ddb"/><path d="M296.333,256.711l1.509,1.509a.812.812,0,0,0,1.139,0,.8.8,0,0,0,0-1.139l-1.509-1.509a.805.805,0,1,0-1.139,1.139Z" transform="translate(-292.005 -251.245)" fill="#832ddb"/><path d="M291.823,252.2l1.509,1.509a.805.805,0,1,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-291.385 -250.625)" fill="#832ddb"/></g><g transform="translate(61.74)"><path d="M309.483,258.45a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,1,0-1.139-1.139l-1.509,1.509A.812.812,0,0,0,309.483,258.45Z" transform="translate(-309.25 -251.276)" fill="#832ddb"/><path d="M314.463,253.48a.8.8,0,0,0,1.138,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.812.812,0,0,0,314.463,253.48Z" transform="translate(-309.935 -250.592)" fill="#832ddb"/><path d="M314.232,256.711l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.138,0A.8.8,0,0,0,314.232,256.711Z" transform="translate(-309.902 -251.245)" fill="#832ddb"/><path d="M309.722,252.2l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-309.282 -250.625)" fill="#832ddb"/></g><g transform="translate(77.173 4.287)"><path d="M327.382,258.45a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,1,0-1.139-1.139l-1.509,1.509A.8.8,0,0,0,327.382,258.45Z" transform="translate(-327.142 -255.563)" fill="#832ddb"/><path d="M334.048,258.73a.724.724,0,0,1-.25-.173l-.768-.768C333.332,258.057,333.668,258.368,334.048,258.73Z" transform="translate(-327.952 -255.869)" fill="#832ddb"/></g></g><g transform="translate(1.223 23.72)"><path d="M222.855,270.737l-1.509,1.509a.744.744,0,0,1-.155.121c.552-.94,1.1-1.872,1.656-2.777l.009.009A.8.8,0,0,1,222.855,270.737Z" transform="translate(-221.19 -265.062)" fill="#832ddb"/><path d="M227.641,265.72l-1.509,1.509a.812.812,0,0,1-1.139,0,.8.8,0,0,1-.224-.69h0c.129-.207.25-.414.38-.612l1.354-1.354a.812.812,0,0,1,1.139,0A.823.823,0,0,1,227.641,265.72Z" transform="translate(-221.681 -264.34)" fill="#832ddb"/><path d="M224.753,270.461l1.509,1.509a.812.812,0,0,0,1.139,0,.8.8,0,0,0,0-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-221.648 -264.993)" fill="#832ddb"/><g transform="translate(14.215 0.002)"><path d="M237.9,272.211a.8.8,0,0,0,1.139,0l1.509-1.509a.8.8,0,0,0,0-1.139.812.812,0,0,0-1.139,0l-1.509,1.509A.812.812,0,0,0,237.9,272.211Z" transform="translate(-237.67 -265.028)" fill="#832ddb"/><path d="M242.883,267.23a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,1,0-1.139-1.139l-1.509,1.509A.812.812,0,0,0,242.883,267.23Z" transform="translate(-238.355 -264.342)" fill="#832ddb"/><path d="M242.652,270.461l1.509,1.509a.805.805,0,1,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,242.652,270.461Z" transform="translate(-238.322 -264.995)" fill="#832ddb"/><path d="M238.133,265.951l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-237.702 -264.375)" fill="#832ddb"/></g><g transform="translate(29.647 0.002)"><path d="M255.8,272.211a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,1,0-1.139-1.139l-1.509,1.509A.8.8,0,0,0,255.8,272.211Z" transform="translate(-255.563 -265.028)" fill="#832ddb"/><path d="M260.782,267.23a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,1,0-1.139-1.139l-1.509,1.509A.8.8,0,0,0,260.782,267.23Z" transform="translate(-256.247 -264.342)" fill="#832ddb"/><path d="M260.543,270.461l1.509,1.509a.812.812,0,0,0,1.139,0,.8.8,0,0,0,0-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-256.215 -264.995)" fill="#832ddb"/><path d="M256.032,265.951l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-255.595 -264.375)" fill="#832ddb"/></g><g transform="translate(45.085 0.002)"><path d="M273.693,272.211a.8.8,0,0,0,1.139,0l1.509-1.509a.8.8,0,0,0,0-1.139.812.812,0,0,0-1.139,0l-1.509,1.509A.812.812,0,0,0,273.693,272.211Z" transform="translate(-273.46 -265.028)" fill="#832ddb"/><path d="M278.673,267.23a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.812.812,0,0,0,278.673,267.23Z" transform="translate(-274.145 -264.342)" fill="#832ddb"/><path d="M278.442,270.461l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,278.442,270.461Z" transform="translate(-274.112 -264.995)" fill="#832ddb"/><path d="M273.932,265.951l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-273.492 -264.375)" fill="#832ddb"/></g><g transform="translate(60.518 0.002)"><path d="M291.592,272.211a.8.8,0,0,0,1.139,0l1.509-1.509a.8.8,0,0,0,0-1.139.812.812,0,0,0-1.139,0l-1.509,1.509A.8.8,0,0,0,291.592,272.211Z" transform="translate(-291.353 -265.028)" fill="#832ddb"/><path d="M296.572,267.23a.8.8,0,0,0,1.139,0l1.51-1.509a.805.805,0,1,0-1.139-1.139l-1.51,1.509A.8.8,0,0,0,296.572,267.23Z" transform="translate(-292.037 -264.342)" fill="#832ddb"/><path d="M296.333,270.461l1.509,1.509a.812.812,0,0,0,1.139,0,.8.8,0,0,0,0-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-292.005 -264.995)" fill="#832ddb"/><path d="M291.823,265.951l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-291.385 -264.375)" fill="#832ddb"/></g><g transform="translate(75.955 0.002)"><path d="M309.483,272.211a.8.8,0,0,0,1.139,0l1.509-1.509a.8.8,0,0,0,0-1.139.812.812,0,0,0-1.139,0l-1.509,1.509A.812.812,0,0,0,309.483,272.211Z" transform="translate(-309.25 -265.028)" fill="#832ddb"/><path d="M314.463,267.23a.8.8,0,0,0,1.138,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.812.812,0,0,0,314.463,267.23Z" transform="translate(-309.935 -264.342)" fill="#832ddb"/><path d="M314.232,270.461l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.138,0A.8.8,0,0,0,314.232,270.461Z" transform="translate(-309.902 -264.995)" fill="#832ddb"/><path d="M309.722,265.951l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-309.282 -264.375)" fill="#832ddb"/></g><g transform="translate(91.388 0.002)"><path d="M327.382,272.211a.8.8,0,0,0,1.139,0l1.509-1.509a.8.8,0,0,0,0-1.139.812.812,0,0,0-1.139,0l-1.509,1.509A.8.8,0,0,0,327.382,272.211Z" transform="translate(-327.142 -265.028)" fill="#832ddb"/><path d="M332.362,267.23a.8.8,0,0,0,1.138,0l1.509-1.509a.805.805,0,1,0-1.139-1.139l-1.509,1.509A.8.8,0,0,0,332.362,267.23Z" transform="translate(-327.827 -264.342)" fill="#832ddb"/><path d="M332.132,270.461l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,332.132,270.461Z" transform="translate(-327.795 -264.995)" fill="#832ddb"/><path d="M327.613,265.951l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.805.805,0,1,0-1.139,1.139Z" transform="translate(-327.175 -264.375)" fill="#832ddb"/></g></g><g transform="translate(0 35.582)"><path d="M220.012,285.96a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.8.8,0,0,0,220.012,285.96Z" transform="translate(-219.773 -278.777)" fill="#832ddb"/><path d="M224.983,280.98a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.812.812,0,0,0,224.983,280.98Z" transform="translate(-220.457 -278.092)" fill="#832ddb"/><path d="M224.753,284.211l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-220.425 -278.745)" fill="#832ddb"/><path d="M220.242,279.7l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,220.242,279.7Z" transform="translate(-219.804 -278.125)" fill="#832ddb"/><g transform="translate(15.437)"><path d="M237.9,285.96a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.812.812,0,0,0,237.9,285.96Z" transform="translate(-237.67 -278.777)" fill="#832ddb"/><path d="M242.883,280.98a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.812.812,0,0,0,242.883,280.98Z" transform="translate(-238.355 -278.092)" fill="#832ddb"/><path d="M242.652,284.211l1.509,1.509a.805.805,0,1,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-238.322 -278.745)" fill="#832ddb"/><path d="M238.133,279.7l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-237.702 -278.125)" fill="#832ddb"/></g><g transform="translate(30.87)"><path d="M255.8,285.96a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,1,0-1.139-1.139l-1.509,1.509A.8.8,0,0,0,255.8,285.96Z" transform="translate(-255.563 -278.777)" fill="#832ddb"/><path d="M260.782,280.98a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.8.8,0,0,0,260.782,280.98Z" transform="translate(-256.247 -278.092)" fill="#832ddb"/><path d="M260.543,284.211l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-256.215 -278.745)" fill="#832ddb"/><path d="M256.032,279.7l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,256.032,279.7Z" transform="translate(-255.594 -278.125)" fill="#832ddb"/></g><g transform="translate(46.307)"><path d="M273.693,285.96a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.812.812,0,0,0,273.693,285.96Z" transform="translate(-273.46 -278.777)" fill="#832ddb"/><path d="M278.673,280.98a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.812.812,0,0,0,278.673,280.98Z" transform="translate(-274.145 -278.092)" fill="#832ddb"/><path d="M278.442,284.211l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.805.805,0,1,0-1.139,1.139Z" transform="translate(-274.112 -278.745)" fill="#832ddb"/><path d="M273.932,279.7l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,273.932,279.7Z" transform="translate(-273.492 -278.125)" fill="#832ddb"/></g><g transform="translate(61.74)"><path d="M291.592,285.96a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.8.8,0,0,0,291.592,285.96Z" transform="translate(-291.353 -278.777)" fill="#832ddb"/><path d="M296.572,280.98a.8.8,0,0,0,1.139,0l1.51-1.509a.805.805,0,0,0-1.139-1.139l-1.51,1.509A.8.8,0,0,0,296.572,280.98Z" transform="translate(-292.037 -278.092)" fill="#832ddb"/><path d="M296.333,284.211l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-292.005 -278.745)" fill="#832ddb"/><path d="M291.823,279.7l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-291.385 -278.125)" fill="#832ddb"/></g><g transform="translate(77.177)"><path d="M309.483,285.96a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.812.812,0,0,0,309.483,285.96Z" transform="translate(-309.25 -278.777)" fill="#832ddb"/><path d="M314.463,280.98a.8.8,0,0,0,1.138,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.812.812,0,0,0,314.463,280.98Z" transform="translate(-309.935 -278.092)" fill="#832ddb"/><path d="M314.232,284.211l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-309.902 -278.745)" fill="#832ddb"/><path d="M309.722,279.7l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,309.722,279.7Z" transform="translate(-309.282 -278.125)" fill="#832ddb"/></g><g transform="translate(92.61)"><path d="M327.382,285.96a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.8.8,0,0,0,327.382,285.96Z" transform="translate(-327.142 -278.777)" fill="#832ddb"/><path d="M332.362,280.98a.8.8,0,0,0,1.138,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.8.8,0,0,0,332.362,280.98Z" transform="translate(-327.827 -278.092)" fill="#832ddb"/><path d="M332.132,284.211l1.509,1.509a.8.8,0,0,0,1.139,0,.812.812,0,0,0,0-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-327.795 -278.745)" fill="#832ddb"/><path d="M327.613,279.7l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.805.805,0,1,0-1.139,1.139Z" transform="translate(-327.175 -278.125)" fill="#832ddb"/></g><g transform="translate(108.056 0.201)"><path d="M347.707,284.666l-1.285,1.285a.805.805,0,0,1-1.139-1.139l1.509-1.509a.8.8,0,0,1,.552-.233C347.456,283.588,347.586,284.114,347.707,284.666Z" transform="translate(-345.05 -278.978)" fill="#832ddb"/><path d="M347.091,281.271a.488.488,0,0,1-.069-.06l-1.509-1.509a.806.806,0,0,1,.785-1.346C346.539,279.175,346.815,280.158,347.091,281.271Z" transform="translate(-345.082 -278.326)" fill="#832ddb"/></g></g><g transform="translate(4.44 47.448)"><path d="M227.663,293.221l-1.389,1.389-1.354-.906a1.266,1.266,0,0,0,.086-.112l1.509-1.509a.808.808,0,0,1,1.147,1.139Z" transform="translate(-224.92 -291.85)" fill="#832ddb"/><g transform="translate(10.997)"><path d="M237.9,299.711a.8.8,0,0,0,1.139,0l1.509-1.509a.8.8,0,0,0,0-1.139.812.812,0,0,0-1.139,0l-1.509,1.509A.812.812,0,0,0,237.9,299.711Z" transform="translate(-237.67 -292.535)" fill="#832ddb"/><path d="M242.883,294.731a.8.8,0,0,0,1.139,0l1.509-1.509a.8.8,0,0,0,0-1.139.812.812,0,0,0-1.139,0l-1.509,1.509A.812.812,0,0,0,242.883,294.731Z" transform="translate(-238.355 -291.85)" fill="#832ddb"/><path d="M242.652,297.971l1.509,1.509a.805.805,0,1,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,242.652,297.971Z" transform="translate(-238.322 -292.503)" fill="#832ddb"/><path d="M238.133,293.451l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-237.702 -291.882)" fill="#832ddb"/></g><g transform="translate(26.43)"><path d="M255.8,299.711a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.8.8,0,0,0,255.8,299.711Z" transform="translate(-255.563 -292.535)" fill="#832ddb"/><path d="M260.782,294.731a.8.8,0,0,0,1.139,0l1.509-1.509a.8.8,0,0,0,0-1.139.812.812,0,0,0-1.139,0l-1.509,1.509A.8.8,0,0,0,260.782,294.731Z" transform="translate(-256.247 -291.85)" fill="#832ddb"/><path d="M260.543,297.971l1.509,1.509a.812.812,0,0,0,1.139,0,.8.8,0,0,0,0-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-256.215 -292.503)" fill="#832ddb"/><path d="M256.032,293.451l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-255.595 -291.882)" fill="#832ddb"/></g><g transform="translate(41.867)"><path d="M273.693,299.711a.8.8,0,0,0,1.139,0l1.509-1.509a.8.8,0,0,0,0-1.139.812.812,0,0,0-1.139,0l-1.509,1.509A.812.812,0,0,0,273.693,299.711Z" transform="translate(-273.46 -292.535)" fill="#832ddb"/><path d="M278.673,294.731a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.812.812,0,0,0,278.673,294.731Z" transform="translate(-274.145 -291.85)" fill="#832ddb"/><path d="M278.442,297.971l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,278.442,297.971Z" transform="translate(-274.112 -292.503)" fill="#832ddb"/><path d="M273.932,293.451l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,273.932,293.451Z" transform="translate(-273.492 -291.882)" fill="#832ddb"/></g><g transform="translate(57.3)"><path d="M291.592,299.711a.8.8,0,0,0,1.139,0l1.509-1.509a.8.8,0,0,0,0-1.139.812.812,0,0,0-1.139,0l-1.509,1.509A.8.8,0,0,0,291.592,299.711Z" transform="translate(-291.353 -292.535)" fill="#832ddb"/><path d="M296.572,294.731a.8.8,0,0,0,1.139,0l1.51-1.509a.8.8,0,0,0,0-1.139.812.812,0,0,0-1.139,0l-1.51,1.509A.8.8,0,0,0,296.572,294.731Z" transform="translate(-292.037 -291.85)" fill="#832ddb"/><path d="M296.333,297.971l1.509,1.509a.812.812,0,0,0,1.139,0,.8.8,0,0,0,0-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-292.005 -292.503)" fill="#832ddb"/><path d="M291.823,293.451l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-291.385 -291.882)" fill="#832ddb"/></g><g transform="translate(72.738)"><path d="M309.483,299.711a.8.8,0,0,0,1.139,0l1.509-1.509a.8.8,0,0,0,0-1.139.812.812,0,0,0-1.139,0l-1.509,1.509A.812.812,0,0,0,309.483,299.711Z" transform="translate(-309.25 -292.535)" fill="#832ddb"/><path d="M314.463,294.731a.8.8,0,0,0,1.138,0l1.509-1.509a.8.8,0,0,0,0-1.139.812.812,0,0,0-1.139,0l-1.509,1.509A.812.812,0,0,0,314.463,294.731Z" transform="translate(-309.935 -291.85)" fill="#832ddb"/><path d="M314.232,297.971l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.138,0A.8.8,0,0,0,314.232,297.971Z" transform="translate(-309.902 -292.503)" fill="#832ddb"/><path d="M309.722,293.451l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,309.722,293.451Z" transform="translate(-309.282 -291.882)" fill="#832ddb"/></g><g transform="translate(88.17)"><path d="M327.382,299.711a.8.8,0,0,0,1.139,0l1.509-1.509a.8.8,0,0,0,0-1.139.812.812,0,0,0-1.139,0l-1.509,1.509A.8.8,0,0,0,327.382,299.711Z" transform="translate(-327.142 -292.535)" fill="#832ddb"/><path d="M332.362,294.731a.8.8,0,0,0,1.138,0l1.509-1.509a.8.8,0,0,0,0-1.139.812.812,0,0,0-1.139,0l-1.509,1.509A.8.8,0,0,0,332.362,294.731Z" transform="translate(-327.827 -291.85)" fill="#832ddb"/><path d="M332.132,297.971l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,332.132,297.971Z" transform="translate(-327.795 -292.503)" fill="#832ddb"/><path d="M327.613,293.451l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.805.805,0,1,0-1.139,1.139Z" transform="translate(-327.175 -291.882)" fill="#832ddb"/></g><g transform="translate(103.61 0.198)"><path d="M345.282,299.711a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.8.8,0,0,0,345.282,299.711Z" transform="translate(-345.042 -292.733)" fill="#832ddb"/><path d="M350.557,298.508l-.535-.526a.8.8,0,0,1,0-1.139.9.9,0,0,1,.25-.164C350.367,297.275,350.462,297.888,350.557,298.508Z" transform="translate(-345.694 -292.712)" fill="#832ddb"/><path d="M345.512,293.451l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,345.512,293.451Z" transform="translate(-345.074 -292.08)" fill="#832ddb"/></g></g><g transform="translate(15.437 59.301)"><path d="M237.9,313.461a.8.8,0,0,0,1.139,0l1.509-1.509a.8.8,0,0,0,0-1.139.812.812,0,0,0-1.139,0l-1.509,1.509A.812.812,0,0,0,237.9,313.461Z" transform="translate(-237.67 -306.278)" fill="#832ddb"/><path d="M242.883,308.48a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.812.812,0,0,0,242.883,308.48Z" transform="translate(-238.355 -305.592)" fill="#832ddb"/><path d="M242.652,311.721l1.509,1.509a.805.805,0,1,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,242.652,311.721Z" transform="translate(-238.322 -306.246)" fill="#832ddb"/><path d="M238.133,307.211l1.509,1.509a.805.805,0,1,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-237.702 -305.626)" fill="#832ddb"/><g transform="translate(15.433)"><path d="M255.8,313.461a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,1,0-1.139-1.139l-1.509,1.509A.8.8,0,0,0,255.8,313.461Z" transform="translate(-255.563 -306.278)" fill="#832ddb"/><path d="M260.782,308.48a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.8.8,0,0,0,260.782,308.48Z" transform="translate(-256.247 -305.592)" fill="#832ddb"/><path d="M260.543,311.721l1.509,1.509a.812.812,0,0,0,1.139,0,.8.8,0,0,0,0-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-256.215 -306.246)" fill="#832ddb"/><path d="M256.032,307.211l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,256.032,307.211Z" transform="translate(-255.594 -305.626)" fill="#832ddb"/></g><g transform="translate(30.87)"><path d="M273.693,313.461a.8.8,0,0,0,1.139,0l1.509-1.509a.8.8,0,0,0,0-1.139.812.812,0,0,0-1.139,0l-1.509,1.509A.812.812,0,0,0,273.693,313.461Z" transform="translate(-273.46 -306.278)" fill="#832ddb"/><path d="M278.673,308.48a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.812.812,0,0,0,278.673,308.48Z" transform="translate(-274.145 -305.592)" fill="#832ddb"/><path d="M278.442,311.721l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,278.442,311.721Z" transform="translate(-274.112 -306.246)" fill="#832ddb"/><path d="M273.932,307.211l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,273.932,307.211Z" transform="translate(-273.492 -305.626)" fill="#832ddb"/></g><g transform="translate(46.303)"><path d="M291.592,313.461a.8.8,0,0,0,1.139,0l1.509-1.509a.8.8,0,0,0,0-1.139.812.812,0,0,0-1.139,0l-1.509,1.509A.8.8,0,0,0,291.592,313.461Z" transform="translate(-291.353 -306.278)" fill="#832ddb"/><path d="M296.572,308.48a.8.8,0,0,0,1.139,0l1.51-1.509a.805.805,0,0,0-1.139-1.139l-1.51,1.509A.8.8,0,0,0,296.572,308.48Z" transform="translate(-292.037 -305.592)" fill="#832ddb"/><path d="M296.333,311.721l1.509,1.509a.812.812,0,0,0,1.139,0,.8.8,0,0,0,0-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-292.005 -306.246)" fill="#832ddb"/><path d="M291.823,307.211l1.509,1.509a.805.805,0,1,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-291.385 -305.626)" fill="#832ddb"/></g><g transform="translate(61.74)"><path d="M309.483,313.461a.8.8,0,0,0,1.139,0l1.509-1.509a.8.8,0,0,0,0-1.139.812.812,0,0,0-1.139,0l-1.509,1.509A.812.812,0,0,0,309.483,313.461Z" transform="translate(-309.25 -306.278)" fill="#832ddb"/><path d="M314.463,308.48a.8.8,0,0,0,1.138,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.812.812,0,0,0,314.463,308.48Z" transform="translate(-309.935 -305.592)" fill="#832ddb"/><path d="M314.232,311.721l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.138,0A.8.8,0,0,0,314.232,311.721Z" transform="translate(-309.902 -306.246)" fill="#832ddb"/><path d="M309.722,307.211l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,309.722,307.211Z" transform="translate(-309.282 -305.626)" fill="#832ddb"/></g><g transform="translate(77.173)"><path d="M327.382,313.461a.8.8,0,0,0,1.139,0l1.509-1.509a.8.8,0,0,0,0-1.139.812.812,0,0,0-1.139,0l-1.509,1.509A.8.8,0,0,0,327.382,313.461Z" transform="translate(-327.142 -306.278)" fill="#832ddb"/><path d="M332.362,308.48a.8.8,0,0,0,1.138,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.8.8,0,0,0,332.362,308.48Z" transform="translate(-327.827 -305.592)" fill="#832ddb"/><path d="M332.132,311.721l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,332.132,311.721Z" transform="translate(-327.795 -306.246)" fill="#832ddb"/><path d="M327.613,307.211l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.805.805,0,1,0-1.139,1.139Z" transform="translate(-327.175 -305.626)" fill="#832ddb"/></g><g transform="translate(92.613 0.213)"><path d="M345.282,313.461a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,1,0-1.139-1.139l-1.509,1.509A.8.8,0,0,0,345.282,313.461Z" transform="translate(-345.042 -306.492)" fill="#832ddb"/><path d="M351.617,308.36l-.216.216a.805.805,0,0,1-1.138-1.139l1.156-1.147C351.488,306.98,351.557,307.67,351.617,308.36Z" transform="translate(-345.728 -305.902)" fill="#832ddb"/><path d="M351.919,313.446a.792.792,0,0,1-.388-.216l-1.509-1.509a.8.8,0,0,1,0-1.139.812.812,0,0,1,1.139,0l.6.612C351.833,311.946,351.876,312.7,351.919,313.446Z" transform="translate(-345.694 -306.46)" fill="#832ddb"/><path d="M345.512,307.211l1.509,1.509a.805.805,0,1,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,345.512,307.211Z" transform="translate(-345.074 -305.84)" fill="#832ddb"/></g></g><g transform="translate(5.622 71.168)"><g transform="translate(0 0.009)"><path d="M227.972,320.731l-.8.794c.121-.724.25-1.44.371-2.165a.806.806,0,0,1,.431.224A.823.823,0,0,1,227.972,320.731Z" transform="translate(-226.411 -319.36)" fill="#832ddb"/><path d="M227.653,327.092a.812.812,0,0,1-1.139,0l-.224-.233c.112-.656.224-1.3.328-1.949l1.044,1.044A.82.82,0,0,1,227.653,327.092Z" transform="translate(-226.29 -320.123)" fill="#832ddb"/></g><g transform="translate(9.816 0.009)"><path d="M237.9,327.21a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,1,0-1.139-1.138l-1.509,1.509A.812.812,0,0,0,237.9,327.21Z" transform="translate(-237.67 -320.042)" fill="#832ddb"/><path d="M242.883,322.241a.8.8,0,0,0,1.139,0l1.509-1.509a.8.8,0,0,0,0-1.139.812.812,0,0,0-1.139,0l-1.509,1.509A.812.812,0,0,0,242.883,322.241Z" transform="translate(-238.355 -319.36)" fill="#832ddb"/><path d="M242.652,325.471l1.509,1.509a.805.805,0,1,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-238.322 -320.011)" fill="#832ddb"/><path d="M238.133,320.961l1.509,1.509a.805.805,0,1,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-237.702 -319.392)" fill="#832ddb"/></g><g transform="translate(25.249 0.009)"><path d="M255.8,327.21a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,1,0-1.139-1.138l-1.509,1.509A.8.8,0,0,0,255.8,327.21Z" transform="translate(-255.563 -320.042)" fill="#832ddb"/><path d="M260.782,322.241a.8.8,0,0,0,1.139,0l1.509-1.509a.8.8,0,0,0,0-1.139.812.812,0,0,0-1.139,0l-1.509,1.509A.8.8,0,0,0,260.782,322.241Z" transform="translate(-256.247 -319.36)" fill="#832ddb"/><path d="M260.543,325.471l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-256.215 -320.011)" fill="#832ddb"/><path d="M256.032,320.961l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,256.032,320.961Z" transform="translate(-255.594 -319.392)" fill="#832ddb"/></g><g transform="translate(40.686 0.009)"><path d="M273.693,327.21a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,1,0-1.139-1.138l-1.509,1.509A.812.812,0,0,0,273.693,327.21Z" transform="translate(-273.46 -320.042)" fill="#832ddb"/><path d="M278.673,322.241a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.812.812,0,0,0,278.673,322.241Z" transform="translate(-274.145 -319.36)" fill="#832ddb"/><path d="M278.442,325.471l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.805.805,0,1,0-1.139,1.139Z" transform="translate(-274.112 -320.011)" fill="#832ddb"/><path d="M273.932,320.961l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,273.932,320.961Z" transform="translate(-273.492 -319.392)" fill="#832ddb"/></g><g transform="translate(56.119 0.009)"><path d="M291.592,327.21a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,1,0-1.139-1.138l-1.509,1.509A.8.8,0,0,0,291.592,327.21Z" transform="translate(-291.353 -320.042)" fill="#832ddb"/><path d="M296.572,322.241a.8.8,0,0,0,1.139,0l1.51-1.509a.8.8,0,0,0,0-1.139.812.812,0,0,0-1.139,0l-1.51,1.509A.8.8,0,0,0,296.572,322.241Z" transform="translate(-292.037 -319.36)" fill="#832ddb"/><path d="M296.333,325.471l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-292.005 -320.011)" fill="#832ddb"/><path d="M291.823,320.961l1.509,1.509a.805.805,0,1,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-291.385 -319.392)" fill="#832ddb"/></g><g transform="translate(71.556 0.009)"><path d="M309.483,327.21a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,1,0-1.139-1.138l-1.509,1.509A.812.812,0,0,0,309.483,327.21Z" transform="translate(-309.25 -320.042)" fill="#832ddb"/><path d="M314.463,322.241a.8.8,0,0,0,1.138,0l1.509-1.509a.8.8,0,0,0,0-1.139.812.812,0,0,0-1.139,0l-1.509,1.509A.812.812,0,0,0,314.463,322.241Z" transform="translate(-309.935 -319.36)" fill="#832ddb"/><path d="M314.232,325.471l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-309.902 -320.011)" fill="#832ddb"/><path d="M309.722,320.961l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,309.722,320.961Z" transform="translate(-309.282 -319.392)" fill="#832ddb"/></g><g transform="translate(87.194)"><path d="M335.012,320.73l-.638.638-2.234.172a.854.854,0,0,1,.224-.448l1.509-1.509a.808.808,0,1,1,1.139,1.147Z" transform="translate(-328.034 -319.35)" fill="#832ddb"/><path d="M330.459,321.65l-2,.155-.845-.845a.812.812,0,0,1,0-1.139.8.8,0,0,1,1.139,0l1.509,1.509A.691.691,0,0,1,330.459,321.65Z" transform="translate(-327.38 -319.382)" fill="#832ddb"/></g><g transform="translate(102.628 0.034)"><path d="M352.288,320.184l-1.018.078.673-.673a.7.7,0,0,1,.345-.2Z" transform="translate(-346.099 -319.39)" fill="#832ddb"/><path d="M347.367,320.528l-2.036.155a.81.81,0,0,1,.181-.871.8.8,0,0,1,1.138,0Z" transform="translate(-345.274 -319.415)" fill="#832ddb"/></g></g><g transform="translate(4.095 83.028)"><path d="M227.645,334.48l-1.509,1.509a.812.812,0,0,1-1.139,0,.946.946,0,0,1-.207-.345c.034-.233.078-.466.121-.7a.907.907,0,0,1,.086-.1l1.509-1.509a.812.812,0,0,1,1.139,0A.823.823,0,0,1,227.645,334.48Z" transform="translate(-224.557 -333.1)" fill="#832ddb"/><path d="M224.753,339.221l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-224.52 -333.753)" fill="#832ddb"/><g transform="translate(11.342 0.009)"><path d="M237.9,340.97a.8.8,0,0,0,1.139,0l1.509-1.51a.805.805,0,0,0-1.139-1.139l-1.509,1.51A.812.812,0,0,0,237.9,340.97Z" transform="translate(-237.67 -333.794)" fill="#832ddb"/><path d="M242.883,335.991a.8.8,0,0,0,1.139,0l1.509-1.509a.8.8,0,0,0,0-1.139.812.812,0,0,0-1.139,0l-1.509,1.509A.812.812,0,0,0,242.883,335.991Z" transform="translate(-238.355 -333.11)" fill="#832ddb"/><path d="M242.652,339.221l1.509,1.509a.812.812,0,0,0,1.139,0,.8.8,0,0,0,0-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,242.652,339.221Z" transform="translate(-238.322 -333.762)" fill="#832ddb"/><path d="M238.133,334.711l1.509,1.509a.805.805,0,1,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-237.702 -333.142)" fill="#832ddb"/></g><g transform="translate(26.775 0.009)"><path d="M255.8,340.97a.8.8,0,0,0,1.139,0l1.509-1.51a.805.805,0,1,0-1.139-1.139l-1.509,1.51A.8.8,0,0,0,255.8,340.97Z" transform="translate(-255.563 -333.794)" fill="#832ddb"/><path d="M260.782,335.991a.8.8,0,0,0,1.139,0l1.509-1.509a.8.8,0,0,0,0-1.139.812.812,0,0,0-1.139,0l-1.509,1.509A.8.8,0,0,0,260.782,335.991Z" transform="translate(-256.247 -333.11)" fill="#832ddb"/><path d="M260.543,339.221l1.509,1.509a.812.812,0,0,0,1.139,0,.8.8,0,0,0,0-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-256.215 -333.762)" fill="#832ddb"/><path d="M256.032,334.711l1.509,1.509a.805.805,0,1,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-255.595 -333.142)" fill="#832ddb"/></g><g transform="translate(42.212 0.009)"><path d="M273.693,340.97a.8.8,0,0,0,1.139,0l1.509-1.51a.805.805,0,0,0-1.139-1.139l-1.509,1.51A.812.812,0,0,0,273.693,340.97Z" transform="translate(-273.46 -333.794)" fill="#832ddb"/><path d="M278.673,335.991a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.812.812,0,0,0,278.673,335.991Z" transform="translate(-274.145 -333.11)" fill="#832ddb"/><path d="M278.442,339.221l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,278.442,339.221Z" transform="translate(-274.112 -333.762)" fill="#832ddb"/><path d="M273.932,334.711l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,273.932,334.711Z" transform="translate(-273.492 -333.142)" fill="#832ddb"/></g><g transform="translate(57.645 0.009)"><path d="M291.592,340.97a.8.8,0,0,0,1.139,0l1.509-1.51a.805.805,0,0,0-1.139-1.139l-1.509,1.51A.8.8,0,0,0,291.592,340.97Z" transform="translate(-291.353 -333.794)" fill="#832ddb"/><path d="M296.572,335.991a.8.8,0,0,0,1.139,0l1.51-1.509a.8.8,0,0,0,0-1.139.812.812,0,0,0-1.139,0l-1.51,1.509A.8.8,0,0,0,296.572,335.991Z" transform="translate(-292.037 -333.11)" fill="#832ddb"/><path d="M296.333,339.221l1.509,1.509a.812.812,0,0,0,1.139,0,.8.8,0,0,0,0-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-292.005 -333.762)" fill="#832ddb"/><path d="M291.823,334.711l1.509,1.509a.805.805,0,1,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-291.385 -333.142)" fill="#832ddb"/></g><g transform="translate(73.083 0.009)"><path d="M309.483,340.97a.8.8,0,0,0,1.139,0l1.509-1.51a.805.805,0,0,0-1.139-1.139l-1.509,1.51A.812.812,0,0,0,309.483,340.97Z" transform="translate(-309.25 -333.794)" fill="#832ddb"/><path d="M314.463,335.991a.8.8,0,0,0,1.138,0l1.509-1.509a.8.8,0,0,0,0-1.139.812.812,0,0,0-1.139,0l-1.509,1.509A.812.812,0,0,0,314.463,335.991Z" transform="translate(-309.935 -333.11)" fill="#832ddb"/><path d="M314.232,339.221l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.138,0A.8.8,0,0,0,314.232,339.221Z" transform="translate(-309.902 -333.762)" fill="#832ddb"/><path d="M309.722,334.711l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,309.722,334.711Z" transform="translate(-309.282 -333.142)" fill="#832ddb"/></g></g><g transform="translate(1.645 94.89)"><path d="M222.922,353.212l-1.242,1.251c.138-.854.285-1.716.423-2.579a.8.8,0,0,1,.819,1.328Z" transform="translate(-221.68 -347.538)" fill="#832ddb"/><path d="M224.983,349.74a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.812.812,0,0,0,224.983,349.74Z" transform="translate(-222.102 -346.852)" fill="#832ddb"/><path d="M224.753,352.971l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-222.07 -347.505)" fill="#832ddb"/><path d="M223.215,350.162a.777.777,0,0,1-.845.181l.311-1.863.535.535A.807.807,0,0,1,223.215,350.162Z" transform="translate(-221.775 -347.076)" fill="#832ddb"/><g transform="translate(13.792)"><path d="M237.9,354.72a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.812.812,0,0,0,237.9,354.72Z" transform="translate(-237.67 -347.537)" fill="#832ddb"/><path d="M242.883,349.74a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.812.812,0,0,0,242.883,349.74Z" transform="translate(-238.355 -346.852)" fill="#832ddb"/><path d="M242.652,352.971l1.509,1.509a.805.805,0,1,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,242.652,352.971Z" transform="translate(-238.322 -347.505)" fill="#832ddb"/><path d="M238.133,348.461l1.509,1.509a.805.805,0,1,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-237.702 -346.885)" fill="#832ddb"/></g><g transform="translate(29.225)"><path d="M255.8,354.72a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,1,0-1.139-1.139l-1.509,1.509A.8.8,0,0,0,255.8,354.72Z" transform="translate(-255.563 -347.537)" fill="#832ddb"/><path d="M260.782,349.74a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.8.8,0,0,0,260.782,349.74Z" transform="translate(-256.247 -346.852)" fill="#832ddb"/><path d="M260.543,352.971l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-256.215 -347.505)" fill="#832ddb"/><path d="M256.032,348.461l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,256.032,348.461Z" transform="translate(-255.594 -346.885)" fill="#832ddb"/></g><g transform="translate(44.662)"><path d="M273.693,354.72a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.812.812,0,0,0,273.693,354.72Z" transform="translate(-273.46 -347.537)" fill="#832ddb"/><path d="M278.673,349.74a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.812.812,0,0,0,278.673,349.74Z" transform="translate(-274.145 -346.852)" fill="#832ddb"/><path d="M278.442,352.971l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,278.442,352.971Z" transform="translate(-274.112 -347.505)" fill="#832ddb"/><path d="M273.932,348.461l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,273.932,348.461Z" transform="translate(-273.492 -346.885)" fill="#832ddb"/></g><g transform="translate(60.095)"><path d="M291.592,354.72a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.8.8,0,0,0,291.592,354.72Z" transform="translate(-291.353 -347.537)" fill="#832ddb"/><path d="M296.572,349.74a.8.8,0,0,0,1.139,0l1.51-1.509a.805.805,0,0,0-1.139-1.139l-1.51,1.509A.8.8,0,0,0,296.572,349.74Z" transform="translate(-292.037 -346.852)" fill="#832ddb"/><path d="M296.333,352.971l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-292.005 -347.505)" fill="#832ddb"/><path d="M291.823,348.461l1.509,1.509a.805.805,0,1,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-291.385 -346.885)" fill="#832ddb"/></g><g transform="translate(75.532)"><path d="M309.483,354.72a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.812.812,0,0,0,309.483,354.72Z" transform="translate(-309.25 -347.537)" fill="#832ddb"/><path d="M314.463,349.74a.8.8,0,0,0,1.138,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.812.812,0,0,0,314.463,349.74Z" transform="translate(-309.935 -346.852)" fill="#832ddb"/><path d="M314.232,352.971l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.138,0A.8.8,0,0,0,314.232,352.971Z" transform="translate(-309.902 -347.505)" fill="#832ddb"/><path d="M309.722,348.461l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,309.722,348.461Z" transform="translate(-309.282 -346.885)" fill="#832ddb"/></g></g><g transform="translate(0 106.749)"><path d="M220.012,368.471a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.8.8,0,0,0,220.012,368.471Z" transform="translate(-219.773 -361.288)" fill="#832ddb"/><path d="M224.983,363.49a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.812.812,0,0,0,224.983,363.49Z" transform="translate(-220.457 -360.602)" fill="#832ddb"/><path d="M224.753,366.731l1.509,1.509A.805.805,0,0,0,227.4,367.1l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-220.425 -361.256)" fill="#832ddb"/><path d="M222.946,363.72a.812.812,0,0,1-1.139,0l-1.4-1.4c.078-.483.155-.966.242-1.458a.785.785,0,0,1,.785.207l1.509,1.509A.8.8,0,0,1,222.946,363.72Z" transform="translate(-219.86 -360.634)" fill="#832ddb"/><g transform="translate(15.437)"><path d="M237.9,368.471a.8.8,0,0,0,1.139,0l1.509-1.509a.8.8,0,0,0,0-1.139.812.812,0,0,0-1.139,0l-1.509,1.509A.812.812,0,0,0,237.9,368.471Z" transform="translate(-237.67 -361.288)" fill="#832ddb"/><path d="M242.883,363.49a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.812.812,0,0,0,242.883,363.49Z" transform="translate(-238.355 -360.602)" fill="#832ddb"/><path d="M242.652,366.731l1.509,1.509A.805.805,0,1,0,245.3,367.1l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,242.652,366.731Z" transform="translate(-238.322 -361.256)" fill="#832ddb"/><path d="M238.133,362.211l1.509,1.509a.805.805,0,1,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-237.702 -360.635)" fill="#832ddb"/></g><g transform="translate(30.87)"><path d="M255.8,368.471a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.8.8,0,0,0,255.8,368.471Z" transform="translate(-255.563 -361.288)" fill="#832ddb"/><path d="M260.782,363.49a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.8.8,0,0,0,260.782,363.49Z" transform="translate(-256.247 -360.602)" fill="#832ddb"/><path d="M260.543,366.731l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-256.215 -361.256)" fill="#832ddb"/><path d="M256.032,362.211l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,256.032,362.211Z" transform="translate(-255.594 -360.635)" fill="#832ddb"/></g><g transform="translate(46.307)"><path d="M273.693,368.471a.8.8,0,0,0,1.139,0l1.509-1.509a.8.8,0,0,0,0-1.139.812.812,0,0,0-1.139,0l-1.509,1.509A.812.812,0,0,0,273.693,368.471Z" transform="translate(-273.46 -361.288)" fill="#832ddb"/><path d="M278.673,363.49a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.812.812,0,0,0,278.673,363.49Z" transform="translate(-274.145 -360.602)" fill="#832ddb"/><path d="M278.442,366.731l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,278.442,366.731Z" transform="translate(-274.112 -361.256)" fill="#832ddb"/><path d="M273.932,362.211l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,273.932,362.211Z" transform="translate(-273.492 -360.635)" fill="#832ddb"/></g><g transform="translate(61.74)"><path d="M291.592,368.471a.8.8,0,0,0,1.139,0l1.509-1.509a.8.8,0,0,0,0-1.139.812.812,0,0,0-1.139,0l-1.509,1.509A.8.8,0,0,0,291.592,368.471Z" transform="translate(-291.353 -361.288)" fill="#832ddb"/><path d="M296.572,363.49a.8.8,0,0,0,1.139,0l1.51-1.509a.805.805,0,0,0-1.139-1.139l-1.51,1.509A.8.8,0,0,0,296.572,363.49Z" transform="translate(-292.037 -360.602)" fill="#832ddb"/><path d="M296.333,366.731l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-292.005 -361.256)" fill="#832ddb"/><path d="M291.823,362.211l1.509,1.509a.805.805,0,1,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-291.385 -360.635)" fill="#832ddb"/></g><g transform="translate(77.177)"><path d="M309.483,368.471a.8.8,0,0,0,1.139,0l1.509-1.509a.8.8,0,0,0,0-1.139.812.812,0,0,0-1.139,0l-1.509,1.509A.812.812,0,0,0,309.483,368.471Z" transform="translate(-309.25 -361.288)" fill="#832ddb"/><path d="M314.463,363.49a.8.8,0,0,0,1.138,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.812.812,0,0,0,314.463,363.49Z" transform="translate(-309.935 -360.602)" fill="#832ddb"/><path d="M314.232,366.731l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.138,0A.8.8,0,0,0,314.232,366.731Z" transform="translate(-309.902 -361.256)" fill="#832ddb"/><path d="M309.722,362.211l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,309.722,362.211Z" transform="translate(-309.282 -360.635)" fill="#832ddb"/></g></g><g transform="translate(0 118.616)"><g transform="translate(0 4.289)"><path d="M220.012,382.22a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.8.8,0,0,0,220.012,382.22Z" transform="translate(-219.773 -379.332)" fill="#832ddb"/></g><g transform="translate(4.293)"><path d="M224.983,377.241a.8.8,0,0,0,1.139,0l1.509-1.509a.8.8,0,0,0,0-1.139.812.812,0,0,0-1.139,0l-1.509,1.509A.812.812,0,0,0,224.983,377.241Z" transform="translate(-224.75 -374.36)" fill="#832ddb"/></g><g transform="translate(4.095 4.097)"><path d="M224.753,380.481l1.509,1.509a.812.812,0,0,0,1.139,0,.8.8,0,0,0,0-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-224.52 -379.11)" fill="#832ddb"/></g><g transform="translate(0.198 0.207)"><path d="M220.242,375.971l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,220.242,375.971Z" transform="translate(-220.003 -374.6)" fill="#832ddb"/></g><g transform="translate(15.437)"><path d="M237.9,382.22a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.812.812,0,0,0,237.9,382.22Z" transform="translate(-237.67 -375.044)" fill="#832ddb"/><path d="M242.883,377.241a.8.8,0,0,0,1.139,0l1.509-1.509a.8.8,0,0,0,0-1.139.812.812,0,0,0-1.139,0l-1.509,1.509A.812.812,0,0,0,242.883,377.241Z" transform="translate(-238.355 -374.36)" fill="#832ddb"/><path d="M242.652,380.481l1.509,1.509a.805.805,0,1,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,242.652,380.481Z" transform="translate(-238.322 -375.013)" fill="#832ddb"/><path d="M238.133,375.971l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-237.702 -374.393)" fill="#832ddb"/></g><g transform="translate(30.87)"><path d="M255.8,382.22a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,1,0-1.139-1.139l-1.509,1.509A.8.8,0,0,0,255.8,382.22Z" transform="translate(-255.563 -375.044)" fill="#832ddb"/><path d="M260.782,377.241a.8.8,0,0,0,1.139,0l1.509-1.509a.8.8,0,0,0,0-1.139.812.812,0,0,0-1.139,0l-1.509,1.509A.8.8,0,0,0,260.782,377.241Z" transform="translate(-256.247 -374.36)" fill="#832ddb"/><path d="M260.543,380.481l1.509,1.509a.812.812,0,0,0,1.139,0,.8.8,0,0,0,0-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-256.215 -375.013)" fill="#832ddb"/><path d="M256.032,375.971l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,256.032,375.971Z" transform="translate(-255.594 -374.393)" fill="#832ddb"/></g><g transform="translate(46.307)"><path d="M273.693,382.22a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.812.812,0,0,0,273.693,382.22Z" transform="translate(-273.46 -375.044)" fill="#832ddb"/><path d="M278.673,377.241a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.812.812,0,0,0,278.673,377.241Z" transform="translate(-274.145 -374.36)" fill="#832ddb"/><path d="M278.442,380.481l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,278.442,380.481Z" transform="translate(-274.112 -375.013)" fill="#832ddb"/><path d="M273.932,375.971l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,273.932,375.971Z" transform="translate(-273.492 -374.393)" fill="#832ddb"/></g><g transform="translate(61.74)"><path d="M291.592,382.22a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.8.8,0,0,0,291.592,382.22Z" transform="translate(-291.353 -375.044)" fill="#832ddb"/><path d="M296.572,377.241a.8.8,0,0,0,1.139,0l1.51-1.509a.8.8,0,0,0,0-1.139.812.812,0,0,0-1.139,0l-1.51,1.509A.8.8,0,0,0,296.572,377.241Z" transform="translate(-292.037 -374.36)" fill="#832ddb"/><path d="M296.333,380.481l1.509,1.509a.812.812,0,0,0,1.139,0,.8.8,0,0,0,0-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-292.005 -375.013)" fill="#832ddb"/><path d="M291.823,375.971l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.805.805,0,0,0-1.139,1.139Z" transform="translate(-291.385 -374.393)" fill="#832ddb"/></g><g transform="translate(77.177)"><path d="M309.483,382.22a.8.8,0,0,0,1.139,0l1.509-1.509a.805.805,0,0,0-1.139-1.139l-1.509,1.509A.812.812,0,0,0,309.483,382.22Z" transform="translate(-309.25 -375.044)" fill="#832ddb"/><path d="M314.463,377.241a.8.8,0,0,0,1.138,0l1.509-1.509a.8.8,0,0,0,0-1.139.812.812,0,0,0-1.139,0l-1.509,1.509A.812.812,0,0,0,314.463,377.241Z" transform="translate(-309.935 -374.36)" fill="#832ddb"/><path d="M314.232,380.481l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.138,0A.8.8,0,0,0,314.232,380.481Z" transform="translate(-309.902 -375.013)" fill="#832ddb"/><path d="M309.722,375.971l1.509,1.509a.805.805,0,0,0,1.139-1.139l-1.509-1.509a.812.812,0,0,0-1.139,0A.8.8,0,0,0,309.722,375.971Z" transform="translate(-309.282 -374.393)" fill="#832ddb"/></g></g></g><g transform="translate(55.073 65.052)"><path d="M286.871,358l-.078-.052a88.537,88.537,0,0,1-14.137-13.6c-6.417-7.72-13.947-19.761-13.421-33.311a.7.7,0,0,1,1.406.06c-.509,13.093,6.831,24.815,13.076,32.336a87.042,87.042,0,0,0,13.9,13.387.7.7,0,0,1,.155.983A.719.719,0,0,1,286.871,358Z" transform="translate(-259.21 -310.359)" fill="#fff"/></g><g transform="translate(65.946 5.137)"><path d="M290,260.073a.759.759,0,0,0,.94-.449.751.751,0,0,0-.431-.983c-15.784-6.21-17.173-16.966-17.182-17.07a.759.759,0,0,0-1.509.164c.052.474,1.449,11.756,18.139,18.329A.064.064,0,0,1,290,260.073Z" transform="translate(-271.815 -240.895)" fill="#fff"/></g><g transform="translate(65.488 15.806)"><path d="M281.993,263.9a.758.758,0,0,0,.517-1.423c-8.643-3.6-9.7-8.539-9.712-8.591a.765.765,0,0,0-1.5.293c.043.233,1.233,5.8,10.626,9.7C281.95,263.887,281.976,263.887,281.993,263.9Z" transform="translate(-271.284 -253.264)" fill="#fff"/></g><g transform="translate(100.272 42.825)"><g transform="translate(2.25)"><path d="M314.229,285.392l1.949,13.878.449,3.174,3.105,22.055.155-9.022L318,302.074l-2.372-16.88a.706.706,0,0,0-1.4.2Z" transform="translate(-314.22 -284.59)" fill="#fff"/></g><g transform="translate(0 10.042)"><path d="M316.941,304.485a.738.738,0,0,0,.4-.121.714.714,0,0,0,.19-.983l-4.632-6.84a.7.7,0,1,0-1.164.785l4.632,6.84A.663.663,0,0,0,316.941,304.485Z" transform="translate(-311.612 -296.232)" fill="#fff"/></g></g><g transform="translate(40.704 132.462)"><path d="M242.55,389.088a.656.656,0,0,1,.25-.423.71.71,0,0,1,.992.112l.259.31Z" transform="translate(-242.55 -388.513)" fill="#832ddb"/></g><g transform="translate(35.749 111.79)"><path d="M258.657,385.794c-8.125-4.088-16.983-10.506-21.779-20.235a.7.7,0,1,1,1.259-.621c5.149,10.454,15.232,16.983,23.78,20.856Z" transform="translate(-236.806 -364.546)" fill="#fff"/></g></g><g transform="translate(37.662)"><g transform="translate(31.747 65.614)"><path d="M284.3,240.46c10.6,4.3,16.267-6.538,16.267-6.538-2.286-4.727-4.158-13.826-4.7-18.268-.276-2.243-.354-3.743-.354-3.743L275.83,224.727l1.044,8.2.259,2C277.124,234.94,277.8,237.821,284.3,240.46Z" transform="translate(-275.83 -211.91)" fill="#ffa19f"/><g transform="translate(0)"><path d="M276.874,232.93c10.721-3.2,16.845-13.154,18.855-18.458a14.388,14.388,0,0,1-.216-2.562L275.83,224.727Z" transform="translate(-275.83 -211.91)" fill="#0d0a3e"/></g></g><g transform="translate(5.407 45.052)"><path d="M255.227,191.684s-2.579-5.115-7.711-3.174-1.415,23.84,11.368,20.063Z" transform="translate(-245.293 -188.07)" fill="#ec7b7b"/></g><g transform="translate(8.111 17.781)"><path d="M248.775,185.225c.147,1.492.474,3.8,1.035,6.573v.009c.388,1.9.871,4,1.484,6.193.121.423.241.854.371,1.277s.25.854.388,1.294c.147.466.293.932.457,1.4.2.6.405,1.2.621,1.794.336.914.69,1.837,1.078,2.734,4.183,9.988,12.4,16.069,23.211,12.369,9.082-3.122,14.077-10.187,16.785-17.7,4.33-12.006,2.821-25.16,2.821-25.16-2.182-17.9-17.984-23.539-33.889-16.794S247.964,176.841,248.775,185.225Z" transform="translate(-248.427 -156.453)" fill="#ffa19f"/></g><g transform="translate(12.184 46.613)"><path d="M293.724,189.88s2.993,10.471,1.285,18.415S283.6,230.73,269.2,226.891s-16.052-20.33-16.052-20.33,1.889,1.958,6.814,2.26,4.589-3.994,13.533-2.26,13.9-3.873,14.637-7.771a42.31,42.31,0,0,0,.785-6.512Z" transform="translate(-253.15 -189.88)" fill="#0d0a3e"/></g><path d="M278.865,210.7s-10.609,2.553-16.725,2.415c0,0,1.889,7.47,10.333,5.52C278.873,217.16,278.865,210.7,278.865,210.7Z" transform="translate(-242.201 -146.129)" fill="#fff"/><g transform="translate(36.156 54.414)"><path d="M280.949,201.439c.138.8,2.432,1.078,5.115.621s4.753-1.492,4.615-2.294-2.432-1.078-5.115-.621S280.811,200.637,280.949,201.439Z" transform="translate(-280.942 -198.924)" fill="#ec7b7b"/></g><g transform="translate(11.348 59.986)"><path d="M252.18,206.01c.121.431.25.854.388,1.294.147.466.293.932.457,1.4.457-.043.94-.1,1.44-.181,2.682-.466,4.753-1.492,4.615-2.294s-2.432-1.078-5.115-.621A12.66,12.66,0,0,0,252.18,206.01Z" transform="translate(-252.18 -205.384)" fill="#ec7b7b"/></g><g transform="translate(29.266 40.86)"><path d="M273.6,186.151a1.237,1.237,0,0,1-.5-.509,1.253,1.253,0,0,1,.526-1.691,6.911,6.911,0,0,1,4.261-.63,8.142,8.142,0,0,1,3.623,1.6,1.254,1.254,0,0,1-1.639,1.9,6.111,6.111,0,0,0-2.484-1.035,4.439,4.439,0,0,0-2.6.388A1.263,1.263,0,0,1,273.6,186.151Z" transform="translate(-272.954 -183.21)" fill="#0d0a3e"/></g><g transform="translate(10.877 43.879)"><path d="M259.471,188.72a1.3,1.3,0,0,0,.25-.673,1.254,1.254,0,0,0-1.173-1.328,6.868,6.868,0,0,0-4.14,1.173,8.166,8.166,0,0,0-2.657,2.941,1.252,1.252,0,0,0,2.268,1.061,5.98,5.98,0,0,1,1.846-1.967,4.4,4.4,0,0,1,2.527-.707A1.239,1.239,0,0,0,259.471,188.72Z" transform="translate(-251.634 -186.71)" fill="#0d0a3e"/></g><g transform="translate(39.035 35.433)"><path d="M291.879,184.346s1.078,5.227,1.32,6.15c2.329.009,3.692-.267,4.8-2.4.052-1.415-.026-2.484-.026-2.484a9.82,9.82,0,0,1,4.157-4.433c-5.529-4.942-17.854-4.226-17.854-4.226C286.393,181.629,290.516,182.932,291.879,184.346Z" transform="translate(-284.28 -176.918)" fill="#0d0a3e"/></g><path d="M295.9,175.534s6.547-6.314,3.605-11.523c-1.691-3-6.348-1.518-6.348-1.518s4.252-12.2.845-15.379-9.367,1.8-9.367,1.8-1.509-13.231-10.557-13.076c-7.953.138-2.527,11.549-2.527,11.549s-14.318-8.91-20.33-5.52c-9.2,5.175,3.165,13.43,3.165,13.43s-17.389-5.744-15.172,3.726c1.061,4.528,7.771,6.512,7.771,6.512s-7.168,2.588-7.625,5.736c-.785,5.4,7.107,3.8,7.107,3.8-.285,1.829.509,3.605.785,4.727l3.148-1.311s-.181-4.537.828-7.228c0,0,21.908,1.19,27.178-6.012,0,0-2.527,1.708-.362,6.046Z" transform="translate(-239.024 -135.838)" fill="#0d0a3e"/><g transform="translate(7.968 42.654)"><path d="M251.676,185.29a10.5,10.5,0,0,1-.914,5.356,8.641,8.641,0,0,0-.871,4.425l.147,1.863s-2.6-7.245-1.509-10.333Z" transform="translate(-248.262 -185.29)" fill="#0d0a3e"/></g><g transform="translate(32.277 47.22)"><path d="M276.527,193.41c.267,1.406,1.113,2.432,1.88,2.286s1.173-1.406.906-2.812-1.113-2.432-1.88-2.286S276.26,192,276.527,193.41Z" transform="translate(-276.445 -190.584)" fill="#0d0a3e"/></g><g transform="translate(15.483 50.558)"><path d="M257.057,197.28c.267,1.406,1.113,2.432,1.88,2.286s1.173-1.406.906-2.812-1.113-2.432-1.88-2.286S256.789,195.865,257.057,197.28Z" transform="translate(-256.975 -194.454)" fill="#0d0a3e"/></g><g transform="translate(24.534 51.898)"><path d="M271.437,204.963a.825.825,0,0,0-.44-1.578,15.073,15.073,0,0,1-1.785.19,52.227,52.227,0,0,1,.043-6.685.826.826,0,0,0-1.647-.112,48.165,48.165,0,0,0,.052,7.746.83.83,0,0,0,.785.716,16.838,16.838,0,0,0,2.812-.224A.629.629,0,0,0,271.437,204.963Z" transform="translate(-267.468 -196.007)" fill="#ec7b7b"/></g><g transform="translate(52.732 39.619)"><path d="M300.16,186.281s1.725-5.46,7.1-4.364,7.323,20.684-5.9,19Z" transform="translate(-300.16 -181.772)" fill="#ffa19f"/><g transform="translate(2.112 3.232)"><path d="M304,197.233a.826.826,0,0,0,.492-.932,9.949,9.949,0,0,1,2.846-9.324.827.827,0,0,0-1.061-1.268,11.48,11.48,0,0,0-3.4,10.92.824.824,0,0,0,.975.638A.283.283,0,0,0,304,197.233Z" transform="translate(-302.608 -185.518)" fill="#ec7b7b"/></g><g transform="translate(2.257 8.465)"><path d="M307.765,193.254a.822.822,0,0,0,.5-.63.834.834,0,0,0-.681-.949c-.3-.052-2.976-.44-4.528.932a.825.825,0,1,0,1.1,1.233c.819-.725,2.6-.63,3.165-.535A.792.792,0,0,0,307.765,193.254Z" transform="translate(-302.777 -191.586)" fill="#ec7b7b"/></g><g transform="translate(3.707 7.925)"><path d="M304.693,190.994a15.683,15.683,0,0,0-.2,3.623s2.5-.155,2.6-1.958C307.2,190.572,304.693,190.994,304.693,190.994Z" transform="translate(-304.459 -190.959)" fill="#fff"/><path d="M305.2,192.93l-.733.293s-.2,6.167,1.164,6.089c1.156-.069.276-5.839.276-5.839Z" transform="translate(-304.458 -191.23)" fill="#fff"/></g></g><g transform="translate(7.385 40.497)"><g transform="translate(19.407 2.705)"><path d="M270.15,195.46a8.5,8.5,0,1,1,9.471,7.4A8.505,8.505,0,0,1,270.15,195.46Zm1.07-.138a7.421,7.421,0,1,0,6.46-8.272A7.429,7.429,0,0,0,271.219,195.322Z" transform="translate(-270.086 -185.926)" fill="#fff"/></g><g transform="translate(33.568)"><path d="M286.708,189.328a.544.544,0,0,0,.405.112.528.528,0,0,0,.354-.2,18.6,18.6,0,0,1,12.36-5.434A8.4,8.4,0,0,1,301,182.79a20.005,20.005,0,0,0-14.387,5.779A.554.554,0,0,0,286.708,189.328Z" transform="translate(-286.504 -182.79)" fill="#fff"/></g><g transform="translate(0 6.155)"><path d="M247.65,199.46a8.5,8.5,0,1,1,9.471,7.4A8.505,8.505,0,0,1,247.65,199.46Zm1.07-.138a7.422,7.422,0,1,0,6.46-8.272A7.429,7.429,0,0,0,248.719,199.322Z" transform="translate(-247.586 -189.926)" fill="#fff"/></g><g transform="translate(15.447 9.896)"><path d="M266.094,196.9a.553.553,0,0,0,.44-.336,2.114,2.114,0,0,1,1.458-1.19,1.772,1.772,0,0,1,1.6.543.538.538,0,1,0,.819-.7,2.859,2.859,0,0,0-2.648-.9,3.193,3.193,0,0,0-2.234,1.854.524.524,0,0,0,.561.725Z" transform="translate(-265.495 -194.263)" fill="#fff"/></g></g></g></g><g transform="translate(143.665 159.26)"><g transform="translate(0.017 0)"><path d="M323.52,322.265c.035.724.069,1.553.112,2.441.009.216.017.431.026.656.233,5.3.543,12.748.914,17.544.293,3.83.638,7.737,1.035,11.411.078.724.155,1.44.233,2.139,0,.026.009.06.009.086.095.828.19,1.63.293,2.424.336,2.674.707,5.123,1.1,7.2,1.665,8.591,6.564,13.628,11.075,12.929,3.131-.492,6.064-3.735,7.608-10.48,2.579-11.282,2.372-30.1,1.276-40.772-.069-.681-.285-3.286-.448-5.236-.078-.923-.147-1.69-.181-2.053a.239.239,0,0,1-.009-.078Z" transform="translate(-323.52 -320.48)" fill="#ffa19f"/></g><path d="M323.5,322.265c.034.906.078,1.949.121,3.1h.026l23.107-2.424c-.086-1.07-.147-1.975-.181-2.381,0-.026-.009-.052-.009-.078l-23.056,1.785Z" transform="translate(-323.5 -320.48)" fill="#0d0a3e"/></g><g transform="translate(75.24 193.088)"><g transform="translate(26.84 1.368)"><path d="M277.429,385.389s24.6.181,43.843-.034c.164,0,7.72.009,7.875,0a7.886,7.886,0,0,0,2.544-.638c3.122-1.354,6.021-4.813,7.168-10.368l.328-1.6a8.944,8.944,0,0,0-4.571-9.764c-.112-.06-.233-.121-.354-.172a8.409,8.409,0,0,0-2.389-.716,24.256,24.256,0,0,0-8.591-.69l-.733.078-.578.078a27.518,27.518,0,0,0-2.769.535c-.086.017-.172.043-.259.06-4.96,1.242-9.773,3.683-14.594,6.426-9.954,5.658-27.368,8.651-27.368,8.651C272.926,381.585,277.429,385.389,277.429,385.389Z" transform="translate(-275.288 -361.286)" fill="#ffa19f"/></g><g transform="translate(70.504)"><path d="M325.91,361.917c.086.009.173.017.259.017a23.248,23.248,0,0,1,8.7,2.544,23.559,23.559,0,0,0-4.632-3.329,11.81,11.81,0,0,0-4.321-1.449c.078.724.155,1.44.233,2.139A1.6,1.6,0,0,1,325.91,361.917Z" transform="translate(-325.91 -359.7)" fill="#0d0a3e"/></g><g transform="translate(0 14.086)"><path d="M272.711,378.747s-10.79-3.942-18.191-2.329-10.35,10.713-10.35,10.713l28.662-.336S276.688,379.955,272.711,378.747Z" transform="translate(-244.17 -376.031)" fill="#ffa19f"/></g></g></g><g transform="translate(139.404 278.414)"><g transform="translate(36.925 59.256)"><path d="M185.484,388.2h109a1.793,1.793,0,0,0,1.794-1.794v-3.148a1.793,1.793,0,0,0-1.794-1.794h-109a1.793,1.793,0,0,0-1.794,1.794V386.4A1.8,1.8,0,0,0,185.484,388.2Z" transform="translate(-183.69 -381.46)" fill="#f7891b"/></g><g transform="translate(0 0.009)"><path d="M140.975,317.048,154.913,376a3.487,3.487,0,0,0,3.39,2.682h85.865a3.482,3.482,0,0,0,3.39-4.278L233.62,315.452a3.487,3.487,0,0,0-3.39-2.682H144.365A3.476,3.476,0,0,0,140.975,317.048Z" transform="translate(-140.88 -312.77)" fill="#ffae0b"/></g><g transform="translate(85.952)"><path d="M240.53,312.76l14.844,65.915h2.5a3.482,3.482,0,0,0,3.39-4.278l-13.939-58.954a3.487,3.487,0,0,0-3.39-2.682Z" transform="translate(-240.53 -312.76)" fill="#f7891b"/></g><g transform="translate(42.523 29.956)"><circle cx="6.529" cy="6.529" r="6.529" fill="#f7891b"/></g><g transform="translate(93.136 3.803)"><path d="M260.392,365.256a.322.322,0,0,1-.31-.25l-11.213-47.439a.323.323,0,1,1,.63-.147l11.213,47.439a.324.324,0,0,1-.242.388C260.444,365.248,260.418,365.256,260.392,365.256Z" transform="translate(-248.859 -317.169)" fill="#fff"/></g></g><g transform="translate(344.213 303.643)"><path d="M420.918,351.05l-.25,2.881-.431,4.865-.078.932-.362,4.14-.4,4.494-.017.216-.448,5.158-.4,4.589-.5,5.7H402.2l-.121-1.3-.483-5.546-.293-3.338-.474-5.477-.009-.086-.293-3.338-.448-5.21-.035-.379-.285-3.312-.44-4.985Z" transform="translate(-398.216 -343.253)" fill="#fd5177"/><path d="M420.28,361.11l-.362,4.14-.4,4.494H400.95l-.009-.086-.293-3.338-.448-5.21Z" transform="translate(-398.337 -344.636)" fill="#fff"/><rect width="23.815" height="4.218" transform="translate(0 3.58)" fill="#d8234f"/><path d="M420.336,345.589H400.11v-2.3a1.274,1.274,0,0,1,1.277-1.277H419.06a1.274,1.274,0,0,1,1.276,1.277Z" transform="translate(-398.324 -342.01)" fill="#fd5177"/></g><g transform="translate(366.61 267.598)"><g transform="translate(6.697)"><path d="M449.288,348.142a2.99,2.99,0,0,1-1.82.673,4.568,4.568,0,0,1-1.285-.086,6.993,6.993,0,0,1-1.76-.587s.285-12.05-4.589-20.347-11.325-19.761-6.158-25.643,13.473,2.294,16.2,16.052C452.583,331.961,449.288,348.142,449.288,348.142Z" transform="translate(-431.771 -300.22)" fill="#53ca2e"/><g transform="translate(6.075 9.45)"><path d="M448.436,350.321a4.568,4.568,0,0,1-1.285-.086,71.542,71.542,0,0,0-8.246-38.081.644.644,0,1,1,1.1-.664A72.832,72.832,0,0,1,448.436,350.321Z" transform="translate(-438.814 -311.176)" fill="#0092c4"/></g></g><g transform="translate(25.618 7.625)"><path d="M469.011,329.907C457.608,348.34,458.4,352.6,458.4,352.6l-1.5-.267-1.277-.224-1.811-.319c-1.009-8.177,5.27-42.73,16.457-42.73C481.673,309.06,472.9,323.637,469.011,329.907Z" transform="translate(-453.708 -309.06)" fill="#53ca2e"/><g transform="translate(1.926 9.284)"><path d="M468.9,320.786c-.1.181-9.652,17.208-11.679,33.026l-1.277-.224c2.079-16.078,11.722-33.259,11.825-33.44a.649.649,0,0,1,1.13.638Z" transform="translate(-455.94 -319.824)" fill="#0092c4"/></g></g><g transform="translate(0 19.718)"><g transform="translate(15.874 30.731)"><path d="M442.41,358.719l.043-.009C442.445,358.744,442.41,358.719,442.41,358.719Z" transform="translate(-442.41 -358.71)" fill="#00bad9"/></g><path d="M443.244,352.915l-1.311.354-1.259.336-.75.2c.043-.164-.526-1.57-8.548-12.291-3.312-4.433-12.144-16.336-4.045-18.338C434.765,321.346,443.46,346.748,443.244,352.915Z" transform="translate(-424.006 -323.081)" fill="#53ca2e"/><g transform="translate(4.745 6.359)"><path d="M442.689,354.282l-1.259.336c-2.312-10.325-11.7-23-11.8-23.133a.663.663,0,0,1,.129-.906.655.655,0,0,1,.906.129C431.079,331.279,440.308,343.725,442.689,354.282Z" transform="translate(-429.507 -330.454)" fill="#0092c4"/></g></g><path d="M460.667,354.35v30.008h-25.5V354.35a.884.884,0,0,1,.88-.88h23.737A.889.889,0,0,1,460.667,354.35Z" transform="translate(-425.541 -307.54)" fill="#fd5177"/><g transform="translate(9.629 48.716)"><rect width="25.497" height="0.733" fill="#d8234f"/><g transform="translate(0 4.166)"><rect width="25.497" height="0.733" fill="#d8234f"/></g><g transform="translate(0 20.985)"><rect width="25.497" height="0.733" fill="#d8234f"/></g><g transform="translate(0 25.143)"><rect width="25.497" height="0.733" fill="#d8234f"/></g></g></g></g></svg>