// Menu Top
.dm-menu{
    &.menu-horizontal{
        border-bottom: 1px solid var(--border-light);
        .dm-menu__item{
            position: relative;
            display: inline-block;
            &:not(:last-child){
                margin-right: 30px;
            }
            &:hover{
                >.dm-submenu{
                    transform: scaleY(1);
                    opacity: 1;
                    visibility: visible;
                }
            }
            &:hover,
            &.active{
                >.dm-menu__link{
                    color:var(--color-primary);
                    &:after{
                        opacity: 1;
                        visibility: visible;
                    }
                    svg,
                    img{
                        color:var(--color-primary);
                    }
                }
            } 
            &.has-submenu{
                >.dm-menu__link{
                    &:before{
                        font-family: 'Line Awesome Free';
                        font-weight: 700;
                        content: "\f107";
                        margin-left: 5px;
                        margin-top: 1px;
                        float: right;
                    }
                }
            }
            &.menu-item-disabled{
                .dm-menu__link{
                    border-color: transparent !important;
                    cursor: not-allowed;
                    svg,
                    img{
                        color:var(--color-lighten);
                    }
                    &:after{
                        display: none;
                    }
                }
                .dm-menu__text{
                    color:var(--color-lighten);
                }
            }
            .dm-menu__item{
                display: block;
                >.dm-menu__link{
                    &:after{
                        display: none;
                    }
                    &:before{
                        content: "\f105";
                    }
                }
            }
        }
        .dm-menu__link{
            padding-bottom:8px;
        }
    }
    @include e("link"){
        position: relative;
        display: block;
        font-size: 14px;
        line-height: 2.9;
        color:var(--color-gray);
        &:after{
            position: absolute;
            left: 0;
            bottom: -1px;
            width: 100%;
            height: 2px;
            border-radius: 10px;
            opacity: 0;
            visibility: hidden;
            transition: .35s;
            content: '';
            background-color: var(--color-primary);
        }
        svg,
        img{
            width: 16px;
            height: 16px;
            color:var(--color-lighten);
        }
        .dm-menu__text{
            font-weight: 500;
            margin-left: 10px;
        }
    }
}

// Atbd Submenu
.dm-submenu{
    position: absolute;
    left: 0;
    top: 48px;
    width: 260px;
    padding: 16px;
    box-shadow: 0 5px 30px rgba(var(--light-gray-rgba) , .15);
    transform: scaleY(0);
    transform-origin: 0 0 0;
    opacity: 0;
    visibility: hidden;
    transition: .35s ease;
    z-index: 999;
    background-color:var(--color-white);
    @include ssm{
        position: static;
        opacity: 1;
        visibility: visible;
        transform: scale(1);
        box-shadow: 0 0;
        padding: 0;
    }
    .submenu-title{
        display: block;
        font-size: 14px;
        padding: 8px 0;
        line-height: 1.67;
        color: var(--color-dark);
    }
    li{
        a{
            display: block;
            font-size: 14px;
            padding: 8px 0;
            line-height: 1.67;
            color: var(--color-dark);
        }
    }
    li{
        >a{
            &:hover{
                color:var(--color-primary);
            }
        }
    }
    ul{
        padding-left: 14px;
    }
    .dm-submenu{
        left: 100%;
        top: 0;
    }
}

// Menu Vertical
.menu-wrapper{
    .dm-menu.menu-vertical{
        max-width: 280px;
    }
}
.dm-menu{
    &.menu-vertical{
        border-right: 1px solid var(--border-color);
        .dm-menu__item{
            padding-right: 15px;
            position: relative;
            &:hover{
                >.dm-submenu{
                    transform: scaleY(1);
                    opacity: 1;
                    visibility: visible;
                }
            }
            &.has-submenu{
                >.dm-menu__link{
                    &:before{
                        font-family: 'Line Awesome Free';
                        font-weight: 700;
                        content: "\f105";
                        margin-left: 5px;
                        margin-top: 1px;
                        float: right;
                    }
                    &:hover{
                        &:before,
                        .dm-menu__icon,
                        .dm-menu__text{
                            color:var(--color-primary);
                        }
                    }
                }
            }
            .dm-menu__link{
                &:hover{
                    &:before,
                    .dm-menu__icon svg,
                    .dm-menu__text{
                        color:var(--color-primary);
                    }
                }
            }
        }
        .dm-submenu{
            left: 100%;
            top: 0;
        }
    }
}

// Collapsable Menu
.dm-menu{
    &.menu-collapsable{
        .dm-menu__item {
            &.has-submenu{
                >.dm-menu__link {
                    &:before{
                        content: "\f107";
                    }
                }
            }
        }
        .dm-submenu{
            position: static;
            display: block;
            opacity: 1;
            visibility: visible;
            transform: scaleY(1);
            box-shadow: 0 0;
            padding: 0 0 0 20px;
            background: transparent;
            .dm-menu__link{
                min-width: auto;
            }
        }
    }
}

// Mobile Navigation Menu
.mobile-nav-wrapper{
    position: fixed;
    top: 0;
    right: 0;
    width: 280px;
    height: 100%;
    transform: translateX(280px);
    transition: .3s;
    overflow-y: auto;
    overflow-x: hidden;
    padding-top: 130px;
    z-index: 22;
    box-shadow: 0 0 30px rgba(var(--light-gray-rgba),.15);
    background-color:var(--bg-white);
    &.show{
        transform: translateX(0);;
    }
    .dm-menu{
        padding: 0 15px;
        &.menu-horizontal{
            border-bottom: 0;
            .dm-menu__item{
                .dm-menu__item{
                    >.dm-menu__link{
                        &:before{
                            content: "\f107";
                        }
                    }
                }
            }
        }
    }
    .dm-menu__item{
        padding: 0;
        margin-right: 0 !important;
        &.has-submenu{
            > .dm-menu__link:before{
                margin: 0 10px  0 0;
            }
        }
    }
    .dm-submenu{
        padding: 0 0 0 15px;
        opacity: 1;
        visibility: visible;
        transform: scaleY(1);
        position: static;
        box-shadow: 0 0;
        .dm-submenu{
            li{
                &:first-child{
                    a{
                        padding-top: 0;
                    }
                }
            }
        }
        
    }
    .nav-close{
        position: absolute;
        top: 95px;
        left: 12px;
        color:var(--color-danger);
    }
}

.menu-mob-trigger {
    max-width: 40px;
    margin: 0 auto;
    display: flex;
    flex-flow: column;
    justify-content: center;
    span{
        display: block;
        width: 30px;
        height: 2px;
        background-color: var(--color-gray-x);
        &:not(:last-child){
            margin-bottom: 4px;
        }
    }
}