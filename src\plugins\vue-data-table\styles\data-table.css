/* Vue Data Table Styles - Enhanced with Tailwind CSS compatibility */

/* CSS Variables for theming - can be overridden */
:root {
  --vdt-primary-color: #3b82f6;
  --vdt-primary-color-dark: #2563eb;
  --vdt-text-color: #111827;
  --vdt-text-color-light: #6b7280;
  --vdt-border-color: #e5e7eb;
  --vdt-bg-color: #ffffff;
  --vdt-bg-color-light: #f9fafb;
  --vdt-bg-color-hover: #f3f4f6;
  --vdt-bg-color-selected: #eff6ff;
  --vdt-border-radius: 0.5rem;
  --vdt-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --vdt-transition: all 0.2s ease;
}

/* Dark theme support */
.dark {
  --vdt-primary-color: #60a5fa;
  --vdt-primary-color-dark: #3b82f6;
  --vdt-text-color: #f9fafb;
  --vdt-text-color-light: #9ca3af;
  --vdt-border-color: #374151;
  --vdt-bg-color: #1f2937;
  --vdt-bg-color-light: #111827;
  --vdt-bg-color-hover: #374151;
  --vdt-bg-color-selected: #1e3a8a;
}

/* Main container */
.vue-data-table {
  width: 100%;
  border: 1px solid var(--vdt-border-color);
  border-radius: var(--vdt-border-radius);
  overflow: hidden;
  background-color: var(--vdt-bg-color);
  box-shadow: var(--vdt-box-shadow);
  font-family: inherit;
}

/* Table header section */
.vue-data-table-header {
  padding: 16px;
  border-bottom: 1px solid var(--vdt-border-color);
}

/* Table body section */
.vue-data-table-body {
  position: relative;
  overflow-x: auto;
}

/* Table footer section */
.vue-data-table-footer {
  padding: 16px;
  border-top: 1px solid var(--vdt-border-color);
}

/* Loading overlay */
.vue-data-table-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.vue-data-table-loading .spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--vdt-primary-color);
  animation: vdt-spin 1s ease-in-out infinite;
  margin-bottom: 8px;
}

@keyframes vdt-spin {
  to { transform: rotate(360deg); }
}

/* Table styles */
.vue-data-table table {
  width: 100%;
  border-collapse: collapse;
}

.vue-data-table th,
.vue-data-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid var(--vdt-border-color);
}

.vue-data-table th {
  font-weight: 600;
  background-color: var(--vdt-bg-color-light);
  white-space: nowrap;
  color: var(--vdt-text-color);
}

.vue-data-table td {
  color: var(--vdt-text-color);
}

.vue-data-table tr:last-child td {
  border-bottom: none;
}

.vue-data-table tbody tr:hover {
  background-color: var(--vdt-bg-color-hover);
}

/* Sortable columns */
.vue-data-table .sortable {
  cursor: pointer;
  position: relative;
  padding-right: 24px;
}

.vue-data-table .sort-icon {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  height: 12px;
}

.vue-data-table .sort-arrow {
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
}

.vue-data-table .sort-arrow-up {
  border-bottom: 4px solid var(--vdt-text-color-light);
  margin-bottom: 2px;
}

.vue-data-table .sort-arrow-down {
  border-top: 4px solid var(--vdt-text-color-light);
}

.vue-data-table .sorted-asc .sort-arrow-up {
  border-bottom-color: var(--vdt-primary-color);
}

.vue-data-table .sorted-desc .sort-arrow-down {
  border-top-color: var(--vdt-primary-color);
}

/* Selection column */
.vue-data-table .select-column {
  width: 40px;
  text-align: center;
}

/* Actions column */
.vue-data-table .actions-column {
  width: 100px;
  text-align: center;
}

/* Selected row */
.vue-data-table tr.selected {
  background-color: var(--vdt-bg-color-selected);
}

/* No data message */
.vue-data-table .no-data {
  text-align: center;
  padding: 32px;
  color: var(--vdt-text-color-light);
}

/* Table header component */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.table-header-left {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
  flex: 1;
}

.table-header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.table-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--vdt-text-color);
}

/* Search component */
.table-search {
  min-width: 200px;
  max-width: 300px;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  width: 100%;
  padding: 8px 36px 8px 12px;
  border: 1px solid var(--vdt-border-color);
  border-radius: 4px;
  font-size: 14px;
  outline: none;
  transition: var(--vdt-transition);
}

.search-input:focus {
  border-color: var(--vdt-primary-color);
}

.search-icon {
  position: absolute;
  right: 12px;
  color: var(--vdt-text-color-light);
  pointer-events: none;
}

.clear-button {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  color: var(--vdt-text-color-light);
  cursor: pointer;
  padding: 0;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.clear-button:hover {
  color: var(--vdt-text-color);
}

/* Pagination component */
.table-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.pagination-info {
  color: var(--vdt-text-color-light);
  font-size: 14px;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.page-size-selector {
  position: relative;
}

.page-size-select {
  appearance: none;
  padding: 6px 32px 6px 12px;
  border: 1px solid var(--vdt-border-color);
  border-radius: 4px;
  background-color: var(--vdt-bg-color);
  font-size: 14px;
  cursor: pointer;
}

.page-size-selector::after {
  content: '';
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid var(--vdt-text-color-light);
  pointer-events: none;
}

.pagination {
  display: flex;
  list-style: none;
  padding: 0;
  margin: 0;
  gap: 4px;
}

.page-item {
  margin: 0;
}

.page-link {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
  padding: 0 8px;
  border: 1px solid var(--vdt-border-color);
  border-radius: 4px;
  background-color: var(--vdt-bg-color);
  color: var(--vdt-text-color-light);
  text-decoration: none;
  font-size: 14px;
  transition: var(--vdt-transition);
}

.page-item.active .page-link {
  background-color: var(--vdt-primary-color);
  border-color: var(--vdt-primary-color);
  color: white;
}

.page-item.disabled .page-link {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-link:hover:not(.page-item.disabled .page-link) {
  background-color: var(--vdt-bg-color-light);
  border-color: var(--vdt-border-color);
}

/* Export button */
.export-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  font-size: 14px;
  border-radius: 4px;
  background-color: var(--vdt-bg-color);
  border: 1px solid var(--vdt-border-color);
  color: var(--vdt-text-color-light);
  cursor: pointer;
  transition: var(--vdt-transition);
}

.export-btn:hover {
  background-color: var(--vdt-bg-color-light);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .vue-data-table th,
  .vue-data-table td {
    padding: 8px 12px;
  }

  .table-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .table-header-left,
  .table-header-right {
    width: 100%;
  }

  .table-header-right {
    justify-content: flex-end;
  }

  .table-search {
    width: 100%;
    max-width: none;
  }

  .table-pagination {
    flex-direction: column;
    align-items: flex-start;
  }

  .pagination-controls {
    width: 100%;
    justify-content: space-between;
  }
}
