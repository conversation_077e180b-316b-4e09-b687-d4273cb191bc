select,
.form-control,
.form-select,
textarea,
input {
  height: 55px;
  padding: 0 45px 0 30px;
  border: 1px solid $border-color;
  color: $body-color;
  background-color: $body-bg;
  border-radius: 16px;
  font-size: 16px;
  width: 100%;

  &:focus {
    outline: 0;
    box-shadow: none;
    background-color: $body-bg;
    border-color: $theme-color;
  }

  @include inputPlaceholder {
    color: $body-color;
  }
}

.form-select,
select {
  display: block;
  width: 100%;
  line-height: 1.5;
  vertical-align: middle;
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3E%3C/svg%3E");
  background-position: right 26px center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  cursor: pointer;
}


input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type=number] {
  -moz-appearance: textfield;
}

textarea.form-control,
textarea {
  min-height: 150px;
  padding-top: 16px;
  padding-bottom: 17px;
  border-radius: 16px;
}


input[type="checkbox"] {
  visibility: hidden;
  opacity: 0;
  vertical-align: middle;
  width: 0;
  height: 0;
  display: none;

  &:checked {
    ~label {

      &:before {
        content: "\f00c";
        color: $white-color;
        background-color: $theme-color;
        border-color: $theme-color;
      }
    }
  }

  ~label {
    position: relative;
    padding-left: 30px;
    cursor: pointer;
    display: block;

    &:before {
      content: '';
      font-family: $icon-font;
      font-weight: 700;
      position: absolute;
      left: 0px;
      top: 5.5px;
      background-color: $body-bg;
      border: 1px solid $border-color;
      height: 18px;
      width: 18px;
      line-height: 18px;
      text-align: center;
      font-size: 12px;
      border-radius: 3px;
    }
  }
}


input[type="radio"] {
  visibility: hidden;
  opacity: 0;
  vertical-align: middle;
  width: 0;
  height: 0;
  display: none;


  ~label {
    position: relative;
    padding-left: 30px;
    cursor: pointer;
    line-height: 1;
    display: inline-block;
    font-weight: 600;
    margin-bottom: 0;

    &:before {
      content: "";
      position: absolute;
      left: 0;
      top: -1px;
      width: 17px;
      height: 17px;
      border-radius: 3px;
      border: 1px solid $border-color;
      outline: 5px solid transparent;
      outline-offset: -5px;
      transition: all ease 0.3s;
    }
  }

  &:checked {
    ~label {
      &::before {
        outline-color: $theme-color;
        border-color: transparent;
      }
    }
  }

}

label {
  margin-bottom: 0.5em;
  margin-top: -0.3em;
  display: block;
  color: $title-color;
  font-family: $title-font;
  font-weight: 500;
  font-size: 16px;
}

.form-group {
  margin-bottom: var(--bs-gutter-x);
  position: relative;

  > i {
    position: absolute;
    right: calc((var(--bs-gutter-x) / 2) + 30px);
    top: 19px;
    font-size: 16px;
    color: $title-color;
  }

  &.has-label {
    >i {
      top: 49px;
    }
  }
}


textarea.is-invalid,
select.is-invalid,
input.is-invalid,
.was-validated input:invalid {
  border: 1px solid $error-color !important;
  background-position: right calc(.375em + 0.8875rem) center;
  background-image: none;

  &:focus {
    outline: 0;
    box-shadow: none;
  }
}

textarea.is-invalid {
  background-position: top calc(.375em + 0.5875rem) right calc(.375em + .8875rem);
}

.row.no-gutters>.form-group {
  margin-bottom: 0;
}

.form-messages {
  display: none;
  margin: 0;
  padding: 10px 15px;
  margin-top: 15px;
  border-radius: 4px;

  >pre {
    &:last-child {
      margin-bottom: 0;
    }

    &:first-of-type {
      margin-top: 0;
    }
  }

  &.success {
    background-color: $success-color;
    color: $white-color;
    display: block;
  }

  &.error {
    background-color: $error-color;
    color: $white-color;
    display: block;
  }

  pre {
    padding: 0;
    background-color: transparent;
    color: inherit;
  }
}