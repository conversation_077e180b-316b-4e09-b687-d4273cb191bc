/**
 * Advanced filtering utilities for data tables
 */

/**
 * Apply search query across all searchable columns
 * @param {Array} data - The data to filter
 * @param {String} query - The search query
 * @param {Array} columns - Column definitions
 * @returns {Array} - Filtered data
 */
export function applySearch(data, query, columns) {
  if (!query || !query.trim()) return data
  
  const searchTerm = query.toLowerCase().trim()
  const searchableColumns = columns.filter(col => col.searchable !== false)
  
  return data.filter(item => {
    return searchableColumns.some(column => {
      const value = getNestedValue(item, column.key)
      if (value === null || value === undefined) return false
      
      const stringValue = String(value).toLowerCase()
      return stringValue.includes(searchTerm)
    })
  })
}

/**
 * Apply column-specific filters
 * @param {Array} data - The data to filter
 * @param {Object} columnFilters - Object with column keys and filter values
 * @returns {Array} - Filtered data
 */
export function applyColumnFilters(data, columnFilters) {
  if (!columnFilters || Object.keys(columnFilters).length === 0) return data
  
  return data.filter(item => {
    return Object.entries(columnFilters).every(([columnKey, filterValue]) => {
      if (filterValue === null || filterValue === undefined || filterValue === '') {
        return true
      }
      
      const itemValue = getNestedValue(item, columnKey)
      return matchesFilter(itemValue, filterValue, 'eq')
    })
  })
}

/**
 * Apply advanced filters with operators
 * @param {Array} data - The data to filter
 * @param {Array} filters - Array of filter objects
 * @returns {Array} - Filtered data
 */
export function applyFilters(data, filters) {
  if (!filters || filters.length === 0) return data
  
  return data.filter(item => {
    return filters.every(filter => {
      const itemValue = getNestedValue(item, filter.key)
      return matchesFilter(itemValue, filter.value, filter.operator || 'eq')
    })
  })
}

/**
 * Check if a value matches a filter condition
 * @param {*} itemValue - The value from the data item
 * @param {*} filterValue - The filter value to compare against
 * @param {String} operator - The comparison operator
 * @returns {Boolean} - Whether the value matches the filter
 */
export function matchesFilter(itemValue, filterValue, operator = 'eq') {
  // Handle null/undefined values
  if (itemValue === null || itemValue === undefined) {
    return filterValue === null || filterValue === undefined || filterValue === ''
  }
  
  switch (operator) {
    case 'eq': // equals
      return itemValue == filterValue
    
    case 'ne': // not equals
      return itemValue != filterValue
    
    case 'gt': // greater than
      return Number(itemValue) > Number(filterValue)
    
    case 'gte': // greater than or equal
      return Number(itemValue) >= Number(filterValue)
    
    case 'lt': // less than
      return Number(itemValue) < Number(filterValue)
    
    case 'lte': // less than or equal
      return Number(itemValue) <= Number(filterValue)
    
    case 'like': // contains (case insensitive)
      return String(itemValue).toLowerCase().includes(String(filterValue).toLowerCase())
    
    case 'not_like': // does not contain (case insensitive)
      return !String(itemValue).toLowerCase().includes(String(filterValue).toLowerCase())
    
    case 'starts_with': // starts with (case insensitive)
      return String(itemValue).toLowerCase().startsWith(String(filterValue).toLowerCase())
    
    case 'ends_with': // ends with (case insensitive)
      return String(itemValue).toLowerCase().endsWith(String(filterValue).toLowerCase())
    
    case 'in': // value is in array
      return Array.isArray(filterValue) && filterValue.includes(itemValue)
    
    case 'not_in': // value is not in array
      return Array.isArray(filterValue) && !filterValue.includes(itemValue)
    
    case 'between': // value is between two values
      if (Array.isArray(filterValue) && filterValue.length === 2) {
        const [min, max] = filterValue
        return Number(itemValue) >= Number(min) && Number(itemValue) <= Number(max)
      }
      return false
    
    case 'date_eq': // date equals (ignoring time)
      return formatDate(itemValue) === formatDate(filterValue)
    
    case 'date_gt': // date after
      return new Date(itemValue) > new Date(filterValue)
    
    case 'date_gte': // date on or after
      return new Date(itemValue) >= new Date(filterValue)
    
    case 'date_lt': // date before
      return new Date(itemValue) < new Date(filterValue)
    
    case 'date_lte': // date on or before
      return new Date(itemValue) <= new Date(filterValue)
    
    case 'date_between': // date between two dates
      if (Array.isArray(filterValue) && filterValue.length === 2) {
        const [startDate, endDate] = filterValue
        const itemDate = new Date(itemValue)
        return itemDate >= new Date(startDate) && itemDate <= new Date(endDate)
      }
      return false
    
    case 'regex': // regular expression match
      try {
        const regex = new RegExp(filterValue, 'i')
        return regex.test(String(itemValue))
      } catch (e) {
        return false
      }
    
    case 'empty': // value is empty (null, undefined, or empty string)
      return itemValue === null || itemValue === undefined || itemValue === ''
    
    case 'not_empty': // value is not empty
      return itemValue !== null && itemValue !== undefined && itemValue !== ''
    
    default:
      return itemValue == filterValue
  }
}

/**
 * Get nested value from object using dot notation
 * @param {Object} obj - The object to get value from
 * @param {String} path - The path to the value (e.g., 'user.name')
 * @returns {*} - The value at the path
 */
export function getNestedValue(obj, path) {
  if (!obj || !path) return undefined
  
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined
  }, obj)
}

/**
 * Format date to YYYY-MM-DD string
 * @param {*} date - Date value to format
 * @returns {String} - Formatted date string
 */
function formatDate(date) {
  if (!date) return ''
  
  try {
    const d = new Date(date)
    if (isNaN(d.getTime())) return ''
    
    return d.toISOString().split('T')[0]
  } catch (e) {
    return ''
  }
}

/**
 * Create filter options for a column based on unique values
 * @param {Array} data - The data to analyze
 * @param {String} columnKey - The column key to get options for
 * @param {Number} maxOptions - Maximum number of options to return
 * @returns {Array} - Array of filter options
 */
export function getFilterOptions(data, columnKey, maxOptions = 50) {
  if (!data || !data.length) return []
  
  const uniqueValues = new Set()
  
  data.forEach(item => {
    const value = getNestedValue(item, columnKey)
    if (value !== null && value !== undefined && value !== '') {
      uniqueValues.add(value)
    }
  })
  
  const options = Array.from(uniqueValues)
    .sort()
    .slice(0, maxOptions)
    .map(value => ({
      label: String(value),
      value: value
    }))
  
  return options
}

/**
 * Detect the best filter type for a column based on its data
 * @param {Array} data - The data to analyze
 * @param {String} columnKey - The column key to analyze
 * @returns {String} - Suggested filter type
 */
export function detectFilterType(data, columnKey) {
  if (!data || !data.length) return 'text'
  
  const values = data
    .map(item => getNestedValue(item, columnKey))
    .filter(value => value !== null && value !== undefined && value !== '')
  
  if (values.length === 0) return 'text'
  
  // Check if all values are booleans
  if (values.every(value => typeof value === 'boolean')) {
    return 'boolean'
  }
  
  // Check if all values are numbers
  if (values.every(value => !isNaN(Number(value)))) {
    return 'number'
  }
  
  // Check if all values are dates
  if (values.every(value => !isNaN(Date.parse(value)))) {
    return 'date'
  }
  
  // Check if there are few unique values (good for select)
  const uniqueValues = new Set(values)
  if (uniqueValues.size <= 10 && uniqueValues.size < values.length * 0.5) {
    return 'select'
  }
  
  return 'text'
}

/**
 * Build server-side filter query parameters
 * @param {Array} filters - Array of filter objects
 * @param {String} searchQuery - Search query string
 * @param {Object} columnFilters - Column-specific filters
 * @returns {Object} - Query parameters object
 */
export function buildFilterParams(filters = [], searchQuery = '', columnFilters = {}) {
  const params = {}
  
  // Add search query
  if (searchQuery && searchQuery.trim()) {
    params.search = searchQuery.trim()
  }
  
  // Add column filters
  Object.entries(columnFilters).forEach(([key, value]) => {
    if (value !== null && value !== undefined && value !== '') {
      params[`filter[${key}]`] = value
    }
  })
  
  // Add advanced filters
  filters.forEach((filter, index) => {
    if (filter.value !== null && filter.value !== undefined && filter.value !== '') {
      params[`filters[${index}][key]`] = filter.key
      params[`filters[${index}][value]`] = filter.value
      params[`filters[${index}][operator]`] = filter.operator || 'eq'
    }
  })
  
  return params
}
