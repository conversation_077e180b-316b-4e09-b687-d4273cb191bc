<html lang="en">
 <head>
  <meta charset="utf-8"/>
  <meta content="width=device-width, initial-scale=1" name="viewport"/>
  <title>
   Stitch Design
  </title>
  <link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
  <link as="style" href="https://fonts.googleapis.com/css2?display=swap&amp;family=Be+Vietnam+Pro%3Awght%40400%3B500%3B700%3B900&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900" onload="this.rel='stylesheet'" rel="stylesheet"/>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css" rel="stylesheet"/>
  <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries">
  </script>
 </head>
 <body class="relative flex min-h-screen flex-col bg-[#221112] font-['Be_Vietnam_Pro','Noto_Sans',sans-serif] overflow-x-hidden">
  <div class="flex flex-col grow h-full layout-container">
   <header class="flex flex-wrap items-center justify-between border-b border-b-[#472426] px-5 py-3 sm:px-10">
    <div class="flex flex-wrap items-center gap-6">
     <div class="flex items-center gap-3 text-white">
      <div class="w-8 h-8">
       <svg class="w-full h-full" fill="none" viewbox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
        <path clip-rule="evenodd" d="M24 4H42V17.3333V30.6667H24V44H6V30.6667V17.3333H24V4Z" fill="currentColor" fill-rule="evenodd">
        </path>
       </svg>
      </div>
      <h2 class="text-white text-lg font-bold leading-tight tracking-[-0.015em]">
       Streamr
      </h2>
     </div>
     <nav class="flex flex-wrap gap-4 sm:gap-9 text-white text-sm font-medium leading-normal whitespace-nowrap">
      <a class="hover:text-[#e92932] transition-colors" href="#">
       Home
      </a>
      <a class="hover:text-[#e92932] transition-colors" href="#">
       Series
      </a>
      <a class="hover:text-[#e92932] transition-colors" href="#">
       Movies
      </a>
      <a class="hover:text-[#e92932] transition-colors" href="#">
       New &amp; Popular
      </a>
      <a class="hover:text-[#e92932] transition-colors" href="#">
       My List
      </a>
      <a class="hover:text-[#e92932] transition-colors" href="#">
       Browse by Languages
      </a>
     </nav>
    </div>
    <div class="flex flex-1 justify-end gap-4 sm:gap-8 items-center min-w-[180px]">
     <label class="flex flex-1 max-w-[280px] min-w-[120px] h-10 rounded-lg overflow-hidden">
      <div class="flex items-center justify-center pl-3 bg-[#472426] text-[#c89295]">
       <svg class="block" fill="currentColor" height="20" viewbox="0 0 256 256" width="20" xmlns="http://www.w3.org/2000/svg">
        <path d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z">
        </path>
       </svg>
      </div>
      <input aria-label="Search" class="form-input flex-1 bg-[#472426] text-white placeholder-[#c89295] px-3 py-2 focus:outline-none focus:ring-0 rounded-r-lg" placeholder="Search" type="search"/>
     </label>
     <img alt="User avatar placeholder image, circular, 40x40 pixels" class="rounded-full w-10 h-10 object-cover" height="40" loading="lazy" src="https://storage.googleapis.com/a1aa/image/9d6279bd-2544-4fe9-cacb-28bf8820fef6.jpg" width="40"/>
    </div>
   </header>
   <main class="flex flex-col flex-1 px-5 py-5 sm:px-10 sm:py-8 max-w-[960px] mx-auto w-full">
    <section class="relative rounded-lg overflow-hidden min-h-[320px] sm:min-h-[400px] mb-4">
     <img alt="The Midnight Bloom movie poster showing a dark magical forest with mystical glowing lights and a young woman standing in the center" class="absolute inset-0 w-full h-full object-cover" height="400" loading="lazy" src="https://storage.googleapis.com/a1aa/image/617e1234-3f76-40f5-748d-ad8b341621bf.jpg" width="960"/>
     <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent flex items-end p-4 sm:p-6">
      <h1 class="text-white text-3xl sm:text-4xl font-bold leading-tight tracking-tight">
       The Midnight Bloom
      </h1>
     </div>
    </section>
    <p class="text-white text-base font-normal leading-relaxed px-2 sm:px-4 mb-4">
     In a world where dreams and reality intertwine, a young woman
          discovers a hidden power within herself as she navigates a mysterious
          realm filled with magic and danger.
    </p>
    <div class="flex flex-wrap gap-3 px-2 sm:px-4 mb-6">
     <button class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center rounded-lg h-10 px-4 bg-[#e92932] text-white text-sm font-bold tracking-wide hover:bg-[#c71f23] transition-colors" type="button">
      <i class="fas fa-play mr-2">
      </i>
      Play
     </button>
     <button class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center rounded-lg h-10 px-4 bg-[#472426] text-white text-sm font-bold tracking-wide hover:bg-[#5a3537] transition-colors" type="button">
      <i class="fas fa-plus mr-2">
      </i>
      My List
     </button>
    </div>
    <h2 class="text-white text-lg font-bold leading-tight tracking-[-0.015em] px-2 sm:px-4 mb-3">
     Cast &amp; Crew
    </h2>
    <div aria-label="Cast and crew list" class="flex gap-6 overflow-x-auto px-2 sm:px-4 pb-4 scrollbar-hide" tabindex="0">
     <div class="flex flex-col items-center min-w-[128px] max-w-[128px] gap-3 rounded-lg pt-4">
      <img alt="Portrait of Ethan Carter, Director, male with short dark hair and glasses, smiling" class="rounded-full w-32 h-32 object-cover" height="128" loading="lazy" src="https://storage.googleapis.com/a1aa/image/b40832b7-effd-4be2-7069-8940ff20a076.jpg" width="128"/>
      <p class="text-white text-base font-medium text-center">
       Ethan Carter
      </p>
      <p class="text-[#c89295] text-sm font-normal text-center">
       Director
      </p>
     </div>
     <div class="flex flex-col items-center min-w-[128px] max-w-[128px] gap-3 rounded-lg pt-4">
      <img alt="Portrait of Olivia Hayes, Lead Actress, female with long brown hair, smiling" class="rounded-full w-32 h-32 object-cover" height="128" loading="lazy" src="https://storage.googleapis.com/a1aa/image/1b9272e6-9bbc-43ec-9ed8-4573f96cd654.jpg" width="128"/>
      <p class="text-white text-base font-medium text-center">
       Olivia Hayes
      </p>
      <p class="text-[#c89295] text-sm font-normal text-center">
       Lead Actress
      </p>
     </div>
     <div class="flex flex-col items-center min-w-[128px] max-w-[128px] gap-3 rounded-lg pt-4">
      <img alt="Portrait of Marcus Reed, Supporting Actor, male with short curly hair and beard, serious expression" class="rounded-full w-32 h-32 object-cover" height="128" loading="lazy" src="https://storage.googleapis.com/a1aa/image/18fafe55-761e-46df-3fdd-d237db1f3881.jpg" width="128"/>
      <p class="text-white text-base font-medium text-center">
       Marcus Reed
      </p>
      <p class="text-[#c89295] text-sm font-normal text-center">
       Supporting Actor
      </p>
     </div>
     <div class="flex flex-col items-center min-w-[128px] max-w-[128px] gap-3 rounded-lg pt-4">
      <img alt="Portrait of Sophia Bennett, Supporting Actress, female with medium length blonde hair, smiling" class="rounded-full w-32 h-32 object-cover" height="128" loading="lazy" src="https://storage.googleapis.com/a1aa/image/7764389e-6ce7-4278-cb9d-5a87e275f800.jpg" width="128"/>
      <p class="text-white text-base font-medium text-center">
       Sophia Bennett
      </p>
      <p class="text-[#c89295] text-sm font-normal text-center">
       Supporting Actress
      </p>
     </div>
     <div class="flex flex-col items-center min-w-[128px] max-w-[128px] gap-3 rounded-lg pt-4">
      <img alt="Portrait of Richard Evans, Producer, male with short gray hair and glasses, smiling" class="rounded-full w-32 h-32 object-cover" height="128" loading="lazy" src="https://storage.googleapis.com/a1aa/image/8ee8fbf3-b2ef-4deb-8a79-473528df81ac.jpg" width="128"/>
      <p class="text-white text-base font-medium text-center">
       Richard Evans
      </p>
      <p class="text-[#c89295] text-sm font-normal text-center">
       Producer
      </p>
     </div>
    </div>
    <h2 class="text-white text-lg font-bold leading-tight tracking-[-0.015em] px-2 sm:px-4 mb-3 mt-6">
     Trailers &amp; More
    </h2>
    <section class="px-2 sm:px-4 mb-6">
     <div class="flex flex-col sm:flex-row gap-4 rounded-lg overflow-hidden bg-[#2f1a1a]">
      <div class="flex flex-col flex-[2_2_0px] p-4 justify-center">
       <p class="text-[#c89295] text-sm font-normal mb-1">
        Trailer
       </p>
       <p class="text-white text-base font-bold mb-1">
        Official Trailer
       </p>
       <p class="text-[#c89295] text-sm font-normal">
        Watch the official trailer for The Midnight Bloom.
       </p>
      </div>
      <img alt="Thumbnail image for the official trailer of The Midnight Bloom showing a dramatic movie scene with a play button overlay" class="aspect-video w-full sm:w-auto object-cover rounded-lg flex-1" height="270" loading="lazy" src="https://storage.googleapis.com/a1aa/image/8ba2c3b6-359e-4f1f-05f6-287101cbffff.jpg" width="480"/>
     </div>
    </section>
    <h2 class="text-white text-lg font-bold leading-tight tracking-[-0.015em] px-2 sm:px-4 mb-3">
     More Like This
    </h2>
    <div aria-label="More like this movie list" class="flex gap-4 overflow-x-auto px-2 sm:px-4 pb-4 scrollbar-hide" tabindex="0">
     <div class="flex flex-col min-w-[160px] max-w-[160px] gap-3 rounded-lg">
      <img alt="Movie poster for Whispers of the Past, showing a misty forest with a mysterious figure" class="rounded-lg w-full h-auto object-cover" height="213" loading="lazy" src="https://storage.googleapis.com/a1aa/image/8dbefa6a-6ea0-401a-5f25-0804edd45a3d.jpg" width="160"/>
      <p class="text-white text-base font-medium">
       Whispers of the Past
      </p>
     </div>
     <div class="flex flex-col min-w-[160px] max-w-[160px] gap-3 rounded-lg">
      <img alt="Movie poster for Echoes of Tomorrow, showing a futuristic cityscape with neon lights" class="rounded-lg w-full h-auto object-cover" height="213" loading="lazy" src="https://storage.googleapis.com/a1aa/image/741323c1-cd82-4e8c-841c-728fa739a9ce.jpg" width="160"/>
      <p class="text-white text-base font-medium">
       Echoes of Tomorrow
      </p>
     </div>
     <div class="flex flex-col min-w-[160px] max-w-[160px] gap-3 rounded-lg">
      <img alt="Movie poster for The Silent Symphony, showing a grand concert hall with a lone violinist" class="rounded-lg w-full h-auto object-cover" height="213" loading="lazy" src="https://storage.googleapis.com/a1aa/image/29d83e3c-c81c-42db-32b3-65003e38e156.jpg" width="160"/>
      <p class="text-white text-base font-medium">
       The Silent Symphony
      </p>
     </div>
     <div class="flex flex-col min-w-[160px] max-w-[160px] gap-3 rounded-lg">
      <img alt="Movie poster for Chronicles of the Unseen, showing an ancient book with glowing runes" class="rounded-lg w-full h-auto object-cover" height="213" loading="lazy" src="https://storage.googleapis.com/a1aa/image/b69e01af-4174-49d7-6491-39344d620e5d.jpg" width="160"/>
      <p class="text-white text-base font-medium">
       Chronicles of the Unseen
      </p>
     </div>
     <div class="flex flex-col min-w-[160px] max-w-[160px] gap-3 rounded-lg">
      <img alt="Movie poster for The Enigma Code, showing a cryptic code on a digital screen" class="rounded-lg w-full h-auto object-cover" height="213" loading="lazy" src="https://storage.googleapis.com/a1aa/image/00d9e95c-26f4-46c4-82c7-9a6cd6a05ae1.jpg" width="160"/>
      <p class="text-white text-base font-medium">
       The Enigma Code
      </p>
     </div>
    </div>
   </main>
  </div>
  <style>
   /* Hide scrollbar for Chrome, Safari and Opera */
      .scrollbar-hide::-webkit-scrollbar {
        display: none;
      }
      /* Hide scrollbar for IE, Edge and Firefox */
      .scrollbar-hide {
        -ms-overflow-style: none; /* IE and Edge */
        scrollbar-width: none; /* Firefox */
      }
  </style>
 </body>
</html>
