.widget {
  padding: var(--widget-padding-y, 40px) var(--widget-padding-x, 40px);
  background-color: $smoke-color;
  position: relative;
  margin-bottom: 40px;
  border-radius: 30px;

  select,
  input {
    height: 60px;
    border: none;
    background-color: var(--input-bg, #ffffff);
  }
}

.widget_title {
  position: relative;
  font-weight: 600;
  font-size: 30px;
  line-height: 1em;
  font-family: $title-font;
  margin: -0.2em 0 30px 0;
  padding: 0 0 13px 0;

  &:before {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    height: 4px;
    width: 30px;
    background-color: $theme-color2;
    border-radius: 9999px;
  }
}

.widget {
  .search-form {
    position: relative;

    input {
      padding-right: 70px;
    }

    button {
      border: none;
      background-color: transparent;
      padding: 4px 0 4px 15px;
      line-height: 1;
      color: $theme-color;
      border-left: 1px solid $border-color;
      position: absolute;
      top: 50%;
      right: 25px;
      transform: translateY(-50%);
      font-size: 18px;

      &:hover {
        color: $secondary-color;
      }
    }
  }
}

.wp-block-tag-cloud,
.tagcloud {
  a {
    position: relative;
    display: inline-block;
    border: none;
    z-index: 1;
    font-size: 16px;
    font-weight: 600;
    line-height: 1;
    padding: 12px 22px;
    margin-right: 5px;
    margin-bottom: 10px;
    color: $title-color;
    background-color: $smoke-color;
    font-family: $title-font;
    border-radius: 9999px;
    text-transform: none;

    &:hover {
      background-color: $theme-color;
      color: $white-color !important;

      &:before {
        background-color: $white-color;
      }
    }
  }
}

.tagcloud {
  margin-right: -5px;
  margin-bottom: -10px;

  a {
    background-color: $white-color;
  }
}

.recent-post {
  display: flex;
  align-items: center;
  margin-bottom: 18px;

  &:last-child {
    margin-bottom: 0;
  }

  .media-img {
    margin-right: 20px;
    width: 100px;
    overflow: hidden;
    border-radius: 20px;

    img {
      width: 100%;
      transform: scale(1.001);
      transition: all ease 0.4s;
    }
  }

  .post-title {
    font-weight: 600;
    font-size: 20px;
    line-height: 24px;
    margin: 0 0 -0.1em 0;
  }

  .recent-post-meta {
    margin: -0.2em 0 2px 0;

    a {
      text-transform: capitalize;
      font-size: 14px;
      font-weight: 500;
      color: $theme-color;
      font-family: $title-font;

      i {
        margin-right: 5px;
      }

      &:hover {
        color: $secondary-color;
      }
    }
  }

  &:hover {
    .media-img {
      img {
        transform: scale(1.15);
      }
    }
  }
}

.recent-event {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }

  .event-title {
    font-size: 22px;
    margin: 0;
  }

  .event-date {
    background-color: $secondary-color;
    color: $white-color;
    line-height: 1;
    width: 80px;
    height: 90px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
    text-transform: uppercase;
    border-radius: 30px;    
    letter-spacing: 0.02em;
    margin-right: 20px;
    transition: all ease 0.4s;
    font: {
      family: $title-font;
      size: 30px;
      weight: 600;
    }

    .month {
      display: block;
      margin-bottom: 2px;
      font-size: 14px;
    }
  }


  &:hover {
    .event-date {
      background-color: $theme-color;
    }
  }
}

.vs-video-widget {
  .video-thumb {
    overflow: hidden;
    margin-bottom: 15px;
    border-radius: 20px;
  }

  .video-title {
    margin: 0 0 -0.2em 0;
  }
}

.range-slider-area {
  .price-amount {
    display: block;
    color: $theme-color;
    font-size: 16px;
    font-weight: 400;
    margin: -0.4em 0 0 0;
    
    .title {
      color: $title-color;
      font-weight: 600;
      margin-right: 10px;
      text-transform: uppercase;
      font-size: 16px;
      font-family: $title-font;
    }
  }

  .ui-slider {
    height: 4px;
    position: relative;
    width: 100%;
    background-color: $secondary-color;
    border: none;
    margin-top: 20px;
    margin-bottom: 29px;
    cursor: pointer;
    border-radius: 3px;
  }

  .ui-slider-range {
    border: none;
    cursor: pointer;
    position: absolute;
    top: 0;
    height: 100%;
    z-index: 1;
    display: block;
    background-color: $theme-color;
  }

  .ui-slider-handle {
    width: 16px;
    height: 16px;
    padding: 0;
    border: none;
    cursor: pointer;
    position: absolute;
    top: 50%;
    margin-top: -8px;
    z-index: 2;
    background-color: $theme-color;
    transform: translateX(0px);
    border-radius: 50%;

    &:focus {
      outline: none;
      box-shadow: none;
    }

    &:last-child {
      transform: translateX(-10px);
    }
  }


  .filter-btn {
    background-color: $secondary-color;
    color: $white-color;
    border: 1px solid transparent;
    padding: 6px 30px;
    border-radius: 9999px;
    font-size: 16px;
    font-weight: 600;
    font-family: $title-font;

    &:hover {
      background-color: $theme-color;
      color: $white-color;
      border-color: transparent;
    }
  }

  .reset-btn {
    float: right;
    border: none;
    padding: 0;
    font-size: 12px;
    text-transform: uppercase;
    color: $title-color;
    font-weight: 700;
    background-color: transparent;
    position: relative;
    top: 8px;

    i {
      margin-right: 7px;
    }

    &:hover {
      color: $theme-color;
    }
  }
}

.category-filter {
  ul {
    margin: 0;
    padding: 2px 0 0 0;
    list-style: none;
  }

  li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    line-height: 1;
    margin-bottom: 20px;
    border-bottom: 1px solid $border-color;
    padding-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
      padding-bottom: 0;
      border-bottom: none;
    }
  }

  input[type=checkbox] {
    ~label {
      margin: 0;
      line-height: 1;
      color: $body-color;
      text-transform: capitalize;

      &:before {
        top: 0px;
      }

      &:hover {
        color: $theme-color;
      }
    }

    &:checked {
      ~ label {
        color: $theme-color;
      }
    }
  }

  .total {
    color: $title-color;
    top: 1px;
    position: relative;
  }
}

.latest-product {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }

  .media-img {
    width: 80px;
    background-color: #ffffff;
    border: none;
    margin-right: 20px;
    overflow: hidden;
    border-radius: 10px;

    img {
      transition: all ease 0.4s;
      transform: scale(1.001);
    }
  }

  .product-title {
    font-size: 18px;
    font-family: $title-font;
    margin-bottom: 8px;
  }

  .product-price {
    font-size: 16px;
    color: $secondary-color;
    font-weight: 500;
    margin: 0 0 -0.2em 0;
  }

  &:hover {
    .media-img {
      img {
        transform: scale(1.15);
      }
    }
  }

}

.sidebar-gallery:not(.vs-carousel) {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
}

.sidebar-gallery {
  .gallery-thumb {
    overflow: hidden;
    position: relative;
    border-radius: 20px;

    img {
      transition: all ease 0.4s;
      width: 100%;
      transform: scale(1);
    }

    &:before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: $theme-color;
      z-index: 1;
      opacity: 0;
      visibility: hidden;
      transition: all ease 0.4s;
    }

    .gal-btn {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: scale(1.5);
      width: var(--icon-size, 35px);
      height: var(--icon-size, 35px);
      line-height: calc(var(--icon-size, 35px) + 2px);
      margin: calc(var(--icon-size, 35px) / -2) 0 0 calc(var(--icon-size, 35px) / -2);
      text-align: center;
      font-size: 18px;
      color: $white-color;
      background-color: transparent;
      border-radius: 50%;
      transition: all ease 0.4s;
      opacity: 0;
      visibility: hidden;
      z-index: 3;

      &:hover {
        transform: scale(1);
        background-color: $white-color;
        color: $theme-color;
      }
    }

    &:hover {

      &:before {
        opacity: 0.80;
        visibility: visible;
      }

      .gal-btn {
        opacity: 1;
        visibility: visible;
        transition-delay: 0.1s;
      }

      img {
        transform: scale(1.12);
      }
    }
  }
}

.shop-sidebar {
  background-color: $smoke-color;
  border-radius: 20px;
  padding: 40px 30px;
  
  .widget{
    padding: 0 0 0 0;
    margin: 0 0 45px 0;
    border-radius: 0;
    background-color: transparent;

    &:last-child {
      margin-bottom: 0;
      padding-bottom: 0;
      border-bottom: none;
    }
  }

  .widget_title {
    font-size: 24px;
  }

  .sidebar-gallery {
    grid-template-columns: repeat(2, 1fr);
  }

}

@include ml {
  .widget {
    --widget-padding-y: 30px;
    --widget-padding-x: 30px;
  }

  .widget_title {
    font-size: 26px;
  }

  .recent-event {
    .event-title {
      font-size: 18px;
    }

    .event-date {
      width: 75px;
      height: 80px;
      letter-spacing: 0;
      font-size: 24px;
    }
  }

  .recent-post {
    .media-img {
      margin-right: 15px;
      width: 85px;
    }

    .recent-post-meta {
      margin: -0.2em 0 0 0;

      a {
        font-size: 14px;
      }
    }

    .post-title {
      font-size: 18px;
      line-height: 22px;
    }
  }

  .wp-block-tag-cloud,
  .tagcloud {
    a {
      font-size: 14px;
      padding: 12px 20px;
    }
  }
}


@include md {
  .sidebar-area {
    padding-top: 30px;
  }
  
  .shop-sidebar {
    padding: 40px 20px;
    
    .sidebar-gallery {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  .wp-block-tag-cloud a,
  .tagcloud a {
    padding: 10.5px 18px;
  }

}

@include sm {

  .widget_title {
    font-size: 22px;
  }

  .sidebar-area {
    .comment-list {
      .comment-author {
        font-size: 18px;
      }
      
      .comment-text {
        font-size: 14px;
      }
    }
  }

}

@include xs {
  .widget {
    padding: 30px 20px;
  }
}