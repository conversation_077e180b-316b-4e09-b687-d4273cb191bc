/* file manager layout */
.fileM-contents {

    .fileM-grid-wrapper {
        padding: 30px;
        background-color: var(--color-white);
        box-shadow: 0 5px 20px rgba(var(--light-gray-rgba), 0.03);
        border-radius: 10px;
    }

    @media (min-width: 1399px) {
        .col-20 {
            flex: 0 0 20.50%;
            max-width: 20.50%;
        }

        .col-79 {
            flex: 0 0 79.5%;
            max-width: 79.5%;
        }
    }
}

/* file manager sidebar */
.fileM-sidebar {
    height: 100vh;
    background: var(--color-white);
    border-radius: 10px;
    @include ssm{
        height: auto;
    }

    .fileM-types {
        margin:0 0 63px 0;
        @include ssm{
            margin: 0 0 30px 0;
        }

        .sidebar__menu-group ul.sidebar_nav {
            margin: 21px 0 0 0;
            li.has-child >ul{
                visibility: hidden;
                opacity: 0;
            }
            li.has-child.open {
               > a{
                    color:var(--color-primary);
                }
                >ul{
                    visibility: visible;
                    opacity: 1;
                }
            }
        }

        .sidebar__menu-group ul.sidebar_nav li.menu-title span {
            color: var(--color-dark);
            @include ofs(16px, lh(16px, 20px), 500);
            padding: 0 14px;
        }

        .sidebar__menu-group ul.sidebar_nav li>a {
            padding: 0 5px 0 14px;
        }

        .sidebar__menu-group ul.sidebar_nav li>a .toggle-icon {
            position: absolute;
            left: -2px;
        }

        .sidebar__menu-group ul.sidebar_nav li>a .nav-icon {
            margin-right: 10px;
        }

        .sidebar__menu-group ul.sidebar_nav li ul {
            padding:0 0 0 15px;
        }

        .sidebar__menu-group ul.sidebar_nav li>a .nav-icon {
            width: 14px;
        }
    }

    ul {
        li a {
            color: var(--color-gray-x);
            font-size: 14px;
            display: flex;
            align-items: center;
            min-height: 38px;
            padding: 0 20px;
            border-radius: 4px;
            transition: var(--transition);
            img,
            svg {
                color:var(--color-lighten);
                width: 18px;
                margin-right: 13px;
            }

            &.active {
                color: var(--color-primary);
                img,
                svg {
                    color: var(--color-primary);
                }
            }

            &:hover {
                color: var(--color-primary);
            }
        }
    }

}

// File Manager progress
.fileM-progress {
    margin: 0 15px;

    @include sm {
        margin: 0;
    }

    .user-group-progress-bar {
        .progress {
            height: 5px;
            border-radius: 100px;

            span {
                color: var(--color-gray-x);
                @include ofs(12px, lh(12px, 16px), 500);
            }

            .progress-bar {
                border-radius: 100px;
            }
        }

        p {
            color: var(--color-gray-x);
            @include ofs(14px, lh(14px, 20px), 500);
            margin-bottom: 3px;
        }

        span {
            color: var(--color-light);
            @include ofs(12px, lh(12px, 20px), 400);
        }
    }
}

// File Manager card
.fileM-wrapper__title {
    @include ofs(16px, lh(16px, 20px), 500);
    color: var(--color-dark);
    margin-bottom: 20px;
}

.fileM-top-search {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 28px;

    @include sm {
        flex-flow: column;

        form {
            margin-bottom: 15px;
        }
    }

    form {
        border: 1px solid var(--border-color);
        border-radius: 6px;
        padding: 0 20px;
        img,
        svg {
            color: var(--color-gray);
            width: 15px;
        }

        input {
            padding: 8px 15px;
            &::placeholder {
                color: var(--body-color);
                @include ofs(14px, lh(14px, 25px), 400);
            }
        }
    }
}

.fileM-card {
    text-align: center;

    .card {
        background-color: var(--bg-lighter);
    }

    .fileM-excerpt {
        @include ofs(14px, lh(14px, 20px), 500);
        color: var(--color-dark);
        margin-bottom: 0;
    }

    .fileM-img {
        margin-bottom: 18px;
    }

}


.fileM-action {
    .fileM-action__right {
        .dropdown {
            top: 9px;
            right: 12px;
        }
    }
}

.fileM-action__right {
    .dropdown {

        .dropdown-default,
        .dropdown-menu {
            padding: 18px 0;
            margin-right: 0;
            box-shadow: 0 5px 20px var(--shadow3);
        }

        .dropdown-item {
            @include ofs(14px, lh(14px, 32px), 400);
            color: var(--color-gray-x);
            display: flex;
            align-items: center;
            padding: 1px 25px;

            &:hover {
                background-color: rgba(var(--color-primary-rgba), 0.05);
                color:var(--color-primary);
                img,
                svg {
                    color:var(--color-primary);
                }
            }
            img,
            svg {
                color: var(--color-gray);
                width: 16px;
                margin-right: 12px;
            }
        }
        img,
        svg {
            color: var(--color-light);
            width: 20px;
        }
    }
}

.fileM-sidebar .fileM-action__right .dropdown {
    >a {
        width: 100%;
        font-size: 14px;
        img,
        svg {
            width: 15px;
            color: var(--color-white);
        }
    }

    .dropdown-menu {
        width: 100%;
    }
}


//table
.filleM-table {
    .projectDatatable-title {
        font-weight: 500;
        color: var(--color-gray-x);
    }

    .files-area__title span {
        line-height: lh(12px, 22px);
        margin-top: 2px;
        text-transform: uppercase;
    }

    .table thead tr th {
        border-top: 1px solid var(--border-color);
        padding: 14px 10px;

        &:first-child {
            border-left: 1px solid var(--border-color);
        }

        &:last-child {
            border-right: 1px solid var(--border-color);
        }
    }

    .table th,
    .table td {
        white-space: nowrap;
        padding: 9px 10px;
        vertical-align: middle;
        border-top: none;

        &:first-child {
            padding-left: 38px;
        }
    }

    tbody tr {
        transition: var(--transition);

        &:hover {
            box-shadow: 0 15px 50px rgba(#9299B8, 0.20);
        }
    }
}


//modal

.fileM-Modal-overlay {
    .modal {
        background-color: rgba(#111217, .60);
    }

    .modal-header {
        border-bottom: none;
        padding-bottom: 30px;
    }

    .modal-footer {
        border-top: none;
        padding: 0 30px 30px 30px;

        button {
            height: 36px;
            border: none;
            @extend .content-center;
        }
    }

    .modal-content {
        border-radius: 10px;
        box-shadow: 0 5px 20px rgba(#9299B8, 0.03);
    }

    .modal-body {
        padding: 0 30px;

        input {
            height: 36px;
            border-radius: 4px;
            &::placeholder{
                @include ofs(14px, lh(14px,25px), 400);
                color: var(--color-gray);
            }
        }
    }

    .modal-title button {
        img,
        svg {
            width: 20px;
            color: var(--color-gray);
        }
    }

    .modal-footer>* {
        margin: 5px;
    }

}

.fileM-sidebar {
    @media (min-width: 576px) {
        .modal-dialog {
            max-width: 390px;
            margin: 1.75rem auto;
        }
    }
}