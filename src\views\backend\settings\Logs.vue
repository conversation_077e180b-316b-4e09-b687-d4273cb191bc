<template>
  <div class="p-6">
    <div class="mb-6">
      <h1 class="text-3xl font-bold text-gray-900">System Logs</h1>
      <p class="text-gray-600 mt-2">Monitor system activity and errors</p>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
      <div class="flex flex-wrap items-center gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Log Level</label>
          <select 
            v-model="selectedLevel"
            class="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">All Levels</option>
            <option value="info">Info</option>
            <option value="warning">Warning</option>
            <option value="error">Error</option>
            <option value="debug">Debug</option>
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
          <select 
            v-model="selectedDateRange"
            class="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="today">Today</option>
            <option value="week">Last 7 days</option>
            <option value="month">Last 30 days</option>
            <option value="all">All time</option>
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
          <input 
            v-model="searchQuery"
            type="text" 
            placeholder="Search logs..."
            class="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
        </div>
        
        <div class="flex items-end">
          <button 
            @click="clearLogs"
            class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors"
          >
            Clear Logs
          </button>
        </div>
      </div>
    </div>

    <!-- Logs Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Timestamp</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Level</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Message</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP Address</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="log in filteredLogs" :key="log.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ formatDate(log.timestamp) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="[
                  'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                  getLevelClass(log.level)
                ]">
                  {{ log.level.toUpperCase() }}
                </span>
              </td>
              <td class="px-6 py-4 text-sm text-gray-900">
                <div class="max-w-xs truncate">{{ log.message }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ log.user || 'System' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ log.ipAddress }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <!-- Pagination -->
      <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-700">
            Showing {{ (currentPage - 1) * itemsPerPage + 1 }} to {{ Math.min(currentPage * itemsPerPage, filteredLogs.length) }} of {{ filteredLogs.length }} results
          </div>
          <div class="flex space-x-2">
            <button 
              @click="currentPage--"
              :disabled="currentPage === 1"
              class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 disabled:opacity-50"
            >
              Previous
            </button>
            <button 
              @click="currentPage++"
              :disabled="currentPage * itemsPerPage >= filteredLogs.length"
              class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 disabled:opacity-50"
            >
              Next
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const selectedLevel = ref('')
const selectedDateRange = ref('week')
const searchQuery = ref('')
const currentPage = ref(1)
const itemsPerPage = 20

const logs = ref([
  {
    id: 1,
    timestamp: new Date('2024-01-15T10:30:00'),
    level: 'info',
    message: 'User logged in successfully',
    user: '<EMAIL>',
    ipAddress: '*************'
  },
  {
    id: 2,
    timestamp: new Date('2024-01-15T10:25:00'),
    level: 'warning',
    message: 'Failed login attempt detected',
    user: null,
    ipAddress: '*************'
  },
  {
    id: 3,
    timestamp: new Date('2024-01-15T10:20:00'),
    level: 'error',
    message: 'Database connection timeout',
    user: 'system',
    ipAddress: '127.0.0.1'
  },
  {
    id: 4,
    timestamp: new Date('2024-01-15T10:15:00'),
    level: 'info',
    message: 'New user registration completed',
    user: '<EMAIL>',
    ipAddress: '*************'
  },
  {
    id: 5,
    timestamp: new Date('2024-01-15T10:10:00'),
    level: 'debug',
    message: 'Cache cleared successfully',
    user: '<EMAIL>',
    ipAddress: '*************'
  }
])

const filteredLogs = computed(() => {
  let filtered = logs.value

  // Filter by level
  if (selectedLevel.value) {
    filtered = filtered.filter(log => log.level === selectedLevel.value)
  }

  // Filter by search query
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(log => 
      log.message.toLowerCase().includes(query) ||
      (log.user && log.user.toLowerCase().includes(query)) ||
      log.ipAddress.includes(query)
    )
  }

  // Filter by date range
  const now = new Date()
  if (selectedDateRange.value !== 'all') {
    const cutoffDate = new Date()
    switch (selectedDateRange.value) {
      case 'today':
        cutoffDate.setHours(0, 0, 0, 0)
        break
      case 'week':
        cutoffDate.setDate(now.getDate() - 7)
        break
      case 'month':
        cutoffDate.setDate(now.getDate() - 30)
        break
    }
    filtered = filtered.filter(log => log.timestamp >= cutoffDate)
  }

  return filtered.sort((a, b) => b.timestamp - a.timestamp)
})

const formatDate = (date) => {
  return date.toLocaleString()
}

const getLevelClass = (level) => {
  switch (level) {
    case 'error':
      return 'bg-red-100 text-red-800'
    case 'warning':
      return 'bg-yellow-100 text-yellow-800'
    case 'info':
      return 'bg-blue-100 text-blue-800'
    case 'debug':
      return 'bg-gray-100 text-gray-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const clearLogs = () => {
  if (confirm('Are you sure you want to clear all logs? This action cannot be undone.')) {
    logs.value = []
  }
}
</script>
