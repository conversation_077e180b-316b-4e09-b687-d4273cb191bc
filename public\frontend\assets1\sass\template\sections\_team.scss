.team-style1,
.team-style2 {
  .team-img {
    overflow: hidden;
    border-radius: 30px;
    position: relative;

    &:before {
      content: '';
      position: absolute;
      left: 5px;
      top: 5px;
      bottom: 5px;
      right: 5px;
      border: 2px solid $theme-color;
      z-index: 1;
      border-radius: inherit;
      transform: scale(0.95);
      transition: all ease 0.4s;
      opacity: 0;
      visibility: hidden;
      pointer-events: none;
    }

    img {
      transform: scale(1.001);
      transition: all ease 0.4s;
    }
  }

  &:hover {
    .team-img {
      &:before {
        opacity: 1;
        visibility: visible;
        transform: scale(1);
      }

      img {
        transform: scale(1.05);
      }
    }
  }
}

.team-style1 {
  display: flex;
  align-items: center;
  background-color: $white-color;
  padding: 30px;
  border-radius: 30px 30px 210px 30px;
  margin-bottom: 30px;
  transition: all ease 0.4s;

  &.layout2 {
    border: 3px solid #D9D9D9;

    &:hover {
      border-color: $theme-color;
    }

    .team-img {
      &:before {
        display: none;
      }
    }
  }

  .team-img {
    margin-right: 35px;
  }

  .team-content {
    flex: 1;
  }

  .team-name {
    margin-bottom: 5px;
    margin-top: -0.22em;
  }

  .team-degi {
    font-size: 18px;
    font-weight: 500;
    padding-bottom: 16px;
    color: $theme-color;
    position: relative;
    text-transform: capitalize;

    &:before {
      content: '';
      position: absolute;
      left: 0;
      bottom: 0;
      width: 70px;
      height: 1px;
      background-color: #B5B5B5;
    }
  }


  .team-number {
    font-size: 18px;
    color: $body-color;
    display: inline-block;
    margin-bottom: 13px;
  }

}

.team-style2 {
  position: relative;
  margin-bottom: 30px;

  .team-img {
    img {
      width: 100%;
    }
  }
  
  .team-name {
    font-size: 26px;
    text-align: center;
    position: absolute;
    left: 17px;
    right: 17px;
    bottom: 20px;
    color: $title-color;
    background-color: $white-color;
    border-radius: 9999px;
    margin: 0;
    padding: 13px 0;
    line-height: 1;
    transition: all ease 0.4s;
  }

  &:hover {
    .team-name {
      background-color: $theme-color;

      a {
        color: $white-color;
      }

      &:hover {
        background-color: $secondary-color;
      }
    }
  }
}

.team-about {
  margin-bottom: 15px;
  
  .team-name {
    margin-top: -0.25em;
    margin-bottom: 10px;
  }

  .team-degi {
    color: $theme-color;
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 33px;
  }

  .inner-title {
    font-size: 26px;
    margin-bottom: 8px;
  }

  .title-divider2 {
    margin-bottom: 10px;
  }

  .team-text {
    line-height: 1;
    margin-bottom: 30px;
    font-size: 18px;
  }

  .team-time {
    font-size: 18px;
    margin-bottom: 8px;

    &:last-of-type {
      margin-bottom: 20px;
    }
  }

  .team-info {
    display: flex;
    align-items: center;
    margin: 0 0 15px 0;
    font-size: 18px;

    i {
      width: 42px;
      height: 42px;
      line-height: 42px;
      border-radius: 50%;
      background-color: $theme-color2;
      color: $title-color;
      text-align: center;
      font-size: 18px;
      margin-right: 10px;
    }
  }
}

.team-description {
  margin-top: 33px;
  margin-bottom: 50px;
}


@include ml {
  .team-style1 {
    padding: 20px;
    border-radius: 30px 30px 100px 30px;

    .team-img {
      margin-right: 30px;
    }

    .team-name {
      font-size: 36px;
    }

    .team-img {
      width: 240px;
    }

    .team-degi {
      padding-bottom: 10px;  
      margin-bottom: 10px;
    }

    .team-number {
      font-size: 16px;
    }
  }

  .team-style2 {
    .team-name {
      font-size: 20px;
      padding: 10px 0;
    }
  }
}

@include lg {
  .team-style1 {
    .team-img {
      width: 180px;
      margin-right: 20px;
    }

    .team-name {
      font-size: 26px;
    }

    .team-degi {
      font-size: 16px;
    }

    .vs-social {
      a {
        --icon-size: 40px;
        font-size: 14px;
      }
    }
  }

  .team-style2 {
    .team-name {
      padding: 10px 0;
      font-size: 18px;
      right: 10px;
      bottom: 10px;
      left: 10px;
    }
  }

  .team-about {
    .inner-title {
      font-size: 20px;
    }

    .team-info,
    .team-time,
    .team-text {
      font-size: 16px;
    }

    .team-degi {
      font-size: 16px;
      margin-bottom: 20px;
    }
  }

  .team-description {
    margin-top: 10px;
  }



}

@include md {
  .team-style1 {
    .team-img {
      width: auto;
    }
  }

  .team-style1 {
    &.layout2 {
      display: block;
      border-radius: 30px;
      padding: 20px;
      text-align: center;

      .team-img {
        width: auto;
        margin-right: 0;
        margin-bottom: 25px;

        img {
          width: 100%;
        }
      }

      .team-degi {
        &:before {
          left: 50%;
          margin-left: -35px;
        }
      }
    }
  }
}

@include sm {
  .team-style1 {
    display: block;
    border-radius: 30px;
    padding: 20px;
    text-align: center;
    
    .team-img {
      width: auto;
      margin-right: 0;
      margin-bottom: 25px;

      img {
        width: 100%;
      }
    }

    .team-degi {
      &:before {
        left: 50%;
        margin-left: -35px;
      }
    }

  }
}