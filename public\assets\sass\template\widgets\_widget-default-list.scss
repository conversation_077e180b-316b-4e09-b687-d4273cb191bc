.widget_nav_menu,
.widget_pages,
.widget_archive,
.widget_categories {

  ul {
    list-style: none;
    margin: 0 0 -10px 0;
    padding: 0;
  }

  a {
    position: relative;
    display: block;
    padding: 20px 20px 20px 55px;
    margin: 0 0 10px 0;
    font-size: 16px;
    font-weight: 600;
    line-height: 1;
    letter-spacing: 0.05em;
    color: $title-color;
    background-color: $white-color;
    font-family: $title-font;
    border-radius: 9999px;

    &:before {
      content: '\f178';
      font-family: $icon-font;
      font-size: 12px;
      color: $white-color;
      font-weight: 400;
      position: absolute;
      left: 16px;
      top: 50%;
      transform: translateY(-50%);
      width: 26px;
      height: 26px;
      line-height: 26px;
      text-align: center;
      background-color: $secondary-color;
      border-radius: 50%;
    }

    &:hover {
      background-color: $theme-color;
      color: $white-color;
    }
  }

  li {
    display: block;
    position: relative;

    >span {
      width: 40px;
      height: 40px;
      line-height: 35.5px;
      font-size: 18px;
      font-weight: 500;
      color: $title-color;
      background-color: transparent;
      right: 10px;
      top: 10px;
      z-index: 1;
      display: inline-block;
      text-align: center;
      position: absolute;
      border: 2px solid $theme-color;
      transition: all ease 0.4s;
      border-radius: 0;
    }

    &:hover {
      >span {
        background-color: $theme-color;
        color: $white-color;
      }
    }
  }

  .children {
    margin-left: 10px;
  }
}

.widget_nav_menu {
  .sub-menu {
    margin-left: 10px;
  }
}

.wp-block-archives {
  list-style: none;
  margin: 0;
  padding: 0;
  margin-bottom: 20px;

  a:not(:hover) {
    color: inherit;
  }
}

.vs-blog ul.wp-block-archives li {
  margin: 5px 0;
}



@include lg {

  .widget_nav_menu,
  .widget_pages,
  .widget_archive,
  .widget_categories {
    a {
      // padding: 18px 50px 18px 20px;
      font-size: 14px;
    }

    li {
      >span {
        top: 0;
        right: 0;
        width: 50px;
        height: 50px;
        line-height: 47px;
        font-size: 14px;
      }
    }
  }
}