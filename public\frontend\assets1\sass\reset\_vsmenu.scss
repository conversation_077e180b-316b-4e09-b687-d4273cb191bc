.vs-menu-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.60);
  z-index: 999999;
  width: 100%;
  height: 100%;
  transition: all ease 0.8s;
  opacity: 0;
  visibility: hidden;


  // Mobile Menu Logo
  .mobile-logo {
    padding-bottom: 30px;
    padding-top: 30px;
    display: block;
    text-align: center;
    // background-color: rgba(#E8063C, 0.1);
    background-color: $body-bg;

    svg {
      max-width: 185px;
    }
  }

  // Menu Close Button
  .vs-menu-toggle {
    border: none;
    position: absolute;
    right: 10px;
    top: 10px;
    padding: 0;
    width: 33px;
    height: 33px;
    line-height: 33px;
    font-size: 18px;
    z-index: 1;
    color: $white-color;
    background-color: $theme-color;
    border-radius: 50%;

    &:hover {
      background-color: $secondary-color;
      color: $white-color;
    }
  }

  .vs-menu-area {
    width: 100%;
    max-width: 310px;
    background-color: $body-bg;
    border-right: 3px solid $theme-color;
    height: 100%;
    position: relative;
    left: -110%;
    opacity: 0;
    visibility: hidden;
    transition: all ease 1s;
    z-index: 1;
  }

  &.vs-body-visible {
    opacity: 1;
    visibility: visible;

    .vs-menu-area {
      left: 0;
      opacity: 1;
      visibility: visible;
    }
  }
}


// Mobile Menu
.vs-mobile-menu {
  overflow-y: scroll;
  max-height: calc(100vh - 200px);
  padding-bottom: 40px;
  margin-top: 33px;
  text-align: left;

  &::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(#E8063C, 0.2);
    background-color: #000;
  }

  &::-webkit-scrollbar {
    width: 6px;
    background-color: #000;
  }

  &::-webkit-scrollbar-thumb {
    background-color: $theme-color;
  }

  ul {
    margin: 0;
    padding: 0 0;

    li {
      border-bottom: 1px solid $border-color;
      list-style-type: none;

      li:first-child {
        border-top: 1px solid $border-color;
      }

      a {
        display: block;
        position: relative;
        padding: 12px 0;
        line-height: 1;
        font-size: 16px;
        text-transform: capitalize;
        color: $title-color;
        font-weight: 700;

        &:before {
          content: '\f105';
          font-family: $icon-font;
          position: relative;
          left: 0;
          top: 0;
          margin-right: 10px;
          display: inline-block;
        }
      }

      &.vs-active {
        >a {
          color: $theme-color;

          &:before {
            transform: rotate(90deg);
          }
        }
      }

      ul {
        li {
          padding-left: 20px;

          &:last-child {
            border-bottom: none;
          }
        }
      }
    }

    .vs-item-has-children {
      >a {
        .vs-mean-expand {
          position: absolute;
          right: 0;
          top: 50%;
          font-weight: 400;
          font-size: 12px;
          width: 25px;
          height: 25px;
          line-height: 25px;
          margin-top: -12.5px;
          display: inline-block;
          text-align: center;
          background-color: $secondary-color;
          color: $white-color;
          box-shadow: 0 0 20px -8px rgba(#E8063C, 0.5);
          border-radius: 50%;
          transition: all ease 0.4s;

          &:before {
            content: '\f067';
            font-family: $icon-font;
          }
        }
      }
      
      &.vs-active {
        >a {
          .vs-mean-expand {
            background-color: $theme-color;
            color: $white-color;

            &:before {
              content: '\f068';
            }
          }
        }
      }
    }
  }

  >ul {
    padding: 0 40px;

    >li {
      &:last-child {
        border-bottom: none;
      }
    }
  }
}



.vs-menu-toggle {
  width: 50px;
  height: 50px;
  padding: 0;
  font-size: 24px;
  border: none;
  background-color: $theme-color;
  color: $white-color;
  display: inline-block;
  border-radius: 50%;

  &:hover {
    background-color: $secondary-color;
    color: $white-color;
  }
}


@media (max-width: 400px) {
  .vs-menu-wrapper .vs-menu-area {
    width: 100%;
    max-width: 270px;
  }

  .vs-mobile-menu>ul {
    padding: 0 20px;
  }
}