<template>
    <section class="bg-smoke space-top space-extra-bottom">
        <div class="container">
            <div class="row flex-row-reverse align-items-center gx-60">
                <div class="col-lg-6 text-center text-lg-start mb-40 mb-lg-0">
                    <img src="/photos/9.jpg" alt="childrens" class="w-100 rounded-circle">
                </div>
                <div class="col-lg-6">
                    <div class="title-area text-center">
                        <span class="sec-subtitle">Testimonials</span>
                        <h2 class="sec-title">Parents Reviews</h2>
                    </div>
                    <!-- Carousel -->
                    <div id="carouselExample" class="carousel slide" data-bs-ride="carousel">
                        <!-- Carousel Inner -->
                        <div class="carousel-inner">
                            <div v-for="(testimonial, index) in testimonials" :key="index"
                                :class="['carousel-item', { active: index === currentIndex }]">
                                <div class="testi-style1">
                                    <div class="testi-icon"><i class="fas fa-quote-left"></i></div>
                                    <h3 class="testi-name h2">{{ testimonial.name }}</h3>
                                    <div class="testi-rating">
                                        <i class="fas fa-star" v-for="n in 5" :key="n"></i>
                                    </div>
                                    <p class="testi-text">{{ testimonial.text }}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Bottom Dots (Indicators) -->
                        <div class="carousel-indicators">
                            <button v-for="(testimonial, index) in testimonials" :key="index"
                                :data-bs-target="'#carouselExample'" :data-bs-slide-to="index"
                                :class="{ 'active': index === currentIndex }" type="button"
                                aria-label="'Slide ' + (index + 1)" @click="setIndex(index)"></button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</template>

<script>
export default {
    data() {
        return {
            currentIndex: 0,
            testimonials: [
                {
                    name: 'Sita Sharma',
                    text: 'यो ठाउँले मेरो छोराछोरीलाई धेरै माया र हेरचाह दिएको छ। यहाँको वातावरण निकै सकारात्मक र प्रेरणादायी छ।',
                },
                {
                    name: 'Ram Thapa',
                    text: 'The teachers here focus on the personal development of children. I highly recommend this place to everyone.',
                },
                {
                    name: 'Gita Koirala',
                    text: 'मेरो बच्चाले यहाँ धेरै नयाँ कुरा सिकेको छ। यहाँको शिक्षा प्रणाली उत्कृष्ट छ।',
                },
                {
                    name: 'Krishna Adhikari',
                    text: 'यो ठाउँले बच्चाहरूलाई आत्मविश्वास र सिर्जनशीलता विकास गर्न मद्दत पुर्‍याउँछ। म यो ठाउँबाट धेरै सन्तुष्ट छु।',
                },
            ],
        };
    },
    methods: {
        setIndex(index) {
            this.currentIndex = index;
        },
    },
};
</script>

<style scoped>
/* Style for the carousel indicators (dots) */
.carousel-indicators {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    justify-content: center;
    gap: 5px;
}

.carousel-indicators button {
    background-color: #bbb;
    border: none;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.carousel-indicators button.active {
    background-color: #333;
}

.carousel-indicators button:hover {
    background-color: #555;
}

/* Remove the left and right control buttons */
.carousel-control-prev,
.carousel-control-next {
    display: none;
}
</style>
