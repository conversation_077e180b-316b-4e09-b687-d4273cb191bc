/**
 * Simple Validator - A lightweight validation system for HTML forms
 * 
 * This validator works directly with standard HTML elements
 * without requiring custom components.
 */

// Validation rules
const rules = {
  required: (message = 'This field is required') => ({
    test: value => !!value || value === 0 || value === false,
    message
  }),
  
  email: (message = 'Please enter a valid email address') => ({
    test: value => {
      if (!value) return true
      const pattern = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
      return pattern.test(value)
    },
    message
  }),
  
  minLength: (min, message = `Must be at least ${min} characters`) => ({
    test: value => {
      if (!value) return true
      return String(value).length >= min
    },
    message
  }),
  
  maxLength: (max, message = `Must be less than ${max} characters`) => ({
    test: value => {
      if (!value) return true
      return String(value).length <= max
    },
    message
  }),
  
  min: (min, message = `Must be at least ${min}`) => ({
    test: value => {
      if (!value && value !== 0) return true
      return Number(value) >= min
    },
    message
  }),
  
  max: (max, message = `Must be less than ${max}`) => ({
    test: value => {
      if (!value && value !== 0) return true
      return Number(value) <= max
    },
    message
  }),
  
  pattern: (regex, message = 'Invalid format') => ({
    test: value => {
      if (!value) return true
      return regex.test(value)
    },
    message
  }),
  
  match: (selector, message = 'Fields do not match') => ({
    test: (value, form) => {
      if (!value) return true
      const matchElement = form.querySelector(selector)
      return matchElement ? value === matchElement.value : true
    },
    message
  }),
  
  custom: (testFn, message = 'Invalid value') => ({
    test: testFn,
    message
  })
}

// Create a validator for a form
function createValidator(formSelector, options = {}) {
  // Default options
  const defaultOptions = {
    validateOnBlur: true,
    validateOnInput: false,
    validateOnSubmit: true,
    showSuccessState: true,
    scrollToError: true,
    errorClass: 'is-invalid',
    successClass: 'is-valid',
    errorMessageClass: 'error-message',
    errorMessageTag: 'div',
    errorMessagePosition: 'after', // 'after', 'before', 'append', 'prepend'
    onSuccess: null,
    onError: null
  }
  
  // Merge options
  const config = { ...defaultOptions, ...options }
  
  // Get form element
  const form = typeof formSelector === 'string' 
    ? document.querySelector(formSelector) 
    : formSelector
  
  if (!form) {
    console.error(`Form not found: ${formSelector}`)
    return null
  }
  
  // Store validation rules for each field
  const validationRules = {}
  
  // Store validation state
  const state = {
    isValid: true,
    errors: {},
    touched: {}
  }
  
  // Add validation rule to a field
  function addRule(selector, rule) {
    const element = typeof selector === 'string' 
      ? form.querySelector(selector) 
      : selector
    
    if (!element) {
      console.error(`Element not found: ${selector}`)
      return this
    }
    
    const name = element.name || element.id
    
    if (!name) {
      console.error('Element must have a name or id attribute')
      return this
    }
    
    if (!validationRules[name]) {
      validationRules[name] = []
    }
    
    validationRules[name].push(rule)
    
    // Add event listeners
    if (config.validateOnBlur) {
      element.addEventListener('blur', () => {
        state.touched[name] = true
        validateField(element)
      })
    }
    
    if (config.validateOnInput) {
      element.addEventListener('input', () => {
        if (state.touched[name]) {
          validateField(element)
        }
      })
    }
    
    return this
  }
  
  // Validate a single field
  function validateField(field) {
    const name = field.name || field.id
    
    if (!name || !validationRules[name]) {
      return true
    }
    
    const value = field.type === 'checkbox' ? field.checked : field.value
    
    // Clear previous errors
    clearFieldError(field)
    
    // Check each rule
    for (const rule of validationRules[name]) {
      const isValid = rule.test(value, form)
      
      if (!isValid) {
        // Show error
        showFieldError(field, rule.message)
        state.errors[name] = rule.message
        return false
      }
    }
    
    // Show success state
    if (config.showSuccessState && state.touched[name]) {
      field.classList.add(config.successClass)
    }
    
    // Clear error
    delete state.errors[name]
    
    return true
  }
  
  // Show error message for a field
  function showFieldError(field, message) {
    // Add error class
    field.classList.add(config.errorClass)
    
    // Create error message element
    const errorElement = document.createElement(config.errorMessageTag)
    errorElement.className = config.errorMessageClass
    errorElement.textContent = message
    
    // Add error message to DOM
    const fieldContainer = field.closest('.form-group') || field.parentNode
    
    switch (config.errorMessagePosition) {
      case 'before':
        fieldContainer.insertBefore(errorElement, field)
        break
      case 'append':
        fieldContainer.appendChild(errorElement)
        break
      case 'prepend':
        fieldContainer.insertBefore(errorElement, fieldContainer.firstChild)
        break
      case 'after':
      default:
        // Insert after the field
        if (field.nextSibling) {
          fieldContainer.insertBefore(errorElement, field.nextSibling)
        } else {
          fieldContainer.appendChild(errorElement)
        }
        break
    }
    
    // Add animation
    errorElement.style.animation = 'fadeIn 0.3s ease-in-out'
    field.style.animation = 'shake 0.6s ease-in-out'
  }
  
  // Clear error message for a field
  function clearFieldError(field) {
    // Remove error class
    field.classList.remove(config.errorClass)
    
    // Remove error message
    const fieldContainer = field.closest('.form-group') || field.parentNode
    const errorElements = fieldContainer.querySelectorAll(`.${config.errorMessageClass}`)
    
    errorElements.forEach(element => {
      element.remove()
    })
  }
  
  // Validate all fields
  function validateAll() {
    state.isValid = true
    state.errors = {}
    
    // Get all fields with validation rules
    const fields = []
    
    for (const name in validationRules) {
      const field = form.querySelector(`[name="${name}"], #${name}`)
      
      if (field) {
        fields.push(field)
        state.touched[name] = true
      }
    }
    
    // Validate each field
    for (const field of fields) {
      const isValid = validateField(field)
      state.isValid = state.isValid && isValid
    }
    
    // Scroll to first error
    if (!state.isValid && config.scrollToError) {
      const firstErrorField = form.querySelector(`.${config.errorClass}`)
      
      if (firstErrorField) {
        firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' })
        firstErrorField.focus()
      }
    }
    
    return state.isValid
  }
  
  // Reset validation state
  function reset() {
    state.isValid = true
    state.errors = {}
    state.touched = {}
    
    // Clear all error messages
    for (const name in validationRules) {
      const field = form.querySelector(`[name="${name}"], #${name}`)
      
      if (field) {
        clearFieldError(field)
        field.classList.remove(config.successClass)
      }
    }
  }
  
  // Handle form submission
  if (config.validateOnSubmit) {
    form.addEventListener('submit', (event) => {
      const isValid = validateAll()
      
      if (!isValid) {
        event.preventDefault()
        
        if (typeof config.onError === 'function') {
          config.onError(state.errors)
        }
        
        return false
      }
      
      if (typeof config.onSuccess === 'function') {
        const formData = new FormData(form)
        const data = {}
        
        for (const [key, value] of formData.entries()) {
          data[key] = value
        }
        
        const result = config.onSuccess(data)
        
        if (result === false) {
          event.preventDefault()
        }
      }
    })
  }
  
  // Return public API
  return {
    addRule,
    validate: validateAll,
    reset,
    isValid: () => state.isValid,
    getErrors: () => ({ ...state.errors }),
    
    // Shorthand methods for common rules
    required: (selector, message) => addRule(selector, rules.required(message)),
    email: (selector, message) => addRule(selector, rules.email(message)),
    minLength: (selector, min, message) => addRule(selector, rules.minLength(min, message)),
    maxLength: (selector, max, message) => addRule(selector, rules.maxLength(max, message)),
    min: (selector, min, message) => addRule(selector, rules.min(min, message)),
    max: (selector, max, message) => addRule(selector, rules.max(max, message)),
    pattern: (selector, regex, message) => addRule(selector, rules.pattern(regex, message)),
    match: (selector, matchSelector, message) => addRule(selector, rules.match(matchSelector, message)),
    custom: (selector, testFn, message) => addRule(selector, rules.custom(testFn, message))
  }
}

// Export
export { createValidator, rules }
