<template>
  <th :colspan="colspan" :rowspan="rowspan" class="column-group-header">
    {{ label }}
  </th>
</template>

<script>
export default {
  name: 'ColumnGroup',
  props: {
    label: {
      type: String,
      required: true
    },
    colspan: {
      type: Number,
      default: 1
    },
    rowspan: {
      type: Number,
      default: 1
    }
  }
}
</script>

<style>
.column-group-header {
  text-align: center;
  background-color: var(--vdt-bg-light, #f8fafc);
  font-weight: 600;
  border-bottom: 1px solid var(--vdt-border-color, #e2e8f0);
  padding: 12px 16px;
  position: relative;
}

/* Enhanced bottom border */
.column-group-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--vdt-border-color, #e2e8f0);
}
</style>
