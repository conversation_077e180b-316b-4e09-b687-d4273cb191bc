<template>
  <div class="additional-filters">
    <div class="filter-header" @click="toggleFilters">
      <div class="header-content">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18" height="18" class="filter-icon">
          <path fill="none" d="M0 0h24v24H0z" />
          <path d="M10 18h4v-2h-4v2zM3 6v2h18V6H3zm3 7h12v-2H6v2z" fill="currentColor" />
        </svg>
        <span>Additional Filters</span>
      </div>
      <div class="toggle-wrapper">
        <span v-if="hasActiveFilters" class="active-filter-badge">{{ activeFilterCount }}</span>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18" height="18" class="toggle-icon"
          :class="{ 'expanded': isExpanded }">
          <path fill="none" d="M0 0h24v24H0z" />
          <path d="M12 13.172l4.95-4.95 1.414 1.414L12 16 5.636 9.636 7.05 8.222z" fill="currentColor" />
        </svg>
      </div>
    </div>
    <div v-if="isExpanded" class="filter-body">
      <div class="filter-grid">
        <div v-for="filter in filters" :key="filter.key" class="filter-item"
          :class="{ 'active': isFilterActive(filter.key) }">
          <div class="filter-item-header">
            <label :for="`filter-${filter.key}`">{{ filter.label }}</label>
            <button v-if="isFilterActive(filter.key)" class="clear-filter" @click="clearFilter(filter.key)"
              title="Clear filter">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="14" height="14">
                <path fill="none" d="M0 0h24v24H0z" />
                <path
                  d="M12 10.586l4.95-4.95 1.414 1.414-4.95 4.95 4.95 4.95-1.414 1.414-4.95-4.95-4.95 4.95-1.414-1.414 4.95-4.95-4.95-4.95L7.05 5.636z"
                  fill="currentColor" />
              </svg>
            </button>
          </div>

          <!-- Text filter -->
          <div v-if="filter.type === 'text'" class="input-with-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" class="input-icon">
              <path fill="none" d="M0 0h24v24H0z" />
              <path
                d="M18.031 16.617l4.283 4.282-1.415 1.415-4.282-4.283A8.96 8.96 0 0 1 11 20c-4.968 0-9-4.032-9-9s4.032-9 9-9 9 4.032 9 9a8.96 8.96 0 0 1-1.969 5.617zm-2.006-.742A6.977 6.977 0 0 0 18 11c0-3.868-3.133-7-7-7-3.868 0-7 3.132-7 7 0 3.867 3.132 7 7 7a6.977 6.977 0 0 0 4.875-1.975l.15-.15z"
                fill="currentColor" />
            </svg>
            <input :id="`filter-${filter.key}`" type="text" class="filter-input" v-model="filterValues[filter.key]"
              :placeholder="`Filter by ${filter.label}...`" @input="debounceFilter(filter.key)" />
          </div>

          <!-- Select filter -->
          <select v-else-if="filter.type === 'select'" :id="`filter-${filter.key}`" class="filter-select"
            v-model="filterValues[filter.key]" @change="applyFilter(filter.key)">
            <option value="">All {{ filter.label }}</option>
            <option v-for="option in filter.options" :key="option.value" :value="option.value">
              {{ option.label }}
            </option>
          </select>

          <!-- Date filter -->
          <div v-else-if="filter.type === 'date'" class="input-with-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" class="input-icon">
              <path fill="none" d="M0 0h24v24H0z" />
              <path
                d="M17 3h4a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h4V1h2v2h6V1h2v2zm-2 2H9v2H7V5H4v4h16V5h-3v2h-2V5zm5 6H4v8h16v-8z"
                fill="currentColor" />
            </svg>
            <input :id="`filter-${filter.key}`" type="date" class="filter-date" v-model="filterValues[filter.key]"
              @input="debounceFilter(filter.key)" />
          </div>

          <!-- Date range filter -->
          <div v-else-if="filter.type === 'daterange'" class="date-range-filter">
            <div class="date-range-inputs">
              <input type="date" class="filter-date start-date" v-model="dateRanges[filter.key].start"
                @input="debounceDateRangeFilter(filter.key)" placeholder="From" />
              <span class="date-separator">to</span>
              <input type="date" class="filter-date end-date" v-model="dateRanges[filter.key].end"
                @input="debounceDateRangeFilter(filter.key)" placeholder="To" />
            </div>
          </div>
        </div>
      </div>

      <div class="filter-actions">
        <button class="btn btn-outline-secondary" @click="resetFilters">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" class="btn-icon">
            <path fill="none" d="M0 0h24v24H0z" />
            <path
              d="M5.463 4.433A9.961 9.961 0 0 1 12 2c5.523 0 10 4.477 10 10 0 2.136-.67 4.116-1.81 5.74L17 12h3A8 8 0 0 0 6.46 6.228l-.997-1.795zm13.074 15.134A9.961 9.961 0 0 1 12 22C6.477 22 2 17.523 2 12c0-2.136.67-4.116 1.81-5.74L7 12H4a8 8 0 0 0 13.54 5.772l.997 1.795z"
              fill="currentColor" />
          </svg>
          Reset Filters
        </button>
      </div>
    </div>
  </div>
</template>

<script>
// Debounce function to limit how often a function can be called
function debounce(func, wait) {
  let timeout;
  return function (...args) {
    const context = this;
    clearTimeout(timeout);
    timeout = setTimeout(() => func.apply(context, args), wait);
  };
}

export default {
  name: 'AdditionalFilters',
  props: {
    filters: {
      type: Array,
      required: true,
      // Each filter should have: key, label, type, and options (for select type)
    },
    debounce: {
      type: Number,
      default: 300 // 300ms is a good default for most use cases
    }
  },
  data() {
    return {
      isExpanded: false,
      filterValues: {},
      dateRanges: {},
      debouncedFilter: null,
      debouncedDateRangeFilter: null
    };
  },
  computed: {
    // Check if any filters are active
    hasActiveFilters() {
      return this.activeFilterCount > 0;
    },
    // Count active filters
    activeFilterCount() {
      let count = 0;

      // Check text and select filters
      for (const key in this.filterValues) {
        if (this.filterValues[key]) {
          count++;
        }
      }

      // Check date range filters
      for (const key in this.dateRanges) {
        if (this.dateRanges[key].start || this.dateRanges[key].end) {
          count++;
        }
      }

      return count;
    }
  },
  created() {
    // Initialize filter values
    this.filters.forEach(filter => {
      if (filter.type === 'daterange') {
        this.dateRanges[filter.key] = { start: '', end: '' };
      } else {
        this.filterValues[filter.key] = '';
      }
    });

    // Create debounced functions
    this.debouncedFilter = debounce(this.applyFilter, this.debounce);
    this.debouncedDateRangeFilter = debounce(this.applyDateRangeFilter, this.debounce);
  },
  methods: {
    toggleFilters() {
      this.isExpanded = !this.isExpanded;
    },

    debounceFilter(key) {
      this.debouncedFilter(key);
    },

    debounceDateRangeFilter(key) {
      this.debouncedDateRangeFilter(key);
    },

    // Check if a specific filter is active
    isFilterActive(key) {
      const filter = this.filters.find(f => f.key === key);
      if (!filter) return false;

      if (filter.type === 'daterange') {
        return !!(this.dateRanges[key]?.start || this.dateRanges[key]?.end);
      } else {
        return !!this.filterValues[key];
      }
    },

    // Clear a specific filter
    clearFilter(key) {
      const filter = this.filters.find(f => f.key === key);
      if (!filter) return;

      if (filter.type === 'daterange') {
        this.dateRanges[key] = { start: '', end: '' };
        this.applyDateRangeFilter(key);
      } else {
        this.filterValues[key] = '';
        this.applyFilter(key);
      }
    },

    applyFilter(key) {
      const value = this.filterValues[key];
      if (!value) {
        this.$emit('filter-remove', key);
      } else {
        this.$emit('filter-change', { key, value });
      }
    },

    applyDateRangeFilter(key) {
      const range = this.dateRanges[key];
      if (!range.start && !range.end) {
        this.$emit('filter-remove', key);
      } else {
        this.$emit('filter-change', { key, value: range });
      }
    },

    resetFilters() {
      // Reset all filter values
      this.filters.forEach(filter => {
        if (filter.type === 'daterange') {
          this.dateRanges[filter.key] = { start: '', end: '' };
        } else {
          this.filterValues[filter.key] = '';
        }
      });

      // Emit reset event
      this.$emit('filters-reset');
    }
  }
};
</script>

<style>
.additional-filters {
  margin-bottom: 16px;
  border: 1px solid var(--vdt-border-color, #e2e8f0);
  border-radius: var(--vdt-radius, 8px);
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  background-color: var(--vdt-bg-light, #f8fafc);
  cursor: pointer;
  font-weight: 600;
  font-size: 14px;
  color: var(--vdt-text-color, #1e293b);
  transition: background-color 0.2s, border-color 0.2s;
  border-bottom: 1px solid transparent;
}

.filter-header:hover {
  background-color: var(--vdt-bg-hover, #f1f5f9);
}

.header-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-icon {
  color: var(--vdt-primary-color, #3b82f6);
}

.toggle-wrapper {
  display: flex;
  align-items: center;
  position: relative;
}

.toggle-icon {
  color: var(--vdt-text-light, #64748b);
  transition: transform 0.2s ease;
}

.toggle-icon.expanded {
  transform: rotate(180deg);
}

.active-filter-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: var(--vdt-primary-color, #3b82f6);
  color: white;
  font-size: 11px;
  font-weight: bold;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.filter-body {
  padding: 12px;
  background-color: var(--vdt-bg-white, #fff);
  border-top: 1px solid var(--vdt-border-color, #e2e8f0);
}

.filter-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
  margin-bottom: 16px;
}

.filter-item {
  background-color: var(--vdt-bg-light, #f8fafc);
  border: 1px solid var(--vdt-border-color, #e2e8f0);
  border-radius: 6px;
  padding: 10px;
  transition: all 0.2s ease;
}

.filter-item.active {
  border-color: var(--vdt-primary-color, #3b82f6);
  background-color: rgba(59, 130, 246, 0.05);
}

.filter-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.filter-item label {
  display: block;
  font-size: 13px;
  font-weight: 500;
  color: var(--vdt-text-color, #1e293b);
}

.clear-filter {
  background: none;
  border: none;
  padding: 2px;
  cursor: pointer;
  color: var(--vdt-text-light, #64748b);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.clear-filter:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--vdt-text-color, #1e293b);
}

.input-with-icon {
  position: relative;
}

.input-icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--vdt-text-light, #64748b);
  pointer-events: none;
}

.filter-input,
.filter-select,
.filter-date {
  width: 100%;
  padding: 6px 10px;
  padding-left: 28px;
  /* Space for icon */
  border: 1px solid var(--vdt-border-color, #e2e8f0);
  border-radius: 4px;
  font-size: 13px;
  background-color: var(--vdt-bg-white, #fff);
  color: var(--vdt-text-color, #1e293b);
  transition: all 0.2s ease;
}

.filter-input:focus,
.filter-select:focus,
.filter-date:focus {
  outline: none;
  border-color: var(--vdt-primary-color, #3b82f6);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.filter-select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='16' height='16'%3E%3Cpath fill='none' d='M0 0h24v24H0z'/%3E%3Cpath d='M12 15l-4.243-4.243 1.415-1.414L12 12.172l2.828-2.829 1.415 1.414z' fill='%2364748b'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 8px center;
  padding-right: 28px;
}

.date-range-filter {
  display: flex;
  align-items: center;
}

.date-range-inputs {
  display: flex;
  align-items: center;
  gap: 4px;
  width: 100%;
  position: relative;
}

.date-range-inputs::before {
  content: '';
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  /* background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='16' height='16'%3E%3Cpath fill='none' d='M0 0h24v24H0z'/%3E%3Cpath d='M17 3h4a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h4V1h2v2h6V1h2v2zm-2 2H9v2H7V5H4v4h16V5h-3v2h-2V5zm5 6H4v8h16v-8z' fill='%2364748b'/%3E%3C/svg%3E"); */
  background-repeat: no-repeat;
  background-position: center;
  pointer-events: none;
}

.date-range-filter .filter-date {
  width: 100%;
  min-width: 0;
  padding-left: 30px;
}

.date-range-filter .end-date {
  padding-left: 10px;
}

.date-separator {
  color: var(--vdt-text-light, #64748b);
  font-size: 12px;
  flex-shrink: 0;
  margin: 0 2px;
}

.filter-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid var(--vdt-border-color, #e2e8f0);
}

.btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-icon {
  opacity: 0.7;
}

.btn-outline-secondary {
  background-color: transparent;
  border: 1px solid var(--vdt-border-color, #e2e8f0);
  color: var(--vdt-text-light, #64748b);
}

.btn-outline-secondary:hover {
  background-color: var(--vdt-bg-light, #f8fafc);
  color: var(--vdt-text-color, #1e293b);
  border-color: var(--vdt-border-color, #cbd5e1);
}

.btn-outline-secondary:hover .btn-icon {
  opacity: 1;
}
</style>
