<script setup>
import {
  toRef,
  watch,
} from 'vue'

const props = defineProps({
    socialMediaDetails: {
        type: Object,
        required: true
    }
});

const socialMediaDetailsRef = toRef(props, 'socialMediaDetails');
watch(socialMediaDetailsRef, (newVal) => {
    console.log(newVal);
});
</script>
<template>
    <div class="sidemenu-wrapper d-none d-lg-block  ">
        <div class="sidemenu-content">
            <button class="closeButton sideMenuCls"><i class="far fa-times"></i></button>
            <div class="widget  ">
                <div class="widget-about">
                    <!-- <div class="footer-logo"><img src="/frontend/assets1/img/logo.svg" alt="Kiddino"></div> -->
                    <img width="150" src="/img/original.png" alt="logo">

                    <p class="mb-0">We are constantly expanding the range of services offered, taking care of
                        children of all ages.</p>
                </div>
            </div>
            <div class="widget  ">
                <h3 class="widget_title">Get In Touch</h3>
                <div>
                    <p class="footer-text">Monday to Friday: <span class="time">8.30am – 02.00pm</span></p>
                    <p class="footer-text">Saturday, Sunday: <span class="time">Close</span></p>
                    <p class="footer-info"><i class="fal fa-envelope"></i>Email: <a href="mailto:<EMAIL>">{{
                        props.socialMediaDetails?.email }}</a></p>
                    <p class="footer-info"><i class="fas fa-mobile-alt"></i>Phone: <a href="#">+977
                            {{ props.socialMediaDetails?.contact_number }}</a></p>
                </div>
            </div>
            <div class="widget  ">
                <h3 class="widget_title">Latest News</h3>
                <div class="recent-post-wrap">
                    <div class="recent-post">
                        <div class="media-img">
                            <a href="blog-details.html"><img src="/frontend/assets1/img/blog/recent-post-1-1.jpg"
                                    alt="Blog Image"></a>
                        </div>
                        <div class="media-body">
                            <div class="recent-post-meta">
                                <a href="blog.html"><i class="far fa-calendar-alt"></i>December 3, 2022</a>
                            </div>
                            <h4 class="post-title"><a class="text-inherit" href="blog-details.html">A very warm
                                    welcome to our new Treasurer</a></h4>
                        </div>
                    </div>
                    <div class="recent-post">
                        <div class="media-img">
                            <a href="blog-details.html"><img src="/frontend/assets1/img/blog/recent-post-1-2.jpg"
                                    alt="Blog Image"></a>
                        </div>
                        <div class="media-body">
                            <div class="recent-post-meta">
                                <a href="blog.html"><i class="far fa-calendar-alt"></i>February 15, 2022</a>
                            </div>
                            <h4 class="post-title"><a class="text-inherit" href="blog-details.html">German kinder
                                    and garten mean child</a></h4>
                        </div>
                    </div>
                    <div class="recent-post">
                        <div class="media-img">
                            <a href="blog-details.html"><img src="/frontend/assets1/img/blog/recent-post-1-3.jpg"
                                    alt="Blog Image"></a>
                        </div>
                        <div class="media-body">
                            <div class="recent-post-meta">
                                <a href="blog.html"><i class="far fa-calendar-alt"></i>Augest 20, 2022</a>
                            </div>
                            <h4 class="post-title"><a class="text-inherit" href="blog-details.html">English uses
                                    term to refer to the earliest</a></h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>