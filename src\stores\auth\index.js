import 'vue3-toastify/dist/index.css'

import { defineStore } from 'pinia'
import { toast } from 'vue3-toastify'

import api from '@/api/api'

const useAuthStore = defineStore("userStore", {
    state: () => ({
        user: JSON.parse(localStorage.getItem('user')) || null,
        // currentUser: null,
    }),
    getters: {
        isAuthenticated: state => !!state.user
    },
    actions: {
        async login(userData) {
            try {
                const response = await api.post('/login', userData);

                if (response.data.data) {
                    const payload = {
                        token: response.data.data.token,
                        user: response.data.data.user
                    }
                    this.user = payload;

                    localStorage.setItem('user', JSON.stringify(payload));

                    toast.success(response.data.data.message);
                    return true;
                }
                return false;
            } catch (error) {
                console.error(error.response);
                toast.error(error.response?.data?.message || 'Login failed');
                return false;
            }
        },

        async logout() {
            this.user = null;
            localStorage.removeItem('user');
            toast.success('Logged out successfully');
        }
    },
});

export default useAuthStore;