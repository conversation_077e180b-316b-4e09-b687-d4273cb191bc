/*
Template Name: <PERSON><PERSON><PERSON>
Template URL: https://html.vecurosoft.com/kiddino
Description: Kiddino - Children School & Kindergarten HTML Template
Author: Vecuro
Author URI: https://themeforest.net/user/vecuro_themes
Version: 1.0.0
*/
/*=================================
    CSS Index Here
==================================*/
/*
01. Theme Base
    1.1. Mixin
    1.2. Function
    1.3. Variable
    1.4. Typography
    1.5. Extend
    1.7. Wordpress Default
02. Reset
    2.1. Container
    2.2. Grid
    2.3. Input
    2.4. Slick Slider
    2.5. Mobile Menu
03. Utilities
    3.1. Preloader
    3.2. Buttons
    3.3. Titles
    3.4. Common
    3.6. Font
    3.7. Background
    3.8. Text Color
    3.9. Overlay
    3.10. Animation
04. Template Style
    4.1. Widget
    4.2. Header
    4.3. Footer
    4.4. Breadcumb
    4.5. Pagination
    4.6. Blog
    4.7. Comments
    4.8. Hero Area
    4.9. Error    
    4.10. About
    4.11. Simple Sections
    4.12. Popup Side Menu
    4.13. Popup Side Menu
    4.14. Serive Area
    4.15. Category
    4.16. Forms
    4.17. Classes
    4.18. Accordion
    4.19. Testimonial
    4.20. Package
    4.21. Call To Active
    4.22. Schedule Area
    4.23. Gallery
    4.24. Event Area
    4.25. Feature
    4.26. Team
    4.27. Wocommerce
    4.28. Products
    4.29. Cart
    4.30. Checkout
    4.31. Wishlist
05. Spacing

*/
/*=================================
    CSS Index End
==================================*/
/*=================================
   01. Theme Base
==================================*/
/*------------------- 1.1. Mixin -------------------*/
/*------------------- 1.2. Function -------------------*/
/*------------------- 1.3. Variable-------------------*/
:root {
  --body-bg: #ffffff;
  --theme-color: #E8063C;
  --theme-color2: #FFD600;
  --title-color: #000000;
  --body-color: #444444;
  --vs-secondary-color: #490D59;
  --smoke-color: #F0F6FA;
  --light-color: #f8f9fa;
  --black-color: #000000;
  --white-color: #ffffff;
  --yellow-color: #fec624;
  --success-color: #28a745;
  --error-color: #dc3545;
  --border-color: #E0E0E0;
  --title-font: "Fredoka", sans-serif;
  --body-font: "Jost", sans-serif;
  --icon-font: "Font Awesome 5 Pro";
  --main-container: 1290px;
  --container-gutters: 30px;
  --section-space: 120px;
  --section-space-mobile: 80px;
  --section-title-space: 60px;
  --ripple-ani-duration: 5s;
}

/*------------------- 1.5. Typography -------------------*/
html,
body {
  scroll-behavior: auto !important;
}

body {
  font-family: var(--body-font);
  font-size: 16px;
  font-weight: 400;
  color: var(--body-color);
  background-color: var(--body-bg);
  line-height: 28px;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
}

iframe {
  border: none;
  width: 100%;
}

.slick-slide:focus,
button:focus,
a:focus,
a:active,
input,
input:hover,
input:focus,
input:active,
textarea,
textarea:hover,
textarea:focus,
textarea:active {
  outline: none;
}

input:focus {
  outline: none;
  box-shadow: none;
}

img:not([draggable]),
embed,
object,
video {
  max-width: 100%;
  height: auto;
}

ul {
  list-style-type: disc;
}

ol {
  list-style-type: decimal;
}

table {
  margin: 0 0 1.5em;
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  border: 1px solid var(--border-color);
}

th {
  font-weight: 700;
  color: var(--title-color);
}

td,
th {
  border: 1px solid var(--border-color);
  padding: 9px 12px;
}

a {
  color: var(--theme-color);
  text-decoration: none;
  outline: 0;
  transition: all ease 0.4s;
}
a:hover {
  color: var(--title-color);
}
a:active, a:focus, a:hover, a:visited {
  text-decoration: none;
  outline: 0;
}

button {
  transition: all ease 0.4s;
}

img {
  border: none;
  max-width: 100%;
}

ins {
  text-decoration: none;
}

pre {
  background: #f5f5f5;
  color: #666;
  font-size: 14px;
  margin: 20px 0;
  overflow: auto;
  padding: 20px;
  white-space: pre-wrap;
  word-wrap: break-word;
}

span.ajax-loader:empty,
p:empty {
  display: none;
}

p {
  margin: 0 0 18px 0;
  color: var(--body-color);
  line-height: 1.75;
}

h1 a,
h2 a,
h3 a,
h4 a,
h5 a,
h6 a,
p a,
span a {
  font-size: inherit;
  font-family: inherit;
  font-weight: inherit;
  line-height: inherit;
}

.h1,
h1,
.h2,
h2,
.h3,
h3,
.h4,
h4,
.h5,
h5,
.h6,
h6 {
  font-family: var(--title-font);
  color: var(--title-color);
  text-transform: none;
  font-weight: 600;
  line-height: 1.2;
  margin: 0 0 15px 0;
}

.h1,
h1 {
  font-size: 46px;
}

.h2,
h2 {
  font-size: 40px;
}

.h3,
h3 {
  font-size: 36px;
}

.h4,
h4 {
  font-size: 30px;
}

.h5,
h5 {
  font-size: 26px;
}

.h6,
h6 {
  font-size: 22px;
}

/* Large devices */
@media (max-width: 1199px) {
  .h1,
  h1 {
    font-size: 40px;
  }
  .h2,
  h2 {
    font-size: 36px;
  }
  .h3,
  h3 {
    font-size: 30px;
  }
  .h4,
  h4 {
    font-size: 24px;
  }
  .h5,
  h5 {
    font-size: 20px;
  }
  .h6,
  h6 {
    font-size: 16px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .h1,
  h1 {
    font-size: 36px;
  }
  .h2,
  h2 {
    font-size: 30px;
  }
  .h3,
  h3 {
    font-size: 26px;
  }
  .h4,
  h4 {
    font-size: 22px;
  }
  .h5,
  h5 {
    font-size: 18px;
  }
  .h6,
  h6 {
    font-size: 16px;
  }
}
/*------------------- 1.6. Extend -------------------*/
/*------------------- 1.7. Wordpress Default -------------------*/
/*=================================
    02. Reset
==================================*/
/*------------------- 2.1. Container -------------------*/
@media only screen and (min-width: 1300px) {
  .container-xxl,
  .container-xl,
  .container-lg,
  .container-md,
  .container-sm,
  .container {
    max-width: calc(var(--main-container) + var(--container-gutters));
    padding-left: calc(var(--container-gutters) / 2);
    padding-right: calc(var(--container-gutters) / 2);
  }
}
@media only screen and (max-width: 1600px) {
  .container-fluid.px-0 {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }
  .container-fluid.px-0 .row {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
}
/*------------------- 2.2. Grid -------------------*/
@media (min-width: 767px) {
  .row:not([class*=gx-]) {
    --bs-gutter-x: 30px;
  }
}
.gy-gx {
  --bs-gutter-y: var(--bs-gutter-x);
}

.gy-30 {
  --bs-gutter-y: 30px;
}

.gx-10 {
  --bs-gutter-x: 10px;
}

.gx-15 {
  --bs-gutter-x: 15px;
}

@media (min-width: 1500px) {
  .gx-35 {
    --bs-gutter-x: 35px;
  }
  .gx-40 {
    --bs-gutter-x: 40px;
  }
  .gx-50 {
    --bs-gutter-x: 50px;
  }
  .gx-60 {
    --bs-gutter-x: 60px;
  }
  .gx-70 {
    --bs-gutter-x: 70px;
  }
  .gx-80 {
    --bs-gutter-x: 80px;
  }
  .gx-100 {
    --bs-gutter-x: 100px;
  }
}
/*------------------- 2.3. Input -------------------*/
select,
.form-control,
.form-select,
textarea,
input {
  height: 55px;
  padding: 0 45px 0 30px;
  border: 1px solid var(--border-color);
  color: var(--body-color);
  background-color: var(--body-bg);
  border-radius: 16px;
  font-size: 16px;
  width: 100%;
}
select:focus,
.form-control:focus,
.form-select:focus,
textarea:focus,
input:focus {
  outline: 0;
  box-shadow: none;
  background-color: var(--body-bg);
  border-color: var(--theme-color);
}
select::-moz-placeholder,
.form-control::-moz-placeholder,
.form-select::-moz-placeholder,
textarea::-moz-placeholder,
input::-moz-placeholder {
  color: var(--body-color);
}
select::-moz-placeholder, .form-control::-moz-placeholder, .form-select::-moz-placeholder, textarea::-moz-placeholder, input::-moz-placeholder {
  color: var(--body-color);
}
select::placeholder,
.form-control::placeholder,
.form-select::placeholder,
textarea::placeholder,
input::placeholder {
  color: var(--body-color);
}

.form-select,
select {
  display: block;
  width: 100%;
  line-height: 1.5;
  vertical-align: middle;
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3E%3C/svg%3E");
  background-position: right 26px center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  cursor: pointer;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type=number] {
  -moz-appearance: textfield;
}

textarea.form-control,
textarea {
  min-height: 150px;
  padding-top: 16px;
  padding-bottom: 17px;
  border-radius: 16px;
}

input[type=checkbox] {
  visibility: hidden;
  opacity: 0;
  vertical-align: middle;
  width: 0;
  height: 0;
  display: none;
}
input[type=checkbox]:checked ~ label:before {
  content: "\f00c";
  color: var(--white-color);
  background-color: var(--theme-color);
  border-color: var(--theme-color);
}
input[type=checkbox] ~ label {
  position: relative;
  padding-left: 30px;
  cursor: pointer;
  display: block;
}
input[type=checkbox] ~ label:before {
  content: "";
  font-family: var(--icon-font);
  font-weight: 700;
  position: absolute;
  left: 0px;
  top: 5.5px;
  background-color: var(--body-bg);
  border: 1px solid var(--border-color);
  height: 18px;
  width: 18px;
  line-height: 18px;
  text-align: center;
  font-size: 12px;
  border-radius: 3px;
}

input[type=radio] {
  visibility: hidden;
  opacity: 0;
  vertical-align: middle;
  width: 0;
  height: 0;
  display: none;
}
input[type=radio] ~ label {
  position: relative;
  padding-left: 30px;
  cursor: pointer;
  line-height: 1;
  display: inline-block;
  font-weight: 600;
  margin-bottom: 0;
}
input[type=radio] ~ label:before {
  content: "";
  position: absolute;
  left: 0;
  top: -1px;
  width: 17px;
  height: 17px;
  border-radius: 3px;
  border: 1px solid var(--border-color);
  outline: 5px solid transparent;
  outline-offset: -5px;
  transition: all ease 0.3s;
}
input[type=radio]:checked ~ label::before {
  outline-color: var(--theme-color);
  border-color: transparent;
}

label {
  margin-bottom: 0.5em;
  margin-top: -0.3em;
  display: block;
  color: var(--title-color);
  font-family: var(--title-font);
  font-weight: 500;
  font-size: 16px;
}

.form-group {
  margin-bottom: var(--bs-gutter-x);
  position: relative;
}
.form-group > i {
  position: absolute;
  right: calc(var(--bs-gutter-x) / 2 + 30px);
  top: 19px;
  font-size: 16px;
  color: var(--title-color);
}
.form-group.has-label > i {
  top: 49px;
}

textarea.is-invalid,
select.is-invalid,
input.is-invalid,
.was-validated input:invalid {
  border: 1px solid var(--error-color) !important;
  background-position: right calc(0.375em + 0.8875rem) center;
  background-image: none;
}
textarea.is-invalid:focus,
select.is-invalid:focus,
input.is-invalid:focus,
.was-validated input:invalid:focus {
  outline: 0;
  box-shadow: none;
}

textarea.is-invalid {
  background-position: top calc(0.375em + 0.5875rem) right calc(0.375em + 0.8875rem);
}

.row.no-gutters > .form-group {
  margin-bottom: 0;
}

.form-messages {
  display: none;
  margin: 0;
  padding: 10px 15px;
  margin-top: 15px;
  border-radius: 4px;
}
.form-messages > pre:last-child {
  margin-bottom: 0;
}
.form-messages > pre:first-of-type {
  margin-top: 0;
}
.form-messages.success {
  background-color: var(--success-color);
  color: var(--white-color);
  display: block;
}
.form-messages.error {
  background-color: var(--error-color);
  color: var(--white-color);
  display: block;
}
.form-messages pre {
  padding: 0;
  background-color: transparent;
  color: inherit;
}

/*------------------- 2.4. Slick Slider -------------------*/
.row > .slick-list {
  padding-left: 0;
  padding-right: 0;
}

.slick-track > [class*=col] {
  flex-shrink: 0;
  width: 100%;
  max-width: 100%;
  padding-right: calc(var(--bs-gutter-x) / 2);
  padding-left: calc(var(--bs-gutter-x) / 2);
  margin-top: var(--bs-gutter-y);
}

.slick-track {
  min-width: 100%;
}

.slick-slide img {
  display: inline-block;
}

.slick-dots {
  list-style-type: none;
  padding: 0;
  margin: 10px 0 30px 0;
  text-align: center;
  height: -moz-max-content;
  height: max-content;
  line-height: 0;
}
.slick-dots li {
  display: inline-block;
  margin-right: 7px;
}
.slick-dots li:last-child {
  margin-right: 0;
}
.slick-dots button {
  position: relative;
  font-size: 0;
  padding: 0;
  width: 14px;
  height: 14px;
  line-height: 0;
  border: none;
  background-color: #B5B5B5;
  border-radius: 50%;
  transition: all ease 0.4s;
}
.slick-dots button:hover,
.slick-dots .slick-active button {
  background-color: var(--theme-color);
  border-color: var(--theme-color);
}

.slick-arrow {
  display: inline-block;
  border: none;
  padding: 0;
  position: absolute;
  top: 50%;
  z-index: 2;
  left: var(--pos-x, -150px);
  width: var(--icon-size, 60px);
  height: var(--icon-size, 60px);
  font-size: var(--icon-font-size, 24px);
  margin-top: calc(var(--icon-size, 60px) / -2);
  background-color: var(--theme-color);
  color: var(--white-color);
  border-radius: 50%;
  transition: all ease 0.6s;
}
.slick-arrow:before {
  content: "";
  position: absolute;
  left: 3px;
  top: 3px;
  bottom: 3px;
  right: 3px;
  background-color: var(--theme-color2);
  border-radius: inherit;
  z-index: -1;
  transform: scale(0.5);
  opacity: 0;
  visibility: hidden;
  transition: all ease 0.4s;
}
.slick-arrow.default {
  position: relative;
  --pos-x: 0;
  margin-top: 0;
}
.slick-arrow.slick-next {
  right: var(--pos-x, -150px);
  left: auto;
}
.slick-arrow:hover {
  background-color: var(--vs-secondary-color);
  color: var(--title-color);
}
.slick-arrow:hover::before {
  opacity: 1;
  visibility: visible;
  transform: scale(1);
}

.arrow-margin .slick-arrow {
  top: calc(50% - 30px);
}

.arrow-wrap .slick-arrow {
  opacity: 0;
  visibility: hidden;
}
.arrow-wrap:hover .slick-arrow {
  opacity: 1;
  visibility: visible;
}

/* Extra large devices */
@media (max-width: 1500px) {
  .slick-arrow {
    --arrow-horizontal: -20px;
  }
}
/* Medium Large devices */
@media (max-width: 1399px) {
  .slick-arrow {
    --arrow-horizontal: 40px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .slick-arrow {
    --icon-size: 40px;
    margin-right: 40px;
  }
  .slick-arrow.slick-next {
    margin-right: 0;
    margin-left: 40px;
  }
  .slick-dots {
    margin: 8px 0 38px 0;
  }
}
/*------------------- 2.5. Mobile Menu -------------------*/
.vs-menu-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 999999;
  width: 100%;
  height: 100%;
  transition: all ease 0.8s;
  opacity: 0;
  visibility: hidden;
}
.vs-menu-wrapper .mobile-logo {
  padding-bottom: 30px;
  padding-top: 30px;
  display: block;
  text-align: center;
  background-color: var(--body-bg);
}
.vs-menu-wrapper .mobile-logo svg {
  max-width: 185px;
}
.vs-menu-wrapper .vs-menu-toggle {
  border: none;
  position: absolute;
  right: 10px;
  top: 10px;
  padding: 0;
  width: 33px;
  height: 33px;
  line-height: 33px;
  font-size: 18px;
  z-index: 1;
  color: var(--white-color);
  background-color: var(--theme-color);
  border-radius: 50%;
}
.vs-menu-wrapper .vs-menu-toggle:hover {
  background-color: var(--vs-secondary-color);
  color: var(--white-color);
}
.vs-menu-wrapper .vs-menu-area {
  width: 100%;
  max-width: 310px;
  background-color: var(--body-bg);
  border-right: 3px solid var(--theme-color);
  height: 100%;
  position: relative;
  left: -110%;
  opacity: 0;
  visibility: hidden;
  transition: all ease 1s;
  z-index: 1;
}
.vs-menu-wrapper.vs-body-visible {
  opacity: 1;
  visibility: visible;
}
.vs-menu-wrapper.vs-body-visible .vs-menu-area {
  left: 0;
  opacity: 1;
  visibility: visible;
}

.vs-mobile-menu {
  overflow-y: scroll;
  max-height: calc(100vh - 200px);
  padding-bottom: 40px;
  margin-top: 33px;
  text-align: left;
}
.vs-mobile-menu::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(232, 6, 60, 0.2);
  background-color: #000;
}
.vs-mobile-menu::-webkit-scrollbar {
  width: 6px;
  background-color: #000;
}
.vs-mobile-menu::-webkit-scrollbar-thumb {
  background-color: var(--theme-color);
}
.vs-mobile-menu ul {
  margin: 0;
  padding: 0 0;
}
.vs-mobile-menu ul li {
  border-bottom: 1px solid var(--border-color);
  list-style-type: none;
}
.vs-mobile-menu ul li li:first-child {
  border-top: 1px solid var(--border-color);
}
.vs-mobile-menu ul li a {
  display: block;
  position: relative;
  padding: 12px 0;
  line-height: 1;
  font-size: 16px;
  text-transform: capitalize;
  color: var(--title-color);
  font-weight: 700;
}
.vs-mobile-menu ul li a:before {
  content: "\f105";
  font-family: var(--icon-font);
  position: relative;
  left: 0;
  top: 0;
  margin-right: 10px;
  display: inline-block;
}
.vs-mobile-menu ul li.vs-active > a {
  color: var(--theme-color);
}
.vs-mobile-menu ul li.vs-active > a:before {
  transform: rotate(90deg);
}
.vs-mobile-menu ul li ul li {
  padding-left: 20px;
}
.vs-mobile-menu ul li ul li:last-child {
  border-bottom: none;
}
.vs-mobile-menu ul .vs-item-has-children > a .vs-mean-expand {
  position: absolute;
  right: 0;
  top: 50%;
  font-weight: 400;
  font-size: 12px;
  width: 25px;
  height: 25px;
  line-height: 25px;
  margin-top: -12.5px;
  display: inline-block;
  text-align: center;
  background-color: var(--vs-secondary-color);
  color: var(--white-color);
  box-shadow: 0 0 20px -8px rgba(232, 6, 60, 0.5);
  border-radius: 50%;
  transition: all ease 0.4s;
}
.vs-mobile-menu ul .vs-item-has-children > a .vs-mean-expand:before {
  content: "\f067";
  font-family: var(--icon-font);
}
.vs-mobile-menu ul .vs-item-has-children.vs-active > a .vs-mean-expand {
  background-color: var(--theme-color);
  color: var(--white-color);
}
.vs-mobile-menu ul .vs-item-has-children.vs-active > a .vs-mean-expand:before {
  content: "\f068";
}
.vs-mobile-menu > ul {
  padding: 0 40px;
}
.vs-mobile-menu > ul > li:last-child {
  border-bottom: none;
}

.vs-menu-toggle {
  width: 50px;
  height: 50px;
  padding: 0;
  font-size: 24px;
  border: none;
  background-color: var(--theme-color);
  color: var(--white-color);
  display: inline-block;
  border-radius: 50%;
}
.vs-menu-toggle:hover {
  background-color: var(--vs-secondary-color);
  color: var(--white-color);
}

@media (max-width: 400px) {
  .vs-menu-wrapper .vs-menu-area {
    width: 100%;
    max-width: 270px;
  }
  .vs-mobile-menu > ul {
    padding: 0 20px;
  }
}
/*=================================
    03. Utilities
==================================*/
/*------------------- 3.1. Preloader -------------------*/
.preloader {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 999;
  background-color: var(--body-bg);
}
.preloader .vs-btn {
  padding: 15px 20px;
  border-radius: 0;
  font-size: 14px;
}
.preloader .vs-btn:after, .preloader .vs-btn:before {
  border-radius: 0;
}

.preloader-inner {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  line-height: 0;
}
.preloader-inner img {
  display: block;
  margin: 0 auto 10px auto;
}

.loader {
  width: 110px;
  height: 110px;
  border-radius: 50%;
  display: inline-block;
  position: relative;
  border: 3px solid;
  border-color: var(--theme-color) var(--theme-color) transparent transparent;
  box-sizing: border-box;
  animation: rotationloader 1.5s linear infinite;
}
.loader::after, .loader::before {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
  border: 3px solid;
  border-color: transparent transparent var(--vs-secondary-color) var(--vs-secondary-color);
  width: 102px;
  height: 102px;
  border-radius: 50%;
  box-sizing: border-box;
  animation: rotationBackloader 0.7s linear infinite;
  transform-origin: center center;
}
.loader::before {
  width: 94px;
  height: 94px;
  border-color: var(--theme-color) var(--theme-color) transparent transparent;
  animation: rotationloader 2s linear infinite;
}

@keyframes rotationloader {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes rotationBackloader {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(-360deg);
  }
}
/*------------------- 3.2. Buttons -------------------*/
.vs-btn {
  display: inline-block;
  vertical-align: middle;
  text-align: center;
  border: none;
  outline: none;
  box-shadow: none;
  overflow: hidden;
  position: relative;
  font-family: var(--title-font);
  font-size: 16px;
  font-weight: 600;
  padding: 19px 40px 20px 40px;
  background-color: var(--theme-color);
  color: var(--white-color);
  text-transform: capitalize;
  border-radius: 9999px;
  line-height: 1;
  transition: all ease 0.4s;
  z-index: 2;
  /* Small devices */
}
@media (max-width: 767px) {
  .vs-btn {
    padding: 16px 30px;
  }
}
.vs-btn:after, .vs-btn:before {
  content: "";
  position: absolute;
  top: var(--border-size, 3px);
  bottom: var(--border-size, 3px);
  left: var(--border-size, 3px);
  width: calc(50% - var(--border-size, 3px));
  background-color: var(--vs-secondary-color);
  z-index: -1;
  transform: scaleY(0.4);
  transform-origin: 100% 0;
  opacity: 0;
  visibility: hidden;
  transition: all ease 0.4s;
  border-radius: 9999px 0 0 9999px;
}
.vs-btn:after {
  left: auto;
  right: var(--border-size, 3px);
  transform-origin: 100% 100%;
  border-radius: 0 9999px 9999px 0;
}
.vs-btn.style2 {
  background-color: var(--theme-color2);
  color: var(--title-color);
}
.vs-btn.style2:hover {
  background-color: var(--white-color);
  color: var(--white-color);
}
.vs-btn.style3 {
  background-color: var(--white-color);
  border: 2px solid var(--theme-color);
  color: var(--theme-color);
  --border-size: 1px;
}
.vs-btn:hover {
  color: var(--white-color);
  background-color: var(--theme-color);
}
.vs-btn:hover:after, .vs-btn:hover:before {
  transform: scaleY(1);
  opacity: 1;
  visibility: visible;
}

.icon-btn {
  padding: 0;
  font-size: var(--icon-font-size, 20px);
  width: var(--icon-size, 56px);
  height: var(--icon-size, 56px);
  line-height: 1;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  color: var(--white-color);
  background-color: var(--theme-color);
  border-radius: 50%;
  transition: all ease 0.4s;
  position: relative;
  z-index: 1;
}
.icon-btn:before {
  content: "";
  position: absolute;
  left: var(--border-size, 2px);
  top: var(--border-size, 2px);
  right: var(--border-size, 2px);
  bottom: var(--border-size, 2px);
  background-color: var(--white-color);
  border-radius: inherit;
  z-index: -1;
  transform: scale(0.5);
  transition: all ease 0.4s;
  opacity: 0;
  visibility: hidden;
}
.icon-btn:hover {
  border-color: var(--theme-color);
  color: var(--theme-color);
}
.icon-btn:hover:before {
  opacity: 1;
  visibility: visible;
  transform: scale(1.001);
}
.icon-btn.style2 {
  --icon-size: 120px;
  background-color: var(--smoke-color);
}

.simple-icon {
  padding: 0;
  border: none;
  background-color: transparent;
  color: var(--title-color);
  line-height: 1;
  font-size: 25px;
  vertical-align: middle;
}
.simple-icon.style2 {
  color: var(--white-color);
}
.simple-icon:hover {
  color: var(--theme-color);
}

.play-btn {
  display: inline-block;
  position: relative;
  z-index: 1;
}
.play-btn > i {
  display: inline-block;
  width: var(--icon-size, 85px);
  height: var(--icon-size, 85px);
  line-height: var(--icon-size, 85px);
  text-align: center;
  background-color: var(--white-color);
  color: var(--theme-color);
  font-size: var(--icon-font-size, 1.5em);
  border-radius: 50%;
  z-index: 1;
  transition: all ease 0.4s;
}
.play-btn:after, .play-btn:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background-color: var(--white-color);
  z-index: -1;
  border-radius: 50%;
  transition: all ease 0.4s;
}
.play-btn:after {
  animation-delay: 2s;
}
.play-btn:hover:after, .play-btn:hover::before,
.play-btn:hover i {
  background-color: var(--theme-color);
  color: var(--white-color);
}

.link-btn {
  font-weight: 600;
  font-size: 16px;
  display: inline-block;
  line-height: 0.8;
  position: relative;
  padding-bottom: 2px;
  margin-bottom: -2px;
  text-transform: capitalize;
}
.link-btn i {
  margin-left: 7px;
  font-size: 0.9rem;
}
.link-btn:before {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 0;
  height: 1px;
  background-color: var(--theme-color);
  transition: all ease 0.4s;
}
.link-btn:hover {
  color: var(--theme-color);
}
.link-btn:hover::before {
  width: 100%;
}

.scroll-btn {
  position: fixed;
  bottom: 300px;
  right: 30px;
  z-index: 94;
  opacity: 0;
  visibility: hidden;
  display: inline-block;
  border-radius: 50%;
  /* Small devices */
}
.scroll-btn i {
  display: inline-block;
  background-color: var(--theme-color);
  color: var(--white-color);
  text-align: center;
  font-size: 16px;
  width: var(--btn-size, 50px);
  height: var(--btn-size, 50px);
  line-height: var(--btn-size, 50px);
  z-index: 2;
  border-radius: inherit;
  position: relative;
  transition: all ease 0.8s;
}
.scroll-btn:before {
  content: "";
  position: absolute;
  left: var(--extra-shape, -6px);
  top: var(--extra-shape, -6px);
  right: var(--extra-shape, -6px);
  bottom: var(--extra-shape, -6px);
  background-color: var(--body-bg);
  border-radius: inherit;
  z-index: 1;
  transition: all ease 0.4s;
}
.scroll-btn:focus i, .scroll-btn:hover i {
  background-color: var(--vs-secondary-color);
  color: var(--white-color);
}
.scroll-btn.show {
  bottom: 120px;
  opacity: 1;
  visibility: visible;
}
@media (max-width: 767px) {
  .scroll-btn {
    --btn-size: 40px;
    --extra-shape: -4px;
    right: 15px;
    bottom: 50px;
  }
  .scroll-btn.show {
    bottom: 15px;
  }
}

.scrollToTop {
  position: fixed;
  right: 60px;
  bottom: 500px;
  opacity: 0;
  visibility: hidden;
  transition: all ease 0.4s;
  z-index: 96;
}
.scrollToTop.show {
  bottom: 60px;
  opacity: 1;
  visibility: visible;
}

/* Small devices */
@media (max-width: 767px) {
  .play-btn {
    --icon-size: 60px;
  }
  .scrollToTop {
    right: 20px;
  }
  .scrollToTop.show {
    bottom: 20px;
  }
}
/*------------------- 3.3. Titles -------------------*/
.sec-title2,
.sec-title {
  font-size: 60px;
  margin-top: -0.25em;
  margin-bottom: 10px;
  text-transform: capitalize;
}

.sec-title2 {
  font-size: 55px;
}

.sec-subtitle {
  display: block;
  text-transform: uppercase;
  font-size: 16px;
  letter-spacing: 0.3em;
  color: var(--theme-color);
  font-weight: 700;
  font-family: var(--title-font);
  line-height: 1;
  margin-top: -0.1em;
  margin-bottom: 25px;
}

.sec-subtitle2 {
  font-family: var(--title-font);
  font-size: 20px;
  font-weight: 600;
  text-transform: uppercase;
  color: var(--title-color);
  position: relative;
  margin-top: -0.25em;
  margin-bottom: 0;
  padding-bottom: 10px;
  display: block;
}

.title-divider2,
.title-divider1 {
  height: 3px;
  width: 24px;
  background-color: var(--theme-color2);
  border-radius: 9999px;
  display: inline-block;
  margin-bottom: 17px;
}

.title-divider2 {
  height: 4px;
  width: 42px;
}

.sec-text {
  font-size: 18px;
}

.sec-bubble {
  width: 36px;
  height: 36px;
  position: relative;
  display: inline-block;
  margin-bottom: 20px;
}
.sec-bubble .bubble {
  position: absolute;
  display: inline-block;
  width: 20px;
  height: 20px;
  background-color: var(--theme-color2);
  border-radius: 50%;
}
.sec-bubble .bubble:nth-child(1) {
  top: 0;
  left: 50%;
  z-index: 3;
  margin-left: -10px;
}
.sec-bubble .bubble:nth-child(2) {
  right: 0;
  bottom: 0;
  z-index: 1;
  background-color: var(--theme-color);
}
.sec-bubble .bubble:nth-child(3) {
  left: 0;
  bottom: 0;
  z-index: 2;
  background-color: var(--vs-secondary-color);
}

.title-area {
  margin-bottom: calc(var(--section-title-space) - 22px);
}

.sec-btns {
  margin-bottom: calc(var(--section-title-space) - 9px);
}
.sec-btns .icon-btn {
  margin-right: 5px;
}
.sec-btns .icon-btn:last-child {
  margin-right: 0;
}

.sec-bottom-btns {
  margin-top: 40px;
  margin-bottom: 30px;
  text-align: center;
}

.vs-carousel + .sec-bottom-btns {
  margin-top: 10px;
}

/* Medium Large devices */
@media (max-width: 1399px) {
  .sec-title2,
  .sec-title {
    font-size: 48px;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .sec-title2,
  .sec-title {
    font-size: 42px;
  }
  .sec-subtitle {
    letter-spacing: 0.1em;
    margin-bottom: 20px;
  }
  .sec-text {
    font-size: 16px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .sec-title2,
  .sec-title {
    font-size: 36px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .sec-btns {
    margin-bottom: 30px;
  }
}
/*------------------- 3.4. Common -------------------*/
.svg-hidden {
  position: absolute;
  width: 0;
  height: 0;
}

.vs-wave-shape {
  /* Small devices */
}
@media (min-width: 1399px) {
  .vs-wave-shape {
    margin-bottom: -40px;
  }
}
@media (max-width: 767px) {
  .vs-wave-shape {
    overflow: hidden;
  }
  .vs-wave-shape svg {
    height: 150px;
  }
}
.vs-wave-shape .svg {
  width: 100%;
}
.vs-wave-shape .wave-path {
  fill: var(--vs-secondary-color);
}
.vs-wave-shape.style2 .wave-path {
  fill: var(--white-color);
}
.vs-wave-shape.style3 .wave-path {
  fill: var(--smoke-color);
}

.shape-mockup-wrap {
  position: relative;
}
.shape-mockup-wrap .container-fluid,
.shape-mockup-wrap .container {
  z-index: 1;
  position: relative;
}

.shape-mockup {
  position: absolute;
  z-index: 1;
}

.image-scale-hover {
  overflow: hidden;
}
.image-scale-hover img {
  transition: all ease 0.4s;
  transform: scale(1.001);
}
.image-scale-hover:hover img {
  transform: scale(1.2);
}

.fa, .fa:before,
.far,
.far:before,
.fas,
.fas:before,
.fal,
.fal:before {
  font-family: var(--icon-font) !important;
}

.vertical-line {
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  height: 100%;
}

.z-index-step1 {
  position: relative;
  z-index: 4 !important;
}

.z-index-common {
  position: relative;
  z-index: 3;
}

.z-index-n1 {
  z-index: -1;
}

.media-body {
  flex: 1;
}

.badge {
  position: absolute;
  width: -moz-fit-content;
  width: fit-content;
  display: inline-block;
  text-align: center;
  background-color: var(--theme-color);
  color: var(--white-color);
  padding: 0.35em 0.55em;
  border-radius: 50%;
}

.mega-hover {
  position: relative;
  overflow: hidden;
}
.mega-hover img {
  transition: all 2s ease;
  transform: scale(1.001);
}
.mega-hover:after, .mega-hover:before {
  content: "";
  position: absolute;
  pointer-events: none;
  opacity: 1;
  z-index: 3;
  transform: rotate(5deg);
}
.mega-hover:before {
  top: -10%;
  right: 51%;
  bottom: -10%;
  left: 50%;
  background: rgba(255, 255, 255, 0.3);
}
.mega-hover:after {
  top: 50%;
  right: -10%;
  bottom: 50%;
  left: -10%;
  background: rgba(255, 255, 255, 0.6);
}
.mega-hover:hover:before {
  left: 0;
  right: 0;
  opacity: 0;
  transition: all 900ms linear;
}
.mega-hover:hover:after {
  top: 0;
  bottom: 0;
  opacity: 0;
  transition: all 900ms linear;
}
.mega-hover:hover img {
  transform: scale(1.05);
}

@media (min-width: 1921px) {
  .d-hd-none {
    display: none !important;
  }
}
@media (min-width: 1700px) {
  .d-xxxl-block {
    display: block !important;
  }
}
/*------------------- 3.6. Font -------------------*/
.font-icon {
  font-family: var(--icon-font);
}

.font-title {
  font-family: var(--title-font);
}

.font-body {
  font-family: var(--body-font);
}

.fw-light {
  font-weight: 300;
}

.fw-normal {
  font-weight: 400;
}

.fw-medium {
  font-weight: 500;
}

.fw-semibold {
  font-weight: 600;
}

.fw-bold {
  font-weight: 700;
}

.fw-extrabold {
  font-weight: 800;
}

.fs-md {
  font-size: 18px;
}

.fs-xs {
  font-size: 14px;
}

.fs-20 {
  font-size: 20px;
}

/* Small devices */
@media (max-width: 767px) {
  .fs-20,
  .fs-md {
    font-size: 16px;
  }
}
/*------------------- 3.7. Background -------------------*/
.bg-theme {
  background-color: var(--theme-color) !important;
}

.bg-vs-secondary {
  background-color: var(--vs-secondary-color) !important;
}

.bg-smoke {
  background-color: var(--smoke-color) !important;
}

.bg-vs-light {
  background-color: var(--light-color) !important;
}

.bg-white {
  background-color: var(--white-color) !important;
}

.bg-black {
  background-color: var(--black-color) !important;
}

.bg-title {
  background-color: var(--title-color) !important;
}

.background-image,
[data-bg-src] {
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
}

.bg-fluid {
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: center center;
}

.bg-auto {
  background-size: auto auto;
}

/*------------------- 3.8. Text Color -------------------*/
.text-theme {
  color: var(--theme-color) !important;
}

.text-title {
  color: var(--title-color) !important;
}

.text-body {
  color: var(--body-color) !important;
}

.text-white {
  color: var(--white-color) !important;
}

.text-yellow {
  color: var(--yellow-color) !important;
}

.text-success {
  color: var(--success-color) !important;
}

.text-error {
  color: var(--error-color) !important;
}

.text-inherit {
  color: inherit;
}
.text-inherit:hover {
  color: var(--theme-color);
}

a.text-theme:hover,
.text-reset:hover {
  text-decoration: underline;
}

/*------------------- 3.9. Overlay -------------------*/
.overlay {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
}

.position-center {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

[data-overlay] {
  position: relative;
}
[data-overlay] [class^=col-],
[data-overlay] [class*=col-] {
  z-index: 1;
}

[data-overlay]:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

[data-overlay=theme]:before {
  background-color: var(--theme-color);
}

[data-overlay=title]:before {
  background-color: var(--title-color);
}

[data-overlay=white]:before {
  background-color: var(--white-color);
}

[data-overlay=black]:before {
  background-color: var(--black-color);
}

[data-opacity="1"]:before {
  opacity: 0.1;
}

[data-opacity="2"]:before {
  opacity: 0.2;
}

[data-opacity="3"]:before {
  opacity: 0.3;
}

[data-opacity="4"]:before {
  opacity: 0.4;
}

[data-opacity="5"]:before {
  opacity: 0.5;
}

[data-opacity="6"]:before {
  opacity: 0.6;
}

[data-opacity="7"]:before {
  opacity: 0.7;
}

[data-opacity="8"]:before {
  opacity: 0.8;
}

[data-opacity="9"]:before {
  opacity: 0.9;
}

[data-opacity="10"]:before {
  opacity: 1;
}

/*------------------- 3.10. Animation -------------------*/
.transform-banner img {
  animation: border-transform 20s linear infinite alternate forwards;
  border-radius: 100%;
}

.jump-reverse-img,
.jump-img,
.jump-reverse,
.jump {
  animation: jumpping var(--duration, 6s) infinite linear;
}

.jump-reverse-img,
.jump-img {
  --duration: 5s;
}

.jump-reverse-img,
.jump-reverse {
  --jump-y: -20px;
}

.rotate-reverse-img,
.rotate-img,
.rotate-reverse,
.rotate {
  animation: rotate var(--duration, 12s) infinite linear;
}

.rotate-reverse-img,
.rotate-img {
  --duration: 40s;
}

.rotate-reverse {
  --rotate-angle: -360deg;
}

.fadeInUp {
  animation-name: fadeInUp;
}

.wow-animated {
  animation-duration: 1s;
  animation-fill-mode: both;
}

.fadein {
  --animation-name: fadein-custom;
}

.slideinup {
  --animation-name: slideinup;
}

.slideindown {
  --animation-name: slideindown;
}

.slideinleft {
  --animation-name: slideinleft;
}

.slideinright {
  --animation-name: slideinright;
}

.animated {
  animation-fill-mode: both;
  animation-iteration-count: 1;
  animation-duration: 1s;
  animation-delay: 0.3s;
  animation-name: var(--animation-name);
}

.ripple-animation, .play-btn:after, .play-btn:before {
  animation-duration: var(--ripple-ani-duration);
  animation-timing-function: ease-in-out;
  animation-iteration-count: infinite;
  animation-name: ripple;
}

@keyframes ripple {
  0% {
    transform: scale(1);
    opacity: 0;
  }
  30% {
    opacity: 0.4;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}
@keyframes rotate {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(var(--rotate-angle, 360deg));
  }
}
@keyframes slideinup {
  0% {
    opacity: 0;
    transform: translateY(70px);
  }
  100% {
    transform: translateY(0);
  }
}
@keyframes slideindown {
  0% {
    opacity: 0;
    transform: translateY(-70px);
  }
  100% {
    transform: translateY(0);
  }
}
@keyframes slideinleft {
  0% {
    opacity: 0;
    transform: translateX(-70px);
  }
  100% {
    transform: translateX(0);
  }
}
@keyframes slideinright {
  0% {
    opacity: 0;
    transform: translateX(70px);
  }
  100% {
    transform: translateX(0);
  }
}
@keyframes fadein-custom {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
@keyframes jumpping {
  0%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40% {
    transform: translate3d(0, var(--jump-y, 20px), var(--jump-x, 0));
  }
}
@keyframes border-transform {
  0%, 100% {
    border-radius: 63% 37% 54% 46%/55% 48% 52% 45%;
  }
  14% {
    border-radius: 40% 60% 54% 46%/49% 60% 40% 51%;
  }
  28% {
    border-radius: 54% 46% 38% 62%/49% 70% 30% 51%;
  }
  42% {
    border-radius: 61% 39% 55% 45%/61% 38% 62% 39%;
  }
  56% {
    border-radius: 61% 39% 67% 33%/70% 50% 50% 30%;
  }
  70% {
    border-radius: 50% 50% 34% 66%/56% 68% 32% 44%;
  }
  84% {
    border-radius: 46% 54% 50% 50%/35% 61% 39% 65%;
  }
}
/*=================================
    04. Template Style
==================================*/
/*------------------- 4.1. Widget  -------------------*/
.widget_nav_menu ul,
.widget_pages ul,
.widget_archive ul,
.widget_categories ul {
  list-style: none;
  margin: 0 0 -10px 0;
  padding: 0;
}
.widget_nav_menu a,
.widget_pages a,
.widget_archive a,
.widget_categories a {
  position: relative;
  display: block;
  padding: 20px 20px 20px 55px;
  margin: 0 0 10px 0;
  font-size: 16px;
  font-weight: 600;
  line-height: 1;
  letter-spacing: 0.05em;
  color: var(--title-color);
  background-color: var(--white-color);
  font-family: var(--title-font);
  border-radius: 9999px;
}
.widget_nav_menu a:before,
.widget_pages a:before,
.widget_archive a:before,
.widget_categories a:before {
  content: "\f178";
  font-family: var(--icon-font);
  font-size: 12px;
  color: var(--white-color);
  font-weight: 400;
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  width: 26px;
  height: 26px;
  line-height: 26px;
  text-align: center;
  background-color: var(--vs-secondary-color);
  border-radius: 50%;
}
.widget_nav_menu a:hover,
.widget_pages a:hover,
.widget_archive a:hover,
.widget_categories a:hover {
  background-color: var(--theme-color);
  color: var(--white-color);
}
.widget_nav_menu li,
.widget_pages li,
.widget_archive li,
.widget_categories li {
  display: block;
  position: relative;
}
.widget_nav_menu li > span,
.widget_pages li > span,
.widget_archive li > span,
.widget_categories li > span {
  width: 40px;
  height: 40px;
  line-height: 35.5px;
  font-size: 18px;
  font-weight: 500;
  color: var(--title-color);
  background-color: transparent;
  right: 10px;
  top: 10px;
  z-index: 1;
  display: inline-block;
  text-align: center;
  position: absolute;
  border: 2px solid var(--theme-color);
  transition: all ease 0.4s;
  border-radius: 0;
}
.widget_nav_menu li:hover > span,
.widget_pages li:hover > span,
.widget_archive li:hover > span,
.widget_categories li:hover > span {
  background-color: var(--theme-color);
  color: var(--white-color);
}
.widget_nav_menu .children,
.widget_pages .children,
.widget_archive .children,
.widget_categories .children {
  margin-left: 10px;
}

.widget_nav_menu .sub-menu {
  margin-left: 10px;
}

.wp-block-archives {
  list-style: none;
  margin: 0;
  padding: 0;
  margin-bottom: 20px;
}
.wp-block-archives a:not(:hover) {
  color: inherit;
}

.vs-blog ul.wp-block-archives li {
  margin: 5px 0;
}

/* Large devices */
@media (max-width: 1199px) {
  .widget_nav_menu a,
  .widget_pages a,
  .widget_archive a,
  .widget_categories a {
    font-size: 14px;
  }
  .widget_nav_menu li > span,
  .widget_pages li > span,
  .widget_archive li > span,
  .widget_categories li > span {
    top: 0;
    right: 0;
    width: 50px;
    height: 50px;
    line-height: 47px;
    font-size: 14px;
  }
}
.widget {
  padding: var(--widget-padding-y, 40px) var(--widget-padding-x, 40px);
  background-color: var(--smoke-color);
  position: relative;
  margin-bottom: 40px;
  border-radius: 30px;
}
.widget select,
.widget input {
  height: 60px;
  border: none;
  background-color: var(--input-bg, #ffffff);
}

.widget_title {
  position: relative;
  font-weight: 600;
  font-size: 30px;
  line-height: 1em;
  font-family: var(--title-font);
  margin: -0.2em 0 30px 0;
  padding: 0 0 13px 0;
}
.widget_title:before {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  height: 4px;
  width: 30px;
  background-color: var(--theme-color2);
  border-radius: 9999px;
}

.widget .search-form {
  position: relative;
}
.widget .search-form input {
  padding-right: 70px;
}
.widget .search-form button {
  border: none;
  background-color: transparent;
  padding: 4px 0 4px 15px;
  line-height: 1;
  color: var(--theme-color);
  border-left: 1px solid var(--border-color);
  position: absolute;
  top: 50%;
  right: 25px;
  transform: translateY(-50%);
  font-size: 18px;
}
.widget .search-form button:hover {
  color: var(--vs-secondary-color);
}

.wp-block-tag-cloud a,
.tagcloud a {
  position: relative;
  display: inline-block;
  border: none;
  z-index: 1;
  font-size: 16px;
  font-weight: 600;
  line-height: 1;
  padding: 12px 22px;
  margin-right: 5px;
  margin-bottom: 10px;
  color: var(--title-color);
  background-color: var(--smoke-color);
  font-family: var(--title-font);
  border-radius: 9999px;
  text-transform: none;
}
.wp-block-tag-cloud a:hover,
.tagcloud a:hover {
  background-color: var(--theme-color);
  color: var(--white-color) !important;
}
.wp-block-tag-cloud a:hover:before,
.tagcloud a:hover:before {
  background-color: var(--white-color);
}

.tagcloud {
  margin-right: -5px;
  margin-bottom: -10px;
}
.tagcloud a {
  background-color: var(--white-color);
}

.recent-post {
  display: flex;
  align-items: center;
  margin-bottom: 18px;
}
.recent-post:last-child {
  margin-bottom: 0;
}
.recent-post .media-img {
  margin-right: 20px;
  width: 100px;
  overflow: hidden;
  border-radius: 20px;
}
.recent-post .media-img img {
  width: 100%;
  transform: scale(1.001);
  transition: all ease 0.4s;
}
.recent-post .post-title {
  font-weight: 600;
  font-size: 20px;
  line-height: 24px;
  margin: 0 0 -0.1em 0;
}
.recent-post .recent-post-meta {
  margin: -0.2em 0 2px 0;
}
.recent-post .recent-post-meta a {
  text-transform: capitalize;
  font-size: 14px;
  font-weight: 500;
  color: var(--theme-color);
  font-family: var(--title-font);
}
.recent-post .recent-post-meta a i {
  margin-right: 5px;
}
.recent-post .recent-post-meta a:hover {
  color: var(--vs-secondary-color);
}
.recent-post:hover .media-img img {
  transform: scale(1.15);
}

.recent-event {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.recent-event:last-child {
  margin-bottom: 0;
}
.recent-event .event-title {
  font-size: 22px;
  margin: 0;
}
.recent-event .event-date {
  background-color: var(--vs-secondary-color);
  color: var(--white-color);
  line-height: 1;
  width: 80px;
  height: 90px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  text-transform: uppercase;
  border-radius: 30px;
  letter-spacing: 0.02em;
  margin-right: 20px;
  transition: all ease 0.4s;
  font-family: var(--title-font);
  font-size: 30px;
  font-weight: 600;
}
.recent-event .event-date .month {
  display: block;
  margin-bottom: 2px;
  font-size: 14px;
}
.recent-event:hover .event-date {
  background-color: var(--theme-color);
}

.vs-video-widget .video-thumb {
  overflow: hidden;
  margin-bottom: 15px;
  border-radius: 20px;
}
.vs-video-widget .video-title {
  margin: 0 0 -0.2em 0;
}

.range-slider-area .price-amount {
  display: block;
  color: var(--theme-color);
  font-size: 16px;
  font-weight: 400;
  margin: -0.4em 0 0 0;
}
.range-slider-area .price-amount .title {
  color: var(--title-color);
  font-weight: 600;
  margin-right: 10px;
  text-transform: uppercase;
  font-size: 16px;
  font-family: var(--title-font);
}
.range-slider-area .ui-slider {
  height: 4px;
  position: relative;
  width: 100%;
  background-color: var(--vs-secondary-color);
  border: none;
  margin-top: 20px;
  margin-bottom: 29px;
  cursor: pointer;
  border-radius: 3px;
}
.range-slider-area .ui-slider-range {
  border: none;
  cursor: pointer;
  position: absolute;
  top: 0;
  height: 100%;
  z-index: 1;
  display: block;
  background-color: var(--theme-color);
}
.range-slider-area .ui-slider-handle {
  width: 16px;
  height: 16px;
  padding: 0;
  border: none;
  cursor: pointer;
  position: absolute;
  top: 50%;
  margin-top: -8px;
  z-index: 2;
  background-color: var(--theme-color);
  transform: translateX(0px);
  border-radius: 50%;
}
.range-slider-area .ui-slider-handle:focus {
  outline: none;
  box-shadow: none;
}
.range-slider-area .ui-slider-handle:last-child {
  transform: translateX(-10px);
}
.range-slider-area .filter-btn {
  background-color: var(--vs-secondary-color);
  color: var(--white-color);
  border: 1px solid transparent;
  padding: 6px 30px;
  border-radius: 9999px;
  font-size: 16px;
  font-weight: 600;
  font-family: var(--title-font);
}
.range-slider-area .filter-btn:hover {
  background-color: var(--theme-color);
  color: var(--white-color);
  border-color: transparent;
}
.range-slider-area .reset-btn {
  float: right;
  border: none;
  padding: 0;
  font-size: 12px;
  text-transform: uppercase;
  color: var(--title-color);
  font-weight: 700;
  background-color: transparent;
  position: relative;
  top: 8px;
}
.range-slider-area .reset-btn i {
  margin-right: 7px;
}
.range-slider-area .reset-btn:hover {
  color: var(--theme-color);
}

.category-filter ul {
  margin: 0;
  padding: 2px 0 0 0;
  list-style: none;
}
.category-filter li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  line-height: 1;
  margin-bottom: 20px;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 20px;
}
.category-filter li:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}
.category-filter input[type=checkbox] ~ label {
  margin: 0;
  line-height: 1;
  color: var(--body-color);
  text-transform: capitalize;
}
.category-filter input[type=checkbox] ~ label:before {
  top: 0px;
}
.category-filter input[type=checkbox] ~ label:hover {
  color: var(--theme-color);
}
.category-filter input[type=checkbox]:checked ~ label {
  color: var(--theme-color);
}
.category-filter .total {
  color: var(--title-color);
  top: 1px;
  position: relative;
}

.latest-product {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.latest-product:last-child {
  margin-bottom: 0;
}
.latest-product .media-img {
  width: 80px;
  background-color: #ffffff;
  border: none;
  margin-right: 20px;
  overflow: hidden;
  border-radius: 10px;
}
.latest-product .media-img img {
  transition: all ease 0.4s;
  transform: scale(1.001);
}
.latest-product .product-title {
  font-size: 18px;
  font-family: var(--title-font);
  margin-bottom: 8px;
}
.latest-product .product-price {
  font-size: 16px;
  color: var(--vs-secondary-color);
  font-weight: 500;
  margin: 0 0 -0.2em 0;
}
.latest-product:hover .media-img img {
  transform: scale(1.15);
}

.sidebar-gallery:not(.vs-carousel) {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
}

.sidebar-gallery .gallery-thumb {
  overflow: hidden;
  position: relative;
  border-radius: 20px;
}
.sidebar-gallery .gallery-thumb img {
  transition: all ease 0.4s;
  width: 100%;
  transform: scale(1);
}
.sidebar-gallery .gallery-thumb:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: var(--theme-color);
  z-index: 1;
  opacity: 0;
  visibility: hidden;
  transition: all ease 0.4s;
}
.sidebar-gallery .gallery-thumb .gal-btn {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: scale(1.5);
  width: var(--icon-size, 35px);
  height: var(--icon-size, 35px);
  line-height: calc(var(--icon-size, 35px) + 2px);
  margin: calc(var(--icon-size, 35px) / -2) 0 0 calc(var(--icon-size, 35px) / -2);
  text-align: center;
  font-size: 18px;
  color: var(--white-color);
  background-color: transparent;
  border-radius: 50%;
  transition: all ease 0.4s;
  opacity: 0;
  visibility: hidden;
  z-index: 3;
}
.sidebar-gallery .gallery-thumb .gal-btn:hover {
  transform: scale(1);
  background-color: var(--white-color);
  color: var(--theme-color);
}
.sidebar-gallery .gallery-thumb:hover:before {
  opacity: 0.8;
  visibility: visible;
}
.sidebar-gallery .gallery-thumb:hover .gal-btn {
  opacity: 1;
  visibility: visible;
  transition-delay: 0.1s;
}
.sidebar-gallery .gallery-thumb:hover img {
  transform: scale(1.12);
}

.shop-sidebar {
  background-color: var(--smoke-color);
  border-radius: 20px;
  padding: 40px 30px;
}
.shop-sidebar .widget {
  padding: 0 0 0 0;
  margin: 0 0 45px 0;
  border-radius: 0;
  background-color: transparent;
}
.shop-sidebar .widget:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}
.shop-sidebar .widget_title {
  font-size: 24px;
}
.shop-sidebar .sidebar-gallery {
  grid-template-columns: repeat(2, 1fr);
}

/* Medium Large devices */
@media (max-width: 1399px) {
  .widget {
    --widget-padding-y: 30px;
    --widget-padding-x: 30px;
  }
  .widget_title {
    font-size: 26px;
  }
  .recent-event .event-title {
    font-size: 18px;
  }
  .recent-event .event-date {
    width: 75px;
    height: 80px;
    letter-spacing: 0;
    font-size: 24px;
  }
  .recent-post .media-img {
    margin-right: 15px;
    width: 85px;
  }
  .recent-post .recent-post-meta {
    margin: -0.2em 0 0 0;
  }
  .recent-post .recent-post-meta a {
    font-size: 14px;
  }
  .recent-post .post-title {
    font-size: 18px;
    line-height: 22px;
  }
  .wp-block-tag-cloud a,
  .tagcloud a {
    font-size: 14px;
    padding: 12px 20px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .sidebar-area {
    padding-top: 30px;
  }
  .shop-sidebar {
    padding: 40px 20px;
  }
  .shop-sidebar .sidebar-gallery {
    grid-template-columns: repeat(3, 1fr);
  }
  .wp-block-tag-cloud a,
  .tagcloud a {
    padding: 10.5px 18px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .widget_title {
    font-size: 22px;
  }
  .sidebar-area .comment-list .comment-author {
    font-size: 18px;
  }
  .sidebar-area .comment-list .comment-text {
    font-size: 14px;
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  .widget {
    padding: 30px 20px;
  }
}
.footer-widget {
  margin-bottom: 40px;
}
.footer-widget,
.footer-widget .widget {
  padding: 0;
  border: none;
  padding-bottom: 0;
  background-color: transparent;
}
.footer-widget .widget_title {
  margin-bottom: 33px;
}
.footer-widget.widget_meta, .footer-widget.widget_pages, .footer-widget.widget_archive, .footer-widget.widget_categories, .footer-widget.widget_nav_menu {
  margin-bottom: 45px;
}
.footer-widget.widget_meta ul, .footer-widget.widget_pages ul, .footer-widget.widget_archive ul, .footer-widget.widget_categories ul, .footer-widget.widget_nav_menu ul {
  padding: 3px 0 0 0;
}
.footer-widget.widget_meta ul ul, .footer-widget.widget_pages ul ul, .footer-widget.widget_archive ul ul, .footer-widget.widget_categories ul ul, .footer-widget.widget_nav_menu ul ul {
  margin-top: 0;
}
.footer-widget.widget_meta a, .footer-widget.widget_pages a, .footer-widget.widget_archive a, .footer-widget.widget_categories a, .footer-widget.widget_nav_menu a {
  width: -moz-max-content;
  width: max-content;
  display: block;
  border: none;
  font-size: 18px;
  font-weight: 400;
  margin-bottom: 23px;
  max-width: 100%;
  padding: 0 0 0 20px;
  background-color: transparent;
  position: relative;
}
.footer-widget.widget_meta a:before, .footer-widget.widget_pages a:before, .footer-widget.widget_archive a:before, .footer-widget.widget_categories a:before, .footer-widget.widget_nav_menu a:before {
  content: "";
  width: 7px;
  height: 7px;
  background-color: var(--theme-color2);
  border-radius: 2px;
  left: 0;
  top: 51%;
  transform: rotate(45deg) translateY(-50%);
}
.footer-widget.widget_meta a:hover, .footer-widget.widget_pages a:hover, .footer-widget.widget_archive a:hover, .footer-widget.widget_categories a:hover, .footer-widget.widget_nav_menu a:hover {
  background-color: transparent;
  color: var(--theme-color);
}
.footer-widget.widget_meta li > span, .footer-widget.widget_pages li > span, .footer-widget.widget_archive li > span, .footer-widget.widget_categories li > span, .footer-widget.widget_nav_menu li > span {
  width: auto;
  height: auto;
  position: relative;
  background-color: transparent;
  color: var(--body-color);
  line-height: 1;
}
.footer-widget.widget_meta li:last-child a, .footer-widget.widget_pages li:last-child a, .footer-widget.widget_archive li:last-child a, .footer-widget.widget_categories li:last-child a, .footer-widget.widget_nav_menu li:last-child a {
  margin-bottom: 0;
}
.footer-widget .footer-menu ul {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
}
.footer-widget .footer-menu a {
  font-size: 18px;
}

.footer-logo {
  margin-bottom: 18px;
}

.footer-social a {
  position: relative;
  display: inline-block;
  border: none;
  background-color: var(--white-color);
  color: var(--black-color);
  text-align: center;
  width: 50px;
  height: 50px;
  line-height: 50px;
  border-radius: 50%;
  font-size: 16px;
  margin-right: 7px;
  z-index: 1;
}
.footer-social a::before {
  content: "";
  position: absolute;
  left: 3px;
  top: 3px;
  bottom: 3px;
  right: 3px;
  background-color: var(--vs-secondary-color);
  border-radius: inherit;
  transition: all ease 0.3s;
  transform: scale(0.4);
  opacity: 0;
  visibility: hidden;
  z-index: -1;
}
.footer-social a:last-child {
  margin-right: 0;
}
.footer-social a.active, .footer-social a:hover {
  background-color: var(--theme-color);
  color: var(--white-color);
  border-color: transparent;
}
.footer-social a.active:before, .footer-social a:hover:before {
  transform: scale(1);
  opacity: 1;
  visibility: visible;
}
.footer-social a.active:hover {
  background-color: var(--vs-secondary-color);
  color: var(--white-color);
}

.map-link {
  display: flex;
  gap: 20px;
  font-size: 18px;
  line-height: 1.6;
  margin: 0 0 30px 0;
  max-width: 370px;
}

.footer-text {
  margin: 0 0 8px 0;
  font-size: 18px;
}
.footer-text .time {
  font-weight: 500;
}

.footer-info {
  font-size: 18px;
  margin-bottom: 15px;
}
.footer-info:nth-last-child(2) {
  margin-top: 25px;
}
.footer-info i {
  width: var(--icon-size, 42px);
  height: var(--icon-size, 42px);
  line-height: var(--icon-size, 42px);
  text-align: center;
  border-radius: 50%;
  margin-right: 10px;
  background-color: var(--theme-color2);
  color: var(--black-color);
}
.footer-info a {
  color: inherit;
}
.footer-info a:hover {
  color: var(--theme-color);
}

/* Large devices */
@media (max-width: 1199px) {
  .footer-widget {
    margin-bottom: 60px;
  }
  .footer-widget .footer-menu a {
    font-size: 16px;
  }
  .footer-text {
    margin: 0 0 4px 0;
    font-size: 16px;
  }
  .footer-info {
    font-size: 16px;
    margin-bottom: 10px;
  }
  .footer-info:nth-last-child(2) {
    margin-top: 15px;
  }
  .map-link {
    gap: 15px;
    font-size: 15px;
    line-height: 1.5;
    margin: 0 0 15px 0;
    max-width: 370px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .footer-widget {
    margin-bottom: 50px;
  }
  .footer-widget .widget_title {
    margin-bottom: 25px;
  }
}
/*------------------- 4.2. Header  -------------------*/
.vs-header {
  position: relative;
  z-index: 41;
}

.header-logo {
  max-width: 270px;
  padding: 15px 0;
}

.will-sticky .sticky-active {
  position: fixed;
  top: -100%;
  right: 0;
  left: 0;
  background-color: var(--white-color);
  transition: all ease 0.8s;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.07);
}
.will-sticky .sticky-active.active {
  top: 0;
}

.main-menu a {
  display: block;
  position: relative;
  color: var(--title-color);
  font-family: var(--title-font);
  font-weight: 600;
  font-size: 18px;
  text-transform: capitalize;
  /* Extra large devices */
}
@media (max-width: 1500px) {
  .main-menu a {
    font-size: 16px;
  }
}
.main-menu a:hover {
  color: var(--theme-color);
}
.main-menu > ul > li {
  margin: 0 18px;
}
.main-menu ul {
  margin: 0;
  padding: 0;
}
.main-menu ul li {
  list-style-type: none;
  display: inline-block;
  position: relative;
}
.main-menu ul li.menu-item-has-children > a:after {
  content: "\f078";
  position: relative;
  font-family: var(--icon-font);
  margin-left: 5px;
  top: -0.8px;
  font-size: 0.8rem;
}
.main-menu ul li:last-child {
  margin-right: 0;
}
.main-menu ul li:first-child {
  margin-left: 0;
}
.main-menu ul li:hover > ul.sub-menu,
.main-menu ul li:hover ul.mega-menu {
  visibility: visible;
  opacity: 1;
  margin-top: 0;
  z-index: 9;
}
.main-menu ul.sub-menu,
.main-menu ul.mega-menu {
  position: absolute;
  text-align: left;
  top: 100%;
  left: 0;
  background-color: var(--body-bg);
  visibility: hidden;
  min-width: 190px;
  width: -moz-max-content;
  width: max-content;
  padding: 7px;
  left: -14px;
  margin-top: 50px;
  opacity: 0;
  z-index: -1;
  border-bottom: 3px solid var(--theme-color);
  box-shadow: 0px 10px 60px 0px rgba(0, 0, 0, 0.09), 0px 3px 0px 0px rgba(231, 13, 60, 0.004);
  transform-origin: top center;
  transition: margin-top 0.4s ease-in-out 0s, visibility 0.4s ease-in-out 0s, opacity 0.4s ease-in-out 0s, z-index 0s;
}
.main-menu ul.sub-menu a,
.main-menu ul.mega-menu a {
  font-size: 16px;
  line-height: 30px;
}
.main-menu ul.sub-menu {
  padding: 18px 20px;
  left: -27px;
}
.main-menu ul.sub-menu:before {
  content: "";
  position: absolute;
  left: 34px;
  top: 30px;
  width: 1px;
  background-color: var(--border-color);
  height: calc(100% - 65px);
}
.main-menu ul.sub-menu li {
  display: block;
  margin: 0 0;
  padding: 3px 9px;
}
.main-menu ul.sub-menu li.menu-item-has-children > a:after {
  content: "\f105";
  float: right;
  top: 3px;
}
.main-menu ul.sub-menu li a {
  position: relative;
  padding-left: 21px;
}
.main-menu ul.sub-menu li a:before {
  content: "\f111";
  position: absolute;
  top: 2.8em;
  left: 0;
  font-family: var(--icon-font);
  width: 11px;
  height: 11px;
  text-align: center;
  border-radius: 50%;
  display: inline-block;
  font-size: 0.2em;
  line-height: 11.5px;
  color: var(--theme-color);
  font-weight: 700;
  background-color: var(--body-bg);
  box-shadow: inset 0px 2px 4px 0px rgba(232, 6, 60, 0.4);
}
.main-menu ul.sub-menu li ul.sub-menu {
  left: 100%;
  right: auto;
  top: 0;
  margin: 0 0;
  margin-left: 20px;
}
.main-menu ul.sub-menu li ul.sub-menu li ul {
  left: 100%;
  right: auto;
}
.main-menu .mega-menu-wrap {
  position: static;
}
.main-menu ul.mega-menu {
  display: flex;
  justify-content: space-between;
  text-align: left;
  width: 100%;
  max-width: var(--main-container);
  padding: 20px 15px 23px 15px;
  left: 50%;
  transform: translateX(-50%);
}
.main-menu ul.mega-menu li {
  display: block;
  width: 100%;
  padding: 0 15px;
}
.main-menu ul.mega-menu li li {
  padding: 4px 0;
}
.main-menu ul.mega-menu li a {
  display: inline-block;
}
.main-menu ul.mega-menu > li > a {
  display: block;
  padding: 0;
  padding-bottom: 5px;
  margin-bottom: 10px;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 700;
  color: var(--title-color);
  border-color: var(--theme-color);
}
.main-menu ul.mega-menu > li > a::after, .main-menu ul.mega-menu > li > a::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 15px;
  height: 1px;
  background-color: var(--theme-color);
}
.main-menu ul.mega-menu > li > a::after {
  width: calc(100% - 20px);
  left: 20px;
}
.main-menu ul.mega-menu > li > a:hover {
  padding-left: 0;
}
.main-menu .home > a {
  position: relative;
}
.main-menu .home > a:before {
  content: "\f80a";
  font-family: var(--icon-font);
  background-color: var(--theme-color);
  color: var(--white-color);
  width: 32px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  display: inline-block;
  border-radius: 50%;
  margin-right: 10px;
  font-size: 14px;
}

.menu-style1 > ul > li > a {
  padding: 38px 0;
}

.menu-style2 > ul > li {
  margin: 0;
}
.menu-style2 > ul > li > a {
  padding: 14.5px 20px 14.5px 25px;
}
.menu-style2 > ul > li:first-child > a {
  padding-left: 0;
}
.menu-style2 > ul > li:not(:first-child) > a:before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  height: 18px;
  margin-top: -9px;
  width: 1px;
  background-color: #BEBEBE;
}

.menu-style3 > ul > li > a {
  padding: 41px 0;
  color: var(--white-color);
}
.menu-style3 > ul > li > a:hover {
  color: var(--theme-color);
}

.header-links ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
}
.header-links li {
  display: inline-block;
  font-size: 16px;
  font-weight: 400;
  color: var(--title-color);
  font-family: var(--body-font);
  margin: 0 35px 0 0;
  line-height: 1;
}
.header-links li:last-child {
  margin-right: 0;
}
.header-links i {
  color: var(--title-color);
  margin: 0 10px 0 0;
  font-size: 20px;
  vertical-align: middle;
  transition: all ease 0.4s;
}
.header-links a {
  color: inherit;
}
.header-links a:hover,
.header-links a:hover i {
  color: var(--theme-color);
}
.header-links.style3 i, .header-links.style2 i {
  width: var(--icon-size, 42px);
  height: var(--icon-size, 42px);
  line-height: calc(var(--icon-size, 42px) - 2px);
  font-size: var(--icon-font-size, 18px);
  text-align: center;
  border: 1px solid var(--title-color);
  border-radius: 50%;
}
.header-links.style3 li:hover i, .header-links.style2 li:hover i {
  background-color: var(--theme-color);
  color: var(--white-color);
  border-color: transparent;
}
.header-links.style-white i,
.header-links.style-white li {
  color: var(--white-color);
  border-color: var(--white-color);
}
.header-links.style3 i {
  border-color: transparent;
  color: var(--title-color);
  background-color: var(--theme-color2);
}

.header-layout1 .header-top {
  background-color: var(--vs-secondary-color);
  padding: 11px 0;
}

@media (min-width: 1399px) {
  .header-layout2 {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
  }
}
.header-layout2 .header-logo {
  margin-right: 75px;
}
.header-layout2 .menu-area {
  padding: 0 35px;
  position: relative;
  z-index: 1;
  background-color: var(--white-color);
  border-radius: 9999px;
}
.header-layout2 .simple-icon:not(.style2) {
  position: relative;
  padding-left: 20px;
}
.header-layout2 .simple-icon:not(.style2):before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  height: 18px;
  margin-top: -9px;
  width: 1px;
  background-color: #BEBEBE;
}
.header-layout2 .vs-menu-toggle {
  margin: 7px 0;
}

.header-layout3 .header-top {
  padding: 10px 0;
}
.header-layout3 .menu-area {
  max-width: 1360px;
  background-color: var(--vs-secondary-color);
  margin: 0 auto -55px auto;
  border-radius: 9999px;
}

/* Medium Large devices */
@media (max-width: 1399px) {
  .header-layout2 {
    background-color: var(--vs-secondary-color);
    padding-bottom: 1px;
  }
  .header-layout2 .header-logo {
    margin-right: 0;
  }
  .header-layout2 .menu-area {
    padding: 0 25px 0 15px;
    margin-bottom: -30px;
  }
  .header-layout2 .menu-area::before {
    width: 100%;
    border-radius: 30px;
  }
  .header-layout2 .menu-style2 {
    padding-left: 0;
  }
  .header-layout3 .menu-area {
    border-radius: 0;
    width: 100%;
    margin: 0;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .header-links li {
    margin: 0 25px 0 0;
  }
  .header-links.style2 i {
    --icon-size: 36px;
    --icon-font-size: 16px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .header-layout2 .menu-area {
    padding: 0 25px 0 9px;
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  .header-layout1 .header-top {
    padding: 17px 0;
  }
  .header-layout2 .vs-btn {
    padding: 14px 15px;
    font-size: 14px;
  }
  .header-layout2 .menu-area {
    margin-bottom: 0;
  }
  .header-links li {
    margin: 0 0 10px 0;
    display: block;
  }
  .header-links li:last-child {
    margin-bottom: 0;
  }
  .header-links.style2 i {
    --icon-size: auto;
    border: none;
    vertical-align: top;
  }
}
/*------------------- 4.3. Footer  -------------------*/
.widget-area {
  padding-top: 80px;
  padding-bottom: 40px;
}

.copyright-wrap {
  text-align: center;
  padding: 29px 0;
  background-color: var(--theme-color2);
}

.copyright-text {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--black-color);
  font-family: var(--title-font);
}
.copyright-text a {
  color: inherit;
  text-decoration: underline;
}
.copyright-text a:hover {
  color: var(--theme-color);
}

.copyright-menu ul {
  margin: 0;
}
.copyright-menu li {
  margin-right: 20px;
  display: inline-block;
}
.copyright-menu a {
  display: block;
  text-decoration: underline;
  text-transform: uppercase;
  color: var(--body-color);
  font-size: 16px;
  font-weight: 500;
}
.copyright-menu a:hover {
  color: var(--theme-color);
}

.footer-layout3,
.footer-layout1 {
  background-color: var(--vs-secondary-color);
  --title-color: #FFFFFF;
  --body-color: #FFFFFF;
}
.footer-layout3 .countdown-style1,
.footer-layout3 .vs-social,
.footer-layout3 .vs-btn,
.footer-layout1 .countdown-style1,
.footer-layout1 .vs-social,
.footer-layout1 .vs-btn {
  --title-color: #000;
}

.footer-layout1 .footer-top {
  padding: 60px 0 0 0;
}

.footer-call {
  font-size: 18px;
  color: var(--white-color);
  display: flex;
}
.footer-call i {
  color: var(--theme-color2);
  margin-right: 10px;
  font-size: 30px;
}
.footer-call a {
  text-decoration: underline;
  text-decoration-thickness: 2px;
  text-decoration-color: var(--theme-color2);
}

.footer-layout3 .copyright-wrap {
  background-color: transparent;
  border-top: 1px solid rgba(255, 255, 255, 0.3);
}
@media (min-width: 1199px) {
  .footer-layout3 .widget-area {
    padding-top: 90px;
    padding-bottom: 50px;
  }
}
.footer-layout3 .footer-top {
  background-color: var(--white-color);
  padding: 30px 60px;
  border-radius: 30px;
}

/* Medium Large devices */
@media (max-width: 1399px) {
  .footer-layout3 .footer-top {
    padding: 25px 30px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .copyright-text {
    font-size: 16px;
  }
  .footer-layout3 .footer-top {
    padding: 25px 15px;
  }
}
/*------------------- 4.4. Breadcumb  -------------------*/
.breadcumb-menu {
  position: relative;
  margin: 0;
  padding: 15px 30px 15px 30px;
  list-style-type: none;
  background-color: var(--vs-secondary-color);
  border-radius: 10px;
}
.breadcumb-menu:after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 99999px;
  z-index: -1;
  border-radius: 0 7px 7px 0;
}
.breadcumb-menu span,
.breadcumb-menu li,
.breadcumb-menu a {
  color: var(--white-color);
  font-size: 18px;
  font-weight: 600;
  text-transform: capitalize;
  word-break: break-word;
  white-space: normal;
  display: inline-block;
  font-family: var(--title-font);
  letter-spacing: 0.02em;
}
.breadcumb-menu a:hover {
  color: var(--theme-color);
}
.breadcumb-menu li:not(:last-child):after {
  content: "-";
  position: relative;
  margin-left: 15px;
  margin-right: 10px;
}

.breadcumb-menu-wrap {
  margin: 95px 0 0 0;
  min-height: 60px;
  position: relative;
}

.breadcumb-title {
  font-size: 70px;
  color: var(--title-color);
  margin: -0.21em 0 -0.4em 0;
}

.breadcumb-text {
  font-size: 18px;
  color: var(--body-color);
  margin: 32px 0 -0.25em 0;
}

.breadcumb-wrapper {
  padding-top: 175px;
  padding-bottom: 20px;
  background-color: var(--smoke-color);
  overflow: hidden;
  background-position: center left;
  /* Medium devices */
}
@media (max-width: 991px) {
  .breadcumb-wrapper {
    padding-top: 100px;
    padding-bottom: 30px;
  }
}

/* Large devices */
@media (max-width: 1199px) {
  .breadcumb-title {
    font-size: 48px;
  }
  .breadcumb-wrapper {
    padding-top: 90px;
  }
  .breadcumb-menu-wrap {
    margin: 65px 0 0 0;
  }
  .breadcumb-text {
    font-size: 16px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .breadcumb-title {
    font-size: 40px;
    margin: -0.15em 0 -0.4em 0;
  }
  .breadcumb-text {
    font-size: 16px;
    margin: 22px 0 -0.25em 0;
  }
  .breadcumb-menu-wrap {
    margin: 75px 0 0 0;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .breadcumb-title {
    font-size: 36px;
  }
  .breadcumb-wrapper {
    padding-top: 60px;
  }
  .breadcumb-menu-wrap {
    margin: 45px 0 0 0;
  }
  .breadcumb-menu li,
  .breadcumb-menu a,
  .breadcumb-menu span {
    font-size: 14px;
  }
  .breadcumb-menu li:after {
    margin-left: 11px;
    margin-right: 9px;
  }
}
/*------------------- 4.5. Pagination  -------------------*/
.vs-pagination {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 30px;
}
.vs-pagination ul {
  margin: 0;
  padding: 7.5px 10px;
  list-style: none;
  border-radius: 9999px;
  background-color: var(--smoke-color);
}
.vs-pagination span,
.vs-pagination a {
  position: relative;
  display: inline-block;
  text-align: center;
  border: none;
  color: var(--title-color);
  font-family: var(--title-font);
  font-size: 18px;
  font-weight: 600;
  width: 45px;
  height: 45px;
  line-height: 45px;
  z-index: 1;
  text-transform: capitalize;
  background-color: var(--white-color);
  border-radius: 50%;
}
.vs-pagination span:before,
.vs-pagination a:before {
  content: "";
  position: absolute;
  left: 3px;
  right: 3px;
  bottom: 3px;
  top: 3px;
  background-color: var(--vs-secondary-color);
  border-radius: inherit;
  transition: all ease 0.4s;
  transform: scale(0.6);
  opacity: 0;
  visibility: hidden;
  z-index: -1;
}
.vs-pagination span i,
.vs-pagination a i {
  margin-left: 7px;
}
.vs-pagination span.active, .vs-pagination span:hover,
.vs-pagination a.active,
.vs-pagination a:hover {
  color: var(--white-color);
  border-color: transparent;
}
.vs-pagination span.active:before, .vs-pagination span:hover:before,
.vs-pagination a.active:before,
.vs-pagination a:hover:before {
  transform: scale(1);
  opacity: 1;
  visibility: visible;
}
.vs-pagination li {
  display: inline-block;
  margin: 0 2px;
  list-style-type: none;
}
.vs-pagination .pagi-btn {
  width: auto;
  height: auto;
  line-height: 1;
  padding: 21px 31px;
  border-radius: 9999px;
  background-color: var(--vs-secondary-color);
  color: var(--white-color);
}
.vs-pagination .pagi-btn:before {
  background-color: var(--theme-color);
}

/* Small devices */
@media (max-width: 767px) {
  .vs-pagination {
    gap: 5px;
  }
  .vs-pagination ul {
    padding: 3px 3px;
  }
  .vs-pagination span,
  .vs-pagination a {
    font-size: 14px;
    width: 30px;
    height: 30px;
    line-height: 30px;
  }
  .vs-pagination li {
    margin: 0 0;
  }
  .vs-pagination .pagi-btn {
    padding: 11px 15px;
    font-size: 12px;
  }
}
/*------------------- 4.6. Blog  -------------------*/
blockquote {
  display: block;
  position: relative;
  overflow: hidden;
  font-size: 20px;
  line-height: 30px;
  font-weight: 400;
  color: var(--title-color);
  background-color: var(--smoke-color);
  border-left: 4px solid var(--theme-color);
  padding: 43px 50px 42px 35px;
  margin: 35px 0;
}
blockquote p {
  font-family: inherit;
  margin-bottom: 0 !important;
  line-height: 1.5;
  color: inherit;
  width: 100%;
  position: relative;
  z-index: 3;
  font-style: italic;
}
blockquote:before {
  content: "\f10e";
  font-family: var(--icon-font);
  position: absolute;
  right: 70px;
  bottom: 27px;
  font-size: 3.5rem;
  font-weight: 900;
  line-height: 1;
  color: var(--theme-color);
}
blockquote p {
  margin-bottom: 0;
}
blockquote p a {
  color: inherit;
}
blockquote cite {
  display: inline-block;
  font-size: 16px;
  position: relative;
  padding-left: 45px;
  line-height: 1;
  font-weight: 400;
  margin-top: 22px;
  font-style: normal;
  color: var(--title-color);
}
blockquote cite:before {
  content: "";
  position: absolute;
  left: 0;
  bottom: 8px;
  width: 30px;
  height: 2px;
  border-top: 2px solid var(--theme-color);
}

blockquote.vs-quote {
  border: 5px solid var(--theme-color);
  background-color: transparent;
  padding: 36px 60px 36px 160px;
}
blockquote.vs-quote:before {
  content: "\f10d";
  font-size: 6rem;
  font-weight: 300;
  right: auto;
  bottom: auto;
  left: 35px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--theme-color);
  opacity: 0.2;
}
blockquote.vs-quote p {
  font-size: 26px;
  line-height: 40px;
  font-weight: 400;
  font-style: normal;
}

.blog-meta span,
.blog-meta a {
  display: inline-block;
  margin-right: 20px;
  font-size: 16px;
  color: var(--theme-color);
  font-weight: 500;
  transition: all ease 0.4s;
}
.blog-meta span:last-child,
.blog-meta a:last-child {
  margin-right: 0;
}
.blog-meta span i,
.blog-meta a i {
  margin-right: 7px;
}
.blog-meta a:hover {
  color: var(--vs-secondary-color);
}

.blog-category {
  margin-bottom: -10px;
}
.blog-category a {
  display: inline-block;
  color: var(--white-color);
  padding: 4.5px 24.5px;
  margin-right: 5px;
  margin-bottom: 10px;
  border: 1px solid transparent;
  background-color: var(--theme-color);
}
.blog-category a:hover {
  background-color: var(--white-color);
  color: var(--body-color);
  border-color: var(--theme-color);
}

.blog-title a {
  color: inherit;
}
.blog-title a:hover {
  color: var(--theme-color);
}

.vs-blog {
  margin-bottom: 30px;
}

.share-links-title {
  font-size: 18px;
  color: var(--title-color);
  font-family: var(--title-font);
  font-weight: 600;
  margin: 0 15px 0 0;
  display: inline-block;
}

.share-links {
  margin: 50px 0 50px 0;
  padding: 17px 30px;
  background-color: var(--theme-color2);
  border-radius: 9999px;
}
.share-links .row {
  align-items: center;
  --bs-gutter-y: 15px;
}
.share-links .tagcloud {
  display: inline-block;
}
.share-links .tagcloud a:not(:hover) {
  background-color: var(--white-color);
}
.share-links .social-links {
  display: inline-block;
  list-style-type: none;
  margin: 0;
  padding: 0;
}
.share-links .social-links li {
  display: inline-block;
  margin-right: 4px;
}
.share-links .social-links li:last-child {
  margin-right: 0;
}
.share-links .social-links a {
  width: 40px;
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  text-align: center;
  display: block;
  color: var(--title-color);
  background-color: var(--white-color);
  border-radius: 50%;
}
.share-links .social-links a:hover {
  color: var(--white-color);
  background-color: var(--theme-color);
  border-color: var(--theme-color);
}

.blog-author {
  display: flex;
  align-items: center;
  padding: 35px 50px 35px 35px;
  margin: 50px 0;
  background-color: var(--vs-secondary-color);
  border-radius: 30px;
}
.blog-author .media-img {
  width: 180px;
  margin-right: 30px;
  border-radius: 30px;
  overflow: hidden;
}
.blog-author .media-img img {
  transform: scale(1.001);
  transition: all ease 0.4s;
}
.blog-author .author-degi {
  color: var(--white-color);
  text-transform: uppercase;
  font-weight: 500;
  letter-spacing: 0.05em;
  font-family: var(--title-font);
  display: block;
  margin: 0 0 5px 0;
}
.blog-author .author-name {
  color: var(--white-color);
  line-height: 1;
  font-size: 30px;
  letter-spacing: 0.01em;
  margin-bottom: 15px;
}
.blog-author .author-text {
  color: #E0E0E0;
  margin: 0;
  font-size: 18px;
  line-height: 1.5;
}
.blog-author:hover .media-img img {
  transform: scale(1.15);
}

.blog-inner-title {
  font-size: 40px;
  position: relative;
  margin: -0.25em 0 40px 0;
  padding: 0 0 7px 0;
}
.blog-inner-title:before {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 30px;
  height: 3px;
  background-color: var(--theme-color2);
}

.blog-single {
  position: relative;
  margin-bottom: 75.5px;
  border: 3px solid var(--theme-color2);
  border-radius: 30px;
}
.blog-single .vs-btn {
  margin-bottom: -25.5px;
}
.blog-single .blog-meta {
  margin-bottom: 5px;
}
.blog-single .blog-title {
  line-height: 1.15;
  margin-bottom: 15px;
  font-size: 46px;
  text-transform: capitalize;
}
.blog-single .blog-text {
  margin-bottom: 40px;
}
.blog-single .blog-audio,
.blog-single .blog-img {
  position: relative;
  background-color: var(--smoke-color);
  margin: -3px -3px 0 -3px;
  border-radius: 30px 30px 0 0;
  overflow: hidden;
}
.blog-single .blog-audio {
  line-height: 1;
}
.blog-single .blog-img img {
  transform: scale(1.002);
  transition: all ease 0.4s;
}
.blog-single .blog-img .slick-arrow {
  --pos-x: 30px;
}
.blog-single .blog-img .play-btn {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.blog-single .blog-content {
  padding: 35px 60px 1px 60px;
}
.blog-single:hover .blog-img img {
  transform: scale(1.15);
}
.blog-single:hover .blog-img .slick-arrow {
  opacity: 1;
  visibility: visible;
}
.blog-single .mega-hover:not(.blog-img) {
  border-radius: 30px;
}

.blog-details .blog-single {
  border: none;
}
.blog-details .blog-single .blog-img {
  border-radius: 30px;
  margin-bottom: 35px;
}
.blog-details .blog-single .blog-content {
  padding: 0;
  border: none;
}

.blog-style1 {
  border: 3px solid var(--theme-color2);
  border-radius: 30px;
}
.blog-style1 .blog-date {
  font-weight: 600;
  font-size: 16px;
  font-family: var(--title-font);
  margin-bottom: 3px;
  display: inline-block;
}
.blog-style1 .blog-date i {
  margin-right: 10px;
}
.blog-style1 .blog-content {
  padding: 21px 30px 7px 30px;
}
.blog-style1 .blog-btn {
  width: 100%;
  display: block;
  font-size: 18px;
  font-weight: 600;
  font-family: var(--title-font);
  border-radius: 9999px;
  line-height: 1;
  padding: 16px 30px;
  color: var(--title-color);
  background-color: var(--theme-color2);
}
.blog-style1 .blog-btn i {
  margin-left: 8px;
  position: relative;
  top: 1px;
}
.blog-style1 .blog-img {
  border-radius: 30px;
  border: inherit;
  margin: -3px;
  overflow: hidden;
}
.blog-style1 .blog-img img {
  transform: scale(1.001);
  transition: all ease 0.4s;
}
.blog-style1:hover .blog-img img {
  transform: scale(1.15);
}

.blog-style2:not(:last-child) {
  margin-bottom: 40px;
}
.blog-style2 .blog-body {
  display: flex;
  align-items: center;
  padding: 15px 50px 15px 15px;
  border: 3px solid var(--theme-color2);
  border-radius: 30px;
  transition: all ease 0.4s;
}
@media (min-width: 1399px) {
  .blog-style2:nth-child(2) .blog-body {
    margin-left: -110px;
    width: 100%;
  }
}
.blog-style2:hover .blog-body {
  border-color: var(--theme-color);
}
.blog-style2 .blog-img {
  min-width: 10px;
  overflow: hidden;
  margin-right: 40px;
  border-radius: 30px;
}
.blog-style2 .blog-img img {
  width: 100%;
  transform: scale(1.001);
  transition: all ease 0.4s;
}
.blog-style2:hover .blog-img img {
  transform: scale(1.15);
}
.blog-style2 .blog-content {
  flex: 1;
}
.blog-style2 .blog-title,
.blog-style2 .blog-meta {
  margin-bottom: 5px;
}
.blog-style2 .blog-text {
  margin-bottom: 0;
}

/* Medium Large devices */
@media (max-width: 1399px) {
  blockquote {
    font-size: 18px;
    padding: 23px 30px 26px 30px;
  }
  blockquote.vs-quote {
    padding: 26px 60px 26px 130px;
  }
  blockquote.vs-quote p {
    font-size: 20px;
    line-height: 30px;
  }
  blockquote.vs-quote:before {
    font-size: 5rem;
    left: 25px;
  }
  .blog-single .blog-content {
    padding: 35px 40px 1px 40px;
  }
  .blog-single .blog-title {
    font-size: 40px;
  }
  .blog-single .blog-text {
    margin-bottom: 25px;
  }
  .blog-style2 .blog-body {
    padding: 15px 15px 25px 15px;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .share-links {
    border-radius: 20px;
  }
  .blog-author .author-text {
    font-size: 16px;
  }
  .blog-author .media-img {
    width: 150px;
    margin-right: 25px;
  }
  .blog-single .blog-content {
    padding: 25px 30px 1px 30px;
  }
  .blog-single .blog-title {
    font-size: 36px;
  }
  .blog-details .blog-single .blog-img {
    margin-bottom: 25px;
    border-radius: 20px;
  }
  .blog-style2 {
    text-align: center;
  }
  .blog-style2 .blog-body {
    display: block;
  }
  .blog-style2 .blog-img {
    margin-right: 0;
    margin-bottom: 20px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  blockquote {
    font-size: 16px;
    padding: 23px 15px 26px 15px;
  }
  blockquote:before {
    right: 30px;
    bottom: 30px;
    font-size: 2rem;
  }
  blockquote cite {
    margin-top: 17px;
    font-size: 14px;
  }
  blockquote cite::before {
    bottom: 6px;
  }
  blockquote.vs-quote {
    padding: 26px 20px 26px 20px;
  }
  blockquote.vs-quote:before {
    content: "\f10e";
    left: auto;
    top: auto;
    transform: none;
    right: 20px;
    bottom: 20px;
  }
  blockquote.vs-quote p {
    font-size: 18px;
  }
  .share-links {
    padding: 17px 20px;
  }
  .blog-inner-title {
    font-size: 26px;
  }
  .share-links-title {
    font-size: 18px;
    display: block;
    margin: 0 0 10px 0;
  }
  .blog-meta > span,
  .blog-meta a {
    font-size: 14px;
    margin-right: 15px;
  }
  .blog-author {
    display: block;
    padding: 35px 25px 35px 25px;
  }
  .blog-author .media-img {
    width: 145px;
    margin-right: 0;
    margin-bottom: 20px;
  }
  .blog-single {
    border-radius: 15px;
  }
  .blog-single .blog-audio,
  .blog-single .blog-img {
    border-radius: 15px 15px 0 0;
  }
  .blog-single .blog-content {
    padding: 25px 20px 1px 20px;
  }
  .blog-single .blog-title {
    font-size: 24px;
    line-height: 1.4;
  }
}
/*------------------- 4.7. Comments  -------------------*/
.vs-comment-form {
  margin: var(--blog-space-y, 50px) 0 var(--blog-space-y, 50px) 0;
}
.vs-comment-form.review-form .blog-inner-title {
  font-size: 36px;
}

.comment-respond {
  position: relative;
}
.comment-respond .form-title a#cancel-comment-reply-link {
  font-size: 0.7em;
  text-decoration: underline;
}
.comment-respond .custom-checkbox.notice {
  margin-bottom: 25px;
}
.comment-respond .row {
  --bs-gutter-x: 20px;
}
.comment-respond .form-control {
  border: 1px solid var(--border-color);
  font-size: 16px;
  color: var(--title-color);
  background-color: transparent;
}
.comment-respond .form-control::-moz-placeholder {
  color: var(--title-color);
}
.comment-respond .form-control::placeholder {
  color: var(--title-color);
}
.comment-respond .form-group i {
  color: var(--theme-color);
}
.comment-respond input[type=checkbox] ~ label:before {
  top: 5.5px;
}
.comment-respond input[type=checkbox]:checked ~ label:before {
  background-color: var(--theme-color);
  border-color: transparent;
}
.comment-respond .blog-inner-title {
  border: none;
  margin-bottom: 15px;
}
.comment-respond .form-text {
  margin-bottom: 25px;
  font-size: 16px;
  color: var(--body-color);
}

.vs-comments-wrap {
  margin: 50px 0;
}
.vs-comments-wrap .description p:last-child {
  margin-bottom: 0;
}
.vs-comments-wrap .comment-respond {
  margin: 30px 0;
}
.vs-comments-wrap pre {
  background: #ededed;
  color: #666;
  font-size: 14px;
  margin: 20px 0;
  overflow: auto;
  padding: 20px;
  white-space: pre-wrap;
  word-wrap: break-word;
}
.vs-comments-wrap blockquote {
  background-color: #eaf8f9;
}
.vs-comments-wrap li {
  margin: 0;
}
.vs-comments-wrap .vs-post-comment {
  padding: 30px 30px 25px 30px;
  display: flex;
  margin: 30px 0 30px 0;
  position: relative;
  background-color: #F0F6FA;
  border-radius: 30px;
}
.vs-comments-wrap ul.comment-list {
  list-style: none;
  margin: -10px 0 0 0;
  padding: 0;
}
.vs-comments-wrap ul.comment-list ul ul,
.vs-comments-wrap ul.comment-list ul ol,
.vs-comments-wrap ul.comment-list ol ul,
.vs-comments-wrap ul.comment-list ol ol {
  margin-bottom: 0;
}
.vs-comments-wrap .comment-avater {
  width: 130px;
  height: 130px;
  margin-right: 25px;
  background-color: var(--theme-color);
  overflow: hidden;
  border-radius: 30px;
}
.vs-comments-wrap .comment-avater img {
  width: 100%;
}
.vs-comments-wrap .comment-content {
  flex: 1;
  align-self: center;
}
.vs-comments-wrap .commented-on {
  font-size: 14px;
  margin-bottom: 10px;
  font-weight: 500;
  display: block;
  color: var(--theme-color);
}
.vs-comments-wrap .commented-on i {
  margin-right: 7px;
  font-size: 0.9rem;
}
.vs-comments-wrap .name {
  margin: -0.25em 10px 2px 0;
  font-size: 26px;
  display: inline-block;
}
.vs-comments-wrap .comment-top {
  display: flex;
  justify-content: space-between;
}
.vs-comments-wrap .text {
  color: var(--body-color);
  margin-bottom: 0;
}
.vs-comments-wrap .text:last-of-type {
  margin-bottom: -0.25em;
}
.vs-comments-wrap .children {
  margin: 0;
  padding: 0;
  list-style-type: none;
  margin-left: 40px;
}
.vs-comments-wrap .reply_and_edit {
  position: absolute;
  right: 10px;
  top: 10px;
  line-height: 1;
}
.vs-comments-wrap .replay-btn {
  color: var(--title-color);
  background-color: var(--white-color);
  display: inline-block;
  text-transform: capitalize;
  font-size: 14px;
  font-weight: 600;
  padding: 10px 19px 13px 19px;
  letter-spacing: 0.05em;
  border-radius: 9999px;
}
.vs-comments-wrap .replay-btn:hover {
  color: var(--white-color);
  background-color: var(--theme-color);
}
.vs-comments-wrap .review-rating {
  margin-bottom: 10px;
}

.woocommerce-Reviews .vs-comments-wrap {
  padding: 0;
  background-color: transparent;
  margin-top: 0;
}
.woocommerce-Reviews .vs-comment-item:first-child .vs-post-comment {
  margin-top: 19px;
}
.woocommerce-Reviews .woocommerce-Reviews-title {
  margin-bottom: 40px;
}

.vs-comments-wrap.vs-comment-form {
  margin: 0;
}

/* Medium Large devices */
@media (max-width: 1399px) {
  .comment-section {
    padding: 40px 40px 0px 40px;
  }
  .vs-comment-form.review-form .blog-inner-title {
    font-size: 30px;
  }
  .vs-comments-wrap .vs-post-comment {
    padding: 30px 30px 30px 30px;
    margin: 30px 0 30px 0;
  }
  .vs-comments-wrap .comment-avater {
    width: 100px;
    height: 100px;
    margin-right: 20px;
  }
  .comment-respond .form-control {
    height: 60px;
    font-size: 14px;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .vs-comments-wrap .vs-post-comment {
    display: block;
    padding: 30px 20px 30px 20px;
  }
  .vs-comments-wrap .star-rating {
    position: relative;
    top: 0;
    right: 0;
  }
  .vs-comments-wrap .comment-avater {
    margin-right: 0;
    margin-bottom: 20px;
  }
  .vs-comments-wrap .children {
    margin-left: 40px;
  }
  .vs-comments-wrap .review .vs-post-comment {
    padding: 30px;
  }
  .comment-respond .form-text {
    font-size: 14px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .vs-comments-wrap .children {
    margin-left: 20px;
  }
  .vs-comments-wrap .name {
    margin: -0.1em 0 7px 0;
    font-size: 24px;
  }
  .vs-comments-wrap .review-rating {
    position: relative;
    right: 0;
    top: 0;
    margin: 10px 0 10px 0;
    width: -moz-max-content;
    width: max-content;
  }
  .vs-comments-wrap .review .vs-post-comment {
    padding: 30px 20px;
  }
  .vs-comments-wrap .reply_and_edit {
    position: relative;
    top: 0;
    right: 0;
    margin-top: 20px;
  }
  .comment-respond textarea,
  .comment-respond input {
    height: 50px;
    padding-left: 20px;
    padding-right: 20px;
  }
  .comment-respond label {
    font-size: 14px;
  }
  .vs-comment-form.review-form .blog-inner-title {
    font-size: 26px;
  }
  .comment-section {
    padding: 40px 20px 0px 20px;
  }
}
/*------------------- 4.8. Hero Area  -------------------*/
.ls-container.ls-v6 .ls-bottom-slidebuttons {
  background-color: #fff;
  padding: 6px 9px;
  line-height: 1;
  border-radius: 9999px;
}

.ls-container.ls-v6 .ls-bottom-slidebuttons a {
  border: none !important;
  background-color: #B5B5B5;
  width: 13px !important;
  height: 13px !important;
}

.ls-container.ls-v6 .ls-bottom-slidebuttons a:hover,
.ls-container.ls-v6 .ls-bottom-slidebuttons a.ls-nav-active {
  border: none !important;
  background-color: var(--theme-color) !important;
}

.ls-container.ls-v6 .ls-bottom-nav-wrapper {
  text-align: right;
  max-width: var(--main-container);
  top: -73px !important;
}

@media (max-width: 1199px) {
  .ls-container.ls-v6 .ls-bottom-slidebuttons {
    display: none !important;
  }
}
@media (max-width: 1024px) {
  .ls-html-layer.ls-layer .vs-btn {
    padding: 10px 23px 12px 23px;
    font-size: 14px;
  }
}
@media (max-width: 767px) {
  .ls-html-layer.ls-layer .vs-btn {
    padding: 9px 22px 12px 22px;
    font-size: 13px;
  }
}
/*------------------- 4.9. Error  -------------------*/
.vs-error-wrapper {
  background-color: var(--smoke-color);
}

.error-number {
  font-size: 150px;
  font-weight: 600;
  display: block;
  line-height: 1;
  margin: -0.17em 0 17px 0;
  color: var(--theme-color);
}

.error-title {
  text-transform: capitalize;
  font-size: 60px;
  margin: -0.3em 0 20px 0;
}

.error-text {
  font-size: 24px;
  font-weight: 400;
  margin-bottom: 38px;
  max-width: 500px;
  text-transform: capitalize;
  color: var(--title-color);
}

.search-inline {
  position: relative;
  width: 100%;
  margin-bottom: 45px;
}
.search-inline input {
  background-color: var(--white-color);
  border: 2px solid #D9D9D9;
  height: 60px;
  padding: 0 0 0 40px;
  font-size: 16px;
  border-radius: 9999px;
}
.search-inline input:focus {
  border-color: var(--theme-color);
}
.search-inline button {
  position: absolute;
  right: 5px;
  top: 5px;
  border: none;
  color: var(--white-color);
  background-color: var(--vs-secondary-color);
  padding: 0;
  width: 50px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  font-size: 18px;
  border-radius: 50%;
}
.search-inline button:hover {
  background-color: var(--theme-color);
  color: var(--white-color);
}

/* Large devices */
@media (max-width: 1199px) {
  .error-number {
    font-size: 120px;
  }
  .error-title {
    font-size: 48px;
  }
  .error-text {
    font-size: 18px;
    margin-bottom: 25px;
    max-width: 100%;
  }
  .search-inline {
    margin-bottom: 30px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .error-content {
    margin-top: 50px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .error-number {
    font-size: 100px;
  }
  .error-title {
    font-size: 28px;
  }
  .error-text {
    font-size: 16px;
  }
  .search-inline input {
    padding: 0 0 0 25px;
  }
}
/*------------------- 4.10. About  -------------------*/
.list-style1 ul {
  margin: 0 0 30px 0;
}
.list-style1 li {
  position: relative;
  display: flex;
  margin-bottom: 12px;
}
.list-style1 li:before {
  content: var(--icon, url("data:image/svg+xml,%3Csvg width='25' height='23' viewBox='0 0 25 23' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9.6842 20.6753C9.59141 20.6753 9.50023 20.651 9.4197 20.6049C9.33917 20.5588 9.27209 20.4924 9.22512 20.4124C7.45633 17.399 2.74074 10.9826 2.69336 10.9182C2.61761 10.8152 2.58145 10.6884 2.59149 10.5609C2.60153 10.4335 2.65709 10.3139 2.74804 10.224L4.19806 8.79111C4.28662 8.7036 4.40305 8.6499 4.52711 8.63934C4.65116 8.62879 4.77499 8.66205 4.87706 8.73335L9.57343 12.0125C12.716 7.98275 15.6351 5.19864 17.5556 3.5558C19.7177 1.70658 21.0941 0.874056 21.1517 0.839774C21.2344 0.790135 21.3291 0.763914 21.4256 0.763916H23.7714C23.8794 0.763909 23.9847 0.796703 24.0736 0.85795C24.1624 0.919197 24.2306 1.006 24.269 1.10686C24.3074 1.20772 24.3142 1.31787 24.2886 1.4227C24.2629 1.52753 24.2061 1.62209 24.1254 1.69385C17.4636 7.62747 10.2191 20.2793 10.1467 20.4064C10.1005 20.4875 10.0338 20.5551 9.9532 20.6023C9.87262 20.6495 9.78104 20.6746 9.68766 20.6752L9.6842 20.6753Z' fill='%23E8063C'/%3E%3Cpath d='M9.58204 22.8931C4.2985 22.8931 0 18.5946 0 13.311C0 8.02746 4.2985 3.72901 9.58204 3.72901C10.6414 3.72784 11.6936 3.90269 12.6957 4.24644C12.8292 4.29232 12.9391 4.38938 13.0011 4.51624C13.063 4.64311 13.0721 4.7894 13.0262 4.92293C12.9803 5.05646 12.8833 5.1663 12.7564 5.22827C12.6295 5.29025 12.4832 5.29929 12.3497 5.25341C11.4588 4.9479 10.5234 4.79254 9.58156 4.79368C4.88503 4.79368 1.06419 8.61457 1.06419 13.311C1.06419 18.0075 4.88503 21.8284 9.58156 21.8284C14.2781 21.8284 18.0989 18.0075 18.0989 13.311C18.0991 12.7348 18.0412 12.1599 17.9261 11.5953C17.9121 11.5268 17.9118 11.4562 17.925 11.3875C17.9383 11.3189 17.965 11.2535 18.0035 11.1952C18.0421 11.1368 18.0917 11.0866 18.1497 11.0475C18.2076 11.0083 18.2727 10.981 18.3412 10.967C18.4097 10.953 18.4803 10.9526 18.549 10.9659C18.6176 10.9792 18.683 11.0059 18.7413 11.0444C18.7997 11.083 18.8498 11.1326 18.889 11.1905C18.9281 11.2485 18.9555 11.3136 18.9695 11.3821C19.0989 12.0169 19.164 12.6632 19.1639 13.311C19.1641 18.5946 14.8656 22.8931 9.58204 22.8931Z' fill='%23E8063C'/%3E%3C/svg%3E%0A"));
  margin-right: 10px;
  font-family: var(--icon-font);
}

.list-style2 ul {
  margin-bottom: 30px;
}
.list-style2 li {
  margin-bottom: 5px;
  display: flex;
  align-items: center;
}
.list-style2 li:before {
  content: "";
  width: 10px;
  height: 10px;
  background-color: var(--theme-color);
  display: inline-block;
  margin-right: 10px;
  border-radius: 50%;
}

.vs-circle {
  width: var(--circle-size, 315px);
  height: var(--circle-size, 315px);
  border: var(--circle-border-size, 60px) solid var(--circle-border-color, var(--theme-color2));
  border-radius: var(--circle-radius, 50%);
}

.vs-media {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
}
.vs-media .media-icon {
  width: var(--icon-size, 94px);
  height: var(--icon-size, 94px);
  line-height: var(--icon-size, 94px);
  background-color: var(--icon-bg, #F0F6FA);
  color: var(--title-color);
  border-radius: 50%;
  text-align: center;
  transition: all ease 0.4s;
  display: inline-block;
}
.vs-media .media-icon img {
  max-width: calc(var(--icon-size, 94px) - 20px);
  transition: all ease 0.4s;
  filter: none;
}
.vs-media:hover .media-icon {
  background-color: var(--theme-color);
  color: var(--white-color);
}
.vs-media:hover .media-icon img {
  filter: brightness(0) invert(1);
}

.exp-pill {
  background-color: var(--vs-secondary-color);
  width: 190px;
  height: 190px;
  border-radius: 50%;
  position: relative;
  z-index: 1;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.exp-pill:before {
  content: "";
  position: absolute;
  left: 5px;
  top: 5px;
  right: 5px;
  bottom: 5px;
  border: 2px dashed var(--white-color);
  z-index: -1;
  border-radius: inherit;
  animation: rotate 40s infinite linear;
}
.exp-pill .exp-number {
  font-size: 50px;
  font-weight: 600;
  line-height: 1;
  margin: 0;
  color: var(--white-color);
  font-family: var(--title-font);
  margin-bottom: 5px;
}
.exp-pill .exp-text {
  color: var(--white-color);
  font-size: 22px;
  font-weight: 600;
  margin: 0 0 5px 0;
  line-height: 1;
}

.media-style1 .media-icon {
  margin-right: 20px;
}
.media-style1 .media-label {
  font-size: 60px;
  font-weight: 600;
  color: var(--theme-color);
  line-height: 1;
  margin: 0;
  font-family: var(--title-font);
}
.media-style1 .media-title {
  font-size: 18px;
  font-weight: 600;
  font-family: var(--title-font);
  margin: 0;
}
.media-style1.layout2 {
  align-items: center;
}
.media-style1.layout2 .media-label {
  font-size: 90px;
}
.media-style1.layout2 .media-title {
  color: var(--title-color);
}

.media-style2 {
  display: flex;
  align-items: center;
}
.media-style2 .media-icon {
  margin-right: 15px;
}
.media-style2 .media-label {
  color: var(--theme-color);
  font-size: 20px;
  font-weight: 600;
  line-height: 1;
  display: block;
  font-family: var(--title-font);
  letter-spacing: 0.02em;
  margin-bottom: 13px;
}
.media-style2 .media-title {
  font-family: var(--title-font);
  font-size: 46px;
  font-weight: 600;
  color: #000;
  margin: 0;
  line-height: 1;
}

.media-style3 {
  display: flex;
  align-items: center;
  padding: 15px 50px 15px 20px;
  background-color: var(--white-color);
  border-radius: 25px;
  text-align: left;
}
.media-style3 .media-icon {
  margin-right: 15px;
}
.media-style3 .media-title {
  font-size: 18px;
  font-weight: 700;
  color: var(--vs-secondary-color);
  line-height: 1;
  margin: 0 0 15px 0;
}
.media-style3 .media-info {
  font-size: 18px;
  color: var(--title-color);
  margin: 0;
  line-height: 1;
}

.img-box1 {
  position: relative;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
  margin-bottom: 30px;
}
.img-box1 [class*=img-] img {
  width: 100%;
}
.img-box1 .img-1 {
  border-radius: var(--radius-outer, 119px) var(--radius-inner, 50px) 0 var(--radius-inner, 50px);
}
.img-box1 .img-2 {
  border-radius: var(--radius-inner, 50px) var(--radius-outer, 119px) var(--radius-inner, 50px) 0;
}
.img-box1 .img-3 {
  border-radius: var(--radius-inner, 50px) 0 var(--radius-inner, 50px) var(--radius-outer, 119px);
}
.img-box1 .img-4 {
  border-radius: 0 var(--radius-inner, 50px) var(--radius-outer, 119px) var(--radius-inner, 50px);
}
.img-box1 .vs-circle {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.img-box2 {
  position: relative;
  z-index: 1;
  margin-bottom: 30px;
}
.img-box2 .vs-circle {
  position: absolute;
  right: -10%;
  top: 3%;
  z-index: -1;
}
.img-box2.style2 .vs-circle {
  right: auto;
  left: -10%;
}

.img-box3 {
  position: relative;
  z-index: 1;
  margin-bottom: 30px;
}
.img-box3 .img-1 {
  border-radius: 290px 290px 94px 298px;
  overflow: hidden;
  position: relative;
}
.img-box3 .img-1 img {
  width: 100%;
}
.img-box3 .vs-circle {
  position: absolute;
  left: 0;
  top: -4%;
  z-index: -1;
}

.img-box4 .img-1 {
  border-radius: 50px;
}

.img-box5 {
  position: relative;
  margin-bottom: 30px;
}
.img-box5 .img-1 {
  -webkit-clip-path: url(#ab-shape1);
          clip-path: url(#ab-shape1);
}
.img-box5 .img-1 img {
  width: 100%;
}
.img-box5 .exp-pill {
  position: absolute;
  top: 0;
  left: 0;
}

.img-box6 {
  margin: 27px 0 86px 0;
}
.img-box6 .img-2,
.img-box6 .img-1 {
  width: -moz-max-content;
  width: max-content;
  max-width: 100%;
  border-radius: 30px;
  overflow: hidden;
  border: 5px solid var(--white-color);
  box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.25);
}
.img-box6 .img-1 {
  margin-left: 114px;
  transform: rotate(-8.183deg);
}
.img-box6 .img-2 {
  margin: -52px 0 0 -36px;
  margin-left: 36px;
  transform: rotate(18.31deg);
}

.img-box7 {
  overflow: hidden;
  margin-bottom: 30px;
  border-radius: 30px;
}

.info-style1 {
  margin-bottom: 25px;
}
.info-style1 .info-text,
.info-style1 .info-title {
  font-size: 18px;
  font-family: var(--title-font);
  font-weight: 600;
  margin: 0 0 15px 0;
  line-height: 1;
}
.info-style1 .info-title {
  color: var(--theme-color);
}

.info-style2 {
  padding: 30px 20px;
  border: 3px solid var(--theme-color2);
  border-radius: 30px;
  text-align: center;
  margin-bottom: 30px;
  transition: all ease 0.4s;
}
.info-style2 .info-icon {
  width: var(--icon-size, 120px);
  height: var(--icon-size, 120px);
  line-height: var(--icon-size, 120px);
  background-color: var(--theme-color2);
  border-radius: 50%;
  display: inline-block;
  margin-bottom: 27px;
  transition: all ease 0.4s;
}
.info-style2 .info-icon img {
  filter: none;
  transition: all ease 0.4s;
}
.info-style2 .info-title {
  font-size: 20px;
  line-height: 1;
  margin-bottom: 4px;
}
.info-style2 .info-text {
  font-size: 18px;
  margin: 0 0 -0.3em 0;
}
.info-style2:hover {
  border-color: var(--theme-color);
}
.info-style2:hover .info-icon {
  background-color: var(--theme-color);
}
.info-style2:hover .info-icon img {
  filter: brightness(0) invert(1);
}

/* Medium Large devices */
@media (max-width: 1399px) {
  .img-box2 .vs-circle {
    right: 0;
    top: 3%;
  }
  .img-box2.style2 .vs-circle {
    left: 0;
  }
  .media-style2 .media-title {
    font-size: 36px;
  }
  .media-style2 .media-label {
    font-size: 16px;
    letter-spacing: 0;
    margin-bottom: 10px;
  }
  .img-box6 {
    margin-top: 0;
    margin-bottom: 30px;
  }
  .img-box6 .img-2,
  .img-box6 .img-1 {
    width: 100%;
    margin: 0;
    transform: none;
  }
  .img-box6 .img-2 img,
  .img-box6 .img-1 img {
    width: 100%;
  }
  .img-box6 .img-1 {
    margin-bottom: 30px;
  }
  .media-style1.layout2 .media-label {
    font-size: 60px;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .media-style1 .media-icon {
    --icon-size: 60px;
  }
  .media-style1 .media-label {
    font-size: 46px;
  }
  .media-style1 .media-title {
    font-size: 16px;
  }
  .vs-circle {
    --circle-size: 350px;
    --circle-border-size: 40px;
  }
  .img-box3 .vs-circle {
    --circle-size: 200px;
    --circle-border-size: 20px;
  }
  .img-box6 {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
    margin-bottom: 40px;
  }
  .img-box6 .img-1 {
    margin-bottom: 0;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .info-style2 {
    padding: 25px 15px;
  }
  .info-style2 .info-icon {
    --icon-size: 80px;
  }
  .info-style2 .info-icon img {
    max-width: 50%;
  }
  .info-style2 .info-title {
    font-size: 16px;
  }
  .info-style2 .info-text {
    font-size: 15px;
  }
  .media-style2 {
    display: block;
    text-align: center;
  }
  .media-style2 .media-icon {
    margin-right: 0;
    margin-bottom: 15px;
  }
  .media-style3 {
    padding: 15px 20px 15px 20px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .list-style1 li {
    display: flex;
    margin-bottom: 4px;
  }
  .media-style2 .media-title {
    font-size: 25px;
  }
  .media-style3 {
    padding: 20px 15px 20px 15px;
  }
  .exp-pill {
    width: 130px;
    height: 130px;
  }
  .exp-pill .exp-number {
    font-size: 32px;
  }
  .exp-pill .exp-text {
    font-size: 16px;
  }
  .vs-circle {
    --circle-size: 200px;
    --circle-border-size: 30px;
  }
  .vs-media {
    display: block;
  }
  .vs-media .media-icon {
    margin-right: 0;
    margin-bottom: 15px;
  }
  .img-box1 {
    gap: 15px;
    --radius-outer: 80px;
    --radius-inner: 30px;
  }
  .img-box4 .img-1 {
    border-radius: 20px;
  }
  .img-box6 {
    display: block;
  }
  .img-box6 .img-1 {
    margin-bottom: 20px;
  }
  .media-style1.layout2 {
    text-align: center;
  }
  .media-style1.layout2 .media-title {
    font-size: 15px;
  }
}
/*------------------- 4.11. Simple Sections  -------------------*/
.map-style1 {
  border-radius: 30px;
  overflow: hidden;
  line-height: 0;
  /* Small devices */
}
@media (max-width: 767px) {
  .map-style1 {
    border-radius: 20px;
  }
}

.vs-social a {
  display: inline-block;
  position: relative;
  width: var(--icon-size, 50px);
  height: var(--icon-size, 50px);
  line-height: var(--icon-size, 50px);
  margin-right: 5px;
  font-size: 18px;
  text-align: center;
  background-color: var(--theme-color2);
  color: var(--title-color);
  border-radius: 50%;
  z-index: 1;
  transition: all ease 0.4s;
}
.vs-social a:last-child {
  margin-right: 0;
}
.vs-social a::before {
  content: "";
  position: absolute;
  left: 3px;
  top: 3px;
  bottom: 3px;
  right: 3px;
  background-color: var(--vs-secondary-color);
  border-radius: inherit;
  transition: all ease 0.3s;
  transform: scale(0.4);
  opacity: 0;
  visibility: hidden;
  z-index: -1;
}
.vs-social a:hover {
  color: var(--white-color);
  background-color: var(--theme-color);
}
.vs-social a:hover:before {
  transform: scale(1);
  opacity: 1;
  visibility: visible;
}

.brand-style1 {
  background-color: var(--white-color);
  border: 3px solid #F1F1F1;
  height: 170px;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all ease 0.4s;
  border-radius: 30px;
}
.brand-style1:hover {
  border-color: var(--theme-color2);
}

.social-style1 .social-title {
  color: var(--white-color);
  display: block;
  margin-bottom: 20px;
  margin-top: -0.22em;
}
.social-style1 a:hover {
  background-color: var(--white-color);
}

.table-style1 {
  background-color: var(--vs-secondary-color);
  padding: 1px 155px 85px 145px;
  border-radius: 274px 30px 274px 274px;
  text-align: center;
  margin-top: calc(var(--icon-size, 102px) / 2);
}
.table-style1 .table-icon {
  display: block;
  width: var(--icon-size, 102px);
  height: var(--icon-size, 102px);
  line-height: var(--icon-size, 102px);
  font-size: var(--icon-font-size, 50px);
  background-color: var(--theme-color);
  color: var(--white-color);
  text-align: center;
  border-radius: 50%;
  margin: calc(var(--icon-size, 102px) / -2) auto 40px auto;
}
.table-style1 .sec-title {
  color: var(--white-color);
  margin-bottom: 5px;
}
.table-style1 .sec-text {
  color: var(--white-color);
  margin-bottom: 20px;
}
.table-style1 .tr {
  display: flex;
  justify-content: space-between;
  background-color: var(--white-color);
  margin-bottom: 5px;
  border-radius: 9999px;
  padding: 11px 0;
  transition: all ease 0.4s;
}
.table-style1 .tr:hover {
  background-color: var(--theme-color2);
}
.table-style1 .th,
.table-style1 .td {
  flex: 1;
  font-size: 18px;
  font-family: var(--title-font);
  font-weight: 600;
  color: var(--title-color);
  line-height: 1.5;
}
.table-style1 .th {
  border-right: 1px solid #A7A7A7;
}

.countdown-style1 ul {
  padding: 0;
  margin: 0;
  list-style-type: none;
}
.countdown-style1 li {
  color: var(--title-color);
  background-color: var(--theme-color2);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  font-size: 16px;
  font-family: var(--title-font);
  font-weight: 600;
  width: 110px;
  height: 110px;
  border-radius: 30px;
  transition: all ease 0.4s;
  margin-right: 7px;
  line-height: 1;
  padding-bottom: 5px;
}
.countdown-style1 li:last-child {
  margin-right: 0;
}
.countdown-style1 li span {
  font-size: 50px;
  display: block;
  margin-bottom: 3px;
}
.countdown-style1 li:hover {
  background-color: var(--theme-color);
  color: var(--white-color);
}

/* Large devices */
@media (max-width: 1199px) {
  .countdown-style1 li {
    width: auto;
    height: auto;
    padding: 20px 15px;
    font-size: 14px;
  }
  .countdown-style1 li span {
    font-size: 40px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .table-style1 {
    padding: 1px 40px 60px 40px;
    border-radius: 30px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .table-style1 {
    padding: 1px 15px 60px 15px;
    border-radius: 30px;
  }
  .table-style1 .table-icon {
    margin-bottom: 30px;
  }
  .table-style1 .th,
  .table-style1 .td {
    font-size: 14px;
  }
  .countdown-style1 li {
    font-size: 12px;
    padding: 10px 10px;
    border-radius: 10px;
  }
  .countdown-style1 li span {
    font-size: 22px;
  }
}
/*------------------- 4.12. Popup Side Menu  -------------------*/
.sidemenu-wrapper {
  position: fixed;
  z-index: 99999;
  right: 0;
  top: 0;
  height: 100%;
  width: 0;
  background-color: rgba(0, 0, 0, 0.75);
  opacity: 0;
  visibility: hidden;
  transition: all ease 0.8s;
}
.sidemenu-wrapper .closeButton {
  display: inline-block;
  border: 1px solid;
  width: 50px;
  height: 50px;
  line-height: 50px;
  font-size: 24px;
  padding: 0;
  position: absolute;
  top: 20px;
  right: 20px;
  background-color: var(--black-color);
  color: var(--white-color);
  border-radius: 50%;
  transform: rotate(0);
  transition: all ease 0.4s;
  z-index: 2;
}
.sidemenu-wrapper .closeButton:hover {
  color: var(--white-color);
  background-color: var(--theme-color);
  border-color: transparent;
  transform: rotate(90deg);
}
.sidemenu-wrapper .sidemenu-content {
  background-color: var(--body-bg);
  width: 450px;
  margin-left: auto;
  padding: 40px 30px 80px 30px;
  height: 100%;
  overflow: scroll;
  position: relative;
  right: -500px;
  cursor: auto;
  transition-delay: 1s;
  transition: right ease 1s;
}
.sidemenu-wrapper .sidemenu-content::-webkit-scrollbar-track {
  box-shadow: inset 0 0 1px rgba(0, 0, 0, 0.1);
  background-color: #F5F5F5;
}
.sidemenu-wrapper .sidemenu-content::-webkit-scrollbar {
  width: 2px;
  background-color: #F5F5F5;
}
.sidemenu-wrapper .widget {
  padding: 0;
  border: none;
  background-color: transparent;
}
.sidemenu-wrapper.show {
  opacity: 1;
  visibility: visible;
  width: 100%;
  transition: all ease 0.8s;
}
.sidemenu-wrapper.show .sidemenu-content {
  right: 0;
  opacity: 1;
  visibility: visible;
}

/*------------------- 4.13. Popup Side Menu  -------------------*/
.popup-search-box {
  position: fixed;
  top: 0;
  left: 50%;
  background-color: rgba(0, 0, 0, 0.95);
  height: 0;
  width: 0;
  overflow: hidden;
  z-index: 99999;
  opacity: 0;
  visibility: hidden;
  border-radius: 50%;
  transform: translateX(-50%);
  transition: all ease 0.4s;
}
.popup-search-box button.searchClose {
  width: 60px;
  height: 60px;
  line-height: 60px;
  position: absolute;
  top: 40px;
  right: 40px;
  border: none;
  color: var(--theme-color);
  background-color: var(--white-color);
  font-size: 30px;
  border-radius: 50%;
  transform: rotate(0);
  transition: all ease 0.4s;
}
.popup-search-box button.searchClose:hover {
  color: var(--white-color);
  background-color: var(--theme-color);
  transform: rotate(90deg);
}
.popup-search-box form {
  position: absolute;
  top: 50%;
  left: 50%;
  display: inline-block;
  padding-bottom: 40px;
  cursor: auto;
  width: 100%;
  max-width: 700px;
  transform: translate(-50%, -50%) scale(0);
  transition: transform ease 0.4s;
  /* Large devices */
}
@media (max-width: 1199px) {
  .popup-search-box form {
    max-width: 600px;
  }
}
.popup-search-box form input {
  font-size: 18px;
  height: 70px;
  width: 100%;
  border: 2px solid var(--theme-color);
  background-color: transparent;
  padding-left: 30px;
  color: #fff;
  border-radius: 50px;
}
.popup-search-box form input::-moz-placeholder {
  color: #fff;
}
.popup-search-box form input::placeholder {
  color: #fff;
}
.popup-search-box form button {
  position: absolute;
  top: 0px;
  background-color: transparent;
  border: none;
  color: #fff;
  font-size: 24px;
  right: 12px;
  color: var(--white-color);
  cursor: pointer;
  width: 70px;
  height: 70px;
  transition: all ease 0.4s;
  transform: scale(1.001);
}
.popup-search-box form button:hover {
  transform: scale(1.1);
}
.popup-search-box.show {
  opacity: 1;
  visibility: visible;
  width: 100.1%;
  height: 100%;
  transition: all ease 0.4s;
  border-radius: 0;
}
.popup-search-box.show form {
  transition-delay: 0.5s;
  transform: translate(-50%, -50%) scale(1);
}

/*------------------- 4.14. Serive Area  -------------------*/
.service-style1 {
  --theme-color: #76AD1E;
  margin-bottom: 30px;
}
.service-style1 .service-body {
  text-align: center;
  padding-bottom: 115px;
  position: relative;
}
.service-style1 .service-img {
  border-radius: 30px 30px 0 0;
  overflow: hidden;
}
.service-style1 .service-img img {
  width: 100%;
  transform: scale(1.001);
  transition: all ease 0.4s;
}
.service-style1 .service-content {
  padding: 0.1px 20px 20px 20px;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  margin-top: -145px;
  background-color: var(--theme-color);
  border-radius: 150px 150px 30px 30px;
}
.service-style1 .service-icon {
  width: var(--icon-size, 100px);
  height: var(--icon-size, 100px);
  line-height: var(--icon-size, 100px);
  background-color: var(--white-color);
  text-align: center;
  border-radius: var(--icon-radius, 10px 50px 10px 10px);
  display: inline-block;
  margin-top: -35px;
  margin-bottom: 28px;
  transition: all ease 0.4s;
}
.service-style1 .service-title {
  color: var(--white-color);
  font-size: 26px;
  margin-bottom: 12px;
}
.service-style1 .service-title a {
  color: inherit;
}
.service-style1 .service-title a:hover {
  text-decoration: underline;
}
.service-style1 .service-text {
  color: var(--white-color);
  margin: 0 0 17px 0;
}
.service-style1 .service-bottom {
  overflow: hidden;
}
.service-style1 .service-btn {
  display: block;
  border: 3px solid var(--white-color);
  color: var(--white-color);
  border-radius: 9999px;
  height: 50px;
  line-height: 43px;
  font-family: var(--title-font);
  font-weight: 600;
  transition: all ease 0.4s;
  margin-bottom: -50px;
  opacity: 0;
  visibility: hidden;
}
.service-style1 .service-btn:hover {
  background-color: var(--white-color);
  color: var(--theme-color);
}
.service-style1:hover .service-btn {
  margin-bottom: 0;
  opacity: 1;
  visibility: visible;
}
.service-style1:hover .service-icon {
  background-color: var(--theme-color2);
}
.service-style1:hover .service-img img {
  transform: scale(1.15);
  transition: all ease 0.4s;
}
.service-style1:nth-child(2n+2) {
  --theme-color: #18C0CB;
  --icon-radius: 10px 10px 10px 50px;
}
.service-style1:nth-child(3n+3) {
  --theme-color: #490D59;
  --icon-radius: 50px 10px 10px 10px;
}
.service-style1:nth-child(4n+4) {
  --theme-color: #E93500;
  --icon-radius: 10px 10px 50px 10px;
}

.service-style2 {
  display: flex;
  margin-bottom: 30px;
}
.service-style2 .service-icon {
  position: relative;
  display: inline-block;
  width: 76px;
  height: 78px;
  line-height: 76px;
  text-align: center;
  margin-right: 20px;
  border-radius: 50%;
  z-index: 1;
}
.service-style2 .service-shape1,
.service-style2 .service-shape2,
.service-style2 .service-shape3 {
  background-color: var(--theme-color2);
  position: absolute;
  left: var(--border-size, 0);
  top: var(--border-size, 0);
  right: var(--border-size, 0);
  bottom: var(--border-size, 0);
  -webkit-clip-path: url(#service-clip1);
          clip-path: url(#service-clip1);
  z-index: -1;
  transition: all ease 0.4s;
}
.service-style2 .service-shape2 {
  --border-size: 2px;
  background-color: var(--white-color);
}
.service-style2 .service-shape3 {
  --border-size: 5px;
  background-color: var(--vs-secondary-color);
}
.service-style2:hover .service-shape3 {
  background-color: var(--theme-color);
}
.service-style2:hover .service-shape1 {
  background-color: var(--vs-secondary-color);
}
.service-style2 .service-content {
  flex: 1;
}
.service-style2 .service-title {
  font-size: 26px;
  margin: -0.2em 0 8px 0;
}
.service-style2 .service-text {
  margin: 0;
}

.service-description {
  margin-top: 30px;
  margin-bottom: 42px;
}

/* Large devices */
@media (max-width: 1199px) {
  .service-style1 .service-body {
    padding-bottom: 90px;
  }
  .service-style2 {
    display: block;
    text-align: center;
  }
  .service-style2 .service-icon {
    margin-right: 0;
    margin-bottom: 20px;
  }
  .gy-30 .service-style2 {
    margin-bottom: 0;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .service-style1 .service-content {
    position: relative;
  }
  .service-style1 .service-body {
    padding-bottom: 0;
  }
  .service-style1 .service-btn {
    margin-bottom: 0;
    opacity: 1;
    visibility: visible;
  }
  .service-style2 .service-title {
    font-size: 22px;
  }
}
/*------------------- 4.15. Category  -------------------*/
.category-style1 {
  position: relative;
  z-index: 1;
  padding: 40px 35px 40px 35px;
  border-radius: 30px;
  margin: 10px 0 40px 0;
}
.category-style1 .category-bg3,
.category-style1 .category-bg2,
.category-style1 .category-bg1 {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  border-radius: 30px;
  opacity: 1;
  visibility: visible;
  transform-origin: top center;
  transition: all ease 0.4s;
  box-shadow: 0 10px 4px rgba(120, 51, 138, 0.3);
}
.category-style1 .category-bg1 {
  background-color: var(--white-color);
  z-index: -1;
}
.category-style1 .category-bg2 {
  background-color: var(--vs-secondary-color);
  transform: scale(0.9) translateY(-10px);
  z-index: -2;
}
.category-style1 .category-bg3 {
  background-color: var(--theme-color);
  transform: scale(0.7) translateY(-10px);
  z-index: -3;
  box-shadow: none;
}
.category-style1:hover .category-bg1 {
  opacity: 0;
  visibility: hidden;
  transform: scale(1.08);
}
.category-style1:hover .category-bg2 {
  transform: scale(1.001) translateY(0);
}
.category-style1:hover .category-bg3 {
  transform: scale(0.9) translateY(-10px);
}
.category-style1 .category-grade {
  width: 90px;
  height: 90px;
  display: inline-flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: var(--theme-color);
  border-radius: 50%;
  line-height: 1;
  font-weight: 600;
  text-transform: uppercase;
  color: var(--white-color);
  font-family: var(--title-font);
  font-size: 14px;
  margin-bottom: 22px;
  box-shadow: inset 5px 0 0 1px rgba(0, 0, 0, 0.18);
}
.category-style1 .grade-name {
  display: block;
  margin-top: 0px;
  font-size: 40px;
}
.category-style1 .category-name {
  margin: 0 0 10px 0;
}
.category-style1 .category-label {
  margin: 0;
  line-height: 1;
  font-weight: 500;
  font-family: var(--title-font);
  transition: all ease 0.4s;
}
.category-style1:hover .category-label,
.category-style1:hover .category-name a {
  color: var(--white-color);
}

.category-style2 {
  position: relative;
  margin-bottom: 30px;
}
.category-style2 .category-img {
  width: 270px;
  height: 270px;
  border-radius: 50%;
  overflow: hidden;
}
.category-style2 .category-img img {
  width: 100%;
  transition: all ease 0.4s;
  transform: scale(1.001);
}
.category-style2 .category-title {
  margin-bottom: 5px;
  margin-top: -0.2em;
}
.category-style2 .category-content {
  background-color: var(--white-color);
  border: 2px solid var(--vs-secondary-color);
  position: absolute;
  right: 0;
  bottom: 30px;
  width: 270px;
  max-width: 100%;
  border-radius: 9999px;
  padding: 23px 48px 25px 48px;
  transition: all ease 0.4s;
}
.category-style2 .category-text {
  margin: 0;
  font-weight: 500;
  color: var(--title-color);
  font-size: 18px;
  line-height: 1;
  text-transform: uppercase;
}
.category-style2:hover .category-img img {
  transform: scale(1.15);
}
.category-style2:hover .category-content {
  border-color: var(--theme-color);
}

/* Medium Large devices */
@media (max-width: 1399px) {
  .category-style2 .category-title {
    font-size: 28px;
  }
  .category-style2 .category-text {
    font-size: 16px;
  }
  .category-style2 .category-content {
    bottom: 20px;
    width: 220px;
    padding: 23px 30px 25px 30px;
    transition: all ease 0.4s;
  }
  .category-style2 .category-text {
    font-size: 16px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .category-style1 {
    padding: 30px 25px 30px 25px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .category-style1 {
    padding: 30px 15px 30px 15px;
    text-align: center;
  }
  .category-style2 {
    text-align: center;
  }
  .category-style2 .category-img {
    width: 100%;
    height: auto;
  }
  .category-style2 .category-content {
    position: relative;
    right: 0;
    bottom: 0;
    width: 100%;
    margin: -30px auto 0 auto;
    padding: 15px 10px 15px 10px;
  }
  .category-style2 .category-title {
    font-size: 18px;
  }
  .category-style2 .category-text {
    font-size: 12px;
  }
}
/*------------------- 4.16. Forms  -------------------*/
.form-wrap1.space {
  /* Medium devices */
}
@media (min-width: 991px) {
  .form-wrap1.space {
    padding-bottom: 480px;
  }
}
@media (max-width: 991px) {
  .form-wrap1.space {
    background-image: none !important;
  }
}

.form-style1 .form-group {
  position: relative;
  margin: 0 auto;
  max-width: 880px;
}
.form-style1 input {
  height: 70px;
  border-color: #D8D8D8;
  border-radius: 9999px;
  padding-left: 50px;
}
.form-style1 .vs-btn {
  position: absolute;
  top: 50%;
  right: 8px;
  transform: translateY(-50%);
}

.form-style2 .form-title {
  color: var(--white-color);
  margin: -0.2em 0 18px 0;
}
.form-style2 .form-group {
  display: flex;
  margin: 0;
}
.form-style2 input {
  border: 2px solid var(--white-color);
  color: var(--white-color);
  background-color: transparent;
  height: 60px;
  border-radius: 9999px;
  margin-right: 10px;
  flex: 1;
}
.form-style2 input::-moz-placeholder {
  color: var(--white-color);
}
.form-style2 input::placeholder {
  color: var(--white-color);
}

.form-style3 {
  border: 3px solid var(--vs-secondary-color);
  padding: 30px 30px 3px 30px;
  margin-bottom: 30px;
  background-color: var(--white-color);
  border-radius: 30px;
}
.form-style3 .form-group {
  margin-bottom: 27px;
}
.form-style3 input {
  border: 1px solid rgba(153, 153, 153, 0.5);
  padding-right: 20px;
  padding-left: 20px;
  border-radius: 10px;
}
.form-style3 input[type=checkbox] ~ label {
  font-weight: 500;
  margin: 0;
}
.form-style3 input[type=checkbox] ~ label:before {
  background-color: #D9D9D9;
  border: none;
}
.form-style3 input[type=checkbox]:checked ~ label:before {
  background-color: var(--theme-color);
}
.form-style3 label {
  font-weight: 400;
}
.form-style3 .required {
  font-size: 14px;
  color: var(--error-color);
}
.form-style3.layout2 {
  padding: 0;
  border: none;
}

/* Small devices */
@media (max-width: 767px) {
  .form-style1 .form-group {
    text-align: center;
  }
  .form-style1 input {
    margin-bottom: 20px;
    height: 50px;
    padding-left: 30px;
    font-size: 14px;
  }
  .form-style1 .vs-btn {
    position: relative;
    top: 0;
    transform: none;
  }
  .form-style2 .form-group {
    display: block;
  }
  .form-style2 .vs-btn {
    margin-top: 10px;
  }
  .form-style2 input {
    height: 50px;
  }
  .form-style3 {
    padding: 30px 15px 3px 15px;
  }
}
/*------------------- 4.17. Classes  -------------------*/
.class-style1 .class-info,
.class-style2 .class-info {
  color: var(--vs-secondary-color);
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 15px 0;
  line-height: 1;
}
.class-style1 .class-info .info,
.class-style2 .class-info .info {
  font-weight: 400;
  color: var(--body-color);
}
.class-style1 .class-title,
.class-style2 .class-title {
  text-transform: capitalize;
}
.class-style1 .class-price,
.class-style2 .class-price {
  font-size: 50px;
  font-weight: 600;
  font-family: var(--title-font);
  color: var(--theme-color);
  margin: 25px 0 0 0;
  line-height: 1;
}
.class-style1 .class-price .duration,
.class-style2 .class-price .duration {
  font-weight: 500;
  font-size: 16px;
  color: var(--title-color);
}

.class-style1 {
  display: flex;
  align-items: center;
}
.class-style1 .class-content {
  flex: 1;
  background-color: var(--white-color);
  border: 2px solid #C5C5C5;
  padding: 33px 40px;
  position: relative;
  z-index: 1;
  border-radius: 35px;
  transition: all ease 0.4s;
  box-shadow: 0px 4px 50px 0px rgba(0, 0, 0, 0.1);
}
.class-style1 .class-img + .class-content {
  margin-left: -85px;
}
.class-style1:hover .class-content {
  border-color: var(--theme-color);
  box-shadow: none;
}
.class-style1 .class-title {
  font-size: 28px;
}
.class-style1 .class-img {
  overflow: hidden;
  border-radius: 35px;
  min-width: 100px;
}
.class-style1 .class-img img {
  width: 100%;
  transform: scale(1.001);
  transition: all ease 0.4s;
}
.class-style1:hover .class-img img {
  transform: scale(1.15);
}
.class-style1 .class-btn {
  display: inline-block;
  position: absolute;
  right: 20px;
  bottom: 20px;
  width: 50px;
  height: 50px;
  line-height: 50px;
  font-size: 20px;
  text-align: center;
  border: 2px solid var(--border-color);
  background-color: var(--white-color);
  color: var(--body-color);
  border-radius: 50%;
  transition: all ease 0.4s;
}
.class-style1:hover .class-btn {
  background-color: var(--theme-color);
  color: var(--white-color);
  border-color: transparent;
}
.class-style1:hover .class-btn:hover {
  background-color: var(--vs-secondary-color);
  color: var(--white-color);
}

.class-style2 {
  background: rgba(255, 255, 255, 0.5);
  border-radius: 30px;
  margin-bottom: 30px;
  padding: 40px 35px 34px 35px;
  box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.15);
  transition: all ease 0.4s;
}
.class-style2:hover {
  background-color: var(--white-color);
}
.class-style2 .class-title {
  margin: -0.2em 0 23px 0;
  text-transform: capitalize;
}
@media (min-width: 1399px) {
  .class-style2 .class-title {
    font-size: 24px;
  }
}
.class-style2 .class-img {
  position: relative;
  margin-bottom: 28px;
}
.class-style2 .class-img .img {
  border-radius: 50%;
  overflow: hidden;
  position: relative;
}
.class-style2 .class-img .img::before {
  content: "";
  position: absolute;
  top: 7px;
  right: 7px;
  bottom: 7px;
  left: 7px;
  border: 3px dashed var(--white-color);
  border-radius: inherit;
  display: block;
  z-index: 1;
  pointer-events: none;
  animation: rotate 45s infinite linear;
  transition: all ease 0.4s;
}
.class-style2 .class-img .img img {
  width: 100%;
  transform: scale(1.001);
  transition: all ease 0.4s;
}
.class-style2:hover .class-img .img:before {
  border-color: var(--theme-color);
}
.class-style2:hover .class-img .img img {
  transform: scale(1.15);
}
.class-style2 .class-number {
  display: inline-block;
  width: 50px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  background-color: var(--vs-secondary-color);
  color: var(--white-color);
  font-size: 16px;
  font-weight: 600;
  font-family: var(--title-font);
  border-radius: 50%;
  position: absolute;
  left: 10px;
  top: 0;
  z-index: 2;
  transition: all ease 0.4s;
}
.class-style2:hover .class-number {
  background-color: var(--theme-color);
}

.class-bar {
  background-color: var(--smoke-color);
  padding: 20px;
  margin: 22px 0 30px 0;
  border-radius: 25px 76px 25px 25px;
}
.class-bar .info-style1 {
  margin: 0;
}
.class-bar .info-title {
  font-family: var(--body-font);
  color: var(--vs-secondary-color);
  font-weight: 700;
}
.class-bar .info-text {
  font-weight: 400;
  font-family: var(--body-font);
  margin: 0;
}

.lession-price {
  font-size: 50px;
  color: var(--theme-color);
  font-weight: 600;
  line-height: 1;
  margin: 0 0 11px 0;
  font-family: var(--title-font);
}
.lession-price .duration {
  font-size: 16px;
  color: #777777;
  text-transform: capitalize;
}

.lession-title {
  margin: -0.2em 0 10px 0;
}

.lession-social {
  margin-bottom: 30px;
}

/* Medium Large devices */
@media (max-width: 1399px) {
  .class-style1 .class-content {
    padding: 27px 25px;
  }
  .class-style1 .class-img + .class-content {
    margin-left: -120px;
  }
  .class-style2 {
    padding: 35px 25px 34px 25px;
  }
  .class-style2 .class-title {
    font-size: 22px;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .class-style1 {
    display: block;
  }
  .class-style1 .class-content {
    margin: -60px 20px 0 20px;
  }
  .class-style1 .class-img + .class-content {
    margin-left: 20px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .class-style1 .class-content {
    margin: -30px 15px 0 15px;
  }
  .class-style1 .class-title {
    font-size: 24px;
  }
  .class-style1 .class-info {
    font-size: 14px;
  }
  .class-style1 .class-price {
    font-size: 38px;
    margin: 20px 0 0 0;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .class-style1 .class-btn {
    right: 10px;
    bottom: 10px;
    width: 40px;
    height: 40px;
    line-height: 40px;
    font-size: 16px;
    border-radius: 50%;
  }
  .class-bar {
    padding: 15px;
    border-radius: 20px;
  }
  .class-style2 {
    padding: 35px 20px 29px 20px;
  }
  .class-style2 .class-price {
    font-size: 34px;
    margin-top: 20px;
  }
  .class-style2 .class-info {
    font-size: 14px;
  }
  .lession-title.h1 {
    font-size: 28px;
  }
  .lession-price {
    font-size: 40px;
  }
  .class-bar {
    text-align: center;
  }
  .class-bar .info-title {
    font-size: 14px;
  }
  .class-bar .info-text {
    font-size: 16px;
  }
}
/*------------------- 4.18. Accordion  -------------------*/
.accordion-item:first-of-type .accordion-button {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.accordion-item p:last-child {
  margin-bottom: 0;
}

.accordion-button {
  padding: 0;
  border: none;
  text-align: left;
  font-family: var(--title-font);
  font-weight: 700;
}
.accordion-button:after {
  display: none;
}
.accordion-button:focus {
  box-shadow: none;
}
.accordion-button:not(.collapsed) {
  color: var(--title-color);
  background-color: transparent;
}

.accordion-collapse {
  border: none;
}

.accordion-body {
  padding: 0;
}

.accordion-style1 .accordion-button {
  font-size: 22px;
  font-weight: 500;
  font-family: var(--title-font);
  padding: 18px 60px 18px 35px;
  background-color: #FAF6F0;
  margin: -3px;
  display: block;
  width: calc(100% + 6px);
  transition: all ease 0.4s;
}
.accordion-style1 .accordion-button:before {
  content: "\f107";
  font-family: var(--icon-font);
  position: absolute;
  right: 10px;
  top: 50%;
  width: 50px;
  height: 50px;
  line-height: 50px;
  font-weight: 400;
  text-align: center;
  margin-top: -25px;
  background-color: var(--white-color);
  color: var(--body-color);
  font-size: 18px;
  border-radius: 50%;
  transition: all ease 0.4s;
}
.accordion-style1 .accordion-button:hover, .accordion-style1 .accordion-button:not(.collapsed) {
  background-color: var(--vs-secondary-color);
  color: var(--white-color);
}
.accordion-style1 .accordion-button:hover:before, .accordion-style1 .accordion-button:not(.collapsed):before {
  background-color: var(--theme-color);
  color: var(--white-color);
}
.accordion-style1 .accordion-item:last-of-type .accordion-button,
.accordion-style1 .accordion-item:first-of-type .accordion-button,
.accordion-style1 .accordion-button {
  border-radius: 30px 50px 30px 50px;
}
.accordion-style1 .accordion-item {
  margin-bottom: 20px;
  border: 3px solid transparent;
  border-radius: 30px 50px 30px 30px;
  transition: all ease 0.4s;
}
.accordion-style1 .accordion-item:last-child {
  margin-bottom: 30px;
}
.accordion-style1 .accordion-item.active {
  border-color: #D8D8D8;
}
.accordion-style1 .accordion-body {
  padding: 20px 35px 20px 35px;
}

/* Medium Large devices */
@media (max-width: 1399px) {
  .accordion-style1 .accordion-button {
    font-size: 18px;
    padding: 15px 45px 15px 25px;
  }
  .accordion-style1 .accordion-button::before {
    width: 40px;
    height: 40px;
    line-height: 40px;
    font-size: 18px;
    margin-top: -20px;
  }
  .accordion-style1 .accordion-body {
    padding: 20px 20px 20px 20px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .accordion-style1 .accordion-button {
    font-size: 16px;
    padding: 15px 40px 15px 20px;
  }
  .accordion-style1 .accordion-button::before {
    width: 32px;
    height: 32px;
    line-height: 32px;
    font-size: 16px;
    margin-top: -16px;
    right: 6px;
  }
}
/*------------------- 4.19. Testimonial  -------------------*/
.testi-style1 {
  position: relative;
  background-color: var(--vs-secondary-color);
  padding: 50px 105px 49px 105px;
  border-radius: 166px 94px 290px 166px;
  margin-bottom: 30px;
}
.testi-style1 .testi-icon {
  position: absolute;
  right: 35px;
  top: 30px;
  width: 76px;
  height: 76px;
  line-height: 76px;
  text-align: center;
  display: inline-block;
  border-radius: 50%;
  background-color: var(--theme-color);
  color: var(--white-color);
  font-size: 30px;
}
.testi-style1 .testi-name {
  color: var(--white-color);
  margin-bottom: 0;
}
.testi-style1 .testi-rating {
  color: var(--yellow-color);
  font-size: 14px;
  margin-bottom: 15px;
}
.testi-style1 .testi-rating i {
  margin-right: 2px;
}
.testi-style1 .testi-text {
  font-size: 20px;
  color: var(--white-color);
  margin-bottom: 0;
}
.testi-style1.layout2 {
  padding: 90px 70px 130px 70px;
  text-align: center;
  border-radius: 79px 315px 315px 315px;
}
.testi-style1.layout2 .testi-icon {
  position: relative;
  top: 0;
  right: 0;
  width: 120px;
  height: 120px;
  line-height: 120px;
  font-size: 40px;
  margin-bottom: 28px;
}
.testi-style1.layout2 .testi-text {
  margin-bottom: 30px;
  font-size: 24px;
  font-weight: 500;
}
.testi-style1.layout2 .testi-rating {
  margin-bottom: 0;
}

.testi-style2 {
  position: relative;
  background-color: var(--vs-secondary-color);
  margin-bottom: 47px;
  padding: 38px 50px 45px 50px;
  border-radius: 30px;
}
.testi-style2:before {
  content: "";
  position: absolute;
  left: 50px;
  bottom: -18px;
  width: 35px;
  height: 50px;
  background-color: var(--vs-secondary-color);
  -webkit-clip-path: polygon(100% 50%, 0 0, 0 100%);
          clip-path: polygon(100% 50%, 0 0, 0 100%);
}
.testi-style2 .testi-text {
  color: var(--white-color);
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 32px;
  margin-top: -0.03em;
}
.testi-style2 .testi-icon {
  width: 78px;
  height: 78px;
  line-height: 78px;
  font-size: 30px;
  text-align: center;
  color: var(--white-color);
  background-color: var(--theme-color);
  border-radius: 50%;
  margin-right: 10px;
}
.testi-style2 .testi-name {
  color: var(--white-color);
  line-height: 1;
  font-weight: 500;
  margin: 0 0 7px 0;
}
.testi-style2 .testi-rating {
  color: var(--yellow-color);
  font-size: 14px;
}
.testi-style2 .testi-rating i {
  margin-right: 5px;
}
.testi-style2 .testi-body {
  display: flex;
  align-items: center;
}

/* Extra large devices */
@media (max-width: 1500px) {
  .testi-style1 {
    padding: 40px 85px 45px 85px;
  }
  .testi-style1 .testi-name {
    font-size: 36px;
  }
  .testi-style1.layout2 {
    padding: 45px 30px 80px 30px;
    border-radius: 30px;
  }
}
/* Medium Large devices */
@media (max-width: 1399px) {
  .testi-style2 {
    padding: 28px 30px 30px 30px;
  }
  .testi-style2 .testi-text {
    font-size: 16px;
    margin-bottom: 20px;
  }
  .testi-style2 .testi-icon {
    width: 50px;
    height: 50px;
    line-height: 50px;
    font-size: 17px;
  }
  .testi-style2 .testi-name {
    font-size: 24px;
    margin-bottom: 3px;
  }
  .testi-style2 .testi-rating {
    font-size: 12px;
  }
  .testi-style1.layout2 .testi-icon {
    width: 70px;
    height: 70px;
    line-height: 70px;
    font-size: 26px;
    margin-bottom: 22px;
  }
  .testi-style1.layout2 .testi-text {
    font-size: 24px;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .testi-style1 {
    padding: 40px 40px 40px 40px;
    border-radius: 50px;
  }
  .testi-style1 .testi-icon {
    right: 15px;
    top: 15px;
    width: 50px;
    height: 50px;
    line-height: 50px;
    font-size: 22px;
  }
  .testi-style1 .testi-text {
    font-size: 18px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .testi-style1 {
    text-align: center;
  }
  .testi-style1.layout2 {
    padding: 45px 25px 45px 25px;
  }
  .testi-style1.layout2 .testi-text {
    font-size: 18px;
    margin-bottom: 10px;
  }
  .testi-style1.layout2 .testi-name {
    font-size: 30px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .testi-style1 {
    padding: 20px 20px 40px 20px;
    border-radius: 50px;
  }
  .testi-style1 .testi-icon {
    position: relative;
    right: 0;
    top: 0;
    margin-bottom: 10px;
  }
  .testi-style1 .testi-name {
    font-size: 26px;
    margin-bottom: 7px;
  }
  .testi-style1.layout2 {
    padding: 45px 15px 45px 15px;
  }
}
/*------------------- 4.20. Package  -------------------*/
.package-style1 {
  margin-top: 50px;
  margin-bottom: 30px;
}
.package-style1:nth-child(even) {
  margin-top: 0;
}
.package-style1 .package-top {
  position: relative;
  margin: 0 30px -80px 30px;
  padding: 25px 40px 22px 40px;
  z-index: 1;
  border: 2px solid var(--vs-secondary-color);
  border-radius: 30px;
  background-color: var(--white-color);
  transition: all ease 0.4s;
}
.package-style1.active .package-top, .package-style1:hover .package-top {
  background-color: var(--vs-secondary-color);
  border-color: transparent;
}
.package-style1 .package-icon {
  width: 66px;
  height: 66px;
  line-height: 66px;
  text-align: center;
  background-color: var(--theme-color2);
  border-radius: 50%;
  position: absolute;
  right: 10px;
  top: 10px;
}
.package-style1 .package-name {
  margin: 0 0 7px 0;
  transition: all ease 0.4s;
}
.package-style1 .package-price {
  font-size: 50px;
  font-weight: 600;
  font-family: var(--title-font);
  color: var(--theme-color);
  margin: 0;
  line-height: 1;
  transition: all ease 0.4s;
}
.package-style1 .package-price .duration {
  font-weight: 600;
  font-size: 22px;
  color: var(--body-color);
  transition: all ease 0.4s;
}
.package-style1.active .package-name,
.package-style1.active .package-price .duration, .package-style1:hover .package-name,
.package-style1:hover .package-price .duration {
  color: var(--white-color);
}
.package-style1 .package-body {
  background-color: var(--smoke-color);
  padding: 115px 72px 40px 72px;
  border-radius: 30px;
}
.package-style1 .list-style1 ul {
  margin-bottom: 25px;
}
.package-style1 .list-style1 li {
  font-size: 18px;
}
.package-style1.active .vs-btn, .package-style1:hover .vs-btn {
  background-color: var(--theme-color);
  color: var(--white-color);
}

/* Medium Large devices */
@media (max-width: 1399px) {
  .package-style1 {
    margin-top: 0;
  }
  .package-style1 .package-top {
    margin: 0 10px -50px 10px;
    padding: 25px 30px 22px 30px;
  }
  .package-style1 .package-body {
    padding: 75px 43px 40px 43px;
  }
  .package-style1 .list-style1 li {
    font-size: 16px;
    margin-bottom: 8px;
  }
  .package-style1 .package-name {
    font-size: 24px;
  }
  .package-style1 .package-price {
    font-size: 40px;
  }
  .package-style1 .package-price .duration {
    font-size: 16px;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .package-style1 .package-icon {
    width: 40px;
    height: 40px;
    line-height: 40px;
  }
  .package-style1 .package-icon img {
    max-width: 60%;
  }
  .package-style1 .package-body {
    padding: 70px 25px 35px 25px;
  }
  .package-style1 .vs-btn {
    padding: 14px 24px 16px 24px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .package-style1 .package-top {
    padding: 25px 20px 22px 20px;
  }
}
/*------------------- 4.21. Call To Active  -------------------*/
.cta-box1 {
  background-color: var(--vs-secondary-color);
  max-width: 610px;
  padding: 90px 75px 70px 110px;
  border-radius: 274px 30px 274px 274px;
}
.cta-box1 .cta-title {
  color: var(--white-color);
  margin-bottom: 18px;
  margin-top: -0.2em;
}
.cta-box1 .cta-text {
  color: #D9D9D9;
  font-size: 18px;
  margin-bottom: 30px;
}

/* Large devices */
@media (max-width: 1199px) {
  .cta-box1 {
    padding: 60px 60px 60px 100px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .cta-box1 {
    border-radius: 50px;
    padding: 60px 80px;
    max-width: 100%;
    text-align: center;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .cta-box1 {
    padding: 50px 20px;
  }
}
/*------------------- 4.22. Schedule Area  -------------------*/
.schedule-style1 {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
}
.schedule-style1 .schedule-img {
  overflow: hidden;
  margin-right: 20px;
  border-radius: 50%;
}
.schedule-style1 .schedule-img img {
  width: 100%;
  transform: scale(1.001);
  transition: all ease 0.4s;
}
.schedule-style1:hover .schedule-img img {
  transform: scale(1.15);
}
.schedule-style1 .schedule-content {
  flex: 1;
}
.schedule-style1 .schedule-title {
  position: relative;
  padding: 0 0 8px 0;
  margin: -0.2em 0 25px 0;
}
.schedule-style1 .schedule-title:before {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 30px;
  height: 5px;
  background-color: var(--theme-color2);
  border-radius: 5px;
}
.schedule-style1 .schedule-table .tr {
  background-color: var(--white-color);
  color: var(--title-color);
  display: flex;
  font-size: 16px;
  font-weight: 500;
  width: 100%;
  margin-bottom: 5px;
  border-radius: 9999px;
  padding: 14px 30px;
  gap: 0 25px;
}
.schedule-style1 .schedule-table .tr:last-child {
  margin-bottom: 0;
}
.schedule-style1 .schedule-table .th {
  color: var(--theme-color);
}

/* Large devices */
@media (max-width: 1199px) {
  .schedule-style1 {
    display: block;
    text-align: center;
  }
  .schedule-style1 .schedule-table .tr {
    justify-content: center;
  }
  .schedule-style1 .schedule-img {
    margin-right: 0;
    margin-bottom: 25px;
  }
  .schedule-style1 .schedule-title:before {
    left: 50%;
    margin-left: -15px;
  }
}
/*------------------- 4.23. Gallery  -------------------*/
.gallery-style1 .gallery-img {
  overflow: hidden;
  position: relative;
  border-radius: 30px;
}
.gallery-style1 .gallery-img img {
  width: 100%;
  transform: scale(1.001);
  transition: all ease 0.4s;
}
.gallery-style1 .gallery-img:after, .gallery-style1 .gallery-img:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 50%;
  height: 40%;
  background-color: rgba(232, 6, 60, 0.9);
  opacity: 0;
  visibility: hidden;
  transition: all ease 0.6s;
  z-index: 1;
}
.gallery-style1 .gallery-img:after {
  top: auto;
  bottom: 0;
  left: auto;
  right: 0;
}
.gallery-style1 .gallery-btn {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 60px;
  height: 60px;
  line-height: 60px;
  text-align: center;
  background-color: var(--theme-color2);
  color: var(--title-color);
  border-radius: 50%;
  font-size: 24px;
  transform: translate(-50%, -50%);
  z-index: 3;
  opacity: 0;
  visibility: hidden;
}
.gallery-style1 .gallery-btn:before {
  content: "";
  position: absolute;
  left: 2px;
  top: 2px;
  right: 2px;
  bottom: 2px;
  background-color: var(--vs-secondary-color);
  border-radius: inherit;
  z-index: -1;
  transition: all ease 0.4s;
  transform: scale(0.5);
  opacity: 0;
  visibility: hidden;
}
.gallery-style1 .gallery-btn:hover {
  color: var(--white-color);
}
.gallery-style1 .gallery-btn:hover:before {
  opacity: 1;
  visibility: visible;
  transform: scale(1);
}
.gallery-style1:hover .gallery-img:after, .gallery-style1:hover .gallery-img:before {
  opacity: 1;
  visibility: visible;
  height: 100%;
}
.gallery-style1:hover .gallery-img img {
  transform: scale(1.15);
}
.gallery-style1:hover .gallery-btn {
  opacity: 1;
  visibility: visible;
}
.gallery-style1.radius-2 .gallery-img {
  border-radius: 150px 150px 30px 30px;
}
.gallery-style1.radius-3 .gallery-img {
  border-radius: 30px 30px 150px 150px;
}
.gallery-style1.layout2 .gallery-btn {
  background-color: transparent;
  color: var(--white-color);
  font-size: 30px;
}

.thumb-style1 {
  border-radius: 30px;
  position: relative;
  overflow: hidden;
  cursor: pointer;
}
.thumb-style1 img {
  width: 100%;
}
.thumb-style1:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: var(--theme-color);
  opacity: 0;
  visibility: hidden;
  transition: all ease 0.4s;
}

.thumb-style1:hover:before,
.slick-current .thumb-style1:before {
  opacity: 0.8;
  visibility: visible;
}

/*------------------- 4.24. Event Area  -------------------*/
.event-style1 {
  display: flex;
  align-items: center;
  border: 1px solid #B5B5B5;
  margin-bottom: 30px;
  border-radius: 30px;
}
.event-style1 .event-date {
  background-color: var(--theme-color);
  color: var(--white-color);
  text-align: center;
  line-height: 1;
  font-weight: 600;
  font-family: var(--title-font);
  text-transform: uppercase;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  border-radius: 30px;
  margin: -1px 30px -1px -1px;
  padding: 27px 12px 6px 12px;
  transition: all ease 0.4s;
}
.event-style1 .event-date .month {
  font-size: 18px;
  margin-bottom: 2px;
}
.event-style1 .event-date .day {
  font-size: 50px;
}
.event-style1 .event-date .time {
  display: block;
  width: 100%;
  font-size: 18px;
  line-height: 1;
  background-color: var(--white-color);
  color: var(--theme-color);
  border-radius: 9999px;
  padding: 6px 16px;
  margin-top: 13px;
  transition: all ease 0.4s;
}
.event-style1:hover .event-date {
  background-color: var(--vs-secondary-color);
}
.event-style1:hover .event-date .time {
  color: var(--vs-secondary-color);
}
.event-style1 .event-title {
  margin: -0.2em 0 12px 0;
}
.event-style1 .event-content {
  flex: 1;
  padding: 30px 40px 21px 0;
}
.event-style1 .event-text {
  margin: 0;
}

/* Medium Large devices */
@media (max-width: 1399px) {
  .event-style1 .event-content {
    flex: 1;
    padding: 20px 30px 20px 0;
  }
  .event-style1 .event-date {
    border-radius: 20px;
    margin: -1px 20px -1px -1px;
    padding: 27px 12px 6px 12px;
  }
  .event-style1 .event-title {
    margin: -0.2em 0 12px 0;
    font-size: 22px;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .event-style1 {
    display: block;
    text-align: center;
  }
  .event-style1 .event-date {
    padding-top: 20px;
    margin: -1px;
  }
  .event-style1 .event-content {
    padding: 25px 20px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .event-style1 .event-title {
    font-size: 20px;
  }
  .event-style1 .event-content {
    padding: 25px 15px;
  }
}
/*------------------- 4.25. Feature  -------------------*/
.feature-style1 {
  margin-bottom: 25px;
}
.feature-style1 .feature-icon {
  width: var(--icon-size, 90px);
  height: var(--icon-size, 90px);
  line-height: var(--icon-size, 90px);
  text-align: center;
  background-color: rgba(242, 0, 58, 0.1);
  border-radius: 50%;
  transition: all ease 0.4s;
  margin-bottom: 27px;
  display: inline-block;
}
.feature-style1 .feature-icon img {
  filter: none;
  transition: all ease 0.4s;
}
.feature-style1 .feature-title {
  text-transform: capitalize;
  margin-bottom: 11px;
}
.feature-style1 .feature-text {
  margin-bottom: 0;
}
.feature-style1:hover .feature-icon {
  background-color: var(--theme-color);
}
.feature-style1:hover .feature-icon img {
  filter: brightness(0) invert(1);
}

.feature-style2 {
  text-align: center;
  position: relative;
  z-index: 1;
  margin-bottom: 23px;
}
.feature-style2 .vs-circle {
  --circle-size: calc(100% + 10px);
  --circle-border-size: 36px;
  -webkit-clip-path: polygon(0 0, 100% 0, 100% 50%, 0 50%);
          clip-path: polygon(0 0, 100% 0, 100% 50%, 0 50%);
  position: absolute;
  left: 50%;
  top: 60px;
  z-index: -1;
  transform: translateX(-50%);
  transition: all ease 0.4s;
}
.feature-style2 .feature-img {
  margin-bottom: 18px;
}
.feature-style2 .feature-title {
  color: var(--white-color);
  margin-bottom: 10px;
  font-size: 22px;
}
.feature-style2 .feature-text {
  color: #E9E9E9;
  margin: 0 auto;
  max-width: 260px;
}

.feature-style3 {
  display: flex;
  align-items: center;
  margin-bottom: 60px;
}
.feature-style3:last-child {
  margin-bottom: 30px;
}
.feature-style3 .feature-img {
  position: relative;
  margin-right: 20px;
  border-radius: 50%;
  width: 120px;
}
.feature-style3 .feature-img:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  border: 2px dashed var(--vs-secondary-color);
  border-radius: 50%;
  animation: rotate 40s infinite linear;
  transition: all ease 0.4s;
}
.feature-style3 .feature-img .img {
  padding: 5px;
  overflow: hidden;
  width: 100%;
  height: 100%;
  border-radius: inherit;
}
.feature-style3 .feature-img .img img {
  width: 100%;
  border-radius: 50%;
  max-width: initial;
  transition: all ease 0.6s;
}
.feature-style3 .feature-body {
  flex: 1;
}
.feature-style3:hover .feature-img:before {
  border-color: var(--theme-color);
}
.feature-style3 .feature-title {
  margin-top: -0.25em;
  margin-bottom: 10px;
}
.feature-style3 .list-style2 ul {
  margin-bottom: -0.2em;
}

.feature-style4 {
  padding: 0;
  margin-bottom: 30px;
}
.feature-style4 .feature-body {
  position: relative;
  padding-top: 50px;
  margin: 0 15px;
}
.feature-style4 .feature-body:after, .feature-style4 .feature-body:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 30px;
  height: 30px;
  background-color: var(--vs-secondary-color);
  border-radius: 50%;
  transition: all ease 0.4s;
  z-index: 1;
}
.feature-style4 .feature-body:after {
  z-index: 0;
  left: 1px;
  height: 8px;
  width: calc(100% + 30px);
  border-radius: 0;
  margin-top: 11px;
}
.feature-style4:last-child .feature-body::after {
  width: 100%;
}
.feature-style4:hover .feature-body:before {
  background-color: var(--theme-color);
}
.feature-style4 .feature-year {
  font-size: 24px;
  color: var(--vs-secondary-color);
  line-height: 1;
  display: block;
  font-family: var(--title-font);
  font-weight: 600;
  margin-bottom: 8px;
}
.feature-style4 .feature-title {
  text-transform: capitalize;
}
.feature-style4 .feature-text {
  margin-bottom: 22px;
}
.feature-style4 .feature-img {
  border-radius: 30px;
  overflow: hidden;
  position: relative;
}
.feature-style4 .feature-img::before {
  content: "";
  position: absolute;
  left: 5px;
  top: 5px;
  right: 5px;
  bottom: 5px;
  border: 2px solid var(--theme-color);
  z-index: 1;
  border-radius: inherit;
  transition: all ease 0.4s;
  pointer-events: none;
  transform: scale(1.05);
  opacity: 0;
  visibility: hidden;
}
.feature-style4 .feature-img img {
  width: 100%;
}
.feature-style4:hover .feature-img:before {
  opacity: 1;
  visibility: visible;
  transform: scale(1.001);
}

/* Medium Large devices */
@media (max-width: 1399px) {
  .feature-style1 .feature-title {
    font-size: 22px;
  }
  .feature-style4 .feature-title {
    font-size: 22px;
  }
  .feature-style4 .feature-text {
    font-size: 14px;
  }
  .feature-style4 {
    text-align: center;
  }
  .feature-style4 .feature-year {
    font-size: 32px;
    margin-top: -0.15em;
  }
  .feature-style4 .feature-body {
    padding-top: 0;
  }
  .feature-style4 .feature-body:before, .feature-style4 .feature-body:after {
    display: none;
    width: 100%;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .feature-style3 {
    display: block;
    margin-bottom: 30px;
    text-align: center;
  }
  .feature-style3 .feature-img {
    width: 140px;
    height: 140px;
    margin-right: auto;
    margin-left: auto;
    margin-bottom: 25px;
  }
  .feature-style3 .feature-title {
    margin-top: -0.1em;
  }
  .feature-style3 .list-style2 li {
    display: block;
  }
  .feature-style3 .list-style2 li:before {
    display: none;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .feature-style2 .vs-circle {
    --circle-size: 100%;
    top: 30px;
  }
}
/*------------------- 4.26. Team  -------------------*/
.team-style1 .team-img,
.team-style2 .team-img {
  overflow: hidden;
  border-radius: 30px;
  position: relative;
}
.team-style1 .team-img:before,
.team-style2 .team-img:before {
  content: "";
  position: absolute;
  left: 5px;
  top: 5px;
  bottom: 5px;
  right: 5px;
  border: 2px solid var(--theme-color);
  z-index: 1;
  border-radius: inherit;
  transform: scale(0.95);
  transition: all ease 0.4s;
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
}
.team-style1 .team-img img,
.team-style2 .team-img img {
  transform: scale(1.001);
  transition: all ease 0.4s;
}
.team-style1:hover .team-img:before,
.team-style2:hover .team-img:before {
  opacity: 1;
  visibility: visible;
  transform: scale(1);
}
.team-style1:hover .team-img img,
.team-style2:hover .team-img img {
  transform: scale(1.05);
}

.team-style1 {
  display: flex;
  align-items: center;
  background-color: var(--white-color);
  padding: 30px;
  border-radius: 30px 30px 210px 30px;
  margin-bottom: 30px;
  transition: all ease 0.4s;
}
.team-style1.layout2 {
  border: 3px solid #D9D9D9;
}
.team-style1.layout2:hover {
  border-color: var(--theme-color);
}
.team-style1.layout2 .team-img:before {
  display: none;
}
.team-style1 .team-img {
  margin-right: 35px;
}
.team-style1 .team-content {
  flex: 1;
}
.team-style1 .team-name {
  margin-bottom: 5px;
  margin-top: -0.22em;
}
.team-style1 .team-degi {
  font-size: 18px;
  font-weight: 500;
  padding-bottom: 16px;
  color: var(--theme-color);
  position: relative;
  text-transform: capitalize;
}
.team-style1 .team-degi:before {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 70px;
  height: 1px;
  background-color: #B5B5B5;
}
.team-style1 .team-number {
  font-size: 18px;
  color: var(--body-color);
  display: inline-block;
  margin-bottom: 13px;
}

.team-style2 {
  position: relative;
  margin-bottom: 30px;
}
.team-style2 .team-img img {
  width: 100%;
}
.team-style2 .team-name {
  font-size: 26px;
  text-align: center;
  position: absolute;
  left: 17px;
  right: 17px;
  bottom: 20px;
  color: var(--title-color);
  background-color: var(--white-color);
  border-radius: 9999px;
  margin: 0;
  padding: 13px 0;
  line-height: 1;
  transition: all ease 0.4s;
}
.team-style2:hover .team-name {
  background-color: var(--theme-color);
}
.team-style2:hover .team-name a {
  color: var(--white-color);
}
.team-style2:hover .team-name:hover {
  background-color: var(--vs-secondary-color);
}

.team-about {
  margin-bottom: 15px;
}
.team-about .team-name {
  margin-top: -0.25em;
  margin-bottom: 10px;
}
.team-about .team-degi {
  color: var(--theme-color);
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 33px;
}
.team-about .inner-title {
  font-size: 26px;
  margin-bottom: 8px;
}
.team-about .title-divider2 {
  margin-bottom: 10px;
}
.team-about .team-text {
  line-height: 1;
  margin-bottom: 30px;
  font-size: 18px;
}
.team-about .team-time {
  font-size: 18px;
  margin-bottom: 8px;
}
.team-about .team-time:last-of-type {
  margin-bottom: 20px;
}
.team-about .team-info {
  display: flex;
  align-items: center;
  margin: 0 0 15px 0;
  font-size: 18px;
}
.team-about .team-info i {
  width: 42px;
  height: 42px;
  line-height: 42px;
  border-radius: 50%;
  background-color: var(--theme-color2);
  color: var(--title-color);
  text-align: center;
  font-size: 18px;
  margin-right: 10px;
}

.team-description {
  margin-top: 33px;
  margin-bottom: 50px;
}

/* Medium Large devices */
@media (max-width: 1399px) {
  .team-style1 {
    padding: 20px;
    border-radius: 30px 30px 100px 30px;
  }
  .team-style1 .team-img {
    margin-right: 30px;
  }
  .team-style1 .team-name {
    font-size: 36px;
  }
  .team-style1 .team-img {
    width: 240px;
  }
  .team-style1 .team-degi {
    padding-bottom: 10px;
    margin-bottom: 10px;
  }
  .team-style1 .team-number {
    font-size: 16px;
  }
  .team-style2 .team-name {
    font-size: 20px;
    padding: 10px 0;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .team-style1 .team-img {
    width: 180px;
    margin-right: 20px;
  }
  .team-style1 .team-name {
    font-size: 26px;
  }
  .team-style1 .team-degi {
    font-size: 16px;
  }
  .team-style1 .vs-social a {
    --icon-size: 40px;
    font-size: 14px;
  }
  .team-style2 .team-name {
    padding: 10px 0;
    font-size: 18px;
    right: 10px;
    bottom: 10px;
    left: 10px;
  }
  .team-about .inner-title {
    font-size: 20px;
  }
  .team-about .team-info,
  .team-about .team-time,
  .team-about .team-text {
    font-size: 16px;
  }
  .team-about .team-degi {
    font-size: 16px;
    margin-bottom: 20px;
  }
  .team-description {
    margin-top: 10px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .team-style1 .team-img {
    width: auto;
  }
  .team-style1.layout2 {
    display: block;
    border-radius: 30px;
    padding: 20px;
    text-align: center;
  }
  .team-style1.layout2 .team-img {
    width: auto;
    margin-right: 0;
    margin-bottom: 25px;
  }
  .team-style1.layout2 .team-img img {
    width: 100%;
  }
  .team-style1.layout2 .team-degi:before {
    left: 50%;
    margin-left: -35px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .team-style1 {
    display: block;
    border-radius: 30px;
    padding: 20px;
    text-align: center;
  }
  .team-style1 .team-img {
    width: auto;
    margin-right: 0;
    margin-bottom: 25px;
  }
  .team-style1 .team-img img {
    width: 100%;
  }
  .team-style1 .team-degi:before {
    left: 50%;
    margin-left: -35px;
  }
}
/*------------------- 4.27. Wocommerce  -------------------*/
.woocommerce-message,
.woocommerce-info {
  position: relative;
  border: 1px solid var(--border-color);
  padding: 11px 20px;
  background-color: var(--smoke-color);
  color: var(--title-color);
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 30px;
  border-radius: 10px;
  font-family: var(--title-font);
}
.woocommerce-message a,
.woocommerce-info a {
  color: inherit;
  text-decoration: underline;
}
.woocommerce-message a:hover,
.woocommerce-info a:hover {
  text-decoration: underline;
}
.woocommerce-message:before,
.woocommerce-info:before {
  content: "\f06a";
  font-family: var(--icon-font);
  font-weight: 900;
  margin-right: 10px;
}

.woocommerce-notices-wrapper .woocommerce-message {
  background-color: var(--vs-secondary-color);
  color: var(--white-color);
  border-color: transparent;
  border-radius: 20px;
}
.woocommerce-notices-wrapper .woocommerce-message:before {
  content: "\f14a";
  font-weight: 300;
  top: 1px;
  position: relative;
}

.star-rating {
  overflow: hidden;
  position: relative;
  width: 100px;
  height: 1.2em;
  line-height: 1.2em;
  display: block;
  font-family: var(--icon-font);
  font-weight: 700;
  font-size: 14px;
}
.star-rating:before {
  content: "\f005\f005\f005\f005\f005";
  color: var(--yellow-color);
  float: left;
  top: 0;
  left: 0;
  position: absolute;
  letter-spacing: 3px;
  font-weight: 400;
}
.star-rating span {
  overflow: hidden;
  float: left;
  top: 0;
  left: 0;
  position: absolute;
  padding-top: 1.5em;
}
.star-rating span:before {
  content: "\f005\f005\f005\f005\f005";
  top: 0;
  position: absolute;
  left: 0;
  color: var(--yellow-color);
  letter-spacing: 3px;
  font-weight: 700;
}

.quantity {
  position: relative;
  width: -moz-max-content;
  width: max-content;
  display: flex;
  align-items: center;
}
.quantity > label {
  font-size: 14px;
  color: var(--body-color);
  font-weight: 700;
  margin: 0 20px 0 0;
  font-family: var(--body-font);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}
.quantity input {
  padding: 0 10px 0 10px;
  width: 50px;
  height: 50px;
  text-align: center;
  font-size: 14px;
  font-weight: 700;
  --body-color: var(--title-color);
  border: 1px solid var(--smoke-color);
  border-left: none;
  border-right: none;
  border-radius: 0;
}
.quantity .qty-btn {
  border: 1px solid var(--smoke-color);
  background-color: var(--smoke-color);
  color: var(--title-color);
  padding: 0;
  width: 50px;
  height: 50px;
  line-height: 50px;
  font-size: 16px;
  border-radius: 9999px 0 0 9999px;
}
.quantity .qty-btn:hover {
  background-color: var(--theme-color);
  color: var(--white-color);
}
.quantity .quantity-plus {
  bottom: 30px;
  border-radius: 0 9999px 9999px 0;
}

.rating-select {
  margin-top: -0.4em;
}
.rating-select label {
  margin: 0 10px 0 0;
  display: inline-block;
}
.rating-select p.stars {
  margin-bottom: 0;
  line-height: 1;
  display: inline-block;
}
.rating-select p.stars a {
  position: relative;
  height: 14px;
  width: 18px;
  text-indent: -999em;
  display: inline-block;
  text-decoration: none;
}
.rating-select p.stars a::before {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 18px;
  height: 14px;
  line-height: 1;
  font-family: var(--icon-font);
  content: "\f005";
  font-weight: 400;
  text-indent: 0;
  color: var(--yellow-color);
}
.rating-select p.stars a:hover ~ a::before {
  content: "\f005";
  font-weight: 400;
}
.rating-select p.stars:hover a::before {
  content: "\f005";
  font-weight: 700;
}
.rating-select p.stars.selected a.active::before {
  content: "\f005";
  font-weight: 700;
}
.rating-select p.stars.selected a.active ~ a::before {
  content: "\f005";
  font-weight: 400;
}
.rating-select p.stars.selected a:not(.active)::before {
  content: "\f005";
  font-weight: 700;
}

/*------------------- 4.28. Products  -------------------*/
.product-big-img {
  background-color: #f1f1f1;
  border: 1px solid rgba(203, 203, 203, 0.3);
  margin-bottom: 20px;
  text-align: center;
  border-radius: 20px;
}
.product-big-img img {
  width: 100%;
}

.product-thumb-slide {
  margin-bottom: 22px;
}
.product-thumb-slide.row {
  --bs-gutter-x: 20px;
}
.product-thumb-slide .thumb {
  background-color: #f1f1f1;
  overflow: hidden;
  transition: all ease 0.4s;
  border: 1px solid rgba(203, 203, 203, 0.3);
  cursor: pointer;
  border-radius: 14px;
}
.product-thumb-slide .thumb img {
  transition: all ease 0.4s;
  width: 100%;
}
.product-thumb-slide .thumb:hover {
  border-color: var(--theme-color);
}
.product-thumb-slide .slick-current .thumb {
  border-color: var(--theme-color);
}
.product-thumb-slide .slick-current .thumb img {
  transform: scale(1.15);
}

.product-about {
  margin-bottom: 25px;
}
.product-about .product-rating {
  display: flex;
  align-items: center;
  font-weight: 500;
  margin: 0 0 17px 0;
}
.product-about .product-rating .star-rating {
  margin-right: 10px;
  font-size: 16px;
  width: 104px;
}
.product-about .product-title {
  font-size: 40px;
  margin-bottom: 6px;
}
.product-about .quantity {
  width: 100%;
}
.product-about .product-price {
  font-size: 34px;
  font-weight: 500;
  color: var(--theme-color);
  line-height: 1;
  margin: 0 0 8px 0;
  font-family: var(--title-font);
}
.product-about .product-price del {
  font-size: 0.6em;
  color: #777777;
}
.product-about .product-text {
  margin-bottom: 24px;
}
.product-about .actions {
  display: flex;
  flex-wrap: wrap;
  gap: 20px 10px;
  margin-bottom: 20px;
}
.product-about .actions .vs-btn {
  flex: 1;
  padding-top: 0;
  padding-bottom: 0;
  height: 50px;
  line-height: 50px;
  background-color: var(--vs-secondary-color);
}
.product-about .actions .vs-btn::after, .product-about .actions .vs-btn::before {
  background-color: var(--theme-color);
}
.product-about .actions .icon-btn {
  --icon-size: 50px;
  background-color: var(--white-color);
  color: var(--body-color);
  border: 2px solid var(--border-color);
  font-size: 17px;
}
.product-about .actions .icon-btn::before {
  background-color: var(--theme-color);
  --border-size: 0;
}
.product-about .actions .icon-btn:hover {
  border-color: transparent;
  color: var(--white-color);
}
.product-about .product-getway {
  border-bottom: 1px solid var(--border-color);
  padding: 0 0 30px 0;
  margin-bottom: 22px;
}
.product-about .getway-title {
  font-size: 14px;
  color: var(--body-color);
  font-weight: 700;
  margin: -0.15em 0 15px 0;
  line-height: 1;
  font-family: var(--body-font);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  display: block;
}

.product_meta {
  font-family: var(--body-font);
  font-size: 16px;
  font-weight: 400;
  text-transform: uppercase;
}
.product_meta > span {
  display: block;
  margin-bottom: 3px;
  color: var(--theme-color);
}
.product_meta > span:last-child {
  margin-bottom: 0;
}
.product_meta > span a {
  color: inherit;
}
.product_meta > span a:hover {
  color: var(--theme-color);
}
.product_meta > span > a,
.product_meta > span > span {
  position: relative;
  margin-left: 3px;
  color: var(--title-color);
  text-transform: capitalize;
  font-weight: 400;
}
.product_meta > span > a:first-child,
.product_meta > span > span:first-child {
  margin-left: 5px;
}
.product_meta > span > a:not(:last-child)::after,
.product_meta > span > span:not(:last-child)::after {
  content: ",";
  margin-right: 3px;
}

.vs-product-wrapper .vs-pagination {
  justify-content: center;
  display: flex;
  margin-top: 30px;
}

.product-description {
  margin-top: 26px;
}

.description {
  margin-bottom: 40px;
}

.product-style1 {
  border: 3px solid var(--theme-color2);
  margin-bottom: var(--bs-gutter-x, 30px);
  border-radius: 30px;
  transition: all ease 0.4s;
}
.product-style1:hover {
  border-color: var(--theme-color);
}
.product-style1 .product-img img {
  width: 100%;
}
.product-style1 .product-content {
  padding: 20px 30px 20px 30px;
}
.product-style1 .product-price {
  font-size: 22px;
  font-weight: 500;
  color: var(--theme-color);
  font-family: var(--title-font);
  margin-bottom: 12px;
  display: block;
  line-height: 1;
}
.product-style1 .product-price del {
  font-size: 14px;
  color: #777777;
}
.product-style1 .product-title {
  font-size: 22px;
  margin-bottom: 10px;
  text-transform: capitalize;
}
.product-style1 .star-rating {
  margin-bottom: 18px;
}
.product-style1 .vs-btn i {
  margin-right: 10px;
}
.product-style1 .actions {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 6px;
}
.product-style1 .icon-btn {
  background-color: transparent;
  color: var(--body-color);
  border: 2px solid #D0D0D0;
  font-size: 16px;
  width: 50px;
  height: 50px;
  line-height: 50px;
}
.product-style1 .icon-btn:before {
  background-color: var(--vs-secondary-color);
  --border-size: 1px;
}
.product-style1 .icon-btn:hover {
  background-color: var(--theme-color);
  border-color: transparent;
  color: var(--white-color);
}
.product-style1 .vs-btn {
  background-color: var(--vs-secondary-color);
  padding: 17px 26px;
}
.product-style1 .vs-btn:after, .product-style1 .vs-btn:before {
  background-color: var(--theme-color);
}

/* Medium Large devices */
@media (max-width: 1399px) {
  .product-style1 .product-content {
    padding: 20px 20px 20px 20px;
  }
  .product-style1 .vs-btn {
    padding: 17px 19px;
  }
}
/* Large devices */
/* Medium devices */
@media (max-width: 991px) {
  .product-thumb-slide {
    margin-bottom: 30px;
  }
  .product-about .product-title {
    font-size: 34px;
    margin-bottom: 6px;
  }
  .product-about .product-price {
    font-size: 26px;
    margin: 0 0 11px 0;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .product-about .product-title {
    font-size: 30px;
    margin-bottom: 8px;
  }
}
/*------------------- 4.29. Cart  -------------------*/
.woocommerce-cart-form {
  text-align: center;
  border: 1px solid var(--border-color);
  border-radius: 20px;
  margin-bottom: 45px;
}

.cart_table {
  margin-bottom: 0;
  border-radius: 20px;
  border: none;
}
.cart_table thead th {
  background-color: var(--vs-secondary-color);
  border: none !important;
  color: var(--white-color);
  font-size: 18px;
  font-family: var(--title-font);
  font-weight: 600;
}
.cart_table thead th:first-child {
  border-radius: 20px 0 0 0;
}
.cart_table thead th:last-child {
  border-radius: 0 20px 0 0;
}
.cart_table td:before,
.cart_table th {
  font-family: var(--title-font);
  color: var(--title-color);
  font-weight: 600;
  border: none;
  padding: 17px 15px;
}
.cart_table td:before {
  content: attr(data-title);
  position: absolute;
  left: 15px;
  top: 50%;
  vertical-align: top;
  padding: 0;
  transform: translateY(-50%);
  display: none;
}
.cart_table td {
  border: none;
  border-bottom: 1px solid var(--border-color);
  border-right: 1px solid var(--border-color);
  color: var(--body-color);
  padding: 13px 10px;
  position: relative;
  vertical-align: middle;
  line-height: 1;
}
.cart_table td:last-child {
  border-right: none;
}
.cart_table tr:last-child td {
  border-bottom: none;
}
.cart_table .product-quantity {
  color: var(--title-color);
}
.cart_table .product-quantity input {
  position: relative;
  top: -2px;
}
.cart_table .cart-productimage {
  border: 2px solid var(--theme-color2);
  display: inline-block;
  width: 100px;
  height: 85px;
  line-height: 85px;
  overflow: hidden;
  border-radius: 10px;
}
.cart_table .cart-productimage img {
  transform: scale(1.001);
  width: 100%;
  transition: all ease 0.4s;
}
.cart_table .cart-productimage:hover img {
  transform: scale(1.1);
}
.cart_table .amount {
  font-size: 20px;
  font-weight: 600;
  color: var(--theme-color);
  font-family: var(--title-font);
}
.cart_table .cart-productname {
  font-size: 18px;
  font-weight: 600;
  font-family: var(--title-font);
  color: var(--title-color);
}
.cart_table .cart-productname:hover {
  color: var(--theme-color);
}
.cart_table .remove {
  color: var(--theme-color);
  font-size: 18px;
}
.cart_table .remove:hover {
  color: var(--title-color);
}
.cart_table .quantity {
  width: -moz-max-content;
  width: max-content;
  display: inline-flex;
}
.cart_table .qty-input {
  height: 40px;
}
.cart_table .qty-btn {
  width: 40px;
  height: 40px;
  line-height: 40px;
  font-size: 14px;
}
.cart_table .actions {
  text-align: right;
  vertical-align: middle;
  padding: 15px 30px;
}
.cart_table .actions > .vs-btn {
  font-size: 16px;
  padding: 17px 28px;
  margin-right: 15px;
  background-color: var(--vs-secondary-color);
}
.cart_table .actions > .vs-btn:last-child {
  margin-right: 0;
}
.cart_table .vs-cart-coupon {
  float: left;
  margin: 0;
  width: 500px;
  max-width: 100%;
  display: flex;
  border: 2px solid var(--vs-secondary-color);
  border-radius: 9999px;
}
.cart_table .vs-cart-coupon input {
  height: 50px;
  color: var(--title-color);
  border: none;
  flex: 1;
  background-color: transparent;
  border-radius: 0;
}
.cart_table .vs-cart-coupon .vs-btn {
  font-size: 16px;
  padding: 17px 22px;
  width: -moz-max-content;
  width: max-content;
  margin: -2px;
  background-color: var(--vs-secondary-color);
}

.summary-title {
  position: relative;
  padding-bottom: 11px;
  margin-bottom: 30px;
}
.summary-title:before {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 24px;
  height: 3px;
  background-color: var(--theme-color2);
  display: inline-block;
}

.cart_totals {
  border: 1px solid var(--border-color);
}
.cart_totals th,
.cart_totals td {
  vertical-align: top;
  padding: 14px 20px;
  border: none;
  border-bottom: 1px solid var(--border-color);
  font-size: 14px;
  color: var(--title-color);
  width: 55%;
}
.cart_totals th:first-child,
.cart_totals td:first-child {
  width: 45%;
  background-color: var(--smoke-color);
  font-weight: 700;
  font-size: 14px;
  color: var(--title-color);
}
.cart_totals .shipping-calculator-button {
  display: inline-block;
  border-bottom: 1px solid;
  color: var(--title-color);
  font-weight: 700;
}
.cart_totals .shipping-calculator-button:hover {
  color: var(--theme-color);
}
.cart_totals .woocommerce-shipping-destination {
  margin-bottom: 10px;
}
.cart_totals .woocommerce-shipping-methods {
  margin-bottom: 0;
}
.cart_totals .shipping-calculator-form {
  display: none;
}
.cart_totals .shipping-calculator-form p:first-child {
  margin-top: 20px;
}
.cart_totals .shipping-calculator-form p:last-child {
  margin-bottom: 0;
}
.cart_totals .shipping-calculator-form .vs-btn {
  padding: 13px 30px;
}
.cart_totals .amount {
  font-weight: 700;
  font-size: 18px;
}
.cart_totals .order-total .amount {
  color: var(--theme-color);
}
.cart_totals input,
.cart_totals select {
  height: 50px;
  padding-left: 20px;
  padding-right: 20px;
  background-position: right 20px center;
  font-size: 14px;
}

/* Medium devices */
@media (max-width: 991px) {
  .cart_table .amount {
    font-size: 16px;
  }
  .cart_table th {
    padding: 23px 8px;
    font-size: 14px;
  }
  .cart_table .cart-productname {
    font-size: 14px;
  }
  .cart_table .vs-cart-coupon {
    width: 100%;
    margin-bottom: 20px;
  }
  .cart_table .actions {
    text-align: center;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .cart_table {
    text-align: left;
    min-width: auto;
    border-collapse: separate;
    border-spacing: 0 20px;
    border: none;
  }
  .cart_table thead {
    display: none;
  }
  .cart_table td {
    padding: 15px;
    display: block;
    width: 100%;
    padding-left: 25%;
    text-align: right;
    border: 1px solid var(--border-color);
    border-bottom: none;
  }
  .cart_table td::before {
    display: block;
  }
  .cart_table td:last-child {
    border-bottom: 1px solid var(--border-color);
  }
  .cart_table td.actions {
    padding-left: 15px;
    text-align: center;
  }
  .cart_table td.actions > .vs-btn {
    margin-top: 10px;
    display: block;
    width: -moz-max-content;
    width: max-content;
    margin-left: auto;
    margin-right: auto;
  }
  .cart_table td.actions > .vs-btn:last-child {
    margin-right: auto;
  }
  .cart_table .vs-cart-coupon {
    width: 100%;
    text-align: center;
    float: none;
    justify-content: center;
    display: block;
    padding-bottom: 10px;
    border: none;
    border-radius: 0;
  }
  .cart_table .vs-cart-coupon input {
    border: 1px solid var(--vs-secondary-color);
    border-radius: 9999px;
    width: 100%;
    margin-bottom: 10px;
  }
  .cart_totals th,
  .cart_totals td {
    padding: 15px 10px;
  }
  .cart_totals th:first-child,
  .cart_totals td:first-child {
    width: 17%;
    line-height: 1.4;
  }
}
/*------------------- 4.30. Checkout  -------------------*/
.woocommerce-form-coupon,
.woocommerce-form-login {
  padding: 40px;
  background-color: var(--smoke-color);
  --bs-gutter-x: 20px;
  margin-bottom: 40px;
  border-radius: 20px;
}
.woocommerce-form-coupon .form-group:last-child,
.woocommerce-form-login .form-group:last-child {
  margin-bottom: 0;
}

.woocommerce-checkout .form-select,
.woocommerce-checkout .select2-container,
.woocommerce-checkout .form-control {
  margin-bottom: 0;
}
.woocommerce-checkout .select2-container--open .select2-dropdown--below {
  margin-top: -35px;
}
.woocommerce-checkout .select2-container--open .select2-dropdown--above {
  position: relative;
  bottom: -30px;
}
.woocommerce-checkout .select2-dropdown {
  border: 1px solid var(--border-color);
  border-top: none;
}
.woocommerce-checkout .select2-container--default .select2-selection--single .select2-selection__rendered,
.woocommerce-checkout .form-control:focus {
  color: var(--body-color);
}

.select2-container--default .select2-selection--single {
  height: 60px;
  border: 1px solid var(--border-color);
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
  line-height: 60px;
  padding-left: 30px;
  padding-right: 25px;
}

.woocommerce-billing-fields .form-row {
  margin-bottom: 0;
}

.select2-container--default .select2-selection--single .select2-selection__arrow b:before {
  content: "\f107";
  font-family: var(--icon-font);
}

.select2-container--default .select2-selection--single .select2-selection__arrow b {
  margin: 0;
  border: none;
  top: 0;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
  height: 60px;
  line-height: 60px;
  margin-right: 18px;
}

span.select2-selection.select2-selection--single:focus {
  outline: none;
}

.shipping-calculator-form .form-select,
.shipping-calculator-form .form-control {
  height: 40px;
  padding-left: 15px;
  font-size: 14px;
  border-radius: 10px;
  background-position: right 13px center;
}
.shipping-calculator-form .vs-btn {
  font-size: 14px;
  padding: 0 20px;
  width: -moz-max-content;
  width: max-content;
  height: 40px;
}

.checkout-ordertable th,
.checkout-ordertable td {
  border: none;
  vertical-align: top;
  padding: 5px 0;
  font-size: 14px;
  font-weight: 700;
  color: var(--title-color);
}
.checkout-ordertable ul {
  margin: 0;
  padding: 0;
}
.checkout-ordertable .order-total .amount {
  color: var(--theme-color);
}
.checkout-ordertable input[type=hidden] ~ label {
  color: var(--theme-color);
}

.woocommerce-checkout .form-group input:not(:last-child) {
  margin-bottom: var(--bs-gutter-x);
}

.checkout-ordertable th,
.checkout-ordertable td {
  border: none;
  border-bottom: 1px solid var(--border-color);
  border-right: 1px solid var(--border-color);
  text-align: right;
  padding: 5px 20px;
  vertical-align: middle;
}
.checkout-ordertable tr:first-child th,
.checkout-ordertable tr:first-child td {
  border-top: 1px solid var(--border-color);
}
.checkout-ordertable tr:last-child th,
.checkout-ordertable tr:last-child td {
  border-bottom: none;
}
.checkout-ordertable th {
  text-align: left;
}

.woocommerce-checkout-payment {
  text-align: left;
}
.woocommerce-checkout-payment ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
}
.woocommerce-checkout-payment ul li {
  padding-top: 10px;
  border-bottom: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 16px;
}
.woocommerce-checkout-payment ul input[type=radio] ~ label {
  margin-bottom: 17px;
  color: var(--body-color);
}
.woocommerce-checkout-payment ul input[type=radio] ~ label img {
  margin-bottom: -2px;
  margin-left: 10px;
}
.woocommerce-checkout-payment .place-order {
  padding-top: 30px;
}
.woocommerce-checkout-payment .payment_box {
  color: var(--title-color);
  background-color: var(--light-color);
  border: 1px solid var(--border-color);
  border-bottom: none;
  font-size: 14px;
  padding: 10px 20px;
  border-radius: 4px;
  display: none;
}
.woocommerce-checkout-payment .payment_box p {
  margin: 0;
}

.vs-checkout-wrapper form.woocommerce-form {
  margin-bottom: 25px;
}

/* Small devices */
@media (max-width: 767px) {
  tfoot.checkout-ordertable th {
    display: none;
  }
  .woocommerce-checkout-payment ul input[type=radio] ~ label img {
    max-width: 150px;
  }
  .checkout-ordertable th,
  .checkout-ordertable td {
    padding: 5px 20px 5px 60px;
  }
  .woocommerce-form-coupon,
  .woocommerce-form-login {
    padding: 40px 20px;
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  .woocommerce-message,
  .woocommerce-info {
    font-size: 12px;
    padding: 11px 15px;
  }
}
/*------------------- 4.31. Wishlist  -------------------*/
/*=================================
    05. Spacing
==================================*/
.pb-30 {
  padding-bottom: 30px;
}

.pb-1px {
  padding-bottom: 1px;
}

.pt-30 {
  padding-top: 30px;
}

.mt-n1 {
  margin-top: -0.25rem;
}

.mt-n2 {
  margin-top: -0.5rem;
}

.mt-n3 {
  margin-top: -1rem;
}

.mt-n4 {
  margin-top: -1.5rem;
}

.mt-n5 {
  margin-top: -3rem;
}

.mb-n1 {
  margin-bottom: -0.25rem;
}

.mb-n2 {
  margin-bottom: -0.5rem;
}

.mb-n3 {
  margin-bottom: -1rem;
}

.mb-n4 {
  margin-bottom: -1.5rem;
}

.mb-n5 {
  margin-bottom: -3rem;
}

.mb-30 {
  margin-bottom: 30px;
}

.mb-40 {
  margin-bottom: 40px;
}

.mt-30 {
  margin-top: 30px;
}

.space,
.space-top {
  padding-top: var(--section-space);
}

.space,
.space-bottom {
  padding-bottom: var(--section-space);
}

.space-extra,
.space-extra-top {
  padding-top: calc(var(--section-space) - 30px);
}

.space-extra,
.space-extra-bottom {
  padding-bottom: calc(var(--section-space) - 30px);
}

/* Medium devices */
@media (max-width: 991px) {
  .space,
  .space-top {
    padding-top: var(--section-space-mobile);
  }
  .space,
  .space-bottom {
    padding-bottom: var(--section-space-mobile);
  }
  .space-extra,
  .space-extra-top {
    padding-top: calc(var(--section-space-mobile) - 30px);
  }
  .space-extra,
  .space-extra-bottom {
    padding-bottom: calc(var(--section-space-mobile) - 30px);
  }
  .space-top-md-none {
    padding-top: 0;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .pt-sm-none {
    padding-top: 0;
  }
}
/*# sourceMappingURL=style.css.map */
