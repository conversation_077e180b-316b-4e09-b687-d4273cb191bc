/* LayerSlider V6 Skin */



.ls-v6 .ls-nav-prev,
.ls-v6 .ls-nav-next,
.ls-v6 .ls-bottom-slidebuttons a,
.ls-v6 .ls-nav-start,
.ls-v6 .ls-nav-stop,
.ls-v6 .ls-nav-prev:after,
.ls-v6 .ls-nav-next:after,
.ls-v6 .ls-playvideo,
.ls-v6 .ls-playvideo:after {
	transition: border .3s ease-in-out,
				border-radius .2s ease-in-out,
				background-color .3s ease-in-out,
				-o-transform .2s ease-in-out,
				-ms-transform .2s ease-in-out,
				-moz-transform .2s ease-in-out,
				-webkit-transform .2s ease-in-out,
				transform .2s ease-in-out
				!important;
}

.ls-v6 .ls-playvideo {
	width: 50px;
	height: 50px;
	margin-left: -25px;
	margin-top: -25px;
}

.ls-v6 .ls-playvideo:after {
	content: '';
	position: absolute;
	top: 50%;
	margin-top: -16px;
	width: 26px;
	height: 26px;
	border: 3px solid transparent;
	border-top: 3px solid rgba(255,255,255,0.75);
	right: 20px;
	-o-transform: rotate(45deg);
	-ms-transform: rotate(45deg);
	-moz-transform: rotate(45deg);
	-webkit-transform: rotate(45deg);
	transform: rotate(45deg);
	border-right: 3px solid rgba(255,255,255,0.75);
}

.ls-v6 .ls-playvideo:hover {
	-o-transform: scaleX(1.2) scaleY(.7);
	-ms-transform: scaleX(1.2) scaleY(.7);
	-moz-transform: scaleX(1.2) scaleY(.7);
	-webkit-transform: scaleX(1.2) scaleY(.7);
	transform: scaleX(1.2) scaleY(.7);
}

.ls-v6 .ls-playvideo:hover:after {
	border-top-color: rgba(255,255,255,1) !important;
	border-right-color: rgba(255,255,255,1) !important;
}

.ls-v6 .ls-nav-prev,
.ls-v6 .ls-nav-next {
	width: 50px;
	height: 50px;
	z-index: 10000;
	top: 50%;
	margin-top: -25px;
	position: absolute;
	border-radius: 25px;
}

.ls-v6 .ls-nav-prev:after,
.ls-v6 .ls-nav-next:after {
	content: '';
	position: absolute;
	top: 50%;
	margin-top: -16px;
	width: 26px;
	height: 26px;
	border: 3px solid transparent;
	border-top: 3px solid rgba(255,255,255,0.75);
}

.ls-v6 .ls-nav-prev:after {
	left: 20px;
	-o-transform: rotate(-45deg);
	-ms-transform: rotate(-45deg);
	-moz-transform: rotate(-45deg);
	-webkit-transform: rotate(-45deg);
	transform: rotate(-45deg);
	border-left: 3px solid rgba(255,255,255,0.75);
}

.ls-v6 .ls-nav-next:after {
	right: 20px;
	-o-transform: rotate(45deg);
	-ms-transform: rotate(45deg);
	-moz-transform: rotate(45deg);
	-webkit-transform: rotate(45deg);
	transform: rotate(45deg);
	border-right: 3px solid rgba(255,255,255,0.75);
}

.ls-v6 .ls-nav-prev:hover,
.ls-v6 .ls-nav-next:hover {
	-o-transform: scaleX(1.2) scaleY(.7);
	-ms-transform: scaleX(1.2) scaleY(.7);
	-moz-transform: scaleX(1.2) scaleY(.7);
	-webkit-transform: scaleX(1.2) scaleY(.7);
	transform: scaleX(1.2) scaleY(.7);
}

.ls-v6 .ls-nav-prev:hover:after {
	border-top-color: rgba(255,255,255,1) !important;
	border-left-color: rgba(255,255,255,1) !important;
}

.ls-v6 .ls-nav-next:hover:after {
	border-top-color: rgba(255,255,255,1) !important;
	border-right-color: rgba(255,255,255,1) !important;
}

.ls-v6 .ls-nav-prev {
	left: 10px;
}

.ls-v6 .ls-nav-next {
	right: 10px;
}

.ls-v6 .ls-bottom-slidebuttons a,
.ls-v6 .ls-nav-sides {
	margin: 0 5px;
	width: 8px !important;
	height: 8px;
}

.ls-v6 .ls-bottom-slidebuttons a {
	border-radius: 10px;
	border: 2px solid rgba(255,255,255,0.75) !important;
}

.ls-v6 .ls-bottom-slidebuttons a.ls-nav-active {
	background-color: rgba(255,255,255,0.75);
}

.ls-v6 .ls-bottom-slidebuttons a:hover {
	border: 2px solid rgba(255,255,255,1) !important;
}

.ls-v6 .ls-bottom-slidebuttons a.ls-nav-active:hover {
	background-color: rgba(255,255,255,1) !important;
}

.ls-v6 .ls-nav-start:after {
	position: absolute;
	content: '';
	width: 7px;
	height: 7px;
	-o-transform: rotate(45deg);
	-ms-transform: rotate(45deg);
	-moz-transform: rotate(45deg);
	-webkit-transform: rotate(45deg);
	transform: rotate(45deg);
	border-top: 2px solid rgba(255,255,255,0.75);
	border-right: 2px solid rgba(255,255,255,0.75);
	left: -14px;
	top: -10px;
}

.ls-v6 .ls-nav-start {
	position: relative;
	-o-transform: scaleX(1.5);
	-ms-transform: scaleX(1.5);
	-moz-transform: scaleX(1.5);
	-webkit-transform: scaleX(1.5);
	transform: scaleX(1.5);
}

.ls-v6 .ls-nav-start:hover:after,
.ls-v6 .ls-nav-start-active:after {
	border-top: 2px solid rgba(255,255,255,1);
	border-right: 2px solid rgba(255,255,255,1);
}

.ls-v6 .ls-nav-stop {
	border-left: 2px solid rgba(255,255,255,0.75) !important;
	border-right: 2px solid rgba(255,255,255,0.75) !important;
	width: 4px;
	height: 12px;
	margin-left: 5px;
}

.ls-v6 .ls-nav-stop:hover,
.ls-v6 .ls-nav-stop-active {
	border-left: 2px solid rgba(255,255,255,1) !important;
	border-right: 2px solid rgba(255,255,255,1) !important;
}

.ls-v6 .ls-bottom-slidebuttons,
.ls-v6 .ls-bottom-slidebuttons a,
.ls-v6 .ls-nav-start,
.ls-v6 .ls-nav-stop,
.ls-v6 .ls-nav-sides {
	display: inline-block !important;
}

.ls-v6 .ls-bottom-nav-wrapper {
	top: -30px;
}

.ls-v6 .ls-nav-sides {
	width: 0px;
}

.ls-v6 .ls-thumbnail-hover {
	bottom: 30px;
	padding: 2px;
	margin-left: 7px;
}

.ls-v6 .ls-thumbnail-hover-bg {
	background: #eee;
	border-radius: 3px;
}

.ls-v6 .ls-thumbnail-hover span {
	border: 5px solid #eee;
	margin-left: -5px;
}

.ls-v6 .ls-thumbnail {
	top: 10px;
}

.ls-v6 .ls-thumbnail-inner {
	padding: 2px;
	margin-left: -2px;
	background: #fff;
}

.ls-v6 .ls-thumbnail-slide a {
	margin-right: 2px;
}

.ls-v6 .ls-nothumb {
	background: #eee;
}

.ls-v6 .ls-loading-container {
	width: 40px;
	height: 40px;
	margin-left: -20px;
	margin-top: -20px;
	background-position: -450px -150px;
}

.ls-v6 .ls-loading-indicator {
	width: 22px;
	height: 22px;
	margin-top: 9px;
	background-image: url(loading.gif);
}

.ls-v6 .ls-circle-timer {
	top: 16px;
	right: 16px;
	width: 24px;
	height: 24px;
}

.ls-v6 .ls-ct-half,
.ls-v6 .ls-ct-center {
	border-width: 2px;
}
