// Calendar Css
.dm-calendar-left {
  .card {
    box-shadow: 0 5px 20px rgba(var(--light-gray-rgba), 0.03);
  }

  .btn-create-event {
    width: 100%;
    border-radius: 8px;
    margin-bottom: 25px;
  }
}

.date-picker__calendar-height{
  min-height: 474px;
  @include xl{
    height: auto;
    min-height: auto;
  }
}
.date-picker--demo5{
  .date-picker__calendar {
    .ui-datepicker-calendar{
      width: 100%;
      thead {
        th{
          padding: 21px 2px 23px 0;
          width: 40px;
          height:40px;
          font-size: 13px;
          font-weight: 500;
          color: var(--color-light);
          @include ssm{
            width: 28px;
            height:28px;
          }

        }
      }
      tbody{
        td {
          a{
            border-radius: 6px;
            width: 40px;
            height: 40px;
            margin: 6px auto;
            color: var(--color-gray);
            font-size:12px;
            font-weight: 400;
            @include ssm{
              width: 28px;
              height:28px;
            }
            &.ui-state-highlight{
              color: var(--color-white);
            }
          }
        }
      }
    }
    .ui-datepicker-header {
      display: flex;
      align-items: center;
      justify-content: center;
      a{
        &.ui-corner-all{
          color:var(--color-lighten);
          position: initial;
          &:hover{
            color: var(--color-primary);
          }
        }
        &.ui-datepicker-prev{
          order: 1;
        }
        &.ui-datepicker-next{
          order: 3;
        }
      }
      .ui-datepicker-title{
        order: 2;
        margin: 0 20px 0 20px;
        padding: 0;
        span{
          font-size: 16px;
          font-weight: 500;
        }
      }
    }
  }
}


table.fc-scrollgrid {
  thead {
    .fc-scroller-harness {
      .fc-scroller {
        overflow-y: hidden !important;
        background-color: var(--bg-normal);
      }
    }
  }
}

.fc-theme-standard .fc-list {
  border: 1px solid var(--border-color);
  .fc-list-empty {
    background-color:var(--color-white);
  }
}

// Calendar Page Grid
.calendar-grid {
  .col-xl-3 {
    @include xxl {
      flex: 0 0 33.3333%;
      max-width: 33.3333%;
    }
    @include md {
      flex: 0 0 100%;
      max-width: 390px;
      margin: 0 auto;
    }
  }
  .col-xl-9 {
    @include xxl {
      flex: 0 0 66.66667%;
      max-width: 66.66667%;
    }
    @include md {
      flex: 0 0 100%;
      max-width: 100%;
    }
  }
}

// Date Picker
.date-picker {
  @include e("top") {
    margin-bottom: 5px;

    a {
      svg,
      img {
        width: 16px;
        height: 16px;
        color:var(--color-light);
      }
    }
  }

  @include e("date") {
    border: 0 none;
    font-size: 16px;
    font-weight: 500;
    background: none;
  }

  @include e("calendar") {
    .ui-datepicker {
      padding: 0;
      box-shadow: 0 0;
      position: relative;
    }
    .ui-datepicker-header {
      .ui-datepicker-prev,
      .ui-datepicker-next {
        position: absolute;
        width: auto;
        height: auto;
        margin: 0;
      }
      .ui-datepicker-title {
        font-size: 16px;
        font-weight: 500;
        line-height: 1.5;
        margin-bottom: 16px;
        span {
          color: var(--color-dark);
        }
      }
      .ui-corner-all {
        top: 5px;
        &:after {
          font-size: 12px;
        }
      }
      a.ui-datepicker-next {
        right: 25px;
      }
      a.ui-datepicker-prev {
        left: 25px;
      }
    }
    .ui-datepicker-calendar {
      margin: 0 auto;
      thead {
        tr th span {
          font-weight: 500;
          color:var(--color-light);
        }
      }
      tbody td a {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 3px auto;
        width: 2.5rem;
        height: 2.5rem;
        @include xxl {
          width: 2.4rem;
          height: 2.4rem;
        }
        @include cMq2(1300px) {
          font-size: 12px;
          width: 1.5rem;
          height: 1.5rem;
        }
        @include cMq2(1300px) {
          width: 2rem;
          height: 2rem;
        }
        @include xl {
          width: 2rem;
          height: 2rem;
        }
        @include xs {
          width: 1.5rem;
          height: 1.5rem;
        }
      }
    }
  }
}

// Draggable Event
.draggable-events {
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-weight: 500;
  }

  a {
    svg,
    img {
      width: 18px;
      height: 18px;
      color:var(--color-light);
    }
  }
}

// Draggable Event List
.draggable-event-list {
  margin-top: 20px;

  @include e("single") {
    cursor: pointer;

    &:not(:last-child) {
      margin-bottom: 15px;
    }

    .event-text {
      font-size: 14px;
      margin-left: 12px;
      color:var(--color-gray);
    }
  }
}

#full-calendar {
  .fc-toolbar {
    flex-wrap: wrap;
  }
  .fc-header-toolbar {
    @include xl {
      flex-flow: column;
      justify-content: center;
    }
  }
  .fc-toolbar-chunk {
    &:last-child {
      @include xl {
        margin-top: 20px;
      }
    }
    > div {
      display: flex;
      align-items: center;
    }

    .fc-today-button {
      padding: 6.5px 15px;
      text-transform: capitalize;
      font-size: 13px;
      font-weight: 500;
      border: 1px solid var(--border-color);
      margin-right: 30px;
      color:var(--color-gray);
      background-color:var(--color-white);
      @include xs {
        margin-right: 15px;
        padding: 6px 10px;
      }
    }

    .fc-button.fc-prev-button,
    .fc-button.fc-next-button {
      padding: 0.25em 0.563em;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      background-color:var(--color-white);
      @include xs {
        padding: 0.2rem 0.4rem;
      }

      span.fc-icon {
        font-size: 14px;
        margin-top: -4px;
        color:var(--color-light);
      }
    }

    .fc-toolbar-title {
      margin: 0 20px;
      font-size: 16px;
      font-weight: 500;
      color: var(--color-dark);
      @include cMq2(1300px) {
        font-size: 14px;
        margin: 0 14px;
      }
      @include xs {
        font-size: 12px;
        margin: 10px;
      }
    }

    .fc-button-group {
      border-radius: 0 4px 4px 0;

      .fc-button-primary {
        background-color:var(--color-white);

        &.fc-button-active {
          background-color: var(--color-primary);
          color:var(--color-white);
        }
      }

      .fc-button {
        font-size: 13px;
        font-weight: 500;
        padding: 6.5px 13.68px;
        text-transform: capitalize;
        border:1px solid var(--border-color);
        color:var(--color-light);
        @include xxl {
          padding: 6.5px 10px;
        }
        &:first-child {
          border-radius: 4px 0 0 4px;
        }

        &.fc-dayGridMonth-button {
          border-radius: 0 4px 4px 0;
          border-right: 1px solid var(--border-color);
        }

        &.fc-listMonth-button {
          display: flex;
          align-items: center;
          border: 0 none;
          margin-left: 0;
          color:var(--color-light);
          i,
          svg,
          img {
            font-size: 15px;
            min-width: 14px;
            margin-right: 6px;
            color:var(--color-primary);
          }
          &.fc-button-active {
            background-color:var(--color-white);
            color:var(--color-primary);
          }
        }

        &:focus {
          outline: none;
          box-shadow: 0 0;
        }
      }
      @media (max-width: 700px) {
        margin-top: 10px;
      }
    }
  }
}

// Full Calender View
.fc-view {
  .fc-col-header {
    background-color: var(--bg-normal);

    tr {
      th {
        padding: 12.5px 0;
        border:1px solid var(--border-color);

        .fc-scrollgrid-sync-inner {
          text-align: left;

          .fc-col-header-cell-cushion {
            padding-left: 22px;
            font-size: 14px;
            font-weight: 500;
            color:var(--color-gray);
            @include cMq2(1300px) {
              padding-left: 5px;
              font-size: 11px;
            }
            @include ssm {
              padding: 0;
              text-overflow: ellipsis;
              font-size: 11px;
            }
          }
        }
      }
    }
  }
}

.fc-theme-standard {
  td,
  th {
    border:1px solid var(--border-color);
  }

  .fc-scrollgrid {
    border:1px solid var(--border-color);
  }

  .fc-daygrid-day.fc-day-today {
    background-color:var(--color-white);
  }
}

.fc-timegrid-event {
  .fc-event-resizer {
    display: block !important;

    &.fc-event-resizer-end {
      position: relative;

      &:after,
      &:before {
        position: absolute;
        left: 50%;
        height: 1px;
        width: 10px;
        background: var(--color-white);
        content: "";
        transform: translateY(-50%);
        top: -8px;
      }
      &:before {
        top: -5px;
      }
    }
  }

  &.primary {
    .fc-event-resizer.fc-event-resizer-end {
      &:after,
      &:before {
        background-color: rgba(var(--color-primary-rgba), 0.5);
      }
    }
  }

  &.secondary {
    .fc-event-resizer.fc-event-resizer-end {
      &:after,
      &:before {
        background-color: rgba(var(--color-secondary-rgba), 0.5);
      }
    }
  }

  &.success {
    .fc-event-resizer.fc-event-resizer-end {
      &:after,
      &:before {
        background-color: rgba($success, 0.5);
      }
    }
  }

  &.warning {
    .fc-event-resizer.fc-event-resizer-end {
      &:after,
      &:before {
        background-color: rgba(var(--color-warning-rgba), 0.5);
      }
    }
  }
}

.fc-timegrid-slots {
  tr {
    &:nth-child(2n) {
      border-bottom: 1px solid var(--border-color);
    }
  }

  .fc-timegrid-slot {
    height: 10px;
    line-height: 1.2;
    padding: 1px 12px;
    background-color:var(--color-white);
    border: 0 none;

    &:first-child {
      border-right: 1px solid var(--border-color);
    }

    .fc-timegrid-slot-label-frame {
      position: relative;
      top: 100%;
      margin-top: 7.5px;
    }
  }

  .fc-timegrid-slot-label-cushion {
    text-transform: uppercase;
    text-align: center;
    font-size: 12px;
  }
}

.fc-media-screen {
  .fc-timegrid-event {
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    margin-left: 10px;
    padding: 4px 10px 4px;
    border-radius: 3px 6px 6px 3px;
    background-color: #efeffe;
    border-color: #efeffe;

    .fc-event-main-frame {
      .fc-event-time {
        font-size: 12px;
      }

      .fc-event-title-container {
        margin-bottom: 1px;
        flex-grow: 0;
        order: -1;
      }

      .fc-event-title {
        font-weight: 500;
        font-size: 13px;
      }
    }
  }
}

.fc-timegrid-event {
  min-width: 140px;
  &.primary {
    background-color: #efeffe !important;
    border-left-color:  var(--color-primary);
    border-left-width: 2px;

    .fc-event-main {
      color: var(--color-white);

      .fc-event-time,
      .fc-event-title {
        color:  var(--color-primary);
      }
    }

    &:hover {
      background-color: #efeffe !important;
    }
  }

  &.secondary {
    background-color: #fff0f6 !important;
    border-left-color: var(--color-secondary);
    border-left-width: 2px;
    color: var(--color-secondary);
    .fc-event-main,
    .fc-event-time,
    .fc-event-title {
      color: var(--color-secondary);
    }
    &:hover {
      background-color: #fff0f6 !important;
    }
  }
  &.success {
    background-color: #e8faf4 !important;
    border-left-color: $success;
    border-left-width: 2px;
    .fc-event-time,
    .fc-event-title {
      color: $success;
    }
    &:hover {
      background-color: #e8faf4 !important;
    }
  }
  &.warning {
    background-color: #fff3e6 !important;
    border-left-color: var(--color-warning);
    border-left-width: 2px;
    .fc-event-time,
    .fc-event-title {
      color: var(--color-warning);
    }

    &.success {
      background-color: #fff3e6 !important;
      border-left-color: $success;
      border-left-width: 2px;

      .fc-event-time,
      .fc-event-title {
        color: $success;
      }

      &:hover {
        background-color: rgba($success, 0.1) !important;
      }
    }
  }
}
// Weekview
.fc-timeGridWeek-view {
  .fc-event-resizer {
    display: none !important;
  }
}
// Month View
.fc-daygrid-day {
  &.fc-day-today {
    background-color: rgba(var(--color-primary-rgba), 0.05) !important;
    border-top: 2px solid  var(--color-primary);
    .fc-daygrid-day-number {
      color:var(--color-primary);
    }
  }

  .fc-daygrid-event {
    color: #444;
    &.primary {
      background-color: var(--color-primary);

      &.fc-h-event {
        border-color: var(--color-primary);
      }
    }

    &.secondary {
     background-color:var(--color-secondary);

      &.fc-h-event {
        border-color: var(--color-secondary);
      }
    }

    &.success {
     background-color:var(--color-success);

      &.fc-h-event {
        border-color: $success;
      }
    }

    &.warning {
     background-color:var(--color-warning);

      &.fc-h-event {
        border-color: var(--color-warning);
      }
    }
    .fc-event-time {
      display: none;
    }
  }

  .fc-daygrid-day-events {
    .fc-daygrid-event-harness {
      margin: 0 6px;
    }

    .fc-daygrid-event-harness + .fc-daygrid-event-harness {
      margin-top: 6px;
    }
  }

  .fc-daygrid-event {
    padding: 5.5px 12px;
    font-size: 13px;
    color: var(--color-white) !important;
    @include ssm {
      font-size: 10px;
      margin: 0;
      padding: 2px 5px;
    }

    .fc-daygrid-event-dot {
      display: none;
    }

    .fc-event-title {
      font-weight: 400;
      @include xs {
        display: none;
      }
    }
  }
}

.fc-daygrid-day-top {
  margin-bottom: 8px;

  .fc-daygrid-day-number {
    font-size: 14px;
    margin: 6px 8px 0 0;
    color:var(--color-gray);
  }
}

// Schedule View
.fc-listMonth-view {
  .fc-list-day {
    flex: 0 0 20%;
    max-width: 20%;
    border-bottom: 1px solid var(--border-color);

    &:last-child {
      border-bottom: 1px solid var(--border-color);
    }

    th {
      color: var(--color-dark);
      border: 0 none;
    }

    .fc-list-day-cushion {
      display: flex;
      font-size: 14px;
      font-weight: 400;
      background-color: transparent;
      padding: 12px 14px;
      .fc-list-day-side-text {
        font-weight: 500;
      }
    }
  }

  .fc-list-event {
    cursor: pointer;
    &.primary,
    &.warning,
    &.secondary,
    &.success {
      background-color: transparent !important;
    }
    &:hover {
      background-color: var(--color-white) !important;
    }

    td {
      font-size: 14px;
      background-color: var(--color-white) !important;
      padding: 16px 14px;
    }
  }

  .fc-list-event + .fc-list-event {
    margin-left: 20%;
    border: 0 none;
  }
}

// Create Event Modal
.c-event-dialog {
  max-width: 500px;

  .modal-header {
    padding: 20px 25px;

    .modal-title {
      font-size: 15px;
      font-weight: 500;
      color: var(--color-dark);
    }
  }

  .modal-content {
    border-radius: 8px;
    box-shadow: 0 15px 40px rgba(var(--light-gray-rgba), 0.03);
  }

  .modal-body {
    padding: 20px 25px;
  }

  .modal-footer {
    border-top: 0 none;
    padding: 0 25px 25px;

    .btn-white {
      color:var(--color-light);
    }

    .btn-sm {
      line-height: 2.15;
    }
  }
}

// c-event-form
.c-event-form {
  .form-control-md {
    border-radius: 4px;
    border:1px solid var(--border-color);
  }

  .e-form-row {
    &:not(:last-child) {
      margin-bottom: 20px;
    }

    @include e("left") {
      min-width: 82px;
      margin-right: 40px;

      label {
        font-size: 14px;
        color:var(--color-light);
      }
    }

    @include e("right") {
      flex: 1;

      textarea {
        min-height: 100px;
      }
    }
  }
}

// e-info-modal
.e-info-modal {
  .modal-header {
    padding: 0.75rem 1.45rem;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
  }

  .modal-content {
    border-radius: 8px;
    background-color: transparent;
  }

  .modal-body {
    padding: 0.95rem 0.95rem 1.15rem;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
    background-color:var(--color-white);
  }

  .e-info-title {
    font-weight: 500;
    color:var(--color-white);
  }

  .e-info-action {
    display: inline-flex;
    align-items: center;
    .btn-icon {
      background-color: transparent;
      border: 0 none;
      width: 36px;
      height: 36px;
      border-radius: 50%;
      display: inline-flex;
      justify-content: center;
      align-items: center;
      background-color: transparent;

      &:hover {
        img,
        i,
        svg {
          opacity: 1;
        }

        background-color: rgba(var(--bg-lighter-rgba), 0.15);
      }
      img{
        max-width: 14px;
      }
      svg,
      i {
        max-width: 14px;
        opacity: 0.6;
        color:var(--color-white);
        transition: 0.3s;
      }
    }

    .btn-closed {
      padding: 0;
      background-color: transparent;
      width: auto;
      height: auto;
      margin-left: 6px;

      &:hover {
        background-color: transparent;
      }
      svg{
        color: var(--color-dark);
      }
    }
  }

  .e-info-list {
    li {
      display: flex;
      align-items: flex-start;

      &:not(:last-child) {
        margin-bottom: 12px;
      }

      svg,
      img {
        min-width: 14px;
        width: 14px;
        height: 18px;
        margin-right: 12px;
      }

      .list-line {
        font-size: 13px;

        .list-label {
          color:var(--color-light);
        }

        .list-meta {
          font-weight: 500;
          color: var(--color-dark);
        }
      }

      .list-text {
        font-size: 14px;
        color:var(--color-gray);
      }
    }
  }
}
