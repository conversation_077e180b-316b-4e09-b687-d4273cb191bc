.event-style1 {
  display: flex;
  align-items: center;
  border: 1px solid #B5B5B5;
  margin-bottom: 30px;
  border-radius: 30px;

  .event-date {
    background-color: $theme-color;
    color: $white-color;
    text-align: center;
    line-height: 1;
    font-weight: 600;
    font-family: $title-font;
    text-transform: uppercase;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    border-radius: 30px;
    margin: -1px 30px -1px -1px;
    padding: 27px 12px 6px 12px;
    transition: all ease 0.4s;

    .month {
      font-size: 18px;
      margin-bottom: 2px;
    }

    .day {
      font-size: 50px;
    }

    .time {
      display: block;
      width: 100%;
      font-size: 18px;
      line-height: 1;
      background-color: $white-color;
      color: $theme-color;
      border-radius: 9999px;
      padding: 6px 16px;
      margin-top: 13px;
      transition: all ease 0.4s;
    }
  }

  &:hover {
    .event-date {
      background-color: $secondary-color;

      .time{
        color: $secondary-color;
      }
    }

  }

  .event-title {
    margin: -0.2em 0 12px 0;
  }

  .event-content {
    flex: 1;
    padding: 30px 40px 21px 0;
  }

  .event-text {
    margin: 0;
  }

}


@include ml {
  .event-style1 {
    .event-content {
      flex: 1;
      padding: 20px 30px 20px 0;
    }

    .event-date {
      border-radius: 20px;
      margin: -1px 20px -1px -1px;
      padding: 27px 12px 6px 12px;
    }

    .event-title {
      margin: -0.2em 0 12px 0;
      font-size: 22px;
    }
  }
}

@include lg {
  .event-style1 {
    display: block;
    text-align: center;

    .event-date {
      padding-top: 20px;
      margin: -1px;
    }

    .event-content {
      padding: 25px 20px;
    }
  }
}

@include md {
  .event-style1 {
    .event-title {
      font-size: 20px;
    }

    .event-content {
      padding: 25px 15px;
    }
  }
}