<script setup lang="ts">
import About from '@/components/frontend/About.vue'
import About2 from '@/components/frontend/About2.vue'
import Category from '@/components/frontend/Category.vue'
import Classes from '@/components/frontend/Classes.vue'
import Faq from '@/components/frontend/Faq.vue'
import HeroSection from '@/components/frontend/HeroSection.vue'
import NewsLetter from '@/components/frontend/NewsLetter.vue'
import Schedule from '@/components/frontend/Schedule.vue'
import Service from '@/components/frontend/Service.vue'
import Testimonial from '@/components/frontend/Testimonial.vue'
</script>

<template>
    <HeroSection />
    <!--==============================
   About Area  
    ==============================-->
    <About />
    <!--==============================
   Service Area  
==============================-->
    <Service />
    <div data-bg-src="/frontend/assets1/img/bg/bg-h-1-1.jpg"><!--==============================
   About Area  
    ==============================-->
        <About2 />
        <!--==============================
    Category Area
    ==============================-->
        <Category />
    </div><!--==============================
    Schedule Area
    ==============================-->
    <Schedule />
    <!--==============================
    Classes Area
    ==============================-->
    <Classes />
    <!--==============================
    FAQ Area
    ==============================-->
    <Faq />
    <!--==============================
    Testimonial Area
    ==============================-->
    <Testimonial />
    <!--==============================
    Newsletter Area
    ==============================-->
    <NewsLetter />
    <!--==============================
			Footer Area
	==============================-->

</template>
