<script setup>
import { ref } from 'vue'

import useSocialMedia from '@/stores/socialMedia'

const email = ref(null);
function subscribe() {
    useSocialMedia().postNewsLetter({ email: email.value });
}

</script>
<template>
    <section class="form-wrap1 space" data-bg-src="/photos/32520.jpg">
        <div class="container">
            <div class="title-area text-center">
                <div class="mb-3 pb-1"><img src="/frontend/assets1/img/icon/bell-1-1.svg" alt="bell icon"></div>
                <p class="sec-text text-title font-title">Keep up to date with the latest news and offers</p>
                <h2 class="sec-title2">Sign up for our newsletter</h2>
            </div>
            <form @submit.prevent="subscribe" class="form-style1">
                <div class="form-group">
                    <input type="email" v-model="email" placeholder="Enter Your Email Address...">
                    <button class="vs-btn" type="submit">Subscribe</button>
                </div>
            </form>
        </div>
    </section>
</template>