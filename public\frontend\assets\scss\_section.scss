//>>>>> Section Title Start <<<<<//

.section-title {
	z-index: 1;
	.sub-title{
		margin-bottom: 14px;
		font-size: 19px;
		line-height: 24px;
		font-family: $sub-font;
		display: block;
	}
}


//>>>>> Section Title End <<<<<//

//>>>>> Basic Css Start <<<<<//

.center {
	text-align: center;
	margin: 0 auto;
}

.section-bg {
	background-color: $bg-color;
}

.section-bg-2 {
	background-color: $bg-color-2;
}

.section-bg-3 {
	background-color: $theme-color-2;
}

.section-padding {
	padding: 120px 0;

	@include breakpoint(max-xl) {
		padding: 100px 0;
	}

	@include breakpoint(max-lg) {
		padding: 80px 0;
	}
}
.space-top {
	padding-top: 120px;
	@include breakpoint(max-xl) {
		padding-top: 100px;
	}
	@include breakpoint(max-lg) {
		padding-top: 80px;
	}
}
.space-bottom {
	padding-bottom: 120px;
	@include breakpoint(max-xl) {
		padding-bottom: 100px;
	}
	@include breakpoint(max-lg) {
		padding-bottom: 80px;
	}
}
//>>>>> Basic Css End <<<<<//
