import 'vue3-toastify/dist/index.css'

import { defineStore } from 'pinia'
import { toast } from 'vue3-toastify'

import api from '@/api/api'

const useSocialMedia = defineStore("useSocialMedia", {
    state: () => ({
        socialMediaDetails: [],
        admissionList: null,
        totalPages: 1, // Default total pages
    }),

    actions: {
        async sendSocialMediaDetails(details) {
            try {
                const response = await api.post('/social-media', details);
                if (response.data.success === true) {
                    this.socialMediaDetails = response.data.data;
                    toast.success(response.data.message);
                }
            } catch (error) {
                toast.error(error.response?.statusText || 'An error occurred');
            }
        },
        async getSocialMediaDetails() {
            try {
                const response = await api.get('/social-media');
                if (response.data.success === true) {
                    this.socialMediaDetails = response.data.data[0];
                    return this.socialMediaDetails
                }
            } catch (error) {
                toast.error(error.response?.statusText || 'An error occurred');
            }
        },

        async getAllnewsLetter() {
            try {
                const response = await api.get('/getAllnewsLetter');
                if (response.data.success === true) {
                    console.log(response.data.message);
                    return response.data.data
                }
            } catch (error) {
                toast.error(error.response?.statusText || 'An error occurred');
            }
        },
        async postNewsLetter(email) {
            try {
                const response = await api.post('/subscribe-newsletter', email);
                console.log(response.data);
                if (response.data.success === true) {
                    console.log(response.data.message);
                    toast.success(response.data.message);
                    // return response.data.data
                }
            } catch (error) {
                console.log(error);
                toast.error(error.response?.statusText || 'An error occurred');
            }
        },

        async createAdmission(admissionData) {
            try {
                console.log(admissionData);

                const response = await api.post('/admissions', admissionData);
                console.log(response.data);
                if (response.data.success === true) {
                    toast.success(response.data.message);
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                    return response.data.data
                }
            } catch (error) {
                console.log(error);
                toast.error(error.response?.data.message || 'An error occurred');
            }
        },

        async getAdmissionList(page = 1, limit = 10) {
            try {
                const response = await api.get(`/admissions?page=${page}&limit=${limit}`);
                if (response.data.success === true) {
                    if(response.data.data.data.length==0){
                        toast.error('End of the List')
                        setTimeout(() => {
                            window.location.reload();
                        }, 2000);
                    }
                    this.admissionList = response.data.data.data;
                    this.totalPages = response.data.data.total; 
                    return response.data.data.data;
                }
            } catch (error) {
                toast.error(error.response?.statusText || 'An error occurred');
            }
        }
    }
});

export default useSocialMedia;