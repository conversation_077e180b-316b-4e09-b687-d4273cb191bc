<script setup>
import { ref } from 'vue'

import useSocialMedia from '@/stores/socialMedia'

const form = ref({
    child_name: '',
    child_dob: '',
    parent_name: '',
    parent_designation: '',
    email: '',
    phone_no: '',
    relation: '',
    gender: '',
    notify: '',
})

const errors = ref({});

function validateForm() {
    errors.value = {};
    if (!form.value.child_name) errors.value.child_name = "Child's name is required.";
    if (!form.value.child_dob) errors.value.child_dob = "Child's DOB is required.";
    if (!form.value.parent_name) errors.value.parent_name = "<PERSON><PERSON>'s name is required.";
    if (!form.value.parent_designation) errors.value.parent_designation = "<PERSON><PERSON>'s designation is required.";
    if (!form.value.email) errors.value.email = "Email is required.";
    else if (!/\S+@\S+\.\S+/.test(form.value.email)) errors.value.email = "Invalid email format.";
    if (!form.value.phone_no) errors.value.phone_no = "Phone number is required.";
    else if (!/^\d+$/.test(form.value.phone_no)) errors.value.phone_no = "Phone number must be numeric.";
    if (!form.value.relation) errors.value.relation = "Relationship to child is required.";
    if (!form.value.gender) errors.value.gender = "Gender is required.";
    return Object.keys(errors.value).length === 0;
}

function admission() {
    if (validateForm()) {
        useSocialMedia().createAdmission(form.value);
    }
}
</script>
<template>
    <!-- public\img\p9.jpg -->
    <div class="breadcumb-wrapper" data-bg-src="/img/p9.jpg">
        <div class="container z-index-common">
            <div class="breadcumb-content">
                <h1 class="breadcumb-title">Registration</h1>
                <p class="breadcumb-text">Montessori Is A Nurturing And Holistic Approach To Learning</p>
                <div class="breadcumb-menu-wrap">
                    <ul class="breadcumb-menu">
                        <li><a href="index.html">Home</a></li>
                        <li>Registration</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <section class="admission  background-image"
        style="background-image: url(&quot;assets/img/bg/bg-con-1-1.png&quot;);">
        <div class="container">
            <div class="row">
                <div class="col-xl-auto col-xxl-6">
                    <div class="img-box6">
                        <!-- <div class="img-1 mega-hover"><img src="/img/about/con-1-1.jpg" alt="image"></div> -->
                        <!-- <div class="img-2 mega-hover"><img src="/img/about/con-1-2.jpg" alt="image"></div> -->
                    </div>
                </div>
                <div class="col-xl col-xxl-6 align-self-center">
                    <h2 class="sec-title mb-3">Apply for Admission</h2>
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="list-style1">
                                <ul class="list-unstyled mb-0">
                                    <li>Assign practice exercises</li>
                                    <li>Track student progress</li>
                                </ul>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="list-style1">
                                <ul class="list-unstyled">
                                    <li>Videos and articles</li>
                                    <li>Join millions of students</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <form @submit.prevent="admission" class="form-style3">
                        <div class="row justify-content-between">
                            <div class="col-md-6 form-group">
                                <label>Child's Name <span class="required">(Required)</span></label>
                                <input v-model="form.child_name" type="text">
                                <span v-if="errors.child_name" class="error">{{ errors.child_name }}</span>
                            </div>
                            <div class="col-md-6 form-group">
                                <label>Child's DOB <span class="required">(Required)</span></label>
                                <input v-model="form.child_dob" type="date">
                                <span v-if="errors.child_dob" class="error">{{ errors.child_dob }}</span>
                            </div>
                            <div class="col-md-6 form-group">
                                <label>Parent's Name <span class="required">(Required)</span></label>
                                <input v-model="form.parent_name" type="text">
                                <span v-if="errors.parent_name" class="error">{{ errors.parent_name }}</span>
                            </div>
                            <div class="col-md-6 form-group">
                                <label>Parent's Designation <span class="required">(Required)</span></label>
                                <input v-model="form.parent_designation" type="text">
                                <span v-if="errors.parent_designation" class="error">{{ errors.parent_designation
                                }}</span>
                            </div>
                            <div class="col-md-6 form-group">
                                <label>Email <span class="required">(Required)</span></label>
                                <input v-model="form.email" type="email">
                                <span v-if="errors.email" class="error">{{ errors.email }}</span>
                            </div>
                            <div class="col-md-6 form-group">
                                <label>Phone No<span class="required">(Required)</span></label>
                                <input v-model="form.phone_no" type="number">
                                <span v-if="errors.phone_no" class="error">{{ errors.phone_no }}</span>
                            </div>
                            <div class="col-md-6 form-group">
                                <label>Relationship to Child (Father/Mother/Guardian)<span
                                        class="required">(Required)</span></label>
                                <input v-model="form.relation" type="text">
                                <span v-if="errors.relation" class="error">{{ errors.relation }}</span>
                            </div>
                            <div class="col-md-6 form-group">
                                <label>Gender<span class="required">(Required)</span></label>
                                <div>
                                    <input v-model="form.gender" type="radio" name="gender" id="male" value="male">
                                    <label for="male">Male</label>
                                </div>
                                <div>
                                    <input v-model="form.gender" type="radio" name="gender" id="female" value="female">
                                    <label for="female">Female</label>
                                </div>
                                <span v-if="errors.gender" class="error">{{ errors.gender }}</span>
                            </div>
                            <div class="col-auto align-self-center form-group">
                                <input v-model="form.notify" type="checkbox" id="notify" name="notify">
                                <label for="notify">Notify Your child weekly progress</label>
                            </div>
                            <div class="col-auto form-group">
                                <button class="vs-btn" type="submit">Apply Now</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>
</template>

<style setup>
.admission {
    margin: 20px 60px;
}

.error {
    color: red;
    font-size: 0.875rem;
}
</style>