// Primary Button 
.vs-btn {
  display: inline-block;
  vertical-align: middle;
  text-align: center;
  border: none;
  outline: none;
  box-shadow: none;
  overflow: hidden;
  position: relative;
  font-family: $title-font;
  font-size: 16px;
  font-weight: 600;
  padding: 19px 40px 20px 40px;
  background-color: $theme-color;
  color: $white-color;
  text-transform: capitalize;
  border-radius: 9999px;
  line-height: 1;
  transition: all ease 0.4s;
  z-index: 2;

  @include sm {
    padding: 16px 30px;
  }
  
  &:after,
  &:before {
    content: '';
    position: absolute;
    top: var(--border-size, 3px);
    bottom: var(--border-size, 3px);
    left: var(--border-size, 3px);
    width: calc(50% - var(--border-size, 3px));
    background-color: $secondary-color;
    z-index: -1;
    transform: scaleY(0.4);
    transform-origin: 100% 0;
    opacity: 0;
    visibility: hidden;
    transition: all ease 0.4s;
    border-radius: 9999px 0 0 9999px;
  }
  
  &:after {
    left: auto;
    right: var(--border-size, 3px);
    transform-origin: 100% 100%;
    border-radius: 0 9999px 9999px 0;
  }

  &.style2 {
    background-color: $theme-color2;
    color: $title-color;

    &:hover {
      background-color: $white-color;
      color: $white-color;
    }
  }

  &.style3 {
    background-color: $white-color;
    border: 2px solid $theme-color;
    color: $theme-color;
    --border-size: 1px;
  }

  &:hover {
    color: $white-color;
    background-color: $theme-color;

    &:after,
    &:before {
      transform: scaleY(1);
      opacity: 1;
      visibility: visible;
    }
  }
}


// Icon Btn
.icon-btn {
  padding: 0;
  font-size: var(--icon-font-size, 20px);
  width: var(--icon-size, 56px);
  height: var(--icon-size, 56px);
  line-height: 1;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  color: $white-color;
  background-color: $theme-color;
  border-radius: 50%;
  transition: all ease 0.4s;
  position: relative;
  z-index: 1;

  &:before {
    content: '';
    position: absolute;
    left: var(--border-size, 2px);
    top: var(--border-size, 2px);
    right: var(--border-size, 2px);
    bottom: var(--border-size, 2px);
    background-color: $white-color;
    border-radius: inherit;
    z-index: -1;
    transform: scale(0.5);
    transition: all ease 0.4s;
    opacity: 0;
    visibility: hidden;
  }

  &:hover {
    border-color: $theme-color;
    color: $theme-color;

    &:before {
      opacity: 1;
      visibility: visible;
      transform: scale(1.001);
    }
  }

  &.style2 {
    --icon-size: 120px;
    background-color: $smoke-color;
  }
}


// Simple Icon
.simple-icon {
  padding: 0;
  border: none;
  background-color: transparent;
  color: $title-color;
  line-height: 1;
  font-size: 25px;
  vertical-align: middle;

  &.style2 {
    color: $white-color;
  }

  &:hover {
    color: $theme-color;
  }
}



// Play Button 
.play-btn {
  display: inline-block;
  position: relative;
  z-index: 1;

  >i {
    display: inline-block;
    width: var(--icon-size, 85px);
    height: var(--icon-size, 85px);
    line-height: var(--icon-size, 85px);
    text-align: center;
    background-color: $white-color;
    color: $theme-color;
    font-size: var(--icon-font-size, 1.5em);
    border-radius: 50%;
    z-index: 1;
    transition: all ease 0.4s;
  }


  &:after,
  &:before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background-color: $white-color;
    @extend .ripple-animation;
    z-index: -1;
    border-radius: 50%;
    transition: all ease 0.4s;
  }

  &:after {
    animation-delay: 2s;
  }


  &:hover {

    &:after,
    &::before,
    i {
      background-color: $theme-color;
      color: $white-color;
    }
  }

  
}


// Link Button
.link-btn {
  font-weight: 600;
  font-size: 16px;
  display: inline-block;
  line-height: 0.8;
  position: relative;
  padding-bottom: 2px;
  margin-bottom: -2px;
  text-transform: capitalize;

  i {
    margin-left: 7px;
    font-size: 0.9rem;
  }

  &:before {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 0;
    height: 1px;
    background-color: $theme-color;
    transition: all ease 0.4s;
  }

  &:hover {
    color: $theme-color;

    &::before {
      width: 100%;
    }
  }

}


// Scroll To Top
.scroll-btn {
  position: fixed;
  bottom: 300px;
  right: 30px;
  z-index: 94;
  opacity: 0;
  visibility: hidden;
  display: inline-block;
  border-radius: 50%;

  i {
    display: inline-block;
    background-color: $theme-color;
    color: $white-color;
    text-align: center;
    font-size: 16px;
    width: var(--btn-size, 50px);
    height: var(--btn-size, 50px);
    line-height: var(--btn-size, 50px);
    z-index: 2;
    border-radius: inherit;
    position: relative;
    transition: all ease 0.8s;
  }

  &:before {
    content: '';
    position: absolute;
    left: var(--extra-shape, -6px);
    top: var(--extra-shape, -6px);
    right: var(--extra-shape, -6px);
    bottom: var(--extra-shape, -6px);
    background-color: $body-bg;
    border-radius: inherit;
    z-index: 1;
    transition: all ease 0.4s;
  }

  &:focus,
  &:hover {
    i {
      background-color: $secondary-color;
      color: $white-color;
    }
  }

  &.show {
    bottom: 120px;
    opacity: 1;
    visibility: visible;
  }

  @include sm {
    --btn-size: 40px;
    --extra-shape: -4px;
    right: 15px;
    bottom: 50px;

    &.show {
      bottom: 15px;
    }
  }
}

.scrollToTop {
  position: fixed;
  right: 60px;
  bottom: 500px;
  opacity: 0;
  visibility: hidden;
  transition: all ease 0.4s;
  z-index: 96;

  &.show {
    bottom: 60px;
    opacity: 1;
    visibility: visible;
  }
}


@include sm {
  .play-btn {
    --icon-size: 60px;
  }

  .scrollToTop {
    right: 20px;

    &.show {
      bottom: 20px;
    }
  }
}