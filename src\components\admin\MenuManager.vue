<template>
  <div class="p-6 bg-white rounded-lg shadow-lg">
    <h2 class="text-2xl font-bold text-gray-800 mb-6">Dynamic Menu Manager</h2>
    
    <!-- Menu Actions -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
      <!-- Add Menu Item -->
      <div class="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg border border-blue-200">
        <h3 class="font-semibold text-blue-800 mb-3">Add Menu Item</h3>
        <div class="space-y-2">
          <input 
            v-model="newMenuItem.text" 
            placeholder="Menu Text" 
            class="w-full px-3 py-2 border border-blue-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
          <input 
            v-model="newMenuItem.icon" 
            placeholder="Icon Class (e.g., uil uil-home)" 
            class="w-full px-3 py-2 border border-blue-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
          <input 
            v-model="newMenuItem.link" 
            placeholder="Link (optional)" 
            class="w-full px-3 py-2 border border-blue-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
          <select 
            v-model="newMenuItem.parent" 
            class="w-full px-3 py-2 border border-blue-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">Main Menu</option>
            <option v-for="item in parentMenus" :key="item.text" :value="item.text">
              {{ item.text }}
            </option>
          </select>
          <button 
            @click="addMenuItem" 
            class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
          >
            Add Menu
          </button>
        </div>
      </div>

      <!-- Set Badge -->
      <div class="bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-lg border border-purple-200">
        <h3 class="font-semibold text-purple-800 mb-3">Set Badge</h3>
        <div class="space-y-2">
          <select 
            v-model="badgeMenuItem" 
            class="w-full px-3 py-2 border border-purple-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          >
            <option value="">Select Menu Item</option>
            <option v-for="item in allMenuItems" :key="item.text" :value="item.text">
              {{ item.text }}
            </option>
          </select>
          <input 
            v-model="badgeText" 
            placeholder="Badge Text" 
            class="w-full px-3 py-2 border border-purple-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          >
          <div class="flex space-x-2">
            <button 
              @click="setBadge" 
              class="flex-1 bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 transition-colors"
            >
              Set Badge
            </button>
            <button 
              @click="clearBadge" 
              class="flex-1 bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 transition-colors"
            >
              Clear
            </button>
          </div>
        </div>
      </div>

      <!-- Menu Controls -->
      <div class="bg-gradient-to-r from-green-50 to-emerald-50 p-4 rounded-lg border border-green-200">
        <h3 class="font-semibold text-green-800 mb-3">Menu Controls</h3>
        <div class="space-y-2">
          <button 
            @click="menuStore.closeAllSubmenus()" 
            class="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors"
          >
            Close All Submenus
          </button>
          <button 
            @click="menuStore.clearAllBadges()" 
            class="w-full bg-yellow-600 text-white py-2 px-4 rounded-md hover:bg-yellow-700 transition-colors"
          >
            Clear All Badges
          </button>
          <button 
            @click="resetMenu" 
            class="w-full bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 transition-colors"
          >
            Reset Menu
          </button>
        </div>
      </div>
    </div>

    <!-- Current Menu Structure -->
    <div class="bg-gray-50 p-6 rounded-lg">
      <h3 class="text-lg font-semibold text-gray-800 mb-4">Current Menu Structure</h3>
      <div class="space-y-2">
        <div v-for="item in menuStore.menuItems" :key="item.text" class="bg-white p-3 rounded-md border">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <i :class="[item.icon, 'text-lg text-gray-600']"></i>
              <span class="font-medium">{{ item.text }}</span>
              <span v-if="item.badge" class="px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded-full">
                {{ item.badge }}
              </span>
              <span v-if="item.link" class="text-sm text-blue-600">{{ item.link }}</span>
            </div>
            <button 
              @click="removeMenuItem(item.text)" 
              class="text-red-600 hover:text-red-800 transition-colors"
            >
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
              </svg>
            </button>
          </div>
          
          <!-- Children -->
          <div v-if="item.children" class="ml-8 mt-2 space-y-1">
            <div v-for="child in item.children" :key="child.text" class="flex items-center justify-between bg-gray-50 p-2 rounded">
              <div class="flex items-center space-x-2">
                <span class="w-2 h-2 bg-gray-400 rounded-full"></span>
                <span class="text-sm">{{ child.text }}</span>
                <span v-if="child.badge" class="px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded-full">
                  {{ child.badge }}
                </span>
                <span v-if="child.link" class="text-xs text-blue-600">{{ child.link }}</span>
              </div>
              <button 
                @click="removeMenuItem(child.text, item.text)" 
                class="text-red-600 hover:text-red-800 transition-colors"
              >
                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Usage Examples -->
    <div class="mt-8 bg-blue-50 p-6 rounded-lg border border-blue-200">
      <h3 class="text-lg font-semibold text-blue-800 mb-4">Usage Examples</h3>
      <div class="space-y-4">
        <div>
          <h4 class="font-medium text-blue-700 mb-2">Programmatic Menu Management:</h4>
          <pre class="bg-white p-3 rounded text-sm text-gray-700 overflow-x-auto"><code>// Add a new menu item
menuStore.addMenuItem({
  text: 'Reports',
  icon: 'uil uil-chart',
  link: '/admin/reports'
})

// Add a child menu item
menuStore.addMenuItem({
  text: 'Sales Report',
  link: '/admin/reports/sales',
  icon: 'uil uil-money-bill'
}, 'Reports')

// Set a badge
menuStore.setBadge('Reports', 'New')

// Toggle submenu
menuStore.toggleSubmenu('Reports')</code></pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useMenuStore } from '@/utils/menuStore.js'

const menuStore = useMenuStore()

// Form data
const newMenuItem = ref({
  text: '',
  icon: '',
  link: '',
  parent: ''
})

const badgeMenuItem = ref('')
const badgeText = ref('')

// Computed properties
const parentMenus = computed(() => {
  return menuStore.menuItems.filter(item => !item.children || item.children.length === 0)
})

const allMenuItems = computed(() => {
  const items = []
  menuStore.menuItems.forEach(item => {
    items.push(item)
    if (item.children) {
      items.push(...item.children)
    }
  })
  return items
})

// Methods
const addMenuItem = () => {
  if (!newMenuItem.value.text || !newMenuItem.value.icon) {
    alert('Please fill in at least the text and icon fields')
    return
  }

  const item = {
    text: newMenuItem.value.text,
    icon: newMenuItem.value.icon,
    link: newMenuItem.value.link || null,
    badge: null,
    children: null
  }

  menuStore.addMenuItem(item, newMenuItem.value.parent || null)
  
  // Reset form
  newMenuItem.value = {
    text: '',
    icon: '',
    link: '',
    parent: ''
  }
}

const setBadge = () => {
  if (!badgeMenuItem.value || !badgeText.value) {
    alert('Please select a menu item and enter badge text')
    return
  }

  // Find if it's a parent or child menu
  const parentMenu = menuStore.menuItems.find(item => item.text === badgeMenuItem.value)
  if (parentMenu) {
    menuStore.setBadge(badgeMenuItem.value, badgeText.value)
  } else {
    // Check if it's a child menu
    for (const parent of menuStore.menuItems) {
      if (parent.children) {
        const child = parent.children.find(child => child.text === badgeMenuItem.value)
        if (child) {
          menuStore.setBadge(badgeMenuItem.value, badgeText.value, parent.text)
          break
        }
      }
    }
  }

  badgeText.value = ''
}

const clearBadge = () => {
  if (!badgeMenuItem.value) {
    alert('Please select a menu item')
    return
  }

  // Find if it's a parent or child menu
  const parentMenu = menuStore.menuItems.find(item => item.text === badgeMenuItem.value)
  if (parentMenu) {
    menuStore.setBadge(badgeMenuItem.value, null)
  } else {
    // Check if it's a child menu
    for (const parent of menuStore.menuItems) {
      if (parent.children) {
        const child = parent.children.find(child => child.text === badgeMenuItem.value)
        if (child) {
          menuStore.setBadge(badgeMenuItem.value, null, parent.text)
          break
        }
      }
    }
  }
}

const removeMenuItem = (itemText, parentText = null) => {
  if (confirm(`Are you sure you want to remove "${itemText}"?`)) {
    menuStore.removeMenuItem(itemText, parentText)
  }
}

const resetMenu = () => {
  if (confirm('Are you sure you want to reset the menu to default?')) {
    menuStore.reset()
    // Reinitialize with default menu items
    location.reload()
  }
}
</script>
