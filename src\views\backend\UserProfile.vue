<script setup>
import { ref } from 'vue'

import { IMAGE_URL } from '@/api/endpoint'
import useAuthStore from '@/stores/auth'
import useUserStore from '@/stores/user'

const password = ref({
    oldPassword: null,
    newPassword: null,
})
const image = ref(null);

function avatarGet(event) {
    image.value = event.target.files[0]
}

const updatePassword = () => {
    useUserStore().updatePassword(password.value)
}

async function uploadImage(event) {
    const formData = new FormData()
    formData.append('avatar', image.value)
    const imageUrl = await useUserStore().uploadAvatar(formData);


    if (imageUrl) {
        console.log(useAuthStore().user.user.image_url);
        useAuthStore().user.user.image_url = imageUrl;
        // Local Storage ko image lai ni Save Gardine...
        const data = JSON.parse(localStorage.getItem('user'));
        data.user.image_url = imageUrl;
        localStorage.setItem('user', JSON.stringify(data));
        window.location.reload();
    }

}


</script>
<template>
    <div class="container-fluid">
        <div class="profile-content mb-50">
            <div class="row">
                <div class="col-lg-12">

                    <div class="breadcrumb-main">
                        <h4 class="text-capitalize breadcrumb-title">My profile</h4>
                        <div class="breadcrumb-action justify-content-center flex-wrap">
                            <nav aria-label="breadcrumb">
                                <ol class="breadcrumb">
                                    <li class="breadcrumb-item"><a href="#"><i class="uil uil-estate"></i>Home</a></li>
                                    <li class="breadcrumb-item active" aria-current="page">My profile</li>
                                </ol>
                            </nav>
                        </div>
                    </div>

                </div>
                <div class="col-xxl-3 col-md-4  ">
                    <aside class="profile-sider">
                        <!-- Profile Acoount -->
                        <div class="card mb-25">
                            <div class="card-body text-center pt-sm-30 pb-sm-0  px-25 pb-0">

                                <div class="account-profile">
                                    <div class="ap-img w-100 d-flex justify-content-center">
                                        <!-- Profile picture image-->
                                        <img class="ap-img__main rounded-circle mb-3  wh-120 d-flex bg-opacity-primary"
                                            :src="IMAGE_URL + useAuthStore().user.user.image_url" alt="profile">
                                    </div>
                                    <div class="ap-nameAddress pb-3 pt-1">
                                        <h5 class="ap-nameAddress__title">Duran Clayton</h5>
                                        <p class="ap-nameAddress__subTitle fs-14 m-0">
                                            {{ useAuthStore().user.user.user_position
                                            }}</p>
                                        <p class="ap-nameAddress__subTitle fs-14 m-0">
                                            <img src="/img/svg/map-pin.svg" alt="map-pin" class="svg">Nepal, Kamaladi
                                        </p>
                                    </div>

                                </div>

                                <div class="card-footer mt-20 pt-20 pb-20 px-0 bg-transparent">
                                    <!-- <div class="profile-overview d-flex justify-content-between flex-wrap">
                                        <div class="po-details">
                                            <h6 class="po-details__title pb-1">$72,572</h6>
                                            <span class="po-details__sTitle">Total Revenue</span>
                                        </div>
                                        <div class="po-details">
                                            <h6 class="po-details__title pb-1">3,257</h6>
                                            <span class="po-details__sTitle">order</span>
                                        </div>
                                        <div class="po-details">
                                            <h6 class="po-details__title pb-1">74</h6>
                                            <span class="po-details__sTitle">Products</span>
                                        </div>
                                    </div> -->
                                </div>
                            </div>
                        </div>
                        <!-- Profile Acoount End -->

                    </aside>
                </div>

                <div class="col-xxl-9 col-md-8">
                    <!-- Tab Menu -->
                    <div class="card card-default card-md mb-4">
                        <div class="card-header">
                            <h6>Use Profile Update</h6>
                        </div>
                        <div class="card-body">
                            <div class="dm-nav-controller date-picker-size">
                                <div class="btn-group dm-button-group btn-group-normal nav" role="tablist">
                                    <a href="#imageUpload"
                                        class="btn btn-sm btn-outline-light color-light nav-link active" id="size-large"
                                        data-bs-toggle="tab" role="tab" aria-selected="true">Image Upload</a>
                                    <a href="#updatePassword" class="btn btn-sm btn-outline-light color-light nav-link"
                                        id="size-default" data-bs-toggle="tab" role="tab" aria-selected="false">Update
                                        Password</a>
                                </div>
                                <div class="nav-controller-content tab-content">
                                    <div class="tab-pane fade active show" id="imageUpload" role="tabpanel"
                                        aria-labelledby="size-large">
                                        <!-- Image Upload file  -->
                                        <div class="image-upload">
                                            <div class="mb-3">
                                                <label for="formFile" class="form-label">Upload Profile Picture</label>
                                                <input class="form-control" @change="avatarGet" type="file"
                                                    id="formFile">
                                            </div>
                                        </div>
                                        <div class="d-flex justify-content-start">
                                            <button @click="uploadImage" class="btn btn-success btn-sm text-capitalize">
                                                <i class="uil uil-check"></i> Submit
                                            </button>
                                        </div>

                                    </div>
                                    <div class="tab-pane fade" id="updatePassword" role="tabpanel"
                                        aria-labelledby="size-default">
                                        <div class="mb-3">
                                            <label for="oldPassword" class="form-label">Old Password</label>
                                            <input type="password" v-model="password.oldPassword" class="form-control"
                                                id="oldPassword" placeholder="Enter Old Password">
                                        </div>

                                        <div class="mb-3">
                                            <label for="newPassword" class="form-label">New Password</label>
                                            <input type="password" v-model="password.newPassword" class="form-control"
                                                id="newPassword" placeholder="Enter New Password">
                                            <div class="invalid-feedback">Please choose a new password.</div>
                                            <div class="mt-3 d-flex justify-content-start">
                                                <button @click="updatePassword()"
                                                    class="btn btn-success btn-sm text-capitalize">
                                                    <i class="uil uil-check"></i> Submit
                                                </button>
                                            </div>

                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</template>