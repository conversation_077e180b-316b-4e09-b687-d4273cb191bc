<script setup>
import {
  computed,
  onMounted,
  ref,
  watchEffect,
} from 'vue'

import html2canvas from 'html2canvas'
import jsPDF from 'jspdf'

import useSocialMedia from '@/stores/socialMedia'

const admissionList = ref([]);
const filteredList = ref([]);
const searchQuery = ref('');
const selectedGender = ref('');
const selectedDateRange = ref('');
const socialMediaStore = useSocialMedia();

onMounted(() => {
    socialMediaStore.getAdmissionList(); // Fetch the admission list
});

// yo vitra ko kunei change vayo ki feri execute vayo...
watchEffect(() => {
    admissionList.value = socialMediaStore.admissionList;
    applyFilters();
});

const currentPage = ref(1);
const totalPages = computed(() => socialMediaStore.totalPages);

function changePage(page) {
    if (page >= 1 && page <= totalPages.value) {
        currentPage.value = page;
        socialMediaStore.getAdmissionList(page);
    }
}

function getDateRange(range) {
    const now = new Date();
    switch (range) {
        case 'today':
            return new Date(now.getFullYear(), now.getMonth(), now.getDate());
        case 'thisWeek':
            const startOfWeek = new Date(now);
            startOfWeek.setDate(now.getDate() - now.getDay());
            return startOfWeek;
        case 'thisMonth':
            return new Date(now.getFullYear(), now.getMonth(), 1);
        case 'thisYear':
            return new Date(now.getFullYear(), 0, 1);
        default:
            return null;
    }
}

function applyFilters() {
    console.log(admissionList.value);
    if (!admissionList.value) {
        filteredList.value = [];
        return;
    }
    filteredList.value = admissionList.value.filter(admission => {
        const matchesGender = selectedGender.value
            ? admission.gender?.toLowerCase() === selectedGender.value.toLowerCase()
            : true;
        const matchesSearch = Object.values(admission).some(value =>
            value?.toString().toLowerCase().includes(searchQuery.value.toLowerCase())
        );
        const matchesDateRange = selectedDateRange.value
            ? new Date(admission.created_at) >= getDateRange(selectedDateRange.value)
            : true;
        return matchesGender && matchesSearch && matchesDateRange;

    });
}

function exportToPDF() {
    const tableElement = document.getElementById('admission-table');
    if (!tableElement) return;

    html2canvas(tableElement).then(canvas => {
        const imgData = canvas.toDataURL('image/png');
        const pdf = new jsPDF();
        const imgWidth = 190; // Adjust width to fit the PDF
        const imgHeight = (canvas.height * imgWidth) / canvas.width;
        pdf.addImage(imgData, 'PNG', 10, 10, imgWidth, imgHeight);
        pdf.save('admissions-list.pdf');
    });
}
</script>
<template>

    <div class="col-12 mb-30 mt-30">
        <div class="support-ticket-system support-ticket-system--search">
            <!-- Filter and Search Section -->
            <div class="d-flex justify-content-between mb-3">
                <div>
                    <label for="genderFilter">Filter by Gender:</label>
                    <select id="genderFilter" class="form-control" v-model="selectedGender" @change="applyFilters">
                        <option value="">All</option>
                        <option value="Male">Male</option>
                        <option value="Female">Female</option>
                    </select>
                </div>
                <div>
                    <label for="searchQuery">Search:</label>
                    <input id="searchQuery" type="text" v-model="searchQuery" @input="applyFilters"
                        placeholder="Search..." class="form-control" />
                </div>
                <div>
                    <label for="dateFilter">Filter by Date:</label>
                    <select id="dateFilter" class="form-control" v-model="selectedDateRange" @change="applyFilters">
                        <option value="">All</option>
                        <option value="today">Today</option>
                        <option value="thisWeek">This Week</option>
                        <option value="thisMonth">This Month</option>
                        <option value="thisYear">This Year</option>
                    </select>
                </div>
            </div>  

            <div class="breadcrumb-main m-0 breadcrumb-main--table justify-content-sm-between ">
                <div class=" d-flex flex-wrap justify-content-center breadcrumb-main__wrapper">
                    <div class="d-flex align-items-center ticket__title justify-content-center me-md-25 mb-md-0 mb-20">
                        <h4 class="text-capitalize fw-500 breadcrumb-title">Admissions List</h4>
                    </div>
                </div>
                <div class="action-btn">
                    <a href="#" @click.prevent="exportToPDF" class="btn btn-primary">
                        Export
                        <i class="las la-angle-down"></i>
                    </a>
                </div>
            </div>

            <div class="userDatatable userDatatable--ticket userDatatable--ticket--2 mt-1">
                <div class="table-responsive">
                    <table id="admission-table" class="table mb-0 table-borderless">
                        <thead>
                            <tr class="userDatatable-header">
                                <th>
                                    <span class="userDatatable-title">ID</span>
                                </th>
                                <th>
                                    <span class="userDatatable-title">Child Name</span>
                                </th>
                                <th>
                                    <span class="userDatatable-title">Email</span>
                                </th>
                                <th>
                                    <span class="userDatatable-title">Gender</span>
                                </th>
                                <th>
                                    <span class="userDatatable-title">Parent Designation</span>
                                </th>
                                <th>
                                    <span class="userDatatable-title">Parent Name</span>
                                </th>
                                <th>
                                    <span class="userDatatable-title">Phone Number</span>
                                </th>
                                <th>
                                    <span class="userDatatable-title">Relation</span>
                                </th>
                                <th>
                                    <span class="userDatatable-title">Child DOB</span>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-if="!filteredList.length">
                                <td :colspan="9" class="text-center py-4">
                                    <span class="text-danger">No admissions Found</span>
                                </td>
                            </tr>
                            <tr v-for="(admission, index) in filteredList" :key="index"
                                class="userDatatable-row userDatatable-row--2">
                                <td>
                                    <div class="d-flex">
                                        <div class="userDatatable-inline-title">
                                            <a href="#" class="text-dark fw-500">
                                                <h6>#{{ admission.id }}</h6>
                                            </a>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="userDatatable-content--subject">
                                        {{ admission.child_name }}
                                    </div>
                                </td>
                                <td>
                                    <div class="userDatatable-content--subject">
                                        {{ admission.email }}
                                    </div>
                                </td>
                                <td>
                                    <div class="userDatatable-content--priority">
                                        {{ admission.gender }}
                                    </div>
                                </td>
                                <td>
                                    <div class="userDatatable-content--priority">
                                        {{ admission.parent_designation }}
                                    </div>
                                </td>
                                <td>
                                    <div class="userDatatable-content--priority">
                                        {{ admission.parent_name }}
                                    </div>
                                </td>
                                <td>
                                    <div class="userDatatable-content--priority">
                                        {{ admission.phone_no }}
                                    </div>
                                </td>
                                <td>
                                    <div class="userDatatable-content--priority">
                                        {{ admission.relation }}
                                    </div>
                                </td>
                                <td>
                                    <div class="userDatatable-content--priority">
                                        {{ admission.child_dob }}
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="d-flex justify-content-end pt-30">
                    <nav class="dm-page">
                        <ul class="dm-pagination d-flex">
                            <li class="dm-pagination__item">
                                <a href="#" class="dm-pagination__link pagination-control"
                                    :class="{ disabled: currentPage == 1 }"
                                    @click.prevent="changePage(currentPage - 1)">
                                    <span class="la la-angle-left"></span>
                                </a>
                            </li>
                            <li class="dm-pagination__item" v-for="page in totalPages" :key="page">
                                <a href="#" class="dm-pagination__link" :class="{ active: currentPage == page }"
                                    @click.prevent="changePage(page)">
                                    <span class="page-number">{{ page }}</span>
                                </a>
                            </li>
                            <li class="dm-pagination__item">
                                <a href="#" class="dm-pagination__link pagination-control"
                                    :class="{ disabled: currentPage == totalPages }"
                                    @click.prevent="changePage(currentPage + 1)">
                                    <span class="la la-angle-right"></span>
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</template>