<script setup lang="ts">
import {
  onMounted,
  ref,
} from 'vue'

import About from '@/components/frontend/About.vue'
import About2 from '@/components/frontend/About2.vue'
import AddNow from '@/components/frontend/AddNow.vue'
import Category from '@/components/frontend/Category.vue'
import Classes from '@/components/frontend/Classes.vue'
import Faq from '@/components/frontend/Faq.vue'
import Footer from '@/components/frontend/Footer.vue'
import Header from '@/components/frontend/Header.vue'
import HeroSection from '@/components/frontend/HeroSection.vue'
import NewsLetter from '@/components/frontend/NewsLetter.vue'
import RightSideBar from '@/components/frontend/RightSideBar.vue'
import Schedule from '@/components/frontend/Schedule.vue'
import Service from '@/components/frontend/Service.vue'
import Testimonial from '@/components/frontend/Testimonial.vue'
import useSocialMedia from '@/stores/socialMedia'

const socialMediaDetails = ref(null);
onMounted(async () => {
    socialMediaDetails.value = await useSocialMedia().getSocialMediaDetails();
    setTimeout(() => {
        preLoaderShow.value = false;
    }, 100);
})

const preLoaderShow = ref(true);

</script>

<template>
    <AddNow />
    <div v-if="preLoaderShow" class="preloader">
        <button class="vs-btn preloaderCls">Cancel Preloader </button>
        <div class="preloader-inner">
            <div class="loader"></div>
        </div>
    </div>
    <div class="vs-menu-wrapper">
        <div class="vs-menu-area text-center">
            <button class="vs-menu-toggle"><i class="fal fa-times"></i></button>
            <div class="mobile-logo">
                <a href="index.html">
                    <!-- <img src="/frontend/assets1/img/logo.svg" alt="Kiddino"> -->
                    <img width="150" src="/img/original.png" alt="Sabhyata">

                </a>
            </div>
            <div class="vs-mobile-menu">
                <ul>
                    <li class="menu-item-has-children">
                        <a href="index.html">Demo</a>
                        <ul class="sub-menu">
                            <li><a href="index.html">Demo Style 1</a></li>
                            <li><a href="index-2.html">Demo Style 2</a></li>
                            <li><a href="index-3.html">Demo Style 3</a></li>
                        </ul>
                    </li>
                    <li>
                        <a href="about.html">About Us</a>
                    </li>
                    <li class="menu-item-has-children">
                        <a href="match.html">Classes</a>
                        <ul class="sub-menu">
                            <li><a href="class.html">Class Style 1</a></li>
                            <li><a href="class-2.html">Class Style 2</a></li>
                            <li><a href="class-details.html">Class Details</a></li>
                        </ul>
                    </li>
                    <li class="menu-item-has-children">
                        <a href="blog.html">Blog</a>
                        <ul class="sub-menu">
                            <li><a href="blog.html">Blog</a></li>
                            <li><a href="blog-details.html">Blog Details</a></li>
                        </ul>
                    </li>
                    <li class="menu-item-has-children">
                        <a href="#">Pages</a>
                        <ul class="sub-menu">
                            <li><a href="service.html">Service</a></li>
                            <li><a href="service-details.html">Service Details</a></li>
                            <li><a href="team.html">Team</a></li>
                            <li><a href="team-details.html">Team Details</a></li>
                            <li><a href="event-details.html">Event Details</a></li>
                            <li><a href="gallery.html">Gallery</a></li>
                            <li><a href="price-plan.html">Price Plan</a></li>
                            <li><a href="faq.html">FAQ</a></li>
                            <li><a href="shop.html">Shop</a></li>
                            <li><a href="shop-details.html">Shop Details</a></li>
                            <li><a href="cart.html">Shopping Cart</a></li>
                            <li><a href="checkout.html">Checkout</a></li>
                            <li><a href="registration.html">Registration</a></li>
                            <li><a href="error.html">Error Page</a></li>
                        </ul>
                    </li>
                    <li>
                        <a href="contact.html">Contact Us</a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
    <!--==============================
    Sidemenu
============================== -->
    <RightSideBar :socialMediaDetails="socialMediaDetails" />
    <!--==============================
    Popup Search Box
    ============================== -->
    <div class="popup-search-box d-none d-lg-block  ">
        <button class="searchClose"><i class="fal fa-times"></i></button>
        <form action="#">
            <input type="text" class="border-theme" placeholder="What are you looking for">
            <button type="submit"><i class="fal fa-search"></i></button>
        </form>
    </div>
    <!--==============================
        Header Area
    ==============================-->
    <Header />

    <!-- Main Content  -->
    <router-view></router-view>
    <Footer />
    <!-- Scroll To Top -->
    <a href="#" class="scrollToTop scroll-btn"><i class="far fa-arrow-up"></i></a>

</template>
