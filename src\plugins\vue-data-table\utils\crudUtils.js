/**
 * CRUD utilities for data table operations
 */

import axios from 'axios'

/**
 * Default CRUD configuration
 */
export const defaultCrudConfig = {
  create: {
    enabled: true,
    method: 'POST',
    endpoint: '',
    fields: [],
    validation: {}
  },
  read: {
    enabled: true,
    method: 'GET',
    endpoint: '',
    pagination: true,
    search: true,
    filters: true
  },
  update: {
    enabled: true,
    method: 'PUT',
    endpoint: '',
    fields: [],
    validation: {}
  },
  delete: {
    enabled: true,
    method: 'DELETE',
    endpoint: '',
    confirmation: true
  }
}

/**
 * CRUD API client class
 */
export class CrudApiClient {
  constructor(baseEndpoint, config = {}) {
    this.baseEndpoint = baseEndpoint
    this.config = { ...defaultCrudConfig, ...config }
    this.axios = axios.create({
      baseURL: config.baseURL || '',
      headers: config.headers || {}
    })
  }

  /**
   * Fetch data with pagination, sorting, and filtering
   * @param {Object} params - Query parameters
   * @returns {Promise} - API response
   */
  async fetchData(params = {}) {
    if (!this.config.read.enabled) {
      throw new Error('Read operation is not enabled')
    }

    const endpoint = this.config.read.endpoint || this.baseEndpoint
    
    try {
      const response = await this.axios.get(endpoint, { params })
      return this.normalizeResponse(response.data)
    } catch (error) {
      throw this.handleError(error)
    }
  }

  /**
   * Create a new record
   * @param {Object} data - Record data
   * @returns {Promise} - API response
   */
  async create(data) {
    if (!this.config.create.enabled) {
      throw new Error('Create operation is not enabled')
    }

    const endpoint = this.config.create.endpoint || this.baseEndpoint
    
    try {
      const response = await this.axios({
        method: this.config.create.method,
        url: endpoint,
        data
      })
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  /**
   * Update an existing record
   * @param {*} id - Record ID
   * @param {Object} data - Updated data
   * @returns {Promise} - API response
   */
  async update(id, data) {
    if (!this.config.update.enabled) {
      throw new Error('Update operation is not enabled')
    }

    const endpoint = this.config.update.endpoint || `${this.baseEndpoint}/${id}`
    
    try {
      const response = await this.axios({
        method: this.config.update.method,
        url: endpoint,
        data
      })
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  /**
   * Delete a record
   * @param {*} id - Record ID
   * @returns {Promise} - API response
   */
  async delete(id) {
    if (!this.config.delete.enabled) {
      throw new Error('Delete operation is not enabled')
    }

    const endpoint = this.config.delete.endpoint || `${this.baseEndpoint}/${id}`
    
    try {
      const response = await this.axios({
        method: this.config.delete.method,
        url: endpoint
      })
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  /**
   * Bulk delete records
   * @param {Array} ids - Array of record IDs
   * @returns {Promise} - API response
   */
  async bulkDelete(ids) {
    if (!this.config.delete.enabled) {
      throw new Error('Delete operation is not enabled')
    }

    const endpoint = this.config.delete.bulkEndpoint || `${this.baseEndpoint}/bulk-delete`
    
    try {
      const response = await this.axios({
        method: 'POST',
        url: endpoint,
        data: { ids }
      })
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  /**
   * Normalize API response to standard format
   * @param {Object} response - Raw API response
   * @returns {Object} - Normalized response
   */
  normalizeResponse(response) {
    // Handle different response formats
    if (response.data && Array.isArray(response.data)) {
      return {
        data: response.data,
        total: response.total || response.meta?.total || response.data.length,
        page: response.page || response.meta?.current_page || 1,
        pageSize: response.per_page || response.meta?.per_page || 10,
        totalPages: response.total_pages || response.meta?.last_page || 1
      }
    }
    
    if (Array.isArray(response)) {
      return {
        data: response,
        total: response.length,
        page: 1,
        pageSize: response.length,
        totalPages: 1
      }
    }
    
    return {
      data: response.items || response.results || [],
      total: response.total || 0,
      page: response.page || 1,
      pageSize: response.pageSize || response.limit || 10,
      totalPages: response.totalPages || 1
    }
  }

  /**
   * Handle API errors
   * @param {Error} error - API error
   * @returns {Error} - Formatted error
   */
  handleError(error) {
    if (error.response) {
      // Server responded with error status
      const message = error.response.data?.message || 
                     error.response.data?.error || 
                     `HTTP ${error.response.status}: ${error.response.statusText}`
      
      const formattedError = new Error(message)
      formattedError.status = error.response.status
      formattedError.data = error.response.data
      return formattedError
    }
    
    if (error.request) {
      // Request was made but no response received
      return new Error('Network error: No response from server')
    }
    
    // Something else happened
    return error
  }
}

/**
 * Validate form data against validation rules
 * @param {Object} data - Form data to validate
 * @param {Object} rules - Validation rules
 * @param {Array} fields - Field definitions
 * @returns {Object} - Validation result
 */
export function validateFormData(data, rules = {}, fields = []) {
  const errors = {}
  const warnings = {}
  
  fields.forEach(field => {
    const value = data[field.key]
    const fieldRules = rules[field.key] || []
    const fieldErrors = []
    
    // Required validation
    if (field.required && (value === null || value === undefined || value === '')) {
      fieldErrors.push(`${field.label} is required`)
    }
    
    // Skip other validations if field is empty and not required
    if (!field.required && (value === null || value === undefined || value === '')) {
      return
    }
    
    // Apply custom validation rules
    fieldRules.forEach(rule => {
      const error = validateRule(value, rule, field)
      if (error) {
        fieldErrors.push(error)
      }
    })
    
    if (fieldErrors.length > 0) {
      errors[field.key] = fieldErrors
    }
  })
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors,
    warnings
  }
}

/**
 * Validate a single rule
 * @param {*} value - Value to validate
 * @param {String} rule - Validation rule
 * @param {Object} field - Field definition
 * @returns {String|null} - Error message or null
 */
function validateRule(value, rule, field) {
  const [ruleName, ...params] = rule.split(':')
  
  switch (ruleName) {
    case 'min':
      const minLength = parseInt(params[0])
      if (String(value).length < minLength) {
        return `${field.label} must be at least ${minLength} characters`
      }
      break
      
    case 'max':
      const maxLength = parseInt(params[0])
      if (String(value).length > maxLength) {
        return `${field.label} must not exceed ${maxLength} characters`
      }
      break
      
    case 'email':
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(value)) {
        return `${field.label} must be a valid email address`
      }
      break
      
    case 'numeric':
      if (isNaN(Number(value))) {
        return `${field.label} must be a number`
      }
      break
      
    case 'integer':
      if (!Number.isInteger(Number(value))) {
        return `${field.label} must be an integer`
      }
      break
      
    case 'min_value':
      const minValue = Number(params[0])
      if (Number(value) < minValue) {
        return `${field.label} must be at least ${minValue}`
      }
      break
      
    case 'max_value':
      const maxValue = Number(params[0])
      if (Number(value) > maxValue) {
        return `${field.label} must not exceed ${maxValue}`
      }
      break
      
    case 'regex':
      const pattern = new RegExp(params[0])
      if (!pattern.test(value)) {
        return `${field.label} format is invalid`
      }
      break
      
    case 'url':
      try {
        new URL(value)
      } catch {
        return `${field.label} must be a valid URL`
      }
      break
      
    case 'date':
      if (isNaN(Date.parse(value))) {
        return `${field.label} must be a valid date`
      }
      break
      
    case 'confirmed':
      const confirmField = params[0] || `${field.key}_confirmation`
      if (value !== field.form?.[confirmField]) {
        return `${field.label} confirmation does not match`
      }
      break
  }
  
  return null
}

/**
 * Generate form fields from table columns
 * @param {Array} columns - Table columns
 * @param {Object} options - Generation options
 * @returns {Array} - Form fields
 */
export function generateFormFields(columns, options = {}) {
  const { exclude = [], include = [], readonly = [] } = options
  
  return columns
    .filter(column => {
      if (include.length > 0) {
        return include.includes(column.key)
      }
      return !exclude.includes(column.key)
    })
    .map(column => ({
      key: column.key,
      label: column.label,
      type: mapColumnTypeToFormType(column),
      required: column.required || false,
      readonly: readonly.includes(column.key),
      options: column.filterOptions || [],
      validation: column.validation || []
    }))
}

/**
 * Map column type to form input type
 * @param {Object} column - Table column
 * @returns {String} - Form input type
 */
function mapColumnTypeToFormType(column) {
  if (column.formType) return column.formType
  
  switch (column.filterType) {
    case 'select':
      return 'select'
    case 'date':
      return 'date'
    case 'number':
      return 'number'
    case 'boolean':
      return 'checkbox'
    default:
      return 'text'
  }
}
