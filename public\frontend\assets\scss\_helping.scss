//page scroll bar add
::-webkit-scrollbar {
	width: 4px;
	height: 4px;
}
/* Track */
::-webkit-scrollbar-track {
	box-shadow: inset 0 0 5px $p2-clr;
	border-radius: 5px;
}
/* Handle */
::-webkit-scrollbar-thumb {
	background: $p2-clr;
	border-radius: 10px;
}
//page scroll bar add

//Basic Code Start
.fix {
	overflow: hidden;
}

.ralt {
	position: relative;
}

.ml-100 {
	margin-left: 100px;
}
//Basic Code End

//Video Css Start
.ripple {
	position: relative;

	&::before,
	&::after {
		position: absolute;
		left: 50%;
		top: 50%;
		width: 60px;
		height: 60px;
		-webkit-transform: translateX(-50%) translateY(-50%);
		transform: translateX(-50%) translateY(-50%);
		border-radius: 50%;
		box-shadow: 0 0 0 0 rgba(243, 159, 95, 0.5);
		-webkit-animation: rippleOne 3s infinite;
		animation: rippleOne 3s infinite;
	}

	&::before {
		-webkit-animation-delay: 0.9s;
		animation-delay: 0.9s;
		content: "";
		position: absolute;
		right: 0;
		bottom: 0;
	}

	&::after {
		-webkit-animation-delay: 0.6s;
		animation-delay: 0.6s;
		content: "";
		position: absolute;
		right: 0;
		bottom: 0;
	}
}
//Video Css End

//pagination default

//pagination default
.swiper-dot {
	margin-bottom: 2px;
	position: relative;

	&::before {
		position: absolute;
		bottom: 13px;
		left: 37%;
		transform: translate(-50%, -50%);
		width: 105px;
		height: 2px;
		background: linear-gradient(
			90deg,
			#f39f5f 4.85%,
			rgba(255, 255, 255, 0) 96.39%
		);
		content: "";
		transform: rotate(-180deg);

		@include breakpoint(max-xxl) {
			display: none;
		}
	}

	&::after {
		position: absolute;
		bottom: 13px;
		right: 37%;
		width: 105px;
		height: 2px;
		background: linear-gradient(
			90deg,
			#f39f5f 4.85%,
			rgba(255, 255, 255, 0) 96.39%
		);
		content: "";

		@include breakpoint(max-xxl) {
			display: none;
		}
	}
	.swiper-pagination-bullet {
		width: 10px;
		height: 10px;
		transition: 0.6s;
		background-color: $p2-clr;
		opacity: 1;
		border-radius: 10px;
		&:not(:last-child) {
			margin-right: 15px;
		}
	}
	.swiper-pagination-bullet.swiper-pagination-bullet-active {
		background-color: $p2-clr;
		transition: 0.6s;
		position: relative;

		&::before {
			position: absolute;
			width: 30px;
			height: 30px;
			line-height: 30px;
			top: -10px;
			left: -10px;
			border-radius: 50%;
			background-color: transparent;
			border: 2px solid $p2-clr;
			content: "";
		}
	}
}

.array-button {
	@include flex;
	gap: 15px;

	.array-prev {
		width: 61px;
		height: 56px;
		line-height: 56px;
		text-align: center;
		background-color: $white;
		color: $header-color;
		border-radius: 22px;
		@include transition;

		&:hover {
			background-color: $p2-clr;
			color: $white;
		}
	}

	.array-next {
		width: 61px;
		height: 56px;
		line-height: 56px;
		text-align: center;
		background-color: $p2-clr;
		color: $white;
		border-radius: 22px;
		@include transition;

		&:hover {
			background-color: $white;
			color: $p2-clr;
		}
	}
}

//pagination default

.mt-10 {
	margin-top: 10px;
}

br {
	@include breakpoint(max-md) {
		display: none;
	}
}

/* background */
.bg-cover {
	background-repeat: no-repeat;
	background-size: cover;
	position: relative;
	background-position: center;
}

.bg-cover-2 {
	background-repeat: no-repeat;
	background-size: cover;
	position: relative;
	background-position: center;
	width: 100%;
	height: 100%;
}

//>>>>> Nice Select Css Start <<<<<//

.nice-select {
	background-color: transparent;
	border: transparent;
	float: initial;
	overflow: initial;
	height: initial;
	padding: 0;
	display: inline-flex;
	align-items: center;
	line-height: 150%;
	width: 100%;
	border: none;

	&:focus,
	&:hover {
		border-color: transparent;
	}

	&::after {
		height: 8px;
		width: 8px;
		right: -25px;
		top: 15px;
		border-color: $header-color;
		border-bottom: 2px solid $header-color;
		border-right: 2px solid $header-color;
	}

	.list {
		width: initial;
		background-color: $p2-clr;
		box-shadow: none;
		overflow: initial;
		box-shadow: rgba(0, 0, 0, 0.15) 0px 3px 3px 0px;
		width: 100%;
		top: 100%;
		padding: 0;
		max-height: 50vh;
		overflow-x: auto;
		right: -50px;
		&::-webkit-scrollbar {
			width: 2px;
			opacity: 1;
			display: block;
		}
		&::-webkit-scrollbar-button,
		&::-webkit-scrollbar-thumb {
			background: $header-color;
		}
	}
	.option {
		background-color: transparent;
		font-size: 16px;
		line-height: 150%;
		padding: 4px 5px;
		min-height: initial;
		font-weight: 500;
		&:hover,
		&:focus,
		&.selected.focus {
			background-color: transparent;
		}
	}
	.current {
		font-weight: 500;
		color: $header-color;
	}
}
//>>>>> Nice Select Css End <<<<<//

.scroll-up {
	cursor: pointer;
	display: block;
	border-radius: 50px;
	box-shadow: inset 0 0 0 2px var(--border);
	z-index: 99;
	opacity: 0;
	visibility: hidden;
	transform: translateY(15px);
	position: fixed;
	right: 25px;
	bottom: 35px;
	height: 50px;
	width: 50px;
	@include transition;
}

.scroll-up::after {
	position: absolute;
	font-family: "Font Awesome 6 free";
	content: "\f062";
	text-align: center;
	line-height: 50px;
	font-weight: 700;
	font-size: 18px;
	color: $p2-clr;
	left: 0;
	top: 0;
	height: 50px;
	width: 50px;
	cursor: pointer;
	display: block;
	z-index: 1;
	@include transition;
}

.scroll-up svg path {
	fill: none;
}

.scroll-up svg.scroll-circle path {
	stroke: $theme-color-2;
	stroke-width: 4px;
	box-sizing: border-box;
	@include transition;
}

.scroll-up.active-scroll {
	opacity: 1;
	visibility: visible;
	transform: translateY(0);
}

.page-nav-wrap {
	ul {
		li {
			display: inline-block;

			.page-numbers {
				&.current {
					background-color: $p2-clr;
					color: $white;
				}

				display: inline-block;
				width: 50px;
				height: 50px;
				line-height: 50px;
				background: transparent;
				font-weight: 600;
				transition: all 0.3s ease-in-out;
				margin: 0 2px;
				border: 1px solid $border-color;
				color: $text-color;
				border-radius: 50%;

				@media (max-width: 767px) {
					margin-top: 10px;
					width: 50px;
					height: 50px;
					line-height: 50px;
					font-size: 14px;
				}

				i {
					margin-top: 2px;
				}
				&:hover {
					background-color: $p2-clr;
					color: $white;
					border: 1px solid transparent;
				}
			}
		}
	}
}

.box-color-1 {
	background-color: rgba(248, 184, 31, 0.15);
	color: #f8b81f;
}

.box-color-2 {
	background-color: rgba(88, 102, 235, 0.15);
	color: #5866eb;
}

.box-color-3 {
	background-color: rgba(57, 192, 250, 0.15);
	color: #39c0fa;
}

.box-color-4 {
	background-color: rgba(249, 37, 150, 0.15);
	color: #f92596;
}

.border-none {
	border: none !important;
}


.box-shadow {
	box-shadow: $shadow;
}

.bor-1 {
	border: 1px solid $p2-clr;
}

.mb-55 {
	margin-bottom: 55px !important;
}

.border-array-style {
	border: 1px solid $p2-clr;
}

.pt-80 {
	padding-top: 80px;
}

.fz-40 {
	font-size: 40px;
}
