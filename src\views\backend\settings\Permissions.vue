<template>
  <div class="p-6">
    <div class="mb-6">
      <h1 class="text-3xl font-bold text-gray-900">User Permissions</h1>
      <p class="text-gray-600 mt-2">Manage user roles and permissions</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Roles -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">User Roles</h2>
        <div class="space-y-3">
          <div 
            v-for="role in roles" 
            :key="role.id"
            class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
          >
            <div>
              <h3 class="font-medium text-gray-900">{{ role.name }}</h3>
              <p class="text-sm text-gray-600">{{ role.description }}</p>
              <span class="text-xs text-gray-500">{{ role.userCount }} users</span>
            </div>
            <div class="flex space-x-2">
              <button 
                @click="editRole(role)"
                class="text-blue-600 hover:text-blue-800 text-sm"
              >
                Edit
              </button>
              <button 
                v-if="role.name !== 'Super Admin'"
                class="text-red-600 hover:text-red-800 text-sm"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
        
        <button 
          @click="showAddRole = true"
          class="mt-4 w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
        >
          Add New Role
        </button>
      </div>

      <!-- Permissions -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">Permissions</h2>
        <div class="space-y-4">
          <div v-for="permission in permissions" :key="permission.id">
            <h3 class="font-medium text-gray-900 mb-2">{{ permission.category }}</h3>
            <div class="space-y-2 ml-4">
              <div 
                v-for="action in permission.actions" 
                :key="action.id"
                class="flex items-center"
              >
                <input 
                  v-model="action.enabled"
                  type="checkbox" 
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                >
                <label class="ml-2 text-sm text-gray-700">{{ action.name }}</label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Add Role Modal -->
    <div v-if="showAddRole" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Add New Role</h3>
        <form @submit.prevent="addRole" class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Role Name</label>
            <input 
              v-model="newRole.name"
              type="text" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            >
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
            <textarea 
              v-model="newRole.description"
              rows="3" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            ></textarea>
          </div>
          <div class="flex space-x-3">
            <button 
              type="submit"
              class="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
            >
              Add Role
            </button>
            <button 
              type="button"
              @click="showAddRole = false"
              class="flex-1 bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 transition-colors"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const showAddRole = ref(false)

const newRole = ref({
  name: '',
  description: ''
})

const roles = ref([
  {
    id: 1,
    name: 'Super Admin',
    description: 'Full access to all features',
    userCount: 1
  },
  {
    id: 2,
    name: 'Admin',
    description: 'Administrative access with some restrictions',
    userCount: 3
  },
  {
    id: 3,
    name: 'Editor',
    description: 'Can create and edit content',
    userCount: 8
  },
  {
    id: 4,
    name: 'Viewer',
    description: 'Read-only access',
    userCount: 25
  }
])

const permissions = ref([
  {
    id: 1,
    category: 'User Management',
    actions: [
      { id: 1, name: 'View Users', enabled: true },
      { id: 2, name: 'Create Users', enabled: true },
      { id: 3, name: 'Edit Users', enabled: false },
      { id: 4, name: 'Delete Users', enabled: false }
    ]
  },
  {
    id: 2,
    category: 'Content Management',
    actions: [
      { id: 5, name: 'View Posts', enabled: true },
      { id: 6, name: 'Create Posts', enabled: true },
      { id: 7, name: 'Edit Posts', enabled: true },
      { id: 8, name: 'Delete Posts', enabled: false }
    ]
  },
  {
    id: 3,
    category: 'System Settings',
    actions: [
      { id: 9, name: 'View Settings', enabled: true },
      { id: 10, name: 'Edit Settings', enabled: false },
      { id: 11, name: 'System Maintenance', enabled: false }
    ]
  }
])

const editRole = (role) => {
  console.log('Editing role:', role)
}

const addRole = () => {
  if (newRole.value.name.trim()) {
    roles.value.push({
      id: Date.now(),
      name: newRole.value.name,
      description: newRole.value.description,
      userCount: 0
    })
    
    newRole.value = {
      name: '',
      description: ''
    }
    
    showAddRole.value = false
  }
}
</script>
