// Import styles
import './styles/data-table.css'

import ApiDataTable from './components/ApiDataTable.vue'
import DataTable from './components/DataTable.vue'
import TableExport from './components/TableExport.vue'
import TableHeader from './components/TableHeader.vue'
import TablePagination from './components/TablePagination.vue'
import TableSearch from './components/TableSearch.vue'
// Import utility functions
import * as exportUtils from './utils/exportUtils'
import * as paginationUtils from './utils/paginationUtils'
import * as sortUtils from './utils/sortUtils'

// Create plugin
const VueDataTable = {
  install(app) {
    // Register components globally
    app.component('DataTable', DataTable);
    app.component('ApiDataTable', ApiDataTable);
    app.component('TableHeader', TableHeader);
    app.component('TableSearch', TableSearch);
    app.component('TablePagination', TablePagination);
    app.component('TableExport', TableExport);
  }
};

// Export components and utilities
export {
  ApiDataTable,
  DataTable,
  exportUtils,
  paginationUtils,
  sortUtils,
  TableExport,
  TableHeader,
  TablePagination,
  TableSearch,
}

// Export plugin as default
export default VueDataTable;
