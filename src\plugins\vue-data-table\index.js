// Import styles
import './styles/data-table.css'

import AdvancedFilter from './components/AdvancedFilter.vue'
// Import components
import ApiDataTable from './components/ApiDataTable.vue'
import CrudModal from './components/CrudModal.vue'
import DataTable from './components/DataTable.vue'
import EnhancedDataTable from './components/EnhancedDataTable.vue'
import TableExport from './components/TableExport.vue'
import TableHeader from './components/TableHeader.vue'
import TablePagination from './components/TablePagination.vue'
import TableSearch from './components/TableSearch.vue'
// Import composables
import * as composables from './composables'
import * as crudUtils from './utils/crudUtils'
// Import utility functions
import * as exportUtils from './utils/exportUtils'
import * as filterUtils from './utils/filterUtils'
import * as paginationUtils from './utils/paginationUtils'
import * as sortUtils from './utils/sortUtils'

// Create plugin
const VueDataTable = {
  install(app) {
    // Register components globally
    app.component('DataTable', DataTable);
    app.component('ApiDataTable', ApiDataTable);
    app.component('EnhancedDataTable', EnhancedDataTable);
    app.component('AdvancedFilter', AdvancedFilter);
    app.component('CrudModal', CrudModal);
    app.component('TableHeader', TableHeader);
    app.component('TableSearch', TableSearch);
    app.component('TablePagination', TablePagination);
    app.component('TableExport', TableExport);
  }
};

// Export components and utilities
export {
  AdvancedFilter,
  ApiDataTable,
  composables,
  CrudModal,
  crudUtils,
  DataTable,
  EnhancedDataTable,
  exportUtils,
  filterUtils,
  paginationUtils,
  sortUtils,
  TableExport,
  TableHeader,
  TablePagination,
  TableSearch,
}

// Export plugin as default
export default VueDataTable;
