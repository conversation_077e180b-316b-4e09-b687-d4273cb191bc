<template>
  <div class="enhanced-data-table" :class="themeClasses">
    <!-- Header Section -->
    <div class="bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden">
      <!-- Table Header with Actions -->
      <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
          <!-- Title and Info -->
          <div class="flex items-center space-x-4">
            <h3 v-if="title" class="text-lg font-semibold text-gray-900">{{ title }}</h3>
            <div v-if="showInfo" class="text-sm text-gray-500">
              {{ paginationInfo.start }}-{{ paginationInfo.end }} of {{ paginationInfo.total }} items
            </div>
          </div>

          <!-- Actions -->
          <div class="flex items-center space-x-3">
            <!-- Search -->
            <div v-if="searchable" class="relative">
              <input v-model="searchQuery" type="text" :placeholder="searchPlaceholder"
                class="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>

            <!-- Advanced Filters -->
            <AdvancedFilter v-if="showAdvancedFilters" :columns="columns" v-model="filterData"
              @apply="handleFiltersApply" />

            <!-- CRUD Actions -->
            <div v-if="crud && crud.create" class="flex items-center space-x-2">
              <button @click="openCreateModal"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                Add New
              </button>
            </div>

            <!-- Bulk Actions -->
            <div v-if="hasSelection && showBulkActions" class="flex items-center space-x-2">
              <span class="text-sm text-gray-500">{{ selectedItems.length }} selected</span>
              <button v-if="crud && crud.delete" @click="handleBulkDelete"
                class="inline-flex items-center px-3 py-2 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                <svg class="mr-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                Delete Selected
              </button>
            </div>

            <!-- Export Actions -->
            <div v-if="exportConfig" class="relative">
              <button @click="toggleExportMenu"
                class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Export
              </button>

              <!-- Export Dropdown -->
              <div v-if="showExportMenu"
                class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-10">
                <div class="py-1">
                  <button v-if="exportConfig.csv" @click="exportData('csv')"
                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    Export as CSV
                  </button>
                  <button v-if="exportConfig.excel" @click="exportData('excel')"
                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    Export as Excel
                  </button>
                  <button v-if="exportConfig.json" @click="exportData('json')"
                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    Export as JSON
                  </button>
                  <button v-if="exportConfig.pdf" @click="exportData('pdf')"
                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    Export as PDF
                  </button>
                </div>
              </div>
            </div>

            <!-- Print Button -->
            <button v-if="printConfig" @click="printTable"
              class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
              <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
              </svg>
              Print
            </button>

            <!-- Refresh Button -->
            <button @click="handleRefresh"
              class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
              <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Refresh
            </button>
          </div>
        </div>
      </div>

      <!-- Table Container -->
      <div class="overflow-x-auto">
        <div class="relative">
          <!-- Loading Overlay -->
          <div v-if="loading" class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10">
            <div class="flex items-center space-x-2">
              <svg class="animate-spin h-5 w-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              <span class="text-sm text-gray-600">{{ loadingText }}</span>
            </div>
          </div>

          <!-- Table -->
          <table class="min-w-full divide-y divide-gray-200">
            <!-- Table Head -->
            <thead class="bg-gray-50">
              <tr>
                <!-- Selection Column -->
                <th v-if="selectable" class="w-12 px-6 py-3">
                  <input type="checkbox" :checked="isAllSelected" @change="toggleSelectAll"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" />
                </th>

                <!-- Data Columns -->
                <th v-for="column in visibleColumns" :key="column.key" :class="[
                  'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider',
                  column.headerClass,
                  { 'cursor-pointer hover:bg-gray-100': column.sortable }
                ]" @click="column.sortable ? handleSort(column.key) : null">
                  <div class="flex items-center space-x-1">
                    <span>{{ column.label }}</span>
                    <div v-if="column.sortable" class="flex flex-col">
                      <svg class="h-3 w-3"
                        :class="{ 'text-blue-600': sortKey === column.key && sortOrder === 'asc', 'text-gray-400': sortKey !== column.key || sortOrder !== 'asc' }"
                        fill="currentColor" viewBox="0 0 20 20">
                        <path
                          d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                      </svg>
                    </div>
                  </div>
                </th>

                <!-- Actions Column -->
                <th v-if="hasRowActions"
                  class="w-32 px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>

            <!-- Table Body -->
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="(item, index) in paginatedItems" :key="getItemKey(item, index)" :class="[
                'hover:bg-gray-50 transition-colors duration-150',
                { 'bg-blue-50': isItemSelected(item) }
              ]" @click="handleRowClick(item, index)">
                <!-- Selection Column -->
                <td v-if="selectable" class="w-12 px-6 py-4">
                  <input type="checkbox" :checked="isItemSelected(item)" @change="toggleItemSelection(item)" @click.stop
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" />
                </td>

                <!-- Data Columns -->
                <td v-for="column in visibleColumns" :key="column.key"
                  :class="['px-6 py-4 whitespace-nowrap text-sm', column.class]"
                  @click="handleCellClick(getItemValue(item, column.key), item, column, index)">
                  <slot :name="`cell-${column.key}`" :value="getItemValue(item, column.key)" :item="item"
                    :column="column" :index="index">
                    <span v-if="column.render"
                      v-html="column.render(getItemValue(item, column.key), item, index)"></span>
                    <span v-else class="text-gray-900">{{ getItemValue(item, column.key) }}</span>
                  </slot>
                </td>

                <!-- Actions Column -->
                <td v-if="hasRowActions" class="w-32 px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div class="flex items-center justify-end space-x-2">
                    <button v-if="crud && crud.update" @click.stop="openEditModal(item)"
                      class="text-blue-600 hover:text-blue-900">
                      <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                    </button>
                    <button v-if="crud && crud.delete" @click.stop="handleDelete(item)"
                      class="text-red-600 hover:text-red-900">
                      <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                    <slot name="row-actions" :item="item" :index="index"></slot>
                  </div>
                </td>
              </tr>

              <!-- No Data Row -->
              <tr v-if="paginatedItems.length === 0 && !loading">
                <td :colspan="totalColumns" class="px-6 py-12 text-center text-sm text-gray-500">
                  {{ noDataText }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Pagination -->
      <div v-if="showPagination" class="px-6 py-4 border-t border-gray-200 bg-gray-50">
        <TablePagination :current-page="currentPage" :page-size="pageSize" :total-items="totalItems"
          :total-pages="totalPages" @page-change="goToPage" @page-size-change="changePageSize" />
      </div>
    </div>

    <!-- CRUD Modal -->
    <CrudModal v-if="crud" :visible="showCrudModal" :mode="crudMode" :title="crudModalTitle" :fields="crudFields"
      :data="crudData" :validation="crudValidation" :loading="crudLoading" @close="closeCrudModal"
      @submit="handleCrudSubmit" />
  </div>
</template>

<script>
import {
  computed,
  ref,
} from 'vue'

import { useDataTable } from '../composables/useDataTable'
import {
  CrudApiClient,
  generateFormFields,
} from '../utils/crudUtils'
import {
  exportToCsv,
  exportToExcel,
  exportToJson,
} from '../utils/exportUtils'
import AdvancedFilter from './AdvancedFilter.vue'
import CrudModal from './CrudModal.vue'
import TablePagination from './TablePagination.vue'

export default {
  name: 'EnhancedDataTable',
  components: {
    AdvancedFilter,
    CrudModal,
    TablePagination
  },
  props: {
    items: { type: Array, default: () => [] },
    columns: { type: Array, default: () => [] },
    title: { type: String, default: '' },
    loading: { type: Boolean, default: false },
    loadingText: { type: String, default: 'Loading...' },
    serverSide: { type: Boolean, default: false },
    pagination: { type: Object, default: () => ({}) },
    sort: { type: Object, default: () => ({}) },
    filters: { type: Array, default: () => [] },
    selectable: { type: Boolean, default: false },
    selected: { type: Array, default: () => [] },
    crud: { type: Object, default: null },
    exportConfig: { type: Object, default: null },
    printConfig: { type: Object, default: null },
    searchable: { type: Boolean, default: true },
    searchPlaceholder: { type: String, default: 'Search...' },
    noDataText: { type: String, default: 'No data available' },
    theme: { type: String, default: 'default' },
    size: { type: String, default: 'md' },
    showInfo: { type: Boolean, default: true },
    showAdvancedFilters: { type: Boolean, default: true },
    showBulkActions: { type: Boolean, default: true },
    showPagination: { type: Boolean, default: true }
  },
  emits: [
    'update:selected', 'update:pagination', 'update:sort', 'update:filters',
    'row-click', 'row-dblclick', 'cell-click', 'search', 'export',
    'crud-create', 'crud-update', 'crud-delete', 'refresh'
  ],
  setup(props, { emit }) {
    // Initialize data table composable
    const dataTable = useDataTable({
      items: props.items,
      columns: props.columns,
      serverSide: props.serverSide,
      initialPageSize: props.pagination?.pageSize || 10,
      initialSort: props.sort,
      initialFilters: props.filters,
      searchable: props.searchable,
      selectable: props.selectable
    })

    // CRUD state
    const showCrudModal = ref(false)
    const crudMode = ref('create')
    const crudData = ref({})
    const crudLoading = ref(false)
    const crudApiClient = ref(null)

    // Export state
    const showExportMenu = ref(false)

    // Filter state
    const filterData = ref({
      columnFilters: {},
      advancedFilters: []
    })

    // Initialize CRUD API client
    if (props.crud && props.crud.endpoint) {
      crudApiClient.value = new CrudApiClient(props.crud.endpoint, props.crud)
    }

    // Computed properties
    const themeClasses = computed(() => ({
      'data-table-dark': props.theme === 'dark',
      'data-table-minimal': props.theme === 'minimal',
      'data-table-sm': props.size === 'sm',
      'data-table-lg': props.size === 'lg'
    }))

    const visibleColumns = computed(() => {
      return props.columns.filter(column => column.visible !== false)
    })

    const hasRowActions = computed(() => {
      return props.crud && (props.crud.update || props.crud.delete)
    })

    const totalColumns = computed(() => {
      let count = visibleColumns.value.length
      if (props.selectable) count++
      if (hasRowActions.value) count++
      return count
    })

    const crudFields = computed(() => {
      if (!props.crud) return []
      return props.crud.fields || generateFormFields(props.columns, {
        exclude: ['id', 'created_at', 'updated_at']
      })
    })

    const crudValidation = computed(() => {
      return props.crud?.validation || {}
    })

    const crudModalTitle = computed(() => {
      if (props.crud?.title) {
        return typeof props.crud.title === 'string'
          ? props.crud.title
          : props.crud.title[crudMode.value]
      }
      return crudMode.value === 'create' ? 'Create New Record' : 'Edit Record'
    })

    // Methods
    const handleSort = (key) => {
      dataTable.sort(key)
      emit('update:sort', { key: dataTable.sortKey.value, order: dataTable.sortOrder.value })
    }

    const handleRowClick = (item, index) => {
      emit('row-click', item, index)
    }

    const handleCellClick = (value, item, column, index) => {
      emit('cell-click', value, item, column, index)
    }

    const handleRefresh = () => {
      dataTable.refresh()
      emit('refresh')
    }

    const handleFiltersApply = (filters) => {
      filterData.value = filters

      // Apply column filters
      Object.entries(filters.columnFilters).forEach(([key, value]) => {
        dataTable.setColumnFilter(key, value)
      })

      // Apply advanced filters
      dataTable.filters.value = filters.advancedFilters

      emit('update:filters', filters.advancedFilters)
    }

    // CRUD Methods
    const openCreateModal = () => {
      crudMode.value = 'create'
      crudData.value = {}
      showCrudModal.value = true
    }

    const openEditModal = (item) => {
      crudMode.value = 'edit'
      crudData.value = { ...item }
      showCrudModal.value = true
    }

    const closeCrudModal = () => {
      showCrudModal.value = false
      crudData.value = {}
      crudLoading.value = false
    }

    const handleCrudSubmit = async (formData) => {
      if (!crudApiClient.value) {
        emit(`crud-${crudMode.value}`, formData)
        closeCrudModal()
        return
      }

      crudLoading.value = true

      try {
        let result
        if (crudMode.value === 'create') {
          result = await crudApiClient.value.create(formData)
          emit('crud-create', result)
        } else {
          result = await crudApiClient.value.update(crudData.value.id, formData)
          emit('crud-update', result)
        }

        closeCrudModal()
        handleRefresh()
      } catch (error) {
        console.error('CRUD operation failed:', error)
        // Handle error (show notification, etc.)
      } finally {
        crudLoading.value = false
      }
    }

    const handleDelete = async (item) => {
      if (!confirm('Are you sure you want to delete this item?')) return

      if (!crudApiClient.value) {
        emit('crud-delete', item)
        return
      }

      try {
        await crudApiClient.value.delete(item.id)
        emit('crud-delete', item)
        handleRefresh()
      } catch (error) {
        console.error('Delete operation failed:', error)
      }
    }

    const handleBulkDelete = async () => {
      if (!confirm(`Are you sure you want to delete ${dataTable.selectedItems.value.length} items?`)) return

      if (!crudApiClient.value) {
        emit('crud-delete', dataTable.selectedItems.value)
        return
      }

      try {
        const ids = dataTable.selectedItems.value.map(item => item.id)
        await crudApiClient.value.bulkDelete(ids)
        emit('crud-delete', dataTable.selectedItems.value)
        dataTable.clearSelection()
        handleRefresh()
      } catch (error) {
        console.error('Bulk delete operation failed:', error)
      }
    }

    // Export Methods
    const toggleExportMenu = () => {
      showExportMenu.value = !showExportMenu.value
    }

    const exportData = (format) => {
      const filename = props.exportConfig?.filename || 'table-export'
      const exportItems = dataTable.selectedItems.value.length > 0
        ? dataTable.selectedItems.value
        : dataTable.filteredItems.value

      const columnLabels = {}
      visibleColumns.value.forEach(col => {
        columnLabels[col.key] = col.label
      })

      switch (format) {
        case 'csv':
          exportToCsv(exportItems, columnLabels, filename)
          break
        case 'excel':
          exportToExcel(exportItems, columnLabels, filename)
          break
        case 'json':
          exportToJson(exportItems, filename)
          break
        case 'pdf':
          // TODO: Implement PDF export
          console.log('PDF export not yet implemented')
          break
      }

      emit('export', format, exportItems)
      showExportMenu.value = false
    }

    const printTable = () => {
      // TODO: Implement print functionality
      console.log('Print functionality not yet implemented')
    }

    // Expose data table methods and state
    return {
      // Data table state and methods
      ...dataTable,

      // Component state
      showCrudModal,
      crudMode,
      crudData,
      crudLoading,
      showExportMenu,
      filterData,

      // Computed
      themeClasses,
      visibleColumns,
      hasRowActions,
      totalColumns,
      crudFields,
      crudValidation,
      crudModalTitle,

      // Methods
      handleSort,
      handleRowClick,
      handleCellClick,
      handleRefresh,
      handleFiltersApply,
      openCreateModal,
      openEditModal,
      closeCrudModal,
      handleCrudSubmit,
      handleDelete,
      handleBulkDelete,
      toggleExportMenu,
      exportData,
      printTable
    }
  }
}
</script>
